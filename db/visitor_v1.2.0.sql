ALTER TABLE `visitor_office_park`
    ADD COLUMN `apply_type` int(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '园区访客申请类型，1：普通，2：团访 3：接待, 4：临时驻场';

ALTER TABLE `visitor_office_park`
DROP
INDEX `uk_park_code_building_code_floor_code`,
ADD UNIQUE INDEX `uk_park_code_building_code_floor_code_apply_type`(`park_code`, `building_code`, `floor_code`, `apply_type`) USING BTREE;

ALTER TABLE `visitor_visit_reason`
    ADD COLUMN `apply_type` int(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '园区访客申请类型，1：普通，2：团访 3：接待, 4：临时驻场';

ALTER TABLE `visitor_apply`
    ADD COLUMN `visit_end_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '来访结束时间',
    ADD COLUMN `complete_type` int(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '完成类型',
    ADD COLUMN `complete_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '完成时间';

ALTER TABLE `visitor_apply_visitor_info`
    ADD COLUMN `visit_info_status` int(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '访客信息状态',
    ADD COLUMN `visit_info_complete_type` int(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '完成类型',
    ADD COLUMN `visit_info_complete_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '完成时间';

ALTER TABLE `visitor_visit_reason`
DROP
INDEX `idx_name`,
ADD UNIQUE INDEX `uniq_name`(`name`, `apply_type`) USING BTREE;

INSERT INTO `safety_ums_config`
(`id`, `app_code`, `message_type`, `message_name`, `bot_biz_id`, `template_biz_id`, `template_content`)
VALUES (18, 'visitor', 3, '临时驻场邀请单通过，短信通知驻场人员', 'B0001', 'TMB000100125',
        '${visitorName!}您好，\n${applyUserName!}邀请您到${parkName!}驻场办公，驻场期间请您严格遵守小米安全管理相关规定，严禁进入保密区域；发生违规行为，将按规定处罚并追究法律责任。\n签入地点：${parkAddress!}。\n驻场期限：${visitDate!}至${visitEndDate!}\n到访前，微信搜索“小米访客”小程序完成信息登记，到达后请您按现场指引办理签入。感谢您的支持与配合！'),
       (19, 'visitor', 1, '超过临时驻场结束日期未审批完成，取消BPM审批并发送通知', 'B0198', 'TLB019800122', NULL),
       (20, 'visitor', 1, '临时驻场申请审核通过，飞书通知接待人', 'B0198', 'TLB019800121', NULL),
       (21, 'visitor', 3, '临时驻场取消邀请单，短信通知访客', 'B0001', 'TMB000100126', ''),
       (22, 'visitor', 1, '临时驻场取消邀请单，飞书通知接待人', 'B0198', 'TLB019800123', NULL),
       (23, 'visitor', 1, '临时驻场到访，飞书通知接待人', 'B0198', 'TLB019800124', NULL),
       (24, 'visitor', 1, '临时驻场未到访，飞书通知接待人', 'B0198', 'TLB019800125', NULL),
       (25, 'visitor', 1, '临时驻场到访预提醒，飞书通知接待人', 'B0198', 'TLB019800126', NULL),
       (26, 'visitor', 1, '临时驻场签到日报提醒，飞书通知接待人', 'B0198', 'TLB019800127', NULL),
       (27, 'visitor', 1, '临时驻场结束提醒，飞书通知接待人', 'B0198', 'TLB019800128', NULL)
;

ALTER TABLE `safety_carrier`
    CHANGE COLUMN `carrier_type` `carrier_type` INT(4) NOT NULL DEFAULT '0' COMMENT '载体类型 0：默认  1：门禁   2：虚拟闸机   3：抬杆   4：闸机 ' ;
ALTER TABLE `safety_carrier`
    CHANGE COLUMN `status` `status` INT(4) NOT NULL DEFAULT '0' COMMENT '载体状态 0:待启用 1:常闭  2:正常 3:常开';
ALTER TABLE `safety_carrier_group`
    CHANGE COLUMN `carrier_group_type` `carrier_group_type` INT(4) NOT NULL DEFAULT '0' COMMENT '载体集类型 0：默认  1：门禁   2：虚拟闸机   3：抬杆   4：闸机 ' ;
ALTER TABLE `safety_record`
    CHANGE COLUMN `carrier_type` `carrier_type` INT(4) NOT NULL DEFAULT '0' COMMENT '载体类型 0：默认  1：门禁   2：虚拟闸机   3：抬杆   4：闸机 ' AFTER `medium_code`;
ALTER TABLE `safety_medium`
    CHANGE COLUMN `medium_type` `medium_type` INT(4) NOT NULL DEFAULT '0' COMMENT '介质类型，0：默认  1：工卡   2：访问码   3：车牌 ';
ALTER TABLE `safety_medium`
    CHANGE COLUMN `sync_status` `sync_status` INT(4) NOT NULL DEFAULT '200' COMMENT '同步状态 0：未同步 1：同步一次 2：同步两次 200：同步成功 ';
ALTER TABLE `visitor_apply`
    CHANGE COLUMN `apply_type` `apply_type` INT(4) NOT NULL DEFAULT '0' COMMENT '申请单类型，1：普通，2：团访 3：接待 4：临时驻场 ';



INSERT INTO `safety_carrier_group` (`carrier_group_code`, `name`, `description`, `carrier_group_type`, `supplier_code`, `supplier_access_code`, `park_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-SOUTH-GROUP', '北京-北京科技园-南门所有闸机', '北京-北京科技园-南门所有闸机', '4', 'zyd', 'BJ-HDKJY-OUTDOOR-SOUTH-GROUP', 'BJ01', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-SOUTH-GROUP', 'BJ-HDKJY-OUTDOOR-SOUTH-001', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-SOUTH-GROUP', 'BJ-HDKJY-OUTDOOR-SOUTH-002', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-SOUTH-GROUP', 'BJ-HDKJY-OUTDOOR-SOUTH-003', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-SOUTH-GROUP', 'BJ-HDKJY-OUTDOOR-SOUTH-004', 'luoyifan1', 'luoyifan1');

INSERT INTO `safety_carrier_group` (`carrier_group_code`, `name`, `description`, `carrier_group_type`, `supplier_code`, `supplier_access_code`, `park_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-WEST-GROUP', '北京-北京科技园-西门所有闸机', '北京-北京科技园-西门所有闸机', '4', 'zyd', 'BJ-HDKJY-OUTDOOR-WEST-GROUP', 'BJ01', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-WEST-GROUP', 'BJ-HDKJY-OUTDOOR-WEST-001', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-WEST-GROUP', 'BJ-HDKJY-OUTDOOR-WEST-002', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-WEST-GROUP', 'BJ-HDKJY-OUTDOOR-WEST-003', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-WEST-GROUP', 'BJ-HDKJY-OUTDOOR-WEST-004', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-WEST-GROUP', 'BJ-HDKJY-OUTDOOR-WEST-005', 'luoyifan1', 'luoyifan1');

INSERT INTO `safety_carrier_group` (`carrier_group_code`, `name`, `description`, `carrier_group_type`, `supplier_code`, `supplier_access_code`, `park_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-NORTH-GROUP', '北京-北京科技园-北门所有闸机', '北京-北京科技园-北门所有闸机', '4', 'zyd', 'BJ-HDKJY-OUTDOOR-NORTH-GROUP', 'BJ01', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-NORTH-GROUP', 'BJ-HDKJY-OUTDOOR-NORTH-001', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-NORTH-GROUP', 'BJ-HDKJY-OUTDOOR-NORTH-002', 'luoyifan1', 'luoyifan1');

INSERT INTO `safety_carrier_group` (`carrier_group_code`, `name`, `description`, `carrier_group_type`, `supplier_code`, `supplier_access_code`, `park_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-EAST-GROUP', '北京-北京科技园-东门所有闸机', '北京-北京科技园-东门所有闸机', '4', 'zyd', 'BJ-HDKJY-OUTDOOR-EAST-GROUP', 'BJ01', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-EAST-GROUP', 'BJ-HDKJY-OUTDOOR-EAST-001', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-EAST-GROUP', 'BJ-HDKJY-OUTDOOR-EAST-002', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-EAST-GROUP', 'BJ-HDKJY-OUTDOOR-EAST-003', 'luoyifan1', 'luoyifan1');
INSERT INTO `safety_carrier_group_carrier` (`carrier_group_code`, `carrier_code`, `create_user`, `update_user`) VALUES ('BJ-HDKJY-OUTDOOR-EAST-GROUP', 'BJ-HDKJY-OUTDOOR-EAST-004', 'luoyifan1', 'luoyifan1');

INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-SOUTH-001', 'NORMAL', '北京-北京科技园-南1闸机', '北京-北京科技园-南1闸机','36', '4','1','BJ-HDKJY-OUTDOOR-SOUTH-001','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-SOUTH-002', 'NORMAL', '北京-北京科技园-南2闸机', '北京-北京科技园-南2闸机','36', '4','1','BJ-HDKJY-OUTDOOR-SOUTH-002','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-SOUTH-003', 'NORMAL', '北京-北京科技园-南3闸机', '北京-北京科技园-南3闸机','36', '4','1','BJ-HDKJY-OUTDOOR-SOUTH-003','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-SOUTH-004', 'NORMAL', '北京-北京科技园-南4闸机', '北京-北京科技园-南4闸机','36', '4','1','BJ-HDKJY-OUTDOOR-SOUTH-004','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-EAST-001', 'NORMAL', '北京-北京科技园-东1闸机', '北京-北京科技园-东1闸机','36', '4','1','BJ-HDKJY-OUTDOOR-EAST-001','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-EAST-002', 'NORMAL', '北京-北京科技园-东2闸机', '北京-北京科技园-东2闸机','36', '4','1','BJ-HDKJY-OUTDOOR-EAST-002','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-EAST-003', 'NORMAL', '北京-北京科技园-东3闸机', '北京-北京科技园-东3闸机','36', '4','1','BJ-HDKJY-OUTDOOR-EAST-003','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-EAST-004', 'NORMAL', '北京-北京科技园-东4闸机', '北京-北京科技园-东4闸机','36', '4','1','BJ-HDKJY-OUTDOOR-EAST-004','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-NORTH-001', 'NORMAL', '北京-北京科技园-北1闸机', '北京-北京科技园-北1闸机','36', '4','1','BJ-HDKJY-OUTDOOR-NORTH-001','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-NORTH-002', 'NORMAL', '北京-北京科技园-北2闸机', '北京-北京科技园-北2闸机','36', '4','1','BJ-HDKJY-OUTDOOR-NORTH-002','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-WEST-001', 'NORMAL', '北京-北京科技园-西1闸机', '北京-北京科技园-西1闸机','36', '4','1','BJ-HDKJY-OUTDOOR-WEST-001','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-WEST-002', 'NORMAL', '北京-北京科技园-西2闸机', '北京-北京科技园-西2闸机','36', '4','1','BJ-HDKJY-OUTDOOR-WEST-002','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-WEST-003', 'NORMAL', '北京-北京科技园-西3闸机', '北京-北京科技园-西3闸机','36', '4','1','BJ-HDKJY-OUTDOOR-WEST-003','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-WEST-004', 'NORMAL', '北京-北京科技园-西4闸机', '北京-北京科技园-西4闸机','36', '4','1','BJ-HDKJY-OUTDOOR-WEST-004','2','BJ01','','','luoyifan1','luoyifan1');
INSERT INTO `safety_carrier` (`supplier_code`, `carrier_code`, `class_code`, `name`,`description`,`city_id`,`carrier_type`,`source`,`supplier_control_serial`,`status`,`park_code`,`building_code`,`floor_code`,`create_user`,`update_user`) VALUES ('zyd', 'BJ-HDKJY-OUTDOOR-WEST-005', 'NORMAL', '北京-北京科技园-西5闸机', '北京-北京科技园-西5闸机','36', '4','1','BJ-HDKJY-OUTDOOR-WEST-005','2','BJ01','','','luoyifan1','luoyifan1');




