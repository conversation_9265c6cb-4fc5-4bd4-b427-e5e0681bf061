<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- 申明是一个springboot项目 -->
    <parent>
        <groupId>com.mi.oa.infra.oaucf</groupId>
        <artifactId>oaucf-springboot-parent</artifactId>
        <version>1.0.15-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <!-- 指定了当前POM模型的版本，对于Maven2及Maven 3来说，它只能是4.0.0 -->
    <modelVersion>4.0.0</modelVersion>
    <!-- 当前pom的信息-->
    <groupId>com.mi.oa.ee.safety</groupId>
    <artifactId>safety-be-parent</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <name>safety-be-parent</name>
    <description>oa safety project</description>

    <!-- 属于当前pom的模块-->
    <modules>
        <module>safety-be-application</module>
        <module>safety-be-common/safety-be-common</module>
        <module>safety-be-common/safety-be-common-application</module>
        <module>safety-be-common/safety-be-common-domain</module>
        <module>safety-be-common/safety-be-common-infra</module>
        <module>safety-be-domain</module>
        <module>safety-be-infra</module>
        <module>safety-be-interface/safety-be-api</module>
        <module>safety-be-interface/safety-be-task</module>
        <module>safety-be-interface/card-be-api</module>
        <module>safety-be-interface/visitor-be-api</module>
        <module>safety-be-interface/reception-be-api</module>
    </modules>

    <!-- 版本参数管理-->
    <properties>
        <!-- sonar.exclusions为不扫描文件配置 -->
        <sonar.exclusions>**/**DO.java,**/**Test.java</sonar.exclusions>
        <!--sonar.cpd.exclusions为重复代码检查排除配置，CI模板默认排除**/**DO.java,**/**BO.java,**/**DTO.java,**/**VO.java,
             **/**PO.java,**/**Model.java,**/**Entity.java,**/**Bean.java-->
        <sonar.cpd.exclusions>**/**Test.java</sonar.cpd.exclusions>
        <revision>1.0-SNAPSHOT</revision>
        <!-- spring相关-->
        <springfox-boot.version>3.0.0</springfox-boot.version>
        <!--基础 build的插件版本 -->
        <java.version>1.8</java.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-compiler-plugin.encoding>UTF-8</maven-compiler-plugin.encoding>
        <maven-source-plugin.version>2.4</maven-source-plugin.version>
        <maven-checkstyle-plugin.version>2.17</maven-checkstyle-plugin.version>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.5</jacoco-maven-plugin.version>
        <mybatis-generator-maven-plugin.version>1.4.0</mybatis-generator-maven-plugin.version>
        <!-- 工具包的版本 -->
        <easyexcel.version>3.0.5</easyexcel.version>
        <pagehepler.spring.boot.version>1.3.0</pagehepler.spring.boot.version>
        <hutool.version>5.3.7</hutool.version>
        <micrometer-registry-prometheus.version>1.7.0</micrometer-registry-prometheus.version>
        <lombok.version>1.18.8</lombok.version>
        <mapstruct.version>1.3.1.Final</mapstruct.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-lang3.version>3.10</commons-lang3.version>
        <commons-pool.verison>2.6.1</commons-pool.verison>
        <guava.version>23.0</guava.version>
        <javax.persistence-api.version>2.2</javax.persistence-api.version>
        <assertj-core.version>3.16.1</assertj-core.version>
        <commons-io.version>2.6</commons-io.version>
        <gson.version>2.8.6</gson.version>
        <httpclient5.version>5.0.1</httpclient5.version>
        <zookeeper.version>3.4.14</zookeeper.version>
        <micrometer-core.version>1.5.5</micrometer-core.version>
        <okhttp.version>4.9.1</okhttp.version>
        <core.version>3.3.3</core.version>
        <javase.version>3.3.3</javase.version>
        <!-- 跟数据源相关的版本 -->
        <orika.spring.version>1.8.0</orika.spring.version>
        <hibernate-validator.version>6.0.18.Final</hibernate-validator.version>
        <mybatis-typehandlers-jsr310.version>1.0.2</mybatis-typehandlers-jsr310.version>
        <mybatis-plus.version>3.4.1</mybatis-plus.version>
        <mysql-connector-java.version>8.0.11</mysql-connector-java.version>
        <dynamic-datasource-spring-boot-starter.version>3.4.1</dynamic-datasource-spring-boot-starter.version>
        <!-- 跟单元测试相关的版本 -->
        <junit.version>4.13</junit.version>
        <powermock.version>2.0.9</powermock.version>
        <!-- 小米自己的组件及jar包版本 -->
        <comb.version>1.1.9-SNAPSHOT</comb.version>
        <xms-plan-spring-boot-starter.version>2.1.8.release</xms-plan-spring-boot-starter.version>
        <common-x5protocol-core.version>1.1.9-SNAPSHOT</common-x5protocol-core.version>
        <mit-starter.version>1.0.27</mit-starter.version>
        <mit-core.version>1.1.8</mit-core.version>
        <keycenter-agent-client.version>3.5.3</keycenter-agent-client.version>
        <ums-api-sdk.version>1.8.8-SNAPSHOT</ums-api-sdk.version>
        <valid-api-sdk.version>1.0.8-SNAPSHOT</valid-api-sdk.version>
        <jwt.version>0.9.1</jwt.version>
        <notify-sdk.version>1.0.2-SNAPSHOT</notify-sdk.version>
        <x5client-version>1.1.9-SNAPSHOT</x5client-version>
        <obix.version>1.1.0</obix.version>
        <galaxy-fds-sdk-java.version>3.0.37</galaxy-fds-sdk-java.version>
        <hrod-api-sdk>1.0.32-RELEASE</hrod-api-sdk>
        <nacos-config.version>0.2.13-xiaomi</nacos-config.version>
        <ps-api-sdk>1.0.12-SNAPSHOT</ps-api-sdk>
        <oaucf-neptune.version>1.0.12-SNAPSHOT</oaucf-neptune.version>
        <oaucf-console.version>1.4.0-SNAPSHOT</oaucf-console.version>
        <fastjson-sdk>1.2.70</fastjson-sdk>
        <apach-http-sdk>4.5.8</apach-http-sdk>
        <apach-bcprov-sdk>1.68</apach-bcprov-sdk>
        <rocketmq.version>2.2.0-mdh2.2.4-RELEASE</rocketmq.version>
        <ok-http-sdk>2.7.5</ok-http-sdk>
        <shiro-core-sdk>1.5.2</shiro-core-sdk>
        <card-open-sdk.version>3.1.1-SNAPSHOT</card-open-sdk.version>
        <artemis-http-client.version>1.1.11.RELEASE</artemis-http-client.version>
        <cglib-version>3.3.0</cglib-version>
        <oaucf-paycenter.version>1.0.13-SNAPSHOT</oaucf-paycenter.version>
        <oaucf-newauth.version>1.0.15-lark-saas-SNAPSHOT</oaucf-newauth.version>
        <oaucf.version>1.1.3</oaucf.version>
        <bpm-sdk.version>1.2.5-SNAPSHOT</bpm-sdk.version>
        <lark-sdk.version>2.4.10</lark-sdk.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.larksuite.oapi</groupId>
                <artifactId>oapi-sdk</artifactId>
                <version>${lark-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.mibpm</groupId>
                <artifactId>mibpm-bpm-spring-boot-starter</artifactId>
                <version>${bpm-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib-version}</version>
            </dependency>
            <dependency>
                <groupId>com.hikvision.ga</groupId>
                <artifactId>artemis-http-client</artifactId>
                <version>${artemis-http-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp</groupId>
                <artifactId>okhttp</artifactId>
                <version>${ok-http-sdk}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${apach-bcprov-sdk}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>fluent-hc</artifactId>
                <version>${apach-http-sdk}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson-sdk}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-ps-spring-boot-starter</artifactId>
                <version>${ps-api-sdk}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-organization-spring-boot-starter</artifactId>
                <version>${oaucf-organization.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-idm-api-spring-boot-starter</artifactId>
            </dependency>
            <!--obix-->
            <dependency>
                <groupId>obix</groupId>
                <artifactId>obix</artifactId>
                <version>${obix.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.oa.hr.hrod</groupId>
                <artifactId>hrod-api-spring-boot-starter</artifactId>
                <version>${hrod-api-sdk}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.comb</groupId>
                <artifactId>x5client-comb-spring-boot-autoconfigure</artifactId>
                <version>${x5client-version}</version>
            </dependency>

            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>3.8.5</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <!-- 小米自己的组件及jar-->
            <dependency>
                <groupId>com.xiaomi.info.infra</groupId>
                <artifactId>notify-sdk</artifactId>
                <!-- 1.0-SNAPSHOT 版本存在 jackson和gson 兼容问题 -->
                <version>${notify-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.cloud</groupId>
                <artifactId>miplan-springboot-starter</artifactId>
                <version>${xms-plan-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mit</groupId>
                <artifactId>mit-starter</artifactId>
                <version>${mit-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi</groupId>
                <artifactId>keycenter-agent-client</artifactId>
                <version>${keycenter-agent-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mioffice.ums</groupId>
                <artifactId>api-sdk</artifactId>
                <version>${ums-api-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.infra.galaxy</groupId>
                <artifactId>galaxy-fds-sdk-java</artifactId>
                <version>${galaxy-fds-sdk-java.version}</version>
            </dependency>

            <!-- 自身的项目jar包注册 -->
            <dependency>
                <groupId>com.mi.oa.ee.safety</groupId>
                <artifactId>safety-be-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.ee.safety</groupId>
                <artifactId>safety-be-common-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.ee.safety</groupId>
                <artifactId>safety-be-common-infra</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.ee.safety</groupId>
                <artifactId>safety-be-common-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.ee.safety</groupId>
                <artifactId>safety-be-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.ee.safety</groupId>
                <artifactId>safety-be-infra</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.ee.safety</groupId>
                <artifactId>safety-be-domain</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 工具相关 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehepler.spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.comb</groupId>
                <artifactId>x5client-comb-spring-boot-starter</artifactId>
                <version>${comb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool.verison}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>${micrometer-core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${micrometer-registry-prometheus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${javase.version}</version>
            </dependency>

            <!-- 测试 -->
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj-core.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>

            <!-- ORM框架依赖 -->
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>${javax.persistence-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-typehandlers-jsr310</artifactId>
                <version>${mybatis-typehandlers-jsr310.version}</version>
            </dependency>
            <dependency>
                <groupId>net.rakugakibox.spring.boot</groupId>
                <artifactId>orika-spring-boot-starter</artifactId>
                <version>${orika.spring.version}</version>
            </dependency>

            <!-- 数据库驱动依赖 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-actuator</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>
            <!-- 国际化 -->
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-neptune-spring-boot-starter</artifactId>
                <version>${oaucf-neptune.version}</version>
            </dependency>
            <!-- 国际化 end-->
            <dependency>
                <groupId>com.mi.oa.infra.console</groupId>
                <artifactId>console-be-sdk</artifactId>
                <version>${oaucf-console.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.mioffice.ee</groupId>
                <artifactId>card-open-sdk</artifactId>
                <version>${card-open-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-newauth-spring-boot-starter</artifactId>
                <version>${oaucf-newauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-paycenter-spring-boot-starter</artifactId>
                <version>${oaucf-paycenter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-mdm-api-spring-boot-starter</artifactId>
                <version>${oaucf.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-core</artifactId>
            <version>${mit-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.oaucf</groupId>
            <artifactId>oaucf-base</artifactId>
        </dependency>
        <!-- 工具相关 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <!-- 测试 -->
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.8.0</version> <!-- 使用最新版本 -->
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.8.0</version> <!-- 使用最新版本 -->
            <scope>test</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net:443/artifactory/maven-release-virtual</url>
        </repository>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>miremote</id>
            <name>maven-remote-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual</url>
        </repository>
        <repository>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>interval:10</updatePolicy>
            </snapshots>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net:443/artifactory/maven-snapshot-virtual</url>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <env>local</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <env>pre</env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/resources-env/${env}</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <encoding>${maven-compiler-plugin.encoding}</encoding>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <testSource>${java.version}</testSource>
                    <testTarget>${java.version}</testTarget>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version> <!-- latest 0.8.3 -->
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.2</version>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <configuration>
                            <!-- checkstyle xml 规则 -->
                            <configLocation>https://git.n.xiaomi.com/coding-standards/mit/-/raw/master/checkstyle.xml
                            </configLocation>
                            <!--<exclud·infra/XXX/*.java</excludes>-->
                            <encoding>UTF-8</encoding>
                            <consoleOutput>true</consoleOutput>
                            <failsOnError>true</failsOnError>

                            <!-- default value ${project.compileSourceRoots} also scans generated code. -->
                            <!-- we only need scan `src/main/java` and `src/test/java`. -->
                            <sourceDirectories>${project.build.sourceDirectory},${project.build.testSourceDirectory}
                            </sourceDirectories>
                        </configuration>
                        <!--                        <goals>
                                                    <goal>check</goal>
                                                </goals>-->
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
