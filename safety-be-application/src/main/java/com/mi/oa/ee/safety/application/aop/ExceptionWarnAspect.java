package com.mi.oa.ee.safety.application.aop;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.annotation.ExceptionWarn;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.domain.ability.SafetyUmsNotifyAbility;
import com.mi.oa.ee.safety.domain.model.SafetyUmsNotifyDo;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/1/12 21:18
 */
@Component
@Aspect
@Slf4j
public class ExceptionWarnAspect {
    @Value("${app.exceptionWarnAdmin:liyunhui1,v-tianjiayin,v-luyunfeng}")
    String exceptionWarnAdmin;

    @Autowired
    SafetyUmsNotifyAbility safetyUmsNotifyAbility;

    @AfterThrowing(value = "@annotation(exceptionWarn)", throwing = "ex")
    public void afterThrowing(JoinPoint joinPoint, Exception ex, ExceptionWarn exceptionWarn) {
        if (Arrays.stream(exceptionWarn.exception()).noneMatch(exception -> exception.isAssignableFrom(ex.getClass()))) {
            return;
        }

        List<SafetyUmsNotifyDo> safetyUmsNotifyDos = Lists.newArrayList();
        ZonedDateTime now = ZonedDateTime.now();
        Arrays.stream(exceptionWarnAdmin.split(",")).forEach(userName -> {
            SafetyUmsNotifyDo ums = new SafetyUmsNotifyDo();
            ums.setAppCode(AppCodeEnum.VISITOR.getAppCode());
            ums.setSendTime(now);
            ums.setConfigId(SafetyUmsConfigEnum.PARTNER_EXCEPTION_WARN.getCode());
            ums.setSendStatus(OAUCFCommonConstants.INT_ZERO);
            Map<String, Object> params = new HashMap<>();
            params.put("bizName", exceptionWarn.value().replaceAll("\"", ""));
            params.put("bizParam", Arrays.toString(joinPoint.getArgs()).replaceAll("\"", ""));
            params.put("bizMsg", ex.getMessage().replaceAll("\"", ""));
            ums.setParams(JacksonUtils.bean2Json(params));
            ums.setReceiver(userName);
            safetyUmsNotifyDos.add(ums);
        });
        safetyUmsNotifyAbility.batchCreateNotify(safetyUmsNotifyDos);
    }
}
