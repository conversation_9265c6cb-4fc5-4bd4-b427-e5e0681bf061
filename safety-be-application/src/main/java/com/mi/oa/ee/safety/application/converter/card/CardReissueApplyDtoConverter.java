package com.mi.oa.ee.safety.application.converter.card;

import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardApplyDto;
import com.mi.oa.ee.safety.domain.model.CardReissueApplyDo;
import com.mi.oa.ee.safety.infra.repository.query.CardReissueApplyQuery;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/3/26 16:50
 */
@Mapper(componentModel = "spring")
public interface CardReissueApplyDtoConverter {

    @Mapping(target = "reissueCode", source = "reissueApplyCode")
    @Mapping(target = "id", source = "reissueApplyId")
    @Mapping(target = "cardApplyDo.receiptParkCode", source = "receiptParkCode")
    @Mapping(target = "cardApplyDo.parkCode", source = "parkCode")
    @Mapping(target = "cardApplyDo.photoUrl", source = "photoUrl")
    @Mapping(target = "cardInfo.cardNum", source = "cardNum")
    @Mapping(target = "cardInfo.startTime", source = "startTime")
    @Mapping(target = "cardInfo.endTime", source = "endTime")
    @Mapping(target = "cardInfo.mediumPhysicsCode", source = "mediumPhysicsCode")
    @Mapping(target = "cardInfo.mediumEncryptCode", source = "mediumEncryptCode")
    @Mapping(target = "reissueStatus", source = "reissueApplyStatus")
    @Mapping(target = "cardInfo.prefixEncryptCode", source = "prefixEncryptCode")
    @Mapping(target = "cardInfo.suffixEncryptCode", source = "suffixEncryptCode")
    CardReissueApplyDo toDo(ReissueCardApplyDto reissueCardApplyDto);

    @Mapping(target = "reissueCode", source = "reissueApplyCode")
    @Mapping(target = "reissueStatus", source = "reissueApplyStatus")
    @Mapping(target = "queryStartTime", source = "queryStartTime", qualifiedByName = "zoneTimeConverterToLong")
    @Mapping(target = "queryEndTime", source = "queryEndTime", qualifiedByName = "zoneTimeConverterToLong")
    CardReissueApplyQuery toQuery(ReissueCardApplyDto reissueCardApplyDto);

    @Named("zoneTimeConverterToLong")
    default Long zoneTimeConverterToLong(ZonedDateTime zonedDateTime) {
        if (ObjectUtils.isEmpty(zonedDateTime)) {
            return null;
        }
        return zonedDateTime.toEpochSecond();
    }

    List<ReissueCardApplyDto> toDtoList(List<CardReissueApplyDo> list);

    @Mapping(target = "reissueApplyCode", source = "reissueCode")
    @Mapping(target = "reissueApplyStatus", source = "reissueStatus")
    @Mapping(target = "firstDeptName", source = "safetyPersonDo.firstDeptName")
    @Mapping(target = "secondDeptName", source = "safetyPersonDo.secondDeptName")
    @Mapping(target = "thirdDeptName", source = "safetyPersonDo.thirdDeptName")
    @Mapping(target = "fourthDeptName", source = "safetyPersonDo.fourthDeptName")
    @Mapping(target = "displayName", source = "safetyPersonDo.displayName")
    @Mapping(target = "name", source = "safetyPersonDo.name")
    @Mapping(target = "firstNameEn", source = "safetyPersonDo.firstNameEn")
    @Mapping(target = "lastNameEn", source = "safetyPersonDo.lastNameEn")
    @Mapping(target = "employeeNo", source = "safetyPersonDo.employeeId")
    @Mapping(target = "email", source = "safetyPersonDo.email")
    @Mapping(target = "photoUrl", source = "cardApplyDo.photoUrl")
    @Mapping(target = "reissueApplyId", source = "id")
    @Mapping(target = "userName", source = "safetyPersonDo.userName")
    @Mapping(target = "employeeTypeDesc", source = "safetyPersonDo.cnType")
    @Mapping(target = "parkName", source = "cardApplyDo.parkName")
    @Mapping(target = "parkCode", source = "cardApplyDo.parkCode")
    @Mapping(target = "receiptParkCode", source = "cardApplyDo.receiptParkCode")
    @Mapping(target = "receiptParkName", source = "cardApplyDo.receiptParkName")
    @Mapping(target = "startTime", source = "cardApplyDo.startTime")
    @Mapping(target = "endTime", source = "cardApplyDo.endTime")
    @Mapping(target = "backTime", source = "cardInfo.backTime")
    @Mapping(target = "cardNum", source = "cardInfo.cardNum")
    @Mapping(target = "mediumPhysicsCode", source = "cardInfo.mediumPhysicsCode")
    @Mapping(target = "mediumEncryptCode", source = "cardInfo.mediumEncryptCode")
    @Mapping(target = "mediumCode", source = "cardInfo.mediumCode")
    @Mapping(target = "cardId", source = "cardInfo.id")
    @Mapping(target = "workCity", source = "cardApplyDo.cityName")
    @Mapping(target = "cardStatus", source = "cardInfo.cardStatus")
    @Mapping(target = "isReissue", source = "reissueCode", qualifiedByName = "checkIsReissue")
    @Mapping(target = "zoneCode", source = "cardApplyDo.zoneCode")
    @Mapping(target = "phone", source = "cardApplyDo.phone")
    ReissueCardApplyDto toDto(CardReissueApplyDo cardReissueApplyDo);

    @Named("checkIsReissue")
    default boolean checkIsReissue(String reissueCode) {
        return StringUtils.isNotEmpty(reissueCode) && reissueCode.contains("BK");
    }
}
