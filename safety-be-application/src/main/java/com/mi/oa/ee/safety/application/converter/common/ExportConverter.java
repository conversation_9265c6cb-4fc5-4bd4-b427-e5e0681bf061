package com.mi.oa.ee.safety.application.converter.common;

import com.mi.oa.ee.safety.application.dto.card.CardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto;
import com.mi.oa.ee.safety.application.dto.card.EmpCardDto;
import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardApplyDto;
import com.mi.oa.ee.safety.application.dto.card.temp.EmpCardApplyDto;
import com.mi.oa.ee.safety.application.dto.card.temp.EmpCardApplyQueryDto;
import com.mi.oa.ee.safety.application.dto.common.*;
import com.mi.oa.ee.safety.application.dto.safety.SafetyRecordDto;
import com.mi.oa.ee.safety.common.dto.CardPageConditionDto;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.AccountTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.*;
import com.mi.oa.ee.safety.domain.model.AsyncExportTaskDo;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.ZonedDateTime;
import java.util.Objects;

@Mapper(componentModel = "spring")
public interface ExportConverter {

    AsyncExportTaskDo asyncExportTaskDo(AsyncExportTaskDto asyncExportRecordDto);

    AsyncExportTaskDto toDto(AsyncExportTaskDo asyncExportTaskDo);

    CardPageConditionDto exportQueryToPage(AsyncExportQueryDto asyncExportQueryDto);

    EmpCardApplyQueryDto exportQueryToPageEmpCardApply(AsyncCardApplyExportQueryDto asyncCardApplyExportQueryDto);

    CardPageConditionDto exportQueryToPageCoopCard(AsyncExportCoopQueryDto param);

    @Mapping(target = "idNumberTypeDesc", source = "idNumberType", qualifiedByName = "toIdNumberTypeDesc")
    @Mapping(target = "cardTypeDesc", source = "cardType", qualifiedByName = "toCardTypeDesc")
    @Mapping(target = "cardStatusDesc", source = "cardStatus", qualifiedByName = "toCardStatusDesc")
    AsyncExportCoopExcelDto dtoToCoopCardExcel(CardInfoDto queryResult);

    @Named("toIdNumberTypeDesc")
    default String toIdNumberTypeDesc(Integer idNumberType) {
        return IdNumberTypeEnum.getDesc(idNumberType);
    }

    @Named("toCardTypeDesc")
    default String toCardTypeDesc(Integer cardType) {
        return CardTypeEnum.getDesc(cardType);
    }

    @Named("toCardStatusDesc")
    default String toCardStatusDesc(Integer cardStatus) {
        return CardStatusEnum.getDesc(cardStatus);
    }

    @Mapping(target = "applyType", source = "cardType", qualifiedByName = "cardTypeCodeToNum")
    CardPartnerApplyDto exportQueryToPageCoopCardApply(AsyncCoopCardApplyExportQueryDto param);
    @Named("cardTypeCodeToNum")
    default Integer cardTypeCodeToNum(String cardType) {
        CardTypeEnum cardTypeEnum = CardTypeEnum.ofCode(cardType);
        if (Objects.nonNull(cardTypeEnum)) {
            return cardTypeEnum.getNumber();
        }
        return null;
    }

    @Mapping(target = "applyStatusDesc", source = "applyStatus", qualifiedByName = "toApplyStatusDesc")
    AsyncCoopCardApplyExportExcelDto dtoToCoopCardApplyExcel(CardPartnerApplyDto queryResult);
    @Named("toApplyStatusDesc")
    default String toApplyStatusDesc(Integer applyStatus) {
        return CardApplyStatusEnum.getDesc(applyStatus);
    }

    @Mapping(target = "cardType", source = "cardType", qualifiedByName = "stringToInt")
    @Mapping(target = "employeeNo", source = "empNo")
    ReissueCardApplyDto toReissueCardApplyDto(AsyncReissueCardApplyExportQueryDto param);

//    @Mapping(target = "employeeTypeDesc", source = "employeeTypeDesc", qualifiedByName = "dealEmployeeTypeDesc")
    @Mapping(target = "fullPinyinName", expression = "java(queryResult.getLastNameEn() + queryResult.getFirstNameEn())")
    @Mapping(target = "applyStatusName", source = "reissueApplyStatus", qualifiedByName = "statusToDesc")
    AsyncReissueCardApplyExportExcelDto toReissueCardApplyExportExcel(ReissueCardApplyDto queryResult);

    @Mapping(target = "fullPinyinName", expression = "java(queryResult.getLastNameEn() + queryResult.getFirstNameEn())")
    @Mapping(target = "applyStatusName", source = "reissueApplyStatus", qualifiedByName = "statusToDesc")
    AsyncReissueCoopPropCardApplyExportExcelDto toReissueCoopPropertyCardApplyExportExcel(ReissueCardApplyDto queryResult);

    @Named("statusToDesc")
    default String statusToDesc(Integer status) {
        if (status != null) {
            return CardReissueApplyStatusEnum.getDesc(status);
        }
        return null;
    }

//    @Named("dealEmployeeTypeDesc")
//    default String dealEmployeeTypeDesc(String employeeTypeDesc) {
//        if (employeeTypeDesc != null) {
//            return employeeTypeDesc.replace("\\{", "").replace("}", "");
//        }
//        return null;
//    }

    @Named("stringToInt")
    default Integer stringToInt(String cardType) {
        CardTypeEnum cardTypeEnum = CardTypeEnum.ofCode(cardType);
        if (cardTypeEnum != null) {
            return cardTypeEnum.getNumber();
        }
        return null;
    }

    SafetyCarrierGroupDto toSafetyCarrierGroupDto(CarrierGroupExportQueryDto param);

    @Mapping(target = "name", source = "displayName")
    @Mapping(target = "account", source = "userName")
    @Mapping(target = "employeeId", source = "employeeNo")
    @Mapping(target = "employeeType", source = "empType", qualifiedByName = "toEmployeeType")
    @Mapping(target = "firstLevelDepartment", source = "firstDeptName")
    @Mapping(target = "secondLevelDepartment", source = "secondDeptName")
    @Mapping(target = "thirdLevelDepartment", source = "thirdDeptName")
    @Mapping(target = "fourthLevelDepartment", source = "fourthDeptName")
    @Mapping(target = "photo", source = "photoUrl")
    @Mapping(target = "officePark", source = "parkName")
    @Mapping(target = "workCountry", source = "nation")
    @Mapping(target = "cardNumber", source = "cardNum")
    @Mapping(target = "physicalCardNumber", source = "mediumPhysicsCode")
    @Mapping(target = "encryptedCardNumber", source = "mediumEncryptCode")
    @Mapping(target = "cardStatus", source = "cardStatus", qualifiedByName = "toCardStatus")
    @Mapping(target = "specialPermissionGroupName", source = "hasSpecialAuth", qualifiedByName = "toSpecialPermissionGroupName")
    AsyncExportExcelDto dtoToAsyncExportExcel(EmpCardDto queryResult);

    @Named("toEmployeeType")
    default String toEmployeeType(Integer empType) {
        return AccountTypeEnum.getcnDesc(empType);
    }

    @Named("toCardStatus")
    default String toCardStatus(Integer cardStatus) {
        return CardStatusEnum.getDesc(cardStatus);
    }

    @Named("toSpecialPermissionGroupName")
    default String toSpecialPermissionGroupName(Integer hasSpecialAuth) {
        return hasSpecialAuth == 1 ? "{{{有}}}" : "{{{无}}}";
    }

    @Mapping(target = "cityName", source = "workCity")
    @Mapping(target = "applyStatusName", source = "applyStatus", qualifiedByName = "toApplyStatusName")
//    @Mapping(target = "employeeTypeDesc", source = "hasSpecialAuth", qualifiedByName = "toSpecialPermissionGroupName")
    AsyncCardApplyExportExcelDto dtoToAsyncCardApplyExportExcel(EmpCardApplyDto queryResult);

    @Named("toApplyStatusName")
    default String toApplyStatusName(Integer applyStatus) {
        return CardApplyStatusEnum.getDesc(applyStatus);
    }

    SafetyRecordDto toSafetyRecordDto(CarrierEntryExitRecordExportQueryDto param);

    @Named("timeConvert")
    default String timeConvert(ZonedDateTime time) {
        return time == null ? null : ZonedDateTimeUtils.formatChinaYMDHMS(time);
    }

    @Mapping(target = "updateTimeDesc", source = "recordTime", qualifiedByName = "timeConvert")
    CarrierEntryExitRecordExportExcelDto toCarrierEntryExitRecordExportExcelDto(SafetyRecordDto queryResult);

    AsyncExportPhotoExcelDto toDownLoadPhotoExcelDto(ExportPhotoDto queryResult);
}
