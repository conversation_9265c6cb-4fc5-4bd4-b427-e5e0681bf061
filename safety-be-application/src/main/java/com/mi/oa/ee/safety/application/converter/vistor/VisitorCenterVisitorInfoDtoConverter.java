package com.mi.oa.ee.safety.application.converter.vistor;

import com.mi.oa.ee.safety.application.dto.visitor.VisitorCenterVisitorInfoDto;
import com.mi.oa.ee.safety.application.dto.visitor.VisitorDetailDto;
import com.mi.oa.ee.safety.application.dto.visitor.VisitorIdentityDto;
import com.mi.oa.ee.safety.application.dto.visitor.VisitorInfoDto;
import com.mi.oa.ee.safety.domain.model.VisitorApplyVisitUserInfoDo;
import com.mi.oa.ee.safety.domain.model.VisitorCenterVisitorInfoDo;
import com.mi.oa.ee.safety.domain.model.VisitorDetailDo;
import com.mi.oa.ee.safety.domain.model.VisitorIdentityDo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 访客中心访客信息dto与do的转换
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/9/6 17:28
 */
@Mapper(componentModel = "spring")
public interface VisitorCenterVisitorInfoDtoConverter {

    /**
    * @desc do -> dto
    * @param visitorInfoDo
    * @return com.mi.oa.ee.safety.application.dto.visitor.VisitorCenterVisitorInfoDto
    * <AUTHOR> denghui
    * @date 2022/9/6 17:31
    */
    VisitorCenterVisitorInfoDto toDto(VisitorCenterVisitorInfoDo visitorInfoDo);

    /**
    * @desc do list -> dto list
    * @param visitorInfoDos
    * @return java.util.List<com.mi.oa.ee.safety.application.dto.visitor.VisitorCenterVisitorInfoDto>
    * <AUTHOR> denghui
    * @date 2022/9/23 17:54
    */
    List<VisitorCenterVisitorInfoDto> toListDto(List<VisitorCenterVisitorInfoDo> visitorInfoDos);

    /**
    * @desc do -> dto
    * @param detailDo
    * @return com.mi.oa.ee.safety.application.dto.visitor.VisitorDetailDto
    * <AUTHOR> denghui
    * @date 2022/9/26 18:15
    */
    VisitorDetailDto toDto(VisitorDetailDo detailDo);

    VisitorIdentityDo toDo(VisitorIdentityDto dto);


    VisitorInfoDto toDto(VisitorApplyVisitUserInfoDo visitUserInfoDo);
}
