package com.mi.oa.ee.safety.application.impl.card.coop;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.converter.card.CardApplyDtoConverter;
import com.mi.oa.ee.safety.application.converter.card.CardDtoConverter;
import com.mi.oa.ee.safety.application.converter.card.CommonCardDtoConverter;
import com.mi.oa.ee.safety.application.converter.safety.SafetyCarrierDtoConverter;
import com.mi.oa.ee.safety.application.converter.safety.SafetyCarrierGroupDtoConverter;
import com.mi.oa.ee.safety.application.converter.safety.SafetyRightDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.CardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.CardLeaveRecordDto;
import com.mi.oa.ee.safety.application.dto.card.EmpCardDto;
import com.mi.oa.ee.safety.application.dto.card.coop.CoopCardEditDto;
import com.mi.oa.ee.safety.application.dto.card.event.CardPinEvent;
import com.mi.oa.ee.safety.application.dto.safety.SafetyCarrierDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyRightDto;
import com.mi.oa.ee.safety.application.errorcode.CardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.ee.safety.application.service.card.electronic.CardElectronRecordService;
import com.mi.oa.ee.safety.application.service.common.event.model.SafetyExportEvent;
import com.mi.oa.ee.safety.application.service.safety.SafetySupplierEngineService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.*;
import com.mi.oa.ee.safety.common.enums.RocketMqTopicEnum;
import com.mi.oa.ee.safety.common.enums.card.*;
import com.mi.oa.ee.safety.common.model.SpecialPageReq;
import com.mi.oa.ee.safety.common.model.SpecialPageVO;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.common.utils.GsonUtils;
import com.mi.oa.ee.safety.domain.enums.ExportStatusEnum;
import com.mi.oa.ee.safety.domain.enums.ExportTypeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.service.*;
import com.mi.oa.ee.safety.infra.remote.mq.CommonProducer;
import com.mi.oa.ee.safety.infra.remote.sdk.HrodSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.repository.*;
import com.mi.oa.ee.safety.infra.repository.query.CardInfoQuery;
import com.mi.oa.ee.safety.infra.repository.query.CardLeaveRecordQuery;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupQuery;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.dto.UserInfoDto;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import com.xiaomi.oa.hr.hrod.boot.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.ZonedDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc 工卡服务
 * @date 2022/11/30 18:37
 */
@Slf4j
@Service
public class CardServiceImpl implements CardService {
    @Autowired
    private CommonCardDtoConverter commonCardDtoConverter;

    @Autowired
    CardDomainService cardDomainService;

    @Autowired
    CardApplyDomainService cardApplyDomainService;

    @Autowired
    SafetyCarrierDomainService safetyCarrierDomainService;

    @Autowired
    SafetyCarrierGroupDomainService safetyCarrierGroupDomainService;

    @Autowired
    SafetyPersonDomainService safetyPersonDomainService;

    @Autowired
    SafetyCarrierGroupDtoConverter safetyCarrierGroupDtoConverter;

    @Autowired
    SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Autowired
    SafetyCarrierRepository safetyCarrierRepository;

    @Autowired
    CardDtoConverter cardDtoConverter;

    @Autowired
    CardApplyDtoConverter cardApplyDtoConverter;

    @Autowired
    SafetyCarrierDtoConverter safetyCarrierDtoConverter;

    @Autowired
    SafetyRightDtoConverter safetyRightDtoConverter;

    @Autowired
    SafetyUmsNotifyDomainService safetyUmsNotifyDomainService;

    @Resource
    private CardLeaveRecordRepository cardLeaveRecordRepository;

    @Resource
    private UserBehaviorRecordRepository userBehaviorRecordRepository;

    @Resource
    private HrodSdk hrodSdk;

    @Resource
    private SafetySupplierEngineService safetySupplierEngineService;

    @Resource
    private CardElectronRecordService cardElectronRecordService;

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Resource
    private CardInfoRepository cardInfoRepository;

    @Resource
    private CommonProducer commonProducer;

    @Resource
    private CardAggregateSearchRepository cardAggregateSearchRepository;

    @Autowired
    private IdmSdk idmSdk;

    @Resource
    IdmRemote idmRemote;

    @Resource
    AsyncExportTaskDomainService asyncExportTaskDomainService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @NacosValue(value = "${card.prefix-encrypt-code:201046}", autoRefreshed = true)
    private String prefixEncryptCode;

    @Resource
    private SafetyRightRepository safetyRightRepository;

    @Resource
    private SafetyRightDomainService safetyRightDomainService;

    @Override
    public List<SafetyCarrierGroupDto> getCarrierGroups(String parkCode, String mediumCode) {
        List<SafetyCarrierGroupDo> carrierGroupDOList = cardDomainService.getListGroupsByParkCode(parkCode, mediumCode);
        return safetyCarrierGroupDtoConverter.toDtoList(carrierGroupDOList);
    }

    @Override
    public PageModel<CardInfoDto> pageConditionList(CardPageConditionDto pageConditionDto, Boolean isExport) {
        PageModel<CardInfoDo> pageModel = cardDomainService.pageConditionList(pageConditionDto, isExport);
        return PageModel.build(cardDtoConverter.toCardDtoList(pageModel.getList()), pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public CardInfoDto getDetailCardInfo(Long id) {
        return cardDtoConverter.toDto(cardDomainService.getDetailCardInfo(id));
    }

    @Override
    public PageModel<SafetyCarrierDto> getCarrierList(String carrierGroupCode, String carrierName, Long pageNum, Long pageSize) {
        SafetyCarrierQuery query = new SafetyCarrierQuery();
        query.setCarrierGroupCode(carrierGroupCode);
        query.setName(carrierName);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        PageModel<SafetyCarrierDo> pageModel = safetyCarrierRepository.queryPageByCarrierGroupList(query);
        //填充载体名称
        safetyCarrierDomainService.fillClassNameForList(pageModel.getList());
        return PageModel.build(safetyCarrierDtoConverter.toDtoList(pageModel.getList()), pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public PageModel<OperateLogDto> pageListOperateLogs(Integer operateStatus, Integer operateType, Long pageNum, Long pageSize, Long cardId) {
        return cardDomainService.pageListOperateLogs(operateStatus, operateType, pageNum, pageSize, cardId);
    }

    @Override
    public void cardOperate(Integer operateType, Long id, String uid) {
        cardDomainService.cardOperate(operateType, id, uid);
        //若是销卡 需直接同步供应商
        if (CardOperateTypeEnum.CARD_DESTROY.getCode().equals(operateType)) {
            CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(id);
            if (cardInfoDo == null) {
                return;
            }
            List<SafetyRightDo> safetyRightDoList =
                    safetyRightRepository.findSafetyRightByMediumCodeAndUid(cardInfoDo.getMediumCode(), cardInfoDo.getUid(), null);
            safetySupplierEngineService.asyncSafetyDataToSupplierByUidList(Lists.newArrayList(uid),
                    safetyRightDomainService.getSupplierCodeList(safetyRightDoList));
        }
    }

    @Override
    public List<AccountModel> findFuzzyUser(String name, Boolean isResponsible) {
        List<AccountModel> accountModelList = Lists.newArrayList();
        if (isResponsible) {
            SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
            safetyPersonDo.setUserName(name);
            List<SafetyPersonDo> list = safetyPersonDomainService.queryPersonInfoListWithBaseAndDeptAndAvatarByFuzzyName(safetyPersonDo);
            if (CollectionUtils.isNotEmpty(list)) {
                for (SafetyPersonDo temp : list) {
                    AccountModel accountModel = new AccountModel();
                    accountModel.setUid(temp.getUid());
                    accountModel.setFullName(temp.getDisplayName());
                    accountModel.setDisplayName(temp.getDisplayName());
                    accountModel.setAccountName(temp.getUserName());
                    accountModelList.add(accountModel);
                }
            }
        } else {
            List<CardApplyDo> cardApplyDoList = cardApplyDomainService.queryApplyByFuzzyName(name);
            if (CollectionUtils.isNotEmpty(cardApplyDoList)) {
                for (CardApplyDo cardApplyDo : cardApplyDoList) {
                    AccountModel accountModel = new AccountModel();
                    accountModel.setUid(cardApplyDo.getUid());
                    accountModel.setFullName(cardApplyDo.getDisplayName());
                    accountModel.setDisplayName(cardApplyDo.getDisplayName());
                    accountModel.setAccountName(cardApplyDo.getPartnerAccount());
                    accountModelList.add(accountModel);
                }
            }
        }
        return accountModelList;
    }

    @Override
    public PageModel<SafetyCarrierGroupDto> pageGroupConditionList(GroupPageConditionDto params) {
        PageModel<SafetyCarrierGroupDo> model = cardDomainService.pageGroupConditionList(params);
        return PageModel.build(cardDtoConverter.toDtoList(model.getList()), model.getPageSize(), model.getPageNum(), model.getTotal());
    }

    @Override
    public List<SafetyRightDto> getCarrierGroupsByMediums(List<String> mediumCodes) {
        return safetyRightDtoConverter.toDtoList(cardDomainService.getCarrierGroupsByMediums(mediumCodes));
    }

    @Override
    public void authorityRedo(AuthLogRedoDto param) {
        cardDomainService.authorityRedo(param);
    }

    @Override
    public CardInfoDto getCardByUid(String uid, List<CardStatusEnum> status) {
        return cardDtoConverter.toDto(cardDomainService.getCardByUid(uid, status));
    }

    @Override
    public void updateStatus(Long id, CardStatusEnum canceled) {
        cardDomainService.updateStatus(id, canceled);
    }

    @Override
    public List<CardInfoDto> getNeedExpireList() {
        return cardDtoConverter.toCardDtoList(cardDomainService.getNeedExpireList());
    }

    @Override
    public void doExpire(CardInfoDto cardInfoDto) {
        CardInfoDo cardInfoDo = cardDtoConverter.toDo(cardInfoDto);
        cardDomainService.doExpire(cardInfoDo);
    }

    @Override
    public List<CardInfoDto> getNeedActiveList() {
        return cardDtoConverter.toCardDtoList(cardDomainService.getNeedActiveList());
    }

    @Override
    public void doActive(CardInfoDto cardInfoDto) {
        CardInfoDo cardInfoDo = cardDtoConverter.toDo(cardInfoDto);
        cardDomainService.doActive(cardInfoDo);
    }

    @Override
    public List<CarrierGroupCityTreeDto> getAccessCityByMedium(String mediumCode) {
        return cardDomainService.getAccessCityByMedium(mediumCode);
    }

    @Override
    public List<CardInfoDto> getNeedNotifyList() {
        return cardDtoConverter.toCardDtoList(cardDomainService.getNeedNotifyList());
    }

    @Override
    public void doNotify(List<CardInfoDto> cardInfoDto) {
        List<CardInfoDo> cardInfoDos = cardDtoConverter.toDoList(cardInfoDto);
        //1.构建消息结构
        List<SafetyUmsNotifyDo> safetyUmsNotifyDos = safetyUmsNotifyDomainService.buildListCardNotify(cardInfoDos);
        //2.批量保存消息
        safetyUmsNotifyDomainService.batchInsert(safetyUmsNotifyDos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTimeAndRight(String pid, Long startTime, Long endTime) {
        log.info("deleteTimeAndRight partnerUser: {}, startTime: {}, endTime: {}", pid, startTime, endTime);

        if (StringUtils.isEmpty(pid) || ObjectUtils.isEmpty(startTime) || ObjectUtils.isEmpty(endTime)) {
            log.error("params can not be null; partnerUser:{}, startTime:{}, endTime:{}", pid, startTime, endTime);
            return;
        }

        //填装申请单的基本信息
        CardApplyDo nowCardApplyDo = new CardApplyDo();
        nowCardApplyDo.setUid(pid);
        nowCardApplyDo.setApplyType(CardApplyTypeEnum.COOPERATION_CARD_APPLY.getCode());
        cardApplyDomainService.fillBaseInfoByUidAndApplyType(nowCardApplyDo);

        //没有对应申请单时
        if (ObjectUtils.isEmpty(nowCardApplyDo.getId())) {
            log.error("deleteTimeAndRight no card apply to update partnerUser: {}", pid);
            return;
        }

        //已取消和已拒绝无需退场处理
        if (CardApplyStatusEnum.REFUSED.getCode().equals(nowCardApplyDo.getApplyStatus())
                || CardApplyStatusEnum.CANCELED.getCode().equals(nowCardApplyDo.getApplyStatus())
                || CardApplyStatusEnum.WAIT_OPEN_CARD.getCode().equals(nowCardApplyDo.getApplyStatus())) {
            log.info("current apply:{} not need to deal", nowCardApplyDo);
            return;
        }

        //已领取或待领取，进行工卡时间的处理
        if (CardApplyStatusEnum.COMPLETED.getCode().equals(nowCardApplyDo.getApplyStatus())
                || CardApplyStatusEnum.WAIT_RECEIVE.getCode().equals(nowCardApplyDo.getApplyStatus())) {
            //消除的时间若正在使用中且没有重叠时间段 需消除卡权限 否则删除对应时间即可
            deleteTimeAndRightForCardInfoDo(pid, startTime, endTime);
            return;
        }
        //其余状态说明此工卡还未进行制卡更新状态已取消 同时删除对应退场有效期
        nowCardApplyDo.setApplyStatus(CardApplyStatusEnum.CANCELED.getCode());
        cardApplyRepository.updateWithCardTimeById(nowCardApplyDo);
        CardTimeValidityDo cardTimeValidityDo = buildValidateTimeDo(pid, startTime, endTime);
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardTimeValidity(cardTimeValidityDo);
        cardDomainService.updateValidateTimeByUidAndTime(cardInfoDo);
    }

    private CardTimeValidityDo buildValidateTimeDo(String pid, Long startTime, Long endTime) {
        String start = TimeUtils.format(startTime.intValue());
        String end = TimeUtils.format(endTime.intValue());
        ZonedDateTime zonedStartTime = ZonedDateTimeUtils.toZonedDateTime(start);
        ZonedDateTime zonedEndTime = ZonedDateTimeUtils.toZonedDateTime(end);
        CardTimeValidityDo cardTimeValidity = new CardTimeValidityDo();
        cardTimeValidity.setStartTime(zonedStartTime);
        cardTimeValidity.setEndTime(zonedEndTime);
        cardTimeValidity.setUid(pid);
        return cardTimeValidity;
    }

    private void deleteTimeAndRightForCardInfoDo(String partnerUser, Long startTime, Long endTime) {
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime nowStartTime = null;
        ZonedDateTime nowEndTime = null;
        CardInfoDo cardInfoDo = cardDomainService.getCardByUid(partnerUser, Lists.newArrayList(CardStatusEnum.USING,
                CardStatusEnum.NOT_ACTIVE_FOR_TIME, CardStatusEnum.NOT_ACTIVE));
        if (!ObjectUtils.isEmpty(cardInfoDo)) {
            CardTimeValidityDo cardTimeValidity = buildValidateTimeDo(partnerUser, startTime, endTime);
            cardInfoDo.setCardTimeValidity(cardTimeValidity);
            cardDomainService.updateValidateTimeByUidAndTime(cardInfoDo);
            //删除(更新)后若没有可用有效时间 将卡置为过期状态
            List<CardTimeValidityDo> cardTimeValidityDtos = cardDomainService.getCardTimeByCardIds(Lists.newArrayList(cardInfoDo.getId()));
            if (!CollectionUtils.isEmpty(cardTimeValidityDtos)) {
                cardTimeValidityDtos.sort((o1, o2) -> ZonedDateTimeUtils.compare(o1.getStartTime(), o2.getStartTime()));
                for (CardTimeValidityDo cardTimeValidityDo : cardTimeValidityDtos) {
                    if (ZonedDateTimeUtils.compare(now, cardTimeValidityDo.getStartTime()) >= OAUCFCommonConstants.INT_ZERO &&
                            ZonedDateTimeUtils.compare(now, cardTimeValidityDo.getEndTime()) < OAUCFCommonConstants.INT_ZERO) {
                        nowStartTime = cardTimeValidityDo.getStartTime();
                        nowEndTime = cardTimeValidityDo.getEndTime();
                        break;
                    }
                }
            }
            //更新工卡申请单开始时间和结束时间
            CardApplyDo cardApplyDo = new CardApplyDo();
            cardApplyDo.setId(cardInfoDo.getCardApplyId());
            if (CollectionUtils.isEmpty(cardTimeValidityDtos) || ObjectUtils.isEmpty(nowStartTime) || ObjectUtils.isEmpty(nowEndTime)) {
                List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
                //1.初始化日志
                SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
                operateLogDo.setOperateTypeEnum(LogOperateType.EXPIRE_CARD);
                operateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
                cardInfoDo.setSafetyOperateLog(operateLogDo);

                //更新卡状态
                cardDomainService.updateStatus(cardInfoDo.getId(), CardStatusEnum.EXPIRED);

                //删除权限
                cardDomainService.deleteSafetyRight(cardInfoDo);
                dealApplyTime(cardApplyDo, cardTimeValidityDtos);
            } else {
                cardApplyDo.setStartTime(nowStartTime);
                cardApplyDo.setEndTime(nowEndTime);
            }
            cardApplyDomainService.updateWithCardTimeById(cardApplyDo);
        } else {
            log.info("no access card to update {}", cardInfoDo);
        }
    }

    private void dealApplyTime(CardApplyDo cardApplyDo, List<CardTimeValidityDo> cardTimeValidityDtos) {
        //若没有可用有效期 则重置申请单时间为0
        if (CollectionUtils.isEmpty(cardTimeValidityDtos)) {
            cardApplyDo.setStartTime(ZonedDateTimeUtils.getZeroTime());
            cardApplyDo.setEndTime(ZonedDateTimeUtils.getZeroTime());
        } else {
            cardTimeValidityDtos.sort((o1, o2) -> ZonedDateTimeUtils.compare(o1.getStartTime(), o2.getStartTime()));
            cardApplyDo.setStartTime(cardTimeValidityDtos.get(0).getStartTime());
            cardApplyDo.setEndTime(cardTimeValidityDtos.get(0).getEndTime());
        }
    }

    @Override
    public void batchSavePartnerCard(MultipartFile file) {
        List<CardApplyDo> cardApplyDos = Lists.newArrayList();
        parsingExcel(file, cardApplyDos);
        cardDomainService.batchSavePartnerCard(cardApplyDos);
    }

    @Override
    public List<CardInfoDto> getListCardInfoByPhysicCodes(List<String> mediumPhysicsCodes) {
        List<CardInfoDo> cardInfoDos = cardDomainService.getListCardInfoByPhysicCodes(mediumPhysicsCodes);
        return cardDtoConverter.toCardDtoList(cardInfoDos);
    }

    @Override
    public List<CardInfoDto> getListCardInfoByEncryptCodes(List<String> mediumEncryptCodes) {
        List<CardInfoDo> cardInfoDos = cardDomainService.getListCardInfoByEncryptCodes(mediumEncryptCodes);
        return cardDtoConverter.toCardDtoList(cardInfoDos);
    }

    @Override
    public List<CardInfoDto> getListCardInfoByUid(List<String> uid) {
        List<CardInfoDo> cardInfoDos = cardDomainService.getListCardInfoByUid(uid);
        return cardDtoConverter.toCardDtoList(cardInfoDos);
    }

    @Override
    public PageModel<SafetyCarrierGroupDto> getRestGroup(GroupPageConditionDto reqDto) {
        SafetyCarrierGroupQuery safetyCarrierGroupQuery = safetyCarrierGroupDtoConverter.toQuery(reqDto);
        PageModel<SafetyCarrierGroupDo> pageModel = safetyCarrierGroupRepository.pageGroupConditionList(safetyCarrierGroupQuery);
        cardDomainService.fillCarrierGroups(pageModel.getList());
        return PageModel.build(safetyCarrierGroupDtoConverter.toDtoList(pageModel.getList()), pageModel.getPageSize(),
                pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public List<CardInfoDto> getListCardInfoByAccounts(List<String> accounts) {
        List<CardInfoDo> cardInfoDos = cardDomainService.getListCardInfoByAccounts(accounts);
        return cardDtoConverter.toCardDtoList(cardInfoDos);
    }

    @Override
    public PageModel<EmpCardDto> pageEmpConditionList(CardPageConditionDto cardPageConditionDto, Boolean isExport) {
        if (Objects.nonNull(cardPageConditionDto.getPreLeaveDateFrom()) && Objects.nonNull(cardPageConditionDto.getPreLeaveDateTo())) {
            CardLeaveRecordQuery leaveRecordQuery = new CardLeaveRecordQuery();
            leaveRecordQuery.setPreLeaveDateFrom(cardPageConditionDto.getPreLeaveDateFrom());
            leaveRecordQuery.setPreLeaveDateTo(cardPageConditionDto.getPreLeaveDateTo());
            List<CardLeaveRecordDo> cardLeaveRecordDos =
                    cardLeaveRecordRepository.queryLeaveRecordList(leaveRecordQuery);
            if (CollectionUtils.isNotEmpty(cardLeaveRecordDos)) {
                cardPageConditionDto.setPreLeaveUidList(cardLeaveRecordDos.stream().map(CardLeaveRecordDo::getUid).collect(Collectors.toList()));
            } else {
                cardPageConditionDto.setPreLeaveUidList(Lists.newArrayList(SafetyConstants.Card.EMPTY_LIST_STR));
            }
        }
//        1.条件查询工卡信息
        PageModel<CardInfoDo> pageModel = cardDomainService.pageEmpCardList(cardPageConditionDto);
        List<CardInfoDo> list = pageModel.getList();
        //查询工卡申请信息
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> applyIdList = list.stream().map(CardInfoDo::getCardApplyId).collect(Collectors.toList());
            List<CardApplyDo> cardApplyDoList = cardApplyDomainService.findByIdList(applyIdList);
            //转换为map
            Map<Long, CardApplyDo> cardApplyMap = cardApplyDoList.stream().collect(Collectors.toMap(CardApplyDo::getId, cardApplyDo -> cardApplyDo));
            //填充申请单
            cardApplyDomainService.fillFullCardApplyV2(cardApplyDoList);
            //查询离职信息
            List<String> uidList = list.stream().map(CardInfoDo::getUid).collect(Collectors.toList());
            List<CardLeaveRecordDo> cardLeaveRecordDoList = cardDomainService.findLeaveRecordByUidList(uidList);
            //调人事查询离职日期 并转换为map 若没有离职记录可能为空
            Map<String, PersonInfoModel> accountMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(cardLeaveRecordDoList)) {
                List<String> accounts = cardLeaveRecordDoList.stream().map(CardLeaveRecordDo::getUserName).collect(Collectors.toList());
                List<PersonInfoModel> personList = hrodSdk.getLeaveDate(accounts);
                //转map
                accountMap = personList.stream().collect(Collectors.toMap(PersonInfoModel::getAccountName, Function.identity(), (key1, key2) -> key1));
            }
            leaveRecordSetToCardInfo(list, accountMap, cardLeaveRecordDoList);
            applySetToCardInfo(list, cardApplyMap);
            //填充权限组和电子卡
            if (isExport) {
                list.stream().parallel().forEach(cardInfoDo -> {
                    //权限组
                    cardDomainService.fillSafetyRight(cardInfoDo);
                    //电子卡 todo
                });
            }
        }
        return PageModel.build(cardDtoConverter.toEmpCardDtoList(pageModel.getList()), pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    private void leaveRecordSetToCardInfo(List<CardInfoDo> list, Map<String, PersonInfoModel> accountMap, List<CardLeaveRecordDo> cardLeaveRecordDoList) {
        for (CardLeaveRecordDo cardLeaveRecordDo : cardLeaveRecordDoList) {
            PersonInfoModel personInfoModel = accountMap.get(cardLeaveRecordDo.getUserName());
            if (!ObjectUtils.isEmpty(personInfoModel)) {
                cardLeaveRecordDo.setLeaveDate(personInfoModel.getLeaveDate());
                cardLeaveRecordDo.setPreLeaveDate(personInfoModel.getPreLeaveDate());
            }
        }
        Map<String, CardLeaveRecordDo> leaveMap = cardLeaveRecordDoList.stream()
                .collect(Collectors.toMap(CardLeaveRecordDo::getUid, Function.identity(), (key1, key2) -> key1));
        for (CardInfoDo cardInfoDo : list) {
            CardLeaveRecordDo cardLeaveRecordDo = leaveMap.get(cardInfoDo.getUid());
            cardInfoDo.setCardLeaveRecord(cardLeaveRecordDo);
        }
    }

    private void applySetToCardInfo(List<CardInfoDo> list, Map<Long, CardApplyDo> cardApplyMap) {
        for (CardInfoDo cardInfoDo : list) {
            CardApplyDo cardApplyDo = cardApplyMap.get(cardInfoDo.getCardApplyId());
            cardInfoDo.setCardApply(cardApplyDo);
        }
    }

    @Override
    public EmpCardDto getEmpCardDetailInfo(Long id) {
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setId(id);
        cardDomainService.getAndFillEmpCardInfo(cardInfoDo);
        return cardDtoConverter.toEmpCardDto(cardInfoDo);
    }

    @Override
    public void editCard(EmpCardDto empCardDto) {
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(empCardDto.getId());
        cardDomainService.fillSafetyRight(cardInfoDo);
        cardDomainService.fillSafetyPersonMedium(cardInfoDo);
        if (!StringUtils.equalsIgnoreCase(cardInfoDo.getMediumPhysicsCode(), empCardDto.getMediumPhysicsCode()) ||
                !org.apache.commons.lang.StringUtils.equalsIgnoreCase(cardInfoDo.getMediumEncryptCode(), empCardDto.getMediumEncryptCode())) {
            fillEmpCardEditInfo(cardInfoDo, empCardDto);
            cardDomainService.editCard(cardInfoDo);
            //电子卡删除
            cardElectronRecordService.forceDeleteAllCardElectronByUid(cardInfoDo.getUid());
            //通知idm卡号变更
            cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
            cardDomainService.notifyCardNumberChange(cardInfoDo);
        }
    }

    @Override
    public void pinCard(Long id) {
        //查询工卡
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(id);
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        //更新工卡状态
        cardDomainService.pinCard(cardInfoDo);
        //电子卡删除
        cardElectronRecordService.forceDeleteAllCardElectronByUid(cardInfoDo.getUid());
        //通知idm
        cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_DELETE);
        cardDomainService.notifyCardNumberChange(cardInfoDo);
        //销卡时间同步，用于后续离职、终止补卡、同步权限等场景
        CardPinEvent cardPinEvent = CardPinEvent.builder().uid(cardInfoDo.getUid()).cardId(cardInfoDo.getId()).build();
        commonProducer.send(RocketMqTopicEnum.PIN_CARD.getTopicName(), GsonUtils.toJsonFilterNullField(cardPinEvent));
    }

    @Override
    public void restoreCard(Long id) {
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(id);
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        //恢复前校验人员状态是否可用
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonDomainService.fillPersonInfoWithBase(safetyPersonDo);
        safetyPersonDomainService.checkPersonIsActive(safetyPersonDo);

        //恢复前校验临时卡是否归还
        cardDomainService.checkTempCardIsReturn(cardInfoDo);
        //卡恢复
        cardDomainService.reopen(cardInfoDo);
        //通知idm
        cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
        cardDomainService.notifyCardNumberChange(cardInfoDo);
    }

    @Override
    public void editCardInfo(CoopCardEditDto editDto) {
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(editDto.getCardId());
        cardDomainService.fillSafetyRight(cardInfoDo);
        cardDomainService.fillSafetyPersonMedium(cardInfoDo);
        if (!StringUtils.equalsIgnoreCase(cardInfoDo.getMediumPhysicsCode(), editDto.getMediumPhysicsCode()) ||
                !StringUtils.equalsIgnoreCase(cardInfoDo.getMediumEncryptCode(), editDto.getMediumEncryptCode())) {
            fillCoopCardEditInfo(cardInfoDo, editDto);
            cardDomainService.editCard(cardInfoDo);
            //通知idm卡号变更
            cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
            cardDomainService.notifyCardNumberChange(cardInfoDo);
        }
    }

    @Override
    public void createPreLeave(CardLeaveRecordDto cardLeaveRecordDto) {
        //创建前填充信息
        CardLeaveRecordDo cardLeaveRecordDo = cardDtoConverter.toLeaveDo(cardLeaveRecordDto);
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardLeaveRecord(cardLeaveRecordDo);
        cardDomainService.fillCardLeaveRecord(cardInfoDo);

        //创建前检查
        cardDomainService.checkBeforeCreateCardLeaveRecord(cardInfoDo);

        //保存
        cardLeaveRecordRepository.save(cardInfoDo.getCardLeaveRecord());
    }

    @Override
    public void cancelLeave(CardLeaveRecordDto cardLeaveRecordDto) {
        CardLeaveRecordDo cardLeaveRecordDo = cardDtoConverter.toLeaveDo(cardLeaveRecordDto);
        cardLeaveRecordRepository.delete(cardLeaveRecordDo);
    }

    @Override
    public void reportLeave(Long cardId) {
        //查询工卡
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(cardId);
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        cardDomainService.reportLeave(cardInfoDo);
    }

    @Override
    public Boolean checkIsNewCard(String userName) {
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setPartnerAccount(userName);
        cardDomainService.fillInfoBeforeCheckIsNewCard(cardInfoDo);

        return cardDomainService.checkIsNewCard(cardInfoDo);
    }

    @Override
    public SafetyPersonDo findPersonInfoByUserName(String username) {
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUserName(username);
        safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
        return safetyPersonDo;
    }


    @Override
    public List<CardInfoDto> getNeedComparePermissionList() {
        CardInfoQuery cardInfoQuery = new CardInfoQuery();
        cardInfoQuery.setCardStatus(CardStatusEnum.USING.getCode());
        cardInfoQuery.setTenantCode(SafetyConstants.Card.CARD_TRAVEL_AUTO_MIGRATE_COMPARING);
        List<CardInfoDo> cardInfoDoList = cardInfoRepository.getListByConditions(cardInfoQuery);
        return cardDtoConverter.toCardDtoList(cardInfoDoList);
    }

    private void fillCoopCardEditInfo(CardInfoDo cardInfoDo, CoopCardEditDto editDto) {
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(editDto.getCardNum());
        cardInfoDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardInfoDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
        if (prefixEncryptCode.equals(editDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = editDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
    }

    private void fillEmpCardEditInfo(CardInfoDo cardInfoDo, EmpCardDto empCardDto) {
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(empCardDto.getCardNum());
        if (prefixEncryptCode.equals(empCardDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = empCardDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
        cardInfoDo.setMediumPhysicsCode(empCardDto.getMediumPhysicsCode());
        cardInfoDo.setMediumEncryptCode(empCardDto.getMediumEncryptCode());
    }

    private void parsingExcel(MultipartFile file, List<CardApplyDo> cardApplyDos) {
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Map<String, Object>> readAll;
            Map<String, String> map = new LinkedHashMap<>();
            buildExcelMap(map);
            reader.setHeaderAlias(map);
            readAll = reader.read(0, 1, reader.getRowCount());
            if (CollectionUtils.isNotEmpty(readAll)) {
                for (Map<String, Object> stringObjectMap : readAll) {
                    CardApplyDo cardApplyDo = new CardApplyDo();
                    for (Map.Entry<String, Object> entry : stringObjectMap.entrySet()) {
                        if (ExcelAttributeMapperEnum.NULL_STRING.getTarget().equals(entry.getKey()) &&
                                ExcelAttributeMapperEnum.NULL_STRING.getTarget().equals(entry.getValue())) {
                            stringObjectMap.remove(entry.getKey(), entry.getValue());
                        }
                    }
                    BeanUtils.populate(cardApplyDo, stringObjectMap);
                    cardApplyDos.add(cardApplyDo);
                }
            }
        } catch (IOException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.EXCEL_EXPORT_ERROR);
        } catch (IllegalAccessException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.ILLEGAL_ACCESS_ERROR);
        } catch (InvocationTargetException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.INVOCATION_TARGET);
        }
    }

    private void buildExcelMap(Map<String, String> map) {
        map.put(ExcelAttributeMapperEnum.DISPLAY_NAME.getSource(), ExcelAttributeMapperEnum.DISPLAY_NAME.getTarget());
        map.put(ExcelAttributeMapperEnum.PARTNER_ACCOUNT.getSource(), ExcelAttributeMapperEnum.PARTNER_ACCOUNT.getTarget());
        map.put(ExcelAttributeMapperEnum.EMAIL.getSource(), ExcelAttributeMapperEnum.EMAIL.getTarget());
        map.put(ExcelAttributeMapperEnum.RESPONSIBLE_EMAIL.getSource(), ExcelAttributeMapperEnum.RESPONSIBLE_EMAIL.getTarget());
        map.put(ExcelAttributeMapperEnum.MEDIUM_PHYSICS_CODE.getSource(), ExcelAttributeMapperEnum.MEDIUM_PHYSICS_CODE.getTarget());
        map.put(ExcelAttributeMapperEnum.MEDIUM_ENCRYPT_CODE.getSource(), ExcelAttributeMapperEnum.MEDIUM_ENCRYPT_CODE.getTarget());
        map.put(ExcelAttributeMapperEnum.GROUP_SERIAL_LIST.getSource(), ExcelAttributeMapperEnum.GROUP_SERIAL_LIST.getTarget());
    }

    @Override
    public SpecialPageVO<CardInfoDto> aggregatePageCardInfo(SpecialPageReq req) {
        SpecialPageVO<CardInfoDo> cardInfoDoPage = cardAggregateSearchRepository.aggregatePage(req);
        List<String> uidList = cardInfoDoPage.getList().stream().map(CardInfoDo::getUid).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<PersonInfoModel> personInfoList = idmSdk.pagePersonByUidList(uidList);
        Map<String, PersonInfoModel> personInfoModelMap = personInfoList.stream()
                .collect(Collectors.toMap(PersonInfoModel::getUid,
                        Function.identity(), (a, b) -> a));
        cardInfoDoPage.getList().forEach(cardInfo -> {
            PersonInfoModel personInfoModel = personInfoModelMap.get(cardInfo.getUid());
            if (Objects.nonNull(personInfoModel)) {
                cardInfo.setPartnerAccount(personInfoModel.getAccountName());
                cardInfo.setDisplayName(personInfoModel.getDisplayName());
            }
        });
        return SpecialPageVO.build(cardInfoDoPage.getPageToken(), cardInfoDoPage.getHasMore(),
                cardDtoConverter.toCardDtoList(cardInfoDoPage.getList()));
    }

    @Override
    public void asyncExportCardList(String queryData) {
        AsyncExportTaskDo asyncExportTaskDo = new AsyncExportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initDO(queryData, asyncExportTaskDo, userInfoDto);

        // 导出任务保存到数据库
        asyncExportTaskDomainService.save(asyncExportTaskDo);
        // 发布导出事件
        eventPublisher.publishEvent(new SafetyExportEvent(asyncExportTaskDo, asyncExportTaskDo.getId()));
    }

    @Override
    public void asyncExportPropertyCardList(String queryData) {
        AsyncExportTaskDo asyncExportTaskDo = new AsyncExportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initPropertyDO(queryData, asyncExportTaskDo, userInfoDto);

        // 导出任务保存到数据库
        asyncExportTaskDomainService.save(asyncExportTaskDo);
        // 发布导出事件
        eventPublisher.publishEvent(new SafetyExportEvent(asyncExportTaskDo, asyncExportTaskDo.getId()));
    }

    private static void initDO(String queryData, AsyncExportTaskDo asyncExportTaskDo, UserInfoDto userInfoDto) {
        asyncExportTaskDo.setOpType(ExportTypeEnum.COOP_CARD_LIST_EXPORT.getType());
        asyncExportTaskDo.setUid(userInfoDto.getUid());
        asyncExportTaskDo.setOperatorName(userInfoDto.getUserName());
        asyncExportTaskDo.setOpTime(ZonedDateTime.now());
        asyncExportTaskDo.setStatus(ExportStatusEnum.TO_EXECUTE.getStatus());
        asyncExportTaskDo.setProgress(SafetyConstants.EXPORT_MAX_PAGE_SIZE_INTEGER);
        asyncExportTaskDo.setParam(queryData);
    }

    public static void initPropertyDO(String queryData, AsyncExportTaskDo asyncExportTaskDo, UserInfoDto userInfoDto) {
        asyncExportTaskDo.setOpType(ExportTypeEnum.PROPERTY_CARD_LIST_EXPORT.getType());
        asyncExportTaskDo.setUid(userInfoDto.getUid());
        asyncExportTaskDo.setOperatorName(userInfoDto.getUserName());
        asyncExportTaskDo.setOpTime(ZonedDateTime.now());
        asyncExportTaskDo.setStatus(ExportStatusEnum.TO_EXECUTE.getStatus());
        asyncExportTaskDo.setProgress(SafetyConstants.EXPORT_MAX_PAGE_SIZE_INTEGER);
        asyncExportTaskDo.setParam(queryData);
    }

    @Override
    public CardInfoDto getCardInfoByUid(String uid, Integer number) {
        return cardDtoConverter.toDto(cardInfoRepository.getCardInfoByUid(uid, number));
    }
}
