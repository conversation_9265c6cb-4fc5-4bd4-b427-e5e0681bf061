package com.mi.oa.ee.safety.application.impl.card.electronic;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.converter.card.CardElectronRecordDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.electronic.CardElectronPayDto;
import com.mi.oa.ee.safety.application.dto.card.electronic.CardElectronRecordDto;
import com.mi.oa.ee.safety.application.dto.card.electronic.CardElectronRemoteDto;
import com.mi.oa.ee.safety.application.service.card.electronic.CardElectronRecordService;
import com.mi.oa.ee.safety.application.service.safety.SafetySupplierEngineService;
import com.mi.oa.ee.safety.common.enums.card.CardElectronStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardElectronStatusForAppEnum;
import com.mi.oa.ee.safety.common.enums.card.CardElectronTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.PayOperationAndResultEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.utils.CardUtil;
import com.mi.oa.ee.safety.domain.errorcode.CardElectronRecordDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.service.CardElectronRecordDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyRightDomainService;
import com.mi.oa.ee.safety.infra.common.x5.rsaserver.RsavalidateUtil;
import com.mi.oa.ee.safety.infra.repository.CardElectronRecordRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyPersonMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.ee.safety.infra.repository.query.ElectronicCardRecordQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/9/15 17:43
 */
@Slf4j
@Service
public class CardElectronRecordServiceImpl implements CardElectronRecordService {

    @Resource
    private CardElectronRecordDtoConverter converter;

    @Resource
    private SafetyPersonDomainService safetyPersonDomainService;

    @Resource
    private CardElectronRecordRepository cardElectronRecordRepository;

    @Resource
    private CardElectronRecordDomainService cardElectronRecordDomainService;

    @Resource
    private SafetyMediumRepository safetyMediumRepository;

    @Resource
    private SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Resource
    private SafetyRightRepository safetyRightRepository;

    @Resource
    private SafetyOperateLogRepository safetyOperateLogRepository;

    @Resource
    private SafetyRightDomainService safetyRightDomainService;

    @Value("${pay.electron.noCardArt:http://cdn.cnbj0.fds.api.mi-img.com/b2c-mioa-res/card_no_open.png}")
    private String noCardPhoto;

    @Value("${pay.electron.cardArt:http://cdn.cnbj0.fds.api.mi-img.com/b2c-mioa-res/card_open.png}")
    private String haveCardPhoto;

    @Value("${pay.privatepem.key}")
    private String privateKey;

    @Resource
    private SafetySupplierEngineService safetySupplierEngineService;

    @Override
    public Boolean checkIsUseElectronNew(String username, String electronCardNum) {
        return true;
    }

    @Override
    public List<CardElectronRemoteDto> findCardElectronList(CardElectronRemoteDto dto) {
        CardElectronRecordDo electronRecordDo = new CardElectronRecordDo();
        electronRecordDo.setUid(dto.getUid());
        List<CardElectronRecordDo> cardElectronRecordDoList = cardElectronRecordDomainService.getElectronCardListByUid(electronRecordDo);
        return buildDtoList(cardElectronRecordDoList, dto.getElectronDeviceId());
    }

    private List<CardElectronRemoteDto> buildDtoList(List<CardElectronRecordDo> recordDoList, String nowDeviceId) {
        List<CardElectronRemoteDto> dtoList = Lists.newArrayList();
        //若没有任何电子卡信息
        if (CollectionUtils.isEmpty(recordDoList)) {
            //添加手机
            dtoList.add(buildDefaultDtoList(CardElectronTypeEnum.MOBILE, null, nowDeviceId));
            //添加手表
            dtoList.add(buildDefaultDtoList(CardElectronTypeEnum.WATCH, null, nowDeviceId));
            //添加手环
            dtoList.add(buildDefaultDtoList(CardElectronTypeEnum.WRISTBAND, null, nowDeviceId));
            return dtoList;
        }
        Map<String, CardElectronRecordDo> electronTypeMap =
                recordDoList.stream().collect(Collectors.toMap(CardElectronRecordDo::getElectronType, Function.identity(), (k1, k2) -> k1));
        for (CardElectronTypeEnum value : CardElectronTypeEnum.values()) {
            CardElectronRecordDo electronRecordDo = electronTypeMap.get(value.getCode());
            CardElectronRemoteDto electronicCardRemoteDto = buildDefaultDtoList(value, electronRecordDo, nowDeviceId);
            if (ObjectUtils.isNotEmpty(electronRecordDo) && StringUtils.isNotEmpty(electronRecordDo.getElectronPhotoUrl())) {
                electronicCardRemoteDto.setElectronPhotoUrl(electronRecordDo.getElectronPhotoUrl());
            }
            dtoList.add(electronicCardRemoteDto);
        }
        return dtoList;
    }

    private CardElectronRemoteDto buildDefaultDtoList(CardElectronTypeEnum typeEnum, CardElectronRecordDo electronRecordDo, String nowDeviceId) {
        CardElectronRemoteDto electronDefaultCard = converter.toRemoteDto(electronRecordDo);
        if (electronDefaultCard == null) {
            electronDefaultCard = new CardElectronRemoteDto();
        }
        electronDefaultCard.setElectronType(typeEnum.getCode());
        if (electronRecordDo == null) {
            electronDefaultCard.setElectronPhotoUrl(noCardPhoto);
            electronDefaultCard.setStatusType(CardElectronStatusForAppEnum.ELECTRON_CARD_APP_CLOSE.getResultType());
        } else {
            if (StringUtils.isNotBlank(nowDeviceId) && !nowDeviceId.equals(electronRecordDo.getElectronDeviceId())
                    && CardElectronStatusEnum.OPENED.getCode().equals(electronRecordDo.getStatus())) {
                if (CardElectronTypeEnum.MOBILE.getCode().equals(electronRecordDo.getElectronType())) {
                    electronDefaultCard.setElectronPhotoUrl(noCardPhoto);
                    electronDefaultCard.setStatusType(CardElectronStatusForAppEnum.ELECTRON_CARD_APP_OTHER_EXISTS.getResultType());
                } else {
                    electronDefaultCard.setElectronPhotoUrl(haveCardPhoto);
                    electronDefaultCard.setStatusType(CardElectronStatusForAppEnum.ELECTRON_CARD_APP_OPEN.getResultType());
                }
            } else if (CardElectronStatusEnum.TO_BE_OPEN.getCode().equals(electronRecordDo.getStatus())) {
                electronDefaultCard.setElectronPhotoUrl(noCardPhoto);
                electronDefaultCard.setStatusType(CardElectronStatusForAppEnum.ELECTRON_CARD_APP_OPENING.getResultType());
            } else if (CardElectronStatusEnum.OPENED.getCode().equals(electronRecordDo.getStatus())) {
                electronDefaultCard.setElectronPhotoUrl(haveCardPhoto);
                electronDefaultCard.setStatusType(CardElectronStatusForAppEnum.ELECTRON_CARD_APP_OPEN.getResultType());
            } else if (CardElectronStatusEnum.DELETING.getCode().equals(electronRecordDo.getStatus())) {
                electronDefaultCard.setElectronPhotoUrl(noCardPhoto);
                electronDefaultCard.setStatusType(CardElectronStatusForAppEnum.ELECTRON_CARD_APP_DELETING.getResultType());
            } else if (CardElectronStatusEnum.DELETED.getCode().equals(electronRecordDo.getStatus())
                    || CardElectronStatusEnum.DELETED_FORCE.getCode().equals(electronRecordDo.getStatus())) {
                electronDefaultCard.setElectronPhotoUrl(noCardPhoto);
                electronDefaultCard.setStatusType(CardElectronStatusForAppEnum.ELECTRON_CARD_APP_CLOSE.getResultType());
            } else if (CardElectronStatusEnum.OPEN_FAIL.getCode().equals(electronRecordDo.getStatus())
                    || CardElectronStatusEnum.DELETE_FAIL.getCode().equals(electronRecordDo.getStatus())) {
                electronDefaultCard.setElectronPhotoUrl(noCardPhoto);
                electronDefaultCard.setStatusType(CardElectronStatusForAppEnum.ELECTRON_CARD_APP_CLOSE.getResultType());
            }
        }

        return electronDefaultCard;
    }

    @Override
    public PageModel<CardElectronRecordDto> page(CardElectronRecordDto dto) {
        ElectronicCardRecordQuery query = converter.toQuery(dto);
        PageModel<CardElectronRecordDo> pageModel = cardElectronRecordRepository.page(query);
        return PageModel.build(converter.toDtoList(pageModel.getList()), pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String electronCardNum) {
        CardElectronRecordDo electronRecordDo = new CardElectronRecordDo();
        electronRecordDo.setElectronCardNum(electronCardNum);
        cardElectronRecordDomainService.fillInfoBeforeDeletingByElectronCardNum(electronRecordDo);
        //待开通 已开通 开通异常状态才能变成删除中
        cardElectronRecordDomainService.checkBeforeDeleting(electronRecordDo);
        //通知小米钱包此卡需删除
        cardElectronRecordRepository.notifyPayCardIsDeleting(electronRecordDo);
        //更新电子工卡状态删除中
        electronRecordDo.setStatus(CardElectronStatusEnum.DELETING.getCode());
        cardElectronRecordRepository.updateById(electronRecordDo);
        //若没有其他已开电子卡删除安防权限
        deleteSafety(electronRecordDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMiEr(Long cardId) {
        CardElectronRecordDo electronRecordDo = new CardElectronRecordDo();
        electronRecordDo.setId(cardId);
        cardElectronRecordDomainService.fillInfoBeforeDeletingById(electronRecordDo);
        //待开通 已开通 开通异常状态才能变成删除中
        cardElectronRecordDomainService.checkBeforeDeleting(electronRecordDo);
        //通知小米钱包此卡需删除
        cardElectronRecordRepository.notifyPayCardIsDeleting(electronRecordDo);
        //更新电子工卡状态删除中
        electronRecordDo.setStatus(CardElectronStatusEnum.DELETING.getCode());
        electronRecordDo.setUpdateTime(ZonedDateTime.now());
        cardElectronRecordRepository.updateById(electronRecordDo);
        //若没有其他已开电子卡删除安防权限
        deleteSafety(electronRecordDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notifyOperation(CardElectronPayDto dto) {
        Integer toStatus = getToStatus(dto);
        if (toStatus == null) {
            log.info("-----notifyOperation not get toStatus {}-----", JacksonUtils.bean2Json(dto));
            return;
        } else {
            log.info("-----notifyOperation {} toStatus {}-----", JacksonUtils.bean2Json(dto), toStatus);
        }
        CardElectronRecordDo cardElectronRecordDo = converter.payDtoToDo(dto);
        //开通前填充
        cardElectronRecordDomainService.fillInfoBeforeDeliver(cardElectronRecordDo);

        if (CardElectronStatusEnum.OPENED.getCode().equals(toStatus)) {
            //若没有其它已开电子卡 添加权限
            addSafety(cardElectronRecordDo);
            //更新状态为已开通
            cardElectronRecordDo.setStatus(toStatus);
            cardElectronRecordRepository.updateById(cardElectronRecordDo);

        }
        if (CardElectronStatusEnum.DELETED.getCode().equals(toStatus)) {
            if (!CardElectronStatusEnum.DELETED_FORCE.getCode().equals(cardElectronRecordDo.getStatus())
                    && !CardElectronStatusEnum.DELETED.getCode().equals(cardElectronRecordDo.getStatus())) {
                //若没有其它已开电子卡 删除安防权限
                deleteSafety(cardElectronRecordDo);
                //更新状态为已删除
                cardElectronRecordDo.setStatus(toStatus);
                cardElectronRecordRepository.updateById(cardElectronRecordDo);

            }
        }
    }

    private Integer getToStatus(CardElectronPayDto dto) {
        Integer toStatus = null;
        //操作成功
        if (PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_RESULT_SUC.getType().equals(dto.getResult())) {
            if (PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_ACTION_SEND.getType().equals(dto.getAction())) {
                //状态改为已开通
                toStatus = CardElectronStatusEnum.OPENED.getCode();
            } else if (PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_ACTION_DELETE.getType().equals(dto.getAction()) ||
                    PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_ACTION_DELETING.getType().equals(dto.getAction())) {
                //状态改为已删除
                toStatus = CardElectronStatusEnum.DELETED.getCode();
            }
        } else {
            if (PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_ACTION_SEND.getType().equals(dto.getAction())) {
                //开卡通知失败
                toStatus = CardElectronStatusEnum.OPEN_FAIL.getCode();
            } else if (PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_ACTION_DELETE.getType().equals(dto.getAction())) {
                //删卡通知失败
                toStatus = CardElectronStatusEnum.DELETE_FAIL.getCode();
            }
        }
        return toStatus;
    }

    private void addSafety(CardElectronRecordDo cardElectronRecordDo) {
        //开通前判断是否存在其他已经开卡的电子卡
        Boolean hasOtherOpenedElectronCard =
                cardElectronRecordDomainService.judgeHasOtherOpenedElectronRecord(cardElectronRecordDo);
        //若没有其他已开电子卡 添加权限
        if (Boolean.FALSE.equals(hasOtherOpenedElectronCard)) {
            //开通前检查
            cardElectronRecordDomainService.checkBeforeDeliver(cardElectronRecordDo);
            //保存介质
            safetyMediumRepository.save(cardElectronRecordDo.getSafetyMediumDo());
            //保存人与介质关系
            SafetyPersonMediumDo safetyPersonMediumDoWithId =
                    safetyPersonMediumRepository.save(cardElectronRecordDo.getSafetyPersonMediumDo());
            cardElectronRecordDo.setSafetyPersonMediumDo(safetyPersonMediumDoWithId);
            //保存权限
            safetyRightRepository.batchSaveWithIds(cardElectronRecordDo.getSafetyRightList());
            //记录日志
            cardElectronRecordDomainService.fillSafetyOperateLogBeforeOpened(cardElectronRecordDo);
            safetyOperateLogRepository.saveOrUpdate(cardElectronRecordDo.getSafetyOperateLog());
            //立即同步供应商
            safetySupplierEngineService.asyncSafetyDataToSupplierByUidList(Lists.newArrayList(cardElectronRecordDo.getUid()),
                    safetyRightDomainService.getSupplierCodeList(cardElectronRecordDo.getSafetyRightList()));
        } else {
            log.info("------addSafety hasOtherOpenedElectronCard true {}-----", cardElectronRecordDo.getUid());
        }
    }

    @Override
    public void checkCanOpenCardElectronByUid(String uid) {
        CardElectronRecordDo cardElectronRecordDo = new CardElectronRecordDo();
        cardElectronRecordDo.setUid(uid);
        cardElectronRecordDomainService.checkCanOpen(cardElectronRecordDo);
    }

    @Override
    public void forceDeleteAllCardElectronByUid(String uid) {
        CardElectronRecordDo cardElectronRecordDo = new CardElectronRecordDo();
        cardElectronRecordDo.setUid(uid);
        List<CardElectronRecordDo> cardElectronRecordDoList = cardElectronRecordDomainService.getCanForceDeleteElectronCardListByUid(cardElectronRecordDo);
        if (CollectionUtils.isNotEmpty(cardElectronRecordDoList)) {
            for (CardElectronRecordDo electronRecordDo : cardElectronRecordDoList) {
                //检查
                cardElectronRecordDomainService.checkBeforeForceDelete(electronRecordDo);
                //填充
                cardElectronRecordDomainService.fillInfoBeforeForceDelete(electronRecordDo);
                //处理
                cardElectronRecordRepository.forceDelete(electronRecordDo);
                //完成之后写日志，用异步处理
                safetyOperateLogRepository.asyncBatchSaveOrUpdate(Lists.newArrayList(electronRecordDo.getSafetyOperateLog()),
                        SecurityContextHolder.getContext());
                //通知小米钱包，用异步处理
                cardElectronRecordRepository.asyncNotifyPayCardIsDeleting(electronRecordDo, SecurityContextHolder.getContext());
            }
        }
    }

    @Override
    public Boolean isUsingNew(String uid) {
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteForPay(CardElectronPayDto cardElectronPayDto) {
        CardElectronRecordDo cardElectronRecordDo = converter.payDtoToDo(cardElectronPayDto);
        Map<String, Object> result = Maps.newHashMap();
        Integer isDelete = cardElectronRecordDomainService.buildCardElectronStatusForAppByCardNum(cardElectronRecordDo);
        if (CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_CAN_DELETE.getOperateType().equals(isDelete)) {
            //更新电子卡状态为删除中
            cardElectronRecordDo.setUpdateTime(ZonedDateTime.now());
            cardElectronRecordDo.setStatus(CardElectronStatusEnum.DELETING.getCode());
            Boolean isUpdateSuccess = cardElectronRecordRepository.updateById(cardElectronRecordDo);
            if (Boolean.TRUE.equals(isUpdateSuccess)) {
                result.put("result", CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_CAN_DELETE.getResultType());
                //若没有其它已开电子卡 删除安防权限
                deleteSafety(cardElectronRecordDo);
            } else {
                result.put("result", CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_NOT_DELETE.getResultType());
            }
        } else if (CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_HAS_DELETED.getOperateType().equals(isDelete)) {
            result.put("result", CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_HAS_DELETED.getResultType());
        } else {
            result.put("result", CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_NOT_DELETE.getResultType());
        }
        result.put("timestamp", String.valueOf(System.currentTimeMillis()));
        String srcData = RsavalidateUtil.asciiAscSort(result);
        String sign = RsavalidateUtil.sign(srcData, privateKey);
        result.put("sign", sign);
        return result;
    }

    /**
     * @param cardElectronRecordDo
     * @return void
     * @desc 若没有其它已开电子卡 删除安防权限
     * <AUTHOR> denghui
     * @date 2023/11/23 15:08
     */
    private void deleteSafety(CardElectronRecordDo cardElectronRecordDo) {
        //删除前判断当前用户是否还有其他已开电子卡
        Boolean hasOtherOpenedElectronCard =
                cardElectronRecordDomainService.judgeHasOtherOpenedElectronRecordV2(cardElectronRecordDo);
        //若没有其他已开电子卡 删除权限
        if (Boolean.FALSE.equals(hasOtherOpenedElectronCard)) {
            //删除前填充
            cardElectronRecordDomainService.fillInfoBeforeDeleted(cardElectronRecordDo);
            //删除前检查
            cardElectronRecordDomainService.checkBeforeDeleted(cardElectronRecordDo);
            //如果是迁移的电子卡，不做安防数据不做删除
            if (cardElectronRecordDo.getSafetyPersonMediumDo() != null) {
                //删除介质
                safetyMediumRepository.deleteByMediumCode(cardElectronRecordDo.getMediumCode());
                //删除人与介质关系(设置同步状态为0)
                cardElectronRecordDo.getSafetyPersonMediumDo().setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                safetyPersonMediumRepository.updateWaitSyncAndDeleteById(cardElectronRecordDo.getSafetyPersonMediumDo());
                //删除权限(设置同步状态为0)
                safetyRightRepository.updateWaitSyncAndDeleteByIdList(cardElectronRecordDo.getSafetyRightList());
            }
            //保存删除日志记录
            safetyOperateLogRepository.saveOrUpdate(cardElectronRecordDo.getSafetyOperateLog());
        }
    }

    @Override
    public Integer findOpenedElectronCard(Long cardId, String uid) {
        CardElectronRecordDo cardElectronRecordDo = new CardElectronRecordDo();
        cardElectronRecordDo.setCardId(cardId);
        cardElectronRecordDo.setUid(uid);
        cardElectronRecordDo.setStatus(CardElectronStatusEnum.OPENED.getCode());
        List<CardElectronRecordDo> cardElectronRecordDoList =
                cardElectronRecordDomainService.findOpenedElectronCardByCardIdAndUid(cardElectronRecordDo);
        return cardElectronRecordDoList == null ? 0 : cardElectronRecordDoList.size();
    }

    @Override
    public void updateOpenedElectronCardName(String uid) {
        CardElectronRecordDo cardElectronRecordDo = new CardElectronRecordDo();
        cardElectronRecordDo.setUid(uid);
        List<CardElectronRecordDo> openedElectronCard = cardElectronRecordDomainService.findOpenedElectronCardByUid(cardElectronRecordDo);
        //最多3台设备
        if (CollectionUtils.isNotEmpty(openedElectronCard)) {
            for (CardElectronRecordDo electronRecordDo : openedElectronCard) {
                cardElectronRecordDomainService.fillBeforeUpdateElectronName(electronRecordDo);
                cardElectronRecordRepository.updateById(electronRecordDo);
            }
        }
    }

    @Override
    public Map<String, Object> deliverCard(CardElectronPayDto payDto) {
        Map<String, Object> result = Maps.newHashMap();
        CardElectronRecordDo electronRecordDo = converter.payDtoToDo(payDto);
        cardElectronRecordDomainService.fillBeforeCreate(electronRecordDo);
        try {
            cardElectronRecordDomainService.checkBeforeCreate(electronRecordDo);
            cardElectronRecordRepository.save(electronRecordDo);
        } catch (BizException e) {
            if (CardInfoDomainErrorCodeEnum.CARD_IS_NOT_ACTIVE.getErrorCode() == e.getErrCode().getErrorCode()) {
                result.put("result", "10001");
            } else if (CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARD_EXIST.getErrorCode() == e.getErrCode().getErrorCode()) {
                result.put("result", "10002");
            }
        } catch (Exception e) {
            throw e;
        }
        buildResultMap(result, electronRecordDo);
        return result;
    }

    private void buildResultMap(Map<String, Object> result, CardElectronRecordDo electronRecordDo) {
        result.put("uid", CardUtil.flipString(electronRecordDo.getCardInfoDo().getMediumPhysicsCode(), 2));
        result.put("xiaomiCardNo", CardUtil.hexadecimalChang(electronRecordDo.getMediumEncryptCode()));
        result.put("cardArt", electronRecordDo.getElectronPhotoUrl());
        result.put("timestamp", String.valueOf(System.currentTimeMillis()));
        if (StringUtils.isBlank((String) result.get("result"))) {
            result.put("result", "0");
        }
        String srcData = RsavalidateUtil.asciiAscSort(result);
        String sign = RsavalidateUtil.sign(srcData, privateKey);
        result.put("sign", sign);
    }
}
