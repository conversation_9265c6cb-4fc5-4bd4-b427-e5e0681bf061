package com.mi.oa.ee.safety.application.impl.card.emp;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mi.oa.ee.safety.application.converter.card.CardApplyDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.temp.EmpCardApplyDto;
import com.mi.oa.ee.safety.application.dto.card.temp.EmpCardApplyEditDto;
import com.mi.oa.ee.safety.application.dto.card.temp.EmpCardApplyQueryDto;
import com.mi.oa.ee.safety.application.errorcode.CardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.emp.EmpCardApplyService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.AddressInfoDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.query.card.CardApplyQuery;
import com.mi.oa.ee.safety.domain.service.CardApplyDomainService;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyRightDomainService;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/6/23 14:41
 */
@Slf4j
@Service
public class EmpCardApplyServiceImpl implements EmpCardApplyService {

    @Resource
    private CardApplyDtoConverter cardApplyDtoConverter;

    @Resource
    private CardApplyDomainService cardApplyDomainService;

    @Resource
    private CardDomainService cardDomainService;

    @Resource
    private SafetyRightDomainService safetyRightDomainService;

    @Resource
    private SafetyRightRepository safetyRightRepository;

    @Resource
    private SpaceSdk spaceSdk;

    @Resource
    private AddressSdk addressSdk;

    @Resource
    SafetyPersonDomainService safetyPersonDomainService;

    @NacosValue(value = "${card.prefix-encrypt-code:201046}", autoRefreshed = true)
    private String prefixEncryptCode;

    @Override
    public PageModel<EmpCardApplyDto> pageConditionApply(EmpCardApplyQueryDto empCardApplyQueryDto, Boolean flag) {
        CardApplyQuery query = cardApplyDtoConverter.empCardApplyDtoToCardApplyDo(empCardApplyQueryDto);
        query.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        PageModel<CardApplyDo> cardApplyPage = cardApplyDomainService.pageConditionApplyList(query, flag);
        cardApplyDomainService.fillFullCardApplyV2(cardApplyPage.getList());
        List<EmpCardApplyDto> tempCardApplyList = cardApplyDtoConverter.cardApplyDoToEmpCardApplyDtoList(cardApplyPage.getList());
        return PageModel.build(tempCardApplyList, cardApplyPage.getPageSize(), cardApplyPage.getPageNum(), cardApplyPage.getTotal());
    }

    @Override
    public void fillExportEmpCardApplyDto(EmpCardApplyDto empCardApplyDto) {
        if (!StringUtils.isEmpty(empCardApplyDto.getReceiptParkCode())) {
            //提示访客系统需要提示的信息
            SafetySpaceParkDto spacePark = spaceSdk.getParkByCode(empCardApplyDto.getReceiptParkCode());
            if (spacePark == null) {
                return;
            }
            //填装城市
            AddressInfoDto addressInfoDto = addressSdk.getAddressById(Integer.valueOf(spacePark.getCityId()));
            if (addressInfoDto == null) {
                return;
            }
            empCardApplyDto.setReceiptCityName(addressInfoDto.getAddrName());
            //填装国家
            AddressInfoDto country = addressSdk.getAddressById(Integer.valueOf(addressInfoDto.getCountryId()));
            if (country == null) {
                return;
            }
            empCardApplyDto.setReceiptCountryName(country.getAddrName());
        }
    }

    @Override
    public EmpCardApplyDto detail(Long applyId) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(applyId);
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);
        cardApplyDomainService.fillFullCardApplyV2(cardApplyDo);
        EmpCardApplyDto empCardApplyDto = cardApplyDtoConverter.cardApplyDoToEmpCardApplyDto(cardApplyDo);
        //如果已经生成了正式卡，则填装对应的加密卡号和物理卡号
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoByApplyId(applyId);
        Optional.ofNullable(cardInfoDo).ifPresent(item -> {
            empCardApplyDto.setCardNum(item.getCardNum());
            empCardApplyDto.setMediumEncryptCode(item.getMediumEncryptCode());
            empCardApplyDto.setMediumPhysicsCode(item.getMediumPhysicsCode());
        });
        //填装对应的临时卡ID
        CardInfoDo tempCardInfo = cardDomainService.findCardByUidAndCardType(cardApplyDo.getUid(), CardTypeEnum.TEMP_CARD);
        if (tempCardInfo != null) {
            empCardApplyDto.setTempCardId(tempCardInfo.getId());
        }
        return empCardApplyDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void openCardByCardApply(EmpCardApplyEditDto editDto) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(editDto.getApplyId());
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);
        Asserts.assertNotNull(cardApplyDo, CardApplicationErrorCodeEnum.CARD_APPLY_NOT_FOUND);

        //校验新工卡正式卡是否已存在
        CardInfoDo cardParam = new CardInfoDo();
        cardParam.setUid(cardApplyDo.getUid());
        cardParam.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        cardParam.putExtField("statusList", CardStatusEnum.exceptNotActive());
        cardDomainService.checkEmpCardIsExist(cardParam);

        //填装申请单
        fillCardApplyEditInfo(cardApplyDo, editDto);
        cardApplyDomainService.updateCardApply(cardApplyDo);

        //获取当前申请单对应的卡信息
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoByApplyId(cardApplyDo.getId());
        if (Objects.isNull(cardInfoDo)) {
            //初次开卡
            cardInfoDo = initCardInfoOnOpenCard(cardApplyDo, editDto);
            cardDomainService.checkIsRepeatOpen(cardInfoDo);
            //填充人员信息
            SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
            safetyPersonDo.setUid(cardInfoDo.getUid());
            safetyPersonDomainService.fillPersonInfoWithBase(safetyPersonDo);

            //初次开卡校验人员状态是否可用
            safetyPersonDomainService.checkPersonIsActive(safetyPersonDo);

            //保存工卡记录
            cardInfoDo.putExtField("isInitStartTime", false);
            cardDomainService.openCard(cardInfoDo);
            //更新生效时间
            cardDomainService.saveValidateTime(cardInfoDo);
            //填装对应的临时卡权限
            CardInfoDo tempCardInfo = cardDomainService.findCardByUidAndCardType(cardApplyDo.getUid(), CardTypeEnum.TEMP_CARD);
            if (tempCardInfo != null) {
                List<SafetyRightDo> tempCardRights =
                        safetyRightRepository.findSafetyRightByMediumCodeAndUid(tempCardInfo.getMediumCode(), null, null);
                if (CollectionUtils.isNotEmpty(tempCardRights)) {
                    for (SafetyRightDo temCardRight : tempCardRights) {
                        temCardRight.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
                        temCardRight.setMediumCode(cardInfoDo.getMediumCode());
                        temCardRight.setOperateTime(ZonedDateTime.now());
                    }
                }
                safetyRightRepository.batchSaveOrUpdate(tempCardRights, false);
            }
        } else {
            cardDomainService.fillSafetyRight(cardInfoDo);
            cardDomainService.fillSafetyPersonMedium(cardInfoDo);
            //如果物理卡号或者加密卡号发生变化，则更新卡信息
            if (!org.apache.commons.lang.StringUtils.equalsIgnoreCase(cardInfoDo.getMediumPhysicsCode(), editDto.getMediumPhysicsCode()) ||
                    !org.apache.commons.lang.StringUtils.equalsIgnoreCase(cardInfoDo.getMediumEncryptCode(), editDto.getMediumEncryptCode())) {
                fillCardInfoEditInfo(cardInfoDo, editDto);
                cardInfoDo.putExtField("isEditForApply", true);
                cardDomainService.editCard(cardInfoDo);
            }
            cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
            cardDomainService.notifyCardNumberChange(cardInfoDo);
        }
    }

    private void fillCardInfoEditInfo(CardInfoDo cardInfoDo, EmpCardApplyEditDto editDto) {
        if (prefixEncryptCode.equals(editDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = editDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(editDto.getCardNum());
        cardInfoDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardInfoDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
    }

    private void fillValidatePeriodEditInfo(CardInfoDo cardInfoDo, EmpCardApplyEditDto editDto) {
        cardInfoDo.setStartTime(editDto.getStartTime());
        cardInfoDo.setEndTime(editDto.getEndTime());
    }

    private void fillCardApplyEditInfo(CardApplyDo cardApplyDo, EmpCardApplyEditDto editDto) {
        cardApplyDo.setStartTime(ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now()));
        cardApplyDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
        cardApplyDo.setCardNum(editDto.getCardNum());
        cardApplyDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardApplyDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
        cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_RECEIVE.getCode());
    }

    private CardInfoDo initCardInfoOnOpenCard(CardApplyDo cardApplyDo, EmpCardApplyEditDto editDto) {
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardApplyId(cardApplyDo.getId());
        cardInfoDo.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        cardInfoDo.setCardStatus(CardStatusEnum.NOT_ACTIVE.getCode());
        cardInfoDo.setUid(cardApplyDo.getUid());
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(editDto.getCardNum());
        cardInfoDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
        cardInfoDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardInfoDo.setStartTime(cardApplyDo.getStartTime());
        cardInfoDo.setEndTime(cardApplyDo.getEndTime());
        if (prefixEncryptCode.equals(editDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = editDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
        return cardInfoDo;
    }

}
