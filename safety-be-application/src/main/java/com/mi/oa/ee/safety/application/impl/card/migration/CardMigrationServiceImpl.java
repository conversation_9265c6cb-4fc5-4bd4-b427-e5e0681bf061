package com.mi.oa.ee.safety.application.impl.card.migration;

import com.mi.oa.ee.safety.application.converter.card.RecyclableCardDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.config.RecyclableCardDto;
import com.mi.oa.ee.safety.application.dto.card.migration.RepairResult;
import com.mi.oa.ee.safety.application.errorcode.RecyclableCardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.migration.CardMigrationService;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.domain.enums.card.RecyclableCardStatusEnum;
import com.mi.oa.ee.safety.domain.enums.card.RecyclableCardUseLogStatusEnum;
import com.mi.oa.ee.safety.domain.errorcode.RecyclableCardDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.RecyclableCardRepository;
import com.mi.oa.ee.safety.infra.repository.migration.CardMigrationRepository;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/12/7 17:09
 */
@Slf4j
@Service("cardMigrationService")
public class CardMigrationServiceImpl implements CardMigrationService {

    @Resource
    private CardMigrationRepository cardMigrationRepository;

    @Resource
    private RecyclableCardDtoConverter recyclableCardDtoConverter;

    @Resource
    private RecyclableCardRepository recyclableCardRepository;

    @Resource
    private CardInfoRepository cardInfoRepository;

    private void checkOnSync(RecyclableCardDto recyclableCardDto) {
        //物理卡号对应的配置已经存在
        RecyclableCardDo recyclableCardDo = recyclableCardRepository.findByMediumPhysicsCode(recyclableCardDto.getMediumPhysicsCode());
        if (Objects.nonNull(recyclableCardDo)) {
            throw new BizException(RecyclableCardDomainErrorCodeEnum.MEDIUM_PHYSICS_CODE_EXIST);
        }
        //加密卡号对应的配置已经存在
        recyclableCardDo = recyclableCardRepository.findByMediumEncryptCode(recyclableCardDto.getMediumEncryptCode());
        if (Objects.nonNull(recyclableCardDo)) {
            throw new BizException(RecyclableCardDomainErrorCodeEnum.MEDIUM_ENCRYPT_CODE_EXIST);
        }
    }

    @Override
    public void sync(RecyclableCardDto recyclableCardDto) {
        RecyclableCardDo recyclableCardDo = recyclableCardRepository.findByCardNum(recyclableCardDto.getCardNum());
        //卡编号对应的配置已经存在
        if (Objects.nonNull(recyclableCardDo)) {
            return;
        }
        checkOnSync(recyclableCardDto);
        recyclableCardDo = recyclableCardDtoConverter.toRecyclableCardDo(recyclableCardDto);
        cardMigrationRepository.saveRecyclableCard(recyclableCardDo);
    }

    @Override
    public long countRecyclableCard() {
        return cardMigrationRepository.countRecyclableCard();
    }

    @Override
    public List<RecyclableCardDto> findRecyclableCard(long offsetId, List<String> cardNumList) {
        List<RecyclableCardDo> recyclableCardDoList = cardMigrationRepository.findRecyclableCard(offsetId, cardNumList);
        return recyclableCardDtoConverter.toRecyclableCardDtoList(recyclableCardDoList);
    }

    @Override
    public RepairResult repair(RecyclableCardDto recyclableCardDto) {
        boolean success = true;
        String errorMsg = StringUtils.EMPTY;
        RecyclableCardDo recyclableCardDo = recyclableCardDtoConverter.toRecyclableCardDo(recyclableCardDto);
        RepairResult.RepairResultBuilder repairResult = RepairResult.builder()
                .cardNum(recyclableCardDto.getCardNum()).mediumPhysicsCode(recyclableCardDto.getMediumPhysicsCode())
                .mediumEncryptCode(recyclableCardDto.getMediumEncryptCode());
        CardInfoDo cardInfoDo = findCardInfoDo(recyclableCardDo);
        try {
            if (Objects.isNull(cardInfoDo)) {
                doRepair(recyclableCardDo);
            } else {
                doRepair(recyclableCardDo, cardInfoDo);
            }
        } catch (Exception e) {
            log.error("repair recyclable card error. cardNum:{}. ", recyclableCardDto.getCardNum(), e);
            success = false;
            errorMsg = e.getMessage();
        }
        return repairResult.success(success).errorMsg(errorMsg).build();
    }


    private void doRepair(RecyclableCardDo recyclableCardDo, CardInfoDo cardInfoDo) {
        if (!StringUtils.equalsIgnoreCase(recyclableCardDo.getMediumPhysicsCode(), cardInfoDo.getMediumPhysicsCode()) ||
                !StringUtils.equalsIgnoreCase(recyclableCardDo.getMediumEncryptCode(), cardInfoDo.getMediumEncryptCode())) {
            throw new BizException(RecyclableCardApplicationErrorCodeEnum.RECYCLABLE_CARD_ERROR);
        }
        //是否需要修复状态
        if (RecyclableCardStatusEnum.IN_USE != recyclableCardDo.getCardStatusEnum()) {
            recyclableCardDo.setCardStatusEnum(RecyclableCardStatusEnum.IN_USE);
        }
        recyclableCardDo.setUid(cardInfoDo.getUid());
        recyclableCardDo.setCardTypeEnum(CardTypeEnum.ofNumber(cardInfoDo.getCardType()));
        recyclableCardRepository.update(recyclableCardDo);
        //是否需要修复工卡记录
        if (StringUtils.isBlank(cardInfoDo.getCardNum())) {
            cardInfoDo.setCardNum(recyclableCardDo.getCardNum());
            cardMigrationRepository.repairCardNum(cardInfoDo);
        }
        //是否需要添加使用日志
        RecyclableCardUseLogDo recyclableCardUseLogDo = recyclableCardRepository.findUsingCardLog(recyclableCardDo);
        if (Objects.isNull(recyclableCardUseLogDo) && Objects.nonNull(cardInfoDo.getId())) {
            recyclableCardUseLogDo = new RecyclableCardUseLogDo();
            recyclableCardUseLogDo.setCardNum(recyclableCardDo.getCardNum());
            recyclableCardUseLogDo.setUid(cardInfoDo.getUid());
            recyclableCardUseLogDo.setMediumPhysicsCode(recyclableCardDo.getMediumPhysicsCode());
            recyclableCardUseLogDo.setMediumEncryptCode(recyclableCardUseLogDo.getMediumEncryptCode());
            recyclableCardUseLogDo.setUseStatusEnum(RecyclableCardUseLogStatusEnum.IN_USE);
            recyclableCardUseLogDo.setOperatorUid("admin");
            recyclableCardUseLogDo.setOperator("admin");
            List<CardTimeValidityDo> cardTimeValidityDoList = cardInfoRepository.getValidatePeriod(cardInfoDo.getId());
            if (CollectionUtils.isEmpty(cardTimeValidityDoList)) {
                recyclableCardUseLogDo.setReceiveTime(ZonedDateTime.now());
            } else {
                recyclableCardUseLogDo.setReceiveTime(cardTimeValidityDoList.get(0).getStartTime());
            }
            cardMigrationRepository.saveLog(recyclableCardUseLogDo);
        }
    }

    private void doRepair(RecyclableCardDo recyclableCardDo) {
        //如果没有卡使用记录，并且卡状态是使用中，且工卡已经全量迁移
        if (RecyclableCardStatusEnum.IN_USE == recyclableCardDo.getCardStatusEnum()) {
            recyclableCardDo.setCardStatusEnum(RecyclableCardStatusEnum.TO_OPEN);
            recyclableCardDo.setUid(StringUtils.EMPTY);
            recyclableCardDo.setCardTypeEnum(null);
            recyclableCardRepository.update(recyclableCardDo);
        }
    }

    private CardInfoDo findCardInfoDo(RecyclableCardDo recyclableCardDo) {
        CardInfoDo cardInfoDo = cardInfoRepository.getCardByCardNum(recyclableCardDo.getCardNum());
        //卡编号对应的配置已经存在
        if (Objects.nonNull(cardInfoDo)) {
            return cardInfoDo;
        }
        //物理卡号对应的配置已经存在
        cardInfoDo = cardInfoRepository.findCardInfoByPhysicsCode(recyclableCardDo.getMediumPhysicsCode());
        if (Objects.nonNull(cardInfoDo)) {
            return cardInfoDo;
        }
        //加密卡号对应的配置已经存在
        return cardInfoRepository.findCardInfoByEncryptCode(recyclableCardDo.getMediumEncryptCode());
    }
}
