package com.mi.oa.ee.safety.application.impl.card.shared;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.application.converter.card.CardMigrateDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.shared.CardTravelRecordDto;
import com.mi.oa.ee.safety.application.service.card.shared.CardMigrateService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.CountryMappingDto;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardApplyTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTravelRecordStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyCarrierGroupClassEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.domain.converter.CardPersonInfoDoConverter;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.CardPersonInfoDo;
import com.mi.oa.ee.safety.domain.model.CardTravelRecordDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.model.SafetyUmsNotifyDo;
import com.mi.oa.ee.safety.domain.service.CardApplyDomainService;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.domain.service.CardElectronRecordDomainService;
import com.mi.oa.ee.safety.domain.ability.CardPersonInfoAbility;
import com.mi.oa.ee.safety.domain.service.CardPersonInfoDomainService;
import com.mi.oa.ee.safety.domain.service.CardTravelRecordDomainService;
import com.mi.oa.ee.safety.infra.remote.model.FindNeedSyncNewTempReq;
import com.mi.oa.ee.safety.infra.remote.model.OldCardTravelRecordDto;
import com.mi.oa.ee.safety.infra.remote.model.supplier.CardInfoRemoteDto;
import com.mi.oa.ee.safety.infra.remote.nacos.CountryMappingConfig;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.OldCardSdk;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.ee.safety.infra.repository.CardElectronRecordRepository;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierGroupRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyPersonMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyUmsNotifyRepository;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 工卡迁移的实现类
 *
 * <AUTHOR>
 * @date 2023/12/7 14:26
 */
@Slf4j
@Service
public class CardMigrateServiceImpl implements CardMigrateService {

    @Resource
    private CardApplyDomainService cardApplyDomainService;

    @Resource
    private CardDomainService cardDomainService;

    @Resource
    private CardElectronRecordDomainService cardElectronRecordDomainService;

    @Resource
    private CardInfoRepository cardInfoRepository;

    @Resource
    private SafetyMediumRepository safetyMediumRepository;

    @Resource
    private SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Resource
    private SafetyRightRepository safetyRightRepository;

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Resource
    private CardElectronRecordRepository cardElectronRecordRepository;

    @Resource
    private OldCardSdk oldCardSdk;

    @Resource
    private CardTravelRecordServiceImpl cardTravelRecordService;

    @Resource
    private CardMigrateDtoConverter converter;

    @Resource
    private CountryMappingConfig countryMappingConfig;

    @Resource
    private CardTravelRecordDomainService cardTravelRecordDomainService;

    @Resource
    private SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Resource
    private SafetyOperateLogRepository safetyOperateLogRepository;

    @Resource
    private SafetyUmsNotifyRepository safetyUmsNotifyRepository;

    @Resource
    private CardPersonInfoDoConverter cardPersonInfoDoConverter;

    @Autowired
    private CardPersonInfoDomainService cardPersonInfoDomainService;

    @NacosValue(value = "${card.travel.chat-id:95c3cc598f7a427a9a60b370f5fd32af}", autoRefreshed = true)
    private String chatId;

    /**
     * 根据用户Uid迁移老工卡的卡片到新工卡来
     *
     * @param safetyPersonDo
     * @return void
     */
    @Override
    public void migrateCardByUid(SafetyPersonDo safetyPersonDo) {
        log.info("migrateCardByUid start uid:{} username:{}", safetyPersonDo.getUid(), safetyPersonDo.getUserName());
        //迁移正式卡
        migrateEmpCard(safetyPersonDo);
        //迁移电子卡
        migrateElectronCard(safetyPersonDo);
        //迁移临时卡
        migrateTempCard(safetyPersonDo);
        //迁移差旅记录
        migrateTravelRecord(safetyPersonDo);

        log.info("migrateCardByUid end uid:{} username:{}", safetyPersonDo.getUid(), safetyPersonDo.getUserName());
    }

    @Override
    public void warningMigrateFailed(SafetyPersonDo safetyPersonDo, Exception e) {
        SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
        safetyUmsNotifyDo.setReceiver(chatId);
        Map<String, String> param = new HashMap<>();
        param.put("errorMessage", e.toString());
        param.put("userName", safetyPersonDo.getUserName());
        safetyUmsNotifyDo.setParams(JacksonUtils.bean2Json(param));
        safetyUmsNotifyDo.setSendStatus(OAUCFCommonConstants.INT_ZERO);
        safetyUmsNotifyDo.setSendTime(ZonedDateTime.now());
        safetyUmsNotifyDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.CARD_MIGRATE_FAILED_SEND_LARK.getCode());
        safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
    }

    private void migrateEmpCard(SafetyPersonDo safetyPersonDo) {

        //填充 人卡权限信息
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPartnerAccount(safetyPersonDo.getUserName());
        cardApplyDomainService.fillCardApplyByOldCard(cardApplyDo);

        //如果是临时卡，不需要在正式卡中迁移
        if (StringUtils.isEmpty(cardApplyDo.getPhotoUrl())) {
            return;
        }

        //检查是否需要迁移
        Boolean ifNeedMigrate = cardApplyDomainService.checkIsNeedMigrate(cardApplyDo);
        if (!ifNeedMigrate) {
            log.info("username: {} is not need migrate", cardApplyDo.getPartnerAccount());
            return;
        }

        //创建前检查本地物理卡号和加密卡号是否被占用
        CardInfoDo card = new CardInfoDo();
        card.setMediumPhysicsCode(cardApplyDo.getMediumPhysicsCode());
        card.setMediumEncryptCode(cardApplyDo.getMediumEncryptCode());
        cardDomainService.checkCardCodeIsOccupied(card);

        //开卡前校验此人是否已有临时卡
        CardInfoDo cardParam = new CardInfoDo();
        cardParam.setCardType(CardTypeEnum.TEMP_CARD.getNumber());
        cardParam.setUid(cardApplyDo.getPersonInfo().getUid());
        cardDomainService.checkTempCardIsReturn(cardParam);

        //开卡前校验此人是否已存在未销卡的正式卡
        cardParam.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        cardParam.putExtField("statusList", CardStatusEnum.usingList());
        cardDomainService.checkEmpCardIsExist(cardParam);

        //创建正式卡申请单
        cardApplyDo.putExtField("isSync", true);
        cardApplyDo.setParkCode(safetyPersonDo.getParkCode());
        //工卡系统没有头像的正式卡为emp，这里更新为空。保证头像数据正确
        if (StringUtils.equalsIgnoreCase("emp", cardApplyDo.getPhotoUrl())) {
            cardApplyDo.setPhotoUrl(StringUtils.EMPTY);
        }
        cardApplyDomainService.fillEmpApplyInfo(cardApplyDo);
        cardApplyDomainService.checkBeforeCreateForMigrate(cardApplyDo);
        if (Objects.isNull(cardApplyDo.getId())) {
            Long applyId = cardApplyRepository.save(cardApplyDo);
            cardApplyDo.setId(applyId);
        } else {
            cardApplyRepository.updateById(cardApplyDo);
        }

        //添加人信息
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(cardApplyDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);

        //创建正式卡信息
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardApply(cardApplyDo);
        cardInfoDo.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        cardDomainService.fillImportCardInfoByApply(cardInfoDo);
        Long cardId = cardInfoRepository.save(cardInfoDo);
        cardInfoDo.setId(cardId);

        //保存介质
        cardDomainService.fillImportMedium(cardInfoDo);
        safetyMediumRepository.batchSaveOrUpdate(Lists.newArrayList(cardInfoDo.getSafetyMedium()));

        // 创建人与介质的关系 并设置为已同步
        cardDomainService.fillImportPersonMedium(cardInfoDo);
        safetyPersonMediumRepository.saveOrUpdate(Lists.newArrayList(cardInfoDo.getSafetyPersonMediumDo()));

        //创建人介质权限的关系 并设置为已同步
        cardDomainService.fillImportRights(cardInfoDo);
        safetyRightRepository.batchSaveOrUpdate(cardInfoDo.getSafetyRightList(), false);

        //创建卡有效期
        cardDomainService.fillImportValidateTimeAndSave(cardInfoDo);

        //同步成功 标记老工卡为已同步
        cardApplyDomainService.markOldCardData(cardApplyDo);
    }

    private void migrateElectronCard(SafetyPersonDo safetyPersonDo) {
        CardElectronRecordDo cardElectronRecordDo = new CardElectronRecordDo();
        cardElectronRecordDo.setUid(safetyPersonDo.getUid());
        cardElectronRecordDo.setSafetyPersonDo(safetyPersonDo);
        List<CardElectronRecordDo> cardElectronRecordDoList = cardElectronRecordDomainService.getCardElectronRecordListByUidFromOldCard(cardElectronRecordDo);
        if (CollectionUtils.isNotEmpty(cardElectronRecordDoList)) {
            for (int i = 0; i < cardElectronRecordDoList.size(); i++) {
                CardElectronRecordDo now = cardElectronRecordDoList.get(i);
                //迁移前填装电子工卡记录
                cardElectronRecordDomainService.fillBeforeMigrate(now);
                if (OAUCFCommonConstants.INT_ZERO.equals(i)) {
                    //创建前检查电子工卡记录
                    Boolean isCan = cardElectronRecordDomainService.judgeIsCanMigrate(now);
                    if (!isCan) {
                        break;
                    }
                }
                //创建电子工卡记录
                cardElectronRecordRepository.save(now);
                if (OAUCFCommonConstants.INT_ZERO.equals(i)) {
                    //记录日志，让当期操作日志的权限都为空，不做收回
                    cardElectronRecordDomainService.fillSafetyOperateLogBeforeMigrate(now);
                    safetyOperateLogRepository.saveOrUpdate(now.getSafetyOperateLog());
                }
            }
        }
    }

    public void migrateTempCard(SafetyPersonDo safetyPersonDo) {
        //获取需要处理的待开卡数据
        FindNeedSyncNewTempReq req = new FindNeedSyncNewTempReq();
        req.setUserNames(Lists.newArrayList(safetyPersonDo.getUserName()));
        req.setStatus(OAUCFCommonConstants.INT_ZERO);

        List<CardInfoRemoteDto> needSyncNewTempForToOpen = oldCardSdk.findNeedSyncNewTemp(req);
        if (CollectionUtils.isNotEmpty(needSyncNewTempForToOpen)) {
            //迁移待开卡临时卡申请单
            migrateOpeningTempCard(safetyPersonDo);
        } else {
            //获取需要处理的已开卡数据
            req.setStatus(OAUCFCommonConstants.INT_ONE);
            List<CardInfoRemoteDto> needSyncNewTempForOpened = oldCardSdk.findNeedSyncNewTemp(req);
            if (CollectionUtils.isNotEmpty(needSyncNewTempForOpened)) {
                //迁移已开卡临时卡申请单和卡信息及权限
                migrateOpenedTempCard(safetyPersonDo);
            }
        }
    }

    private void migrateOpenedTempCard(SafetyPersonDo safetyPersonDo) {

        //填充 人卡权限信息
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPartnerAccount(safetyPersonDo.getUserName());
        cardApplyDomainService.fillCardApplyByOldCard(cardApplyDo);

        //创建前检查本地物理卡号和加密卡号是否被占用
        CardInfoDo card = new CardInfoDo();
        card.setMediumPhysicsCode(cardApplyDo.getMediumPhysicsCode());
        card.setMediumEncryptCode(cardApplyDo.getMediumEncryptCode());
        cardDomainService.checkCardCodeIsOccupied(card);

        //开卡前校验此人是否已有临时卡
        CardInfoDo cardParam = new CardInfoDo();
        cardParam.setCardType(CardTypeEnum.TEMP_CARD.getNumber());
        cardParam.setUid(cardApplyDo.getPersonInfo().getUid());
        cardDomainService.checkTempCardIsExist(cardParam);

        //开卡前校验此人是否已存在未销卡的正式卡
        cardParam.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        cardParam.putExtField("statusList", CardStatusEnum.usingList());
        cardDomainService.checkEmpCardIsExist(cardParam);

        //创建临时卡申请单
        cardApplyDo.putExtField("isSync", true);
        cardApplyDo.setParkCode(safetyPersonDo.getParkCode());
        cardApplyDomainService.fillTempApplyInfo(cardApplyDo);
        cardApplyDomainService.checkBeforeCreateForMigrate(cardApplyDo);
        saveOrUpdateTempCardApply(cardApplyDo);

        //添加人信息
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(cardApplyDo.getPersonInfo());
        cardPersonInfoDo.setPhotoUrl(cardApplyDo.getPhotoUrl());
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);

        //创建临时卡信息
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardApply(cardApplyDo);
        cardInfoDo.setCardType(CardTypeEnum.TEMP_CARD.getNumber());
        cardDomainService.fillImportCardInfoByApply(cardInfoDo);
        Long cardId = cardInfoRepository.save(cardInfoDo);
        cardInfoDo.setId(cardId);

        //保存介质
        cardDomainService.fillImportMedium(cardInfoDo);
        safetyMediumRepository.batchSaveOrUpdate(Lists.newArrayList(cardInfoDo.getSafetyMedium()));

        //保存介质与人的关系
        cardDomainService.fillImportPersonMedium(cardInfoDo);
        safetyPersonMediumRepository.saveOrUpdate(Lists.newArrayList(cardInfoDo.getSafetyPersonMediumDo()));

        //创建人介质权限的关系 并设置为已同步
        cardDomainService.fillImportRights(cardInfoDo);
        safetyRightRepository.batchSaveOrUpdate(cardInfoDo.getSafetyRightList(), false);

        //创建卡有效期
        cardDomainService.fillImportValidateTimeAndSave(cardInfoDo);

        //同步成功 标记老工卡为已同步
        cardApplyDomainService.markOldCardData(cardApplyDo);
    }

    private void saveOrUpdateTempCardApply(CardApplyDo cardApplyDo) {
        CardApplyDo oldCardApply = new CardApplyDo();
        oldCardApply.setUid(cardApplyDo.getPersonInfo().getUid());
        oldCardApply.setApplyType(CardApplyTypeEnum.TEMP_CARD_APPLY.getCode());
        cardApplyDomainService.fillCardApplyByUidAndType(oldCardApply);
        if (Objects.isNull(oldCardApply.getId())) {
            Long applyId = cardApplyRepository.save(cardApplyDo);
            cardApplyDo.setId(applyId);
        } else {
            cardApplyDo.setId(oldCardApply.getId());
            cardApplyRepository.updateById(cardApplyDo);
        }
    }

    private void migrateOpeningTempCard(SafetyPersonDo safetyPersonDo) {
        //填充申请单数据
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardApplyDomainService.fillTempApplyInfo(cardApplyDo);
        cardApplyDomainService.checkBeforeCreateForMigrate(cardApplyDo);
        saveOrUpdateTempCardApply(cardApplyDo);
        //添加人信息
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(safetyPersonDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);
    }

    public void migrateTravelRecord(SafetyPersonDo safetyPersonDo) {
        try {
            //获取还未处理差旅记录
            List<OldCardTravelRecordDto> needSyncTravelRecordForWaitDeal =
                    oldCardSdk.findNeedSyncTravelRecord(safetyPersonDo.getUserName(), OAUCFCommonConstants.INT_ONE);
            if (CollectionUtils.isNotEmpty(needSyncTravelRecordForWaitDeal)) {
                List<CardTravelRecordDto> cardTravelRecordDtoList =
                        converter.toTravelDtoList(needSyncTravelRecordForWaitDeal);
                for (CardTravelRecordDto cardTravelRecordDto : cardTravelRecordDtoList) {
                    //保存差旅
                    cardTravelRecordService.saveForMigration(cardTravelRecordDto);
                    //更新数据为已处理
                    oldCardSdk.updateTravelRecordStatus(safetyPersonDo.getUserName(),
                            cardTravelRecordDto.getTravelApplyId(), cardTravelRecordDto.getDestination());
                }
            }
            //获取已经处理差旅记录
            List<OldCardTravelRecordDto> needSyncTravelRecordForOpened =
                    oldCardSdk.findNeedSyncTravelRecord(safetyPersonDo.getUserName(), OAUCFCommonConstants.INT_TWO);
            if (CollectionUtils.isNotEmpty(needSyncTravelRecordForOpened)) {
                List<CardTravelRecordDto> cardTravelRecordDtoList =
                        converter.toTravelDtoList(needSyncTravelRecordForOpened);
                for (CardTravelRecordDto cardTravelRecordDto : cardTravelRecordDtoList) {
                    //保存差旅
                    cardTravelRecordDto.setStatus(CardTravelRecordStatusEnum.NOT_NEED_DEAL.getCode());
                    cardTravelRecordService.saveForMigration(cardTravelRecordDto);
                    //校验并补全权限
                    CardInfoDo cardInfoDo = new CardInfoDo();
                    cardInfoDo.setUid(safetyPersonDo.getUid());
                    CardInfoDo card = cardInfoRepository.findTempAndEmpCard(cardInfoDo);
                    //实体存在
                    if (ObjectUtils.isNotEmpty(card)) {
                        //当前已开权限
                        List<SafetyRightDo> existSafetyRightList =
                                safetyRightRepository.findSafetyRightByMediumCodeAndUid(card.getMediumCode(),
                                        safetyPersonDo.getUid(), null);
                        if (existSafetyRightList == null) { //防空
                            existSafetyRightList = Lists.newArrayList();
                        }
                        if (StringUtils.isNotEmpty(cardTravelRecordDto.getCountry())) {
                            CountryMappingDto countryMappingDto = countryMappingConfig.findByTripCountryName(cardTravelRecordDto.getCountry());
                            //不在配置名单内 差旅不处理 无需补权限
                            if (Objects.isNull(countryMappingDto)) {
                                log.warn("travel destination country not config, skip. record :{}", cardTravelRecordDto);
                                continue;
                            }
                            //中国差旅按城市赋差旅权限
                            if (AddressSdk.CHINA.equals(countryMappingDto.getCountryId())) {
                                patchingRightByCity(card, existSafetyRightList, cardTravelRecordDto, safetyPersonDo);
                            } else { //其它按国家赋差旅权限
                                patchingRightByCountry(card, existSafetyRightList, countryMappingDto.getCountryId());
                            }
                        } else { //国家为空 默认是中国 也通过城市补齐权限
                            patchingRightByCity(card, existSafetyRightList, cardTravelRecordDto, safetyPersonDo);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("{}:migrateTravelRecord failed:{}", safetyPersonDo.getUserName(), e);
        }
    }

    private void patchingRightByCountry(CardInfoDo card, List<SafetyRightDo> existSafetyRightList, String countryId) {
        SafetyCarrierGroupQuery query = new SafetyCarrierGroupQuery();
        query.setPageNum(OAUCFCommonConstants.LONG_ONE);
        query.setPageSize(SafetyConstants.EXPORT_MAX_PAGE_SIZE);
        query.setCountryId(countryId);
        query.setClassCode(SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode());
        query.setCarrierGroupType(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode());
        PageModel<SafetyCarrierGroupDo> pageModel = safetyCarrierGroupRepository.pageCondition(query);
        //当前国家下的所有权限
        List<SafetyCarrierGroupDo> safetyCarrierGroupDos = pageModel.getList();
        if (CollectionUtils.isEmpty(safetyCarrierGroupDos)) {
            return;
        }
        List<String> existGroupCodes = existSafetyRightList.stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
        safetyCarrierGroupDos.removeIf(safetyCarrierGroupDo -> existGroupCodes.contains(safetyCarrierGroupDo.getCarrierGroupCode()));
        if (CollectionUtils.isNotEmpty(safetyCarrierGroupDos)) {
            //构建right并保存
            List<SafetyRightDo> safetyRightDoList = Lists.newArrayList();
            for (SafetyCarrierGroupDo safetyCarrierGroupDo : safetyCarrierGroupDos) {
                SafetyRightDo safetyRightDo = new SafetyRightDo();
                safetyRightDo.setMediumCode(card.getMediumCode());
                safetyRightDo.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
                safetyRightDo.setCarrierGroupCode(safetyCarrierGroupDo.getCarrierGroupCode());
                safetyRightDo.setUid(card.getUid());
                safetyRightDo.setStartTime(SafetyConstants.Card.DEFAULT_START_TIME);
                safetyRightDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
                safetyRightDo.setSupplierCheckCode(card.getMediumPhysicsCode());
                safetyRightDo.setTenantCode("迁移数据补全权限");
                safetyRightDo.setSupplierCheckCode(card.getMediumPhysicsCode());
                safetyRightDo.setSupplierAccessCode(safetyCarrierGroupDo.getSupplierAccessCode());
                safetyRightDoList.add(safetyRightDo);
            }
            safetyRightRepository.batchSaveOrUpdate(safetyRightDoList, false);
        }
    }

    private void patchingRightByCity(CardInfoDo card, List<SafetyRightDo> existSafetyRightList,
                                     CardTravelRecordDto cardTravelRecordDto, SafetyPersonDo safetyPersonDo) {
        CardTravelRecordDo cardTravelRecordDo = new CardTravelRecordDo();
        cardTravelRecordDo.setUserName(safetyPersonDo.getUserName());
        cardTravelRecordDo.setCountryId(AddressSdk.CHINA);
        cardTravelRecordDo.setDestination(cardTravelRecordDto.getDestination());
        try {
            cardTravelRecordDomainService.fillCardTravelRecordWithUidAndCityId(cardTravelRecordDo);
            List<String> cityList = Arrays.asList(cardTravelRecordDo.getCityIds().split(","));
            SafetyCarrierGroupQuery query = new SafetyCarrierGroupQuery();
            query.setCityId(Integer.parseInt(cityList.get(0)));
            query.setPageNum(OAUCFCommonConstants.LONG_ONE);
            query.setPageSize(SafetyConstants.EXPORT_MAX_PAGE_SIZE);
            query.setClassCode(SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode());
            query.setCarrierGroupType(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode());
            PageModel<SafetyCarrierGroupDo> pageModel = safetyCarrierGroupRepository.pageCondition(query);
            //当前城市下的所有权限
            List<SafetyCarrierGroupDo> safetyCarrierGroupDos = pageModel.getList();
            if (CollectionUtils.isEmpty(safetyCarrierGroupDos)) {
                return;
            }
            List<String> existGroupCodes = existSafetyRightList.stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
            safetyCarrierGroupDos.removeIf(safetyCarrierGroupDo -> existGroupCodes.contains(safetyCarrierGroupDo.getCarrierGroupCode()));
            if (CollectionUtils.isNotEmpty(safetyCarrierGroupDos)) {
                //构建right并保存
                List<SafetyRightDo> safetyRightDoList = Lists.newArrayList();
                for (SafetyCarrierGroupDo safetyCarrierGroupDo : safetyCarrierGroupDos) {
                    SafetyRightDo safetyRightDo = new SafetyRightDo();
                    safetyRightDo.setMediumCode(card.getMediumCode());
                    safetyRightDo.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
                    safetyRightDo.setCarrierGroupCode(safetyCarrierGroupDo.getCarrierGroupCode());
                    safetyRightDo.setUid(card.getUid());
                    safetyRightDo.setStartTime(SafetyConstants.Card.DEFAULT_START_TIME);
                    safetyRightDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
                    safetyRightDo.setTenantCode("迁移数据补全权限");
                    safetyRightDo.setSupplierCode(safetyCarrierGroupDo.getSupplierCode());
                    safetyRightDo.setSupplierCheckCode(card.getMediumPhysicsCode());
                    safetyRightDo.setSupplierAccessCode(safetyCarrierGroupDo.getSupplierAccessCode());
                    safetyRightDoList.add(safetyRightDo);
                }
                safetyRightRepository.batchSaveOrUpdate(safetyRightDoList, false);
            }
        } catch (Exception e) {
            log.error("fill city failed:{}", e.toString());
        }
    }
}
