package com.mi.oa.ee.safety.application.impl.card.shared;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.application.converter.card.CommonCardDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.shared.CardTravelRecordDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionAddDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionDeleteDto;
import com.mi.oa.ee.safety.application.errorcode.CardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.shared.CardTravelRecordService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.*;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.StatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTravelRecordSpecialStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTravelRecordStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyCarrierGroupClassEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsSendStatusEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.DateUtils;
import com.mi.oa.ee.safety.domain.errorcode.CardTravelRecordDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.query.card.PermissionQuery;
import com.mi.oa.ee.safety.domain.service.CardApplyDomainService;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.domain.service.CardTravelRecordDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyCarrierGroupDomainService;
import com.mi.oa.ee.safety.infra.remote.nacos.CountryMappingConfig;
import com.mi.oa.ee.safety.infra.remote.nacos.TravelSafetyRightMappingConfig;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.repository.*;
import com.mi.oa.ee.safety.infra.repository.query.CardTravelRecordQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.rep.DeptInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 工卡差旅记录服务
 *
 * <AUTHOR>
 * @date 2023/8/9 17:05
 */
@Slf4j
@Service
public class CardTravelRecordServiceImpl implements CardTravelRecordService {

    @Autowired
    private CardTravelRecordDomainService cardTravelRecordDomainService;

    @Resource
    private SafetyUmsNotifyRepository safetyUmsNotifyRepository;

    @Resource
    private SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Resource
    private SafetyOperateLogRepository safetyOperateLogRepository;

    @Resource
    private CardDomainService cardDomainService;

    @Autowired
    private CommonCardDtoConverter commonCardDtoConverter;

    @Autowired
    private CardTravelRecordRepository cardTravelRecordRepository;

    @Resource
    private CountryMappingConfig countryMappingConfig;

    @Resource
    private SafetyRightRepository safetyRightRepository;

    @Resource
    private TravelSafetyRightMappingConfig travelSafetyRightMappingConfig;

    @Resource
    private SafetyCarrierGroupDomainService safetyCarrierGroupDomainService;

    @Resource
    private IdmSdk idmSdk;

    @Resource
    private CardGroupConfigRepository cardGroupConfigRepository;

    @Override
    public Boolean checkSafetyOperateLogIsDone(List<CardTravelRecordDto> cardTravelRecordDtoList) {
        boolean isAllDone = true;
        if (CollectionUtils.isNotEmpty(cardTravelRecordDtoList)) {
            for (CardTravelRecordDto item : cardTravelRecordDtoList) {
                CardTravelRecordDo cardTravelRecordDo = commonCardDtoConverter.toCardTravelRecordDo(item);
                //如果操作ID为空或为0，说明此差旅单没有生成对应的操作，不需要校验操作是否完成
                if (cardTravelRecordDo.getSafetyOperateLogId() == null || OAUCFCommonConstants.LONG_ZERO.equals(cardTravelRecordDo.getSafetyOperateLogId())) {
                    continue;
                }
                try {
                    cardTravelRecordDomainService.checkIsSafetyOperateDone(cardTravelRecordDo);
                } catch (BizException e) {
                    isAllDone = false;
                    break;
                }
            }
        }
        return isAllDone;
    }

    @Override
    public List<CardTravelRecordDto> queryCardTravelRecordListByUmsSendStatus(Integer umsSendStatus) {
        CardTravelRecordQuery cardTravelRecordQuery = new CardTravelRecordQuery();
        Asserts.assertNotNull(umsSendStatus, CardTravelRecordDomainErrorCodeEnum.TRAVEL_QUERY_STATUS_NOT_EMPTY);
        cardTravelRecordQuery.setUmsSendStatus(umsSendStatus);
        //当天10点
        ZonedDateTime startTime = ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now());
        //14点后不发通知，因此只查询
        ZonedDateTime endTime = ZonedDateTimeUtils.getZonedDateTimeEnd(ZonedDateTime.now());
        //开始时间大于当前时间，并且待处理的
        cardTravelRecordQuery.setQueryStartTimeFrom(startTime);
        cardTravelRecordQuery.setQueryStartTimeTo(endTime);
        List<CardTravelRecordDo> list = cardTravelRecordRepository.queryCardTravelRecordList(cardTravelRecordQuery);
        return commonCardDtoConverter.toCardTravelRecordDtoList(list);
    }

    @Override
    public void updateUmsSendStatusById(CardTravelRecordDto cardTravelRecordDto) {
        CardTravelRecordDo cardTravelRecordDo = commonCardDtoConverter.toCardTravelRecordDo(cardTravelRecordDto);
        cardTravelRecordRepository.updateUmsSendStatusById(cardTravelRecordDo);
    }

    @Override
    public void updateStatusById(CardTravelRecordDto cardTravelRecordDto) {
        CardTravelRecordDo cardTravelRecordDo = commonCardDtoConverter.toCardTravelRecordDo(cardTravelRecordDto);
        cardTravelRecordRepository.updateStatusById(cardTravelRecordDo);
    }

    @Override
    public List<CardTravelRecordDto> queryCardTravelRecordListByStatus(Integer status) {
        //获取当前状态下所有差旅记录
        CardTravelRecordQuery cardTravelRecordQuery = new CardTravelRecordQuery();
        Asserts.assertNotNull(status, CardTravelRecordDomainErrorCodeEnum.TRAVEL_QUERY_STATUS_NOT_EMPTY);
        cardTravelRecordQuery.setStatus(status);
        ZonedDateTime startTime = ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now());
        ZonedDateTime endTime = ZonedDateTimeUtils.getZonedDateTimeEnd(ZonedDateTime.now());
        if (CardTravelRecordStatusEnum.WAIT_DEAL.getCode().equals(status)) {
            //开始时间大于当前时间，并且待处理的
            cardTravelRecordQuery.setQueryStartTimeTo(endTime);
            cardTravelRecordQuery.setQueryEndTimeFrom(startTime);
        } else if (CardTravelRecordStatusEnum.OPENED_RIGHT.getCode().equals(status)) {
            //已开通权限，并且结束时间小于当前时间往前减3天的时间，才处理删除权限
            cardTravelRecordQuery.setQueryEndTimeFrom(startTime.minusDays(3L));
            cardTravelRecordQuery.setQueryEndTimeTo(endTime.minusDays(3L));
        }
        List<CardTravelRecordDo> list = cardTravelRecordRepository.queryCardTravelRecordList(cardTravelRecordQuery);
        //转换
        return commonCardDtoConverter.toCardTravelRecordDtoList(list);
    }

    @Override
    public void saveForMigration(CardTravelRecordDto cardTravelRecordDto) {
        //转换领域对象
        CardTravelRecordDo cardTravelRecordDo = commonCardDtoConverter.toCardTravelRecordDo(cardTravelRecordDto);
        //差旅默认是中国区域
        String countryId = AddressSdk.CHINA;
        //根据国家找配置的权限区域,如果有国家字段，但是未配置在开放工卡白名单，则跳过差旅记录
        if (StringUtils.isNotBlank(cardTravelRecordDto.getCountry())) {
            CountryMappingDto countryMappingDto = countryMappingConfig.findByTripCountryName(cardTravelRecordDto.getCountry());
            if (Objects.isNull(countryMappingDto)) {
                log.warn("travel destination country not config, skip. record :{}", cardTravelRecordDto);
                return;
            }
            countryId = countryMappingDto.getCountryId();
        }
        String originCountryId = "";
        //根据国家找配置的权限区域,如果有国家字段，但是未配置在开放工卡白名单，则跳过差旅记录
        if (StringUtils.isNotBlank(cardTravelRecordDto.getOriginCountry())) {
            CountryMappingDto countryMappingDto = countryMappingConfig.findByTripCountryName(cardTravelRecordDto.getOriginCountry());
            originCountryId = Objects.isNull(countryMappingDto) ? AddressSdk.CHINA : countryMappingDto.getCountryId();
        }
        cardTravelRecordDo.setOriginCountryId(originCountryId);
        cardTravelRecordDo.setCountryId(countryId);
        //填装对应的uid和cityId值
        cardTravelRecordDomainService.fillCardTravelRecordWithUidAndCityId(cardTravelRecordDo);
        //保存前检查
        cardTravelRecordDomainService.checkBeforeSave(cardTravelRecordDo);
        //保存
        cardTravelRecordRepository.save(cardTravelRecordDo);
    }

    @Override
    public void save(CardTravelRecordDto cardTravelRecordDto) {
        //转换领域对象
        CardTravelRecordDo cardTravelRecordDo = commonCardDtoConverter.toCardTravelRecordDo(cardTravelRecordDto);
        //差旅默认是中国区域
        String countryId = AddressSdk.CHINA;
        //根据国家找配置的权限区域,如果有国家字段，但是未配置在开放工卡白名单，则跳过差旅记录
        if (StringUtils.isNotBlank(cardTravelRecordDto.getCountry())) {
            CountryMappingDto countryMappingDto = countryMappingConfig.findByTripCountryName(cardTravelRecordDto.getCountry());
            if (Objects.isNull(countryMappingDto)) {
                log.warn("travel destination country not config, skip. record :{}", cardTravelRecordDto);
                return;
            }
            countryId = countryMappingDto.getCountryId();
        }
        String originCountryId = "";
        //根据国家找配置的权限区域,如果有国家字段，但是未配置在开放工卡白名单，则跳过差旅记录
        if (StringUtils.isNotBlank(cardTravelRecordDto.getOriginCountry())) {
            CountryMappingDto countryMappingDto = countryMappingConfig.findByTripCountryName(cardTravelRecordDto.getOriginCountry());
            originCountryId = Objects.isNull(countryMappingDto) ? AddressSdk.CHINA : countryMappingDto.getCountryId();
        }
        cardTravelRecordDo.setOriginCountryId(originCountryId);
        cardTravelRecordDo.setCountryId(countryId);
        ZonedDateTime nowDayStartTime = ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now());
        if (ZonedDateTimeUtils.compare(cardTravelRecordDo.getEndTime(), nowDayStartTime) < 0) {
            cardTravelRecordDo.setStatus(CardTravelRecordStatusEnum.NOT_NEED_DEAL.getCode());
        } else {
            cardTravelRecordDo.setStatus(CardTravelRecordStatusEnum.WAIT_DEAL.getCode());
            if (ZonedDateTimeUtils.compare(cardTravelRecordDo.getStartTime(), nowDayStartTime) < 0) {
                //差旅开始时间小于当前时间的零点零分零秒，说明差旅已经开始，不需要处理
                cardTravelRecordDo.setPermissionGrantTime(nowDayStartTime);
            } else {
                //差旅开始时间大于或等于前时间的零点零分零秒的时候才需要处理
                cardTravelRecordDo.setPermissionGrantTime(cardTravelRecordDo.getStartTime());
            }
        }
        //填装对应的uid和cityId值
        cardTravelRecordDomainService.fillCardTravelRecordWithUidAndCityId(cardTravelRecordDo);
        //保存前检查
        cardTravelRecordDomainService.checkBeforeSave(cardTravelRecordDo);
        //保存
        cardTravelRecordRepository.save(cardTravelRecordDo);
    }

    @Override
    public PermissionAddDto getPermissionAddDtoForOpenRight(CardTravelRecordDto cardTravelRecordDto) {

        //当前工卡差旅记录对象
        CardTravelRecordDo cardTravelRecordDo = commonCardDtoConverter.toCardTravelRecordDo(cardTravelRecordDto);
        //获取当前用户对应的正式卡或临时卡
        CardInfoDo cardInfoDo = this.getCardInfoForTravel(cardTravelRecordDo);

        PermissionQuery permissionQuery = new PermissionQuery();
        permissionQuery.setHasAnyAuth(true);
        if (StringUtils.isNotBlank(cardTravelRecordDo.getCityIds())) {
            permissionQuery.setAuthCityIdList(Lists.newArrayList(cardTravelRecordDo.getCityIds().split(",")));
        }
        permissionQuery.setCountryId(cardTravelRecordDo.getCountryId());
        permissionQuery.setCarrierGroupClass(SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode());
        List<SafetyCarrierGroupDo> safetyCarrierGroupDoList = safetyCarrierGroupRepository.permissionGroupConditionList(permissionQuery);
        //组装权限
        PermissionAddDto permissionAddDto = new PermissionAddDto();
        permissionAddDto.setCardId(cardInfoDo.getId());
        permissionAddDto.setIsTravel(true);
        permissionAddDto.setHasCarrierGroup(CollectionUtils.isNotEmpty(safetyCarrierGroupDoList));
        //过滤已开通权限
        List<SafetyRightDo> rightList = safetyRightRepository.findSafetyRightByMediumCodeAndUid(cardInfoDo.getMediumCode(),
                cardInfoDo.getUid(), null);
        List<String> existGroupCodeList =
                rightList.stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(safetyCarrierGroupDoList) && CollectionUtils.isNotEmpty(rightList)) {
            safetyCarrierGroupDoList.removeIf(item -> existGroupCodeList.contains(item.getCarrierGroupCode()));
        }

        //需要添加的权限结果集合
        List<PermissionAddDto.PermissionDetailDto> permissionDetailDtoList = Lists.newArrayList();
        PersonInfoModel userInfo = idmSdk.findPersonInfo(cardTravelRecordDo.getUid());
        if (Objects.isNull(userInfo) || Objects.isNull(userInfo.getUid())) {
            throw new BizException(CardApplicationErrorCodeEnum.USER_NOT_EXIST);
        }
        fillSpecialPermission(safetyCarrierGroupDoList, cardTravelRecordDo, permissionDetailDtoList, existGroupCodeList,
                userInfo);

        //如果普通权限和特殊权限都为空，该差旅单则不需要处理，变更差旅单状态
        if (CollectionUtils.isEmpty(safetyCarrierGroupDoList) && CollectionUtils.isEmpty(permissionDetailDtoList)) {
            //更新成不需要处理
            cardTravelRecordDo.setStatus(CardTravelRecordStatusEnum.NOT_HAVE_RIGHT.getCode());
            cardTravelRecordRepository.updateStatusById(cardTravelRecordDo);
            //判断当前是否有特殊权限
            permissionQuery.setCarrierGroupClass(null);
            permissionQuery.setCarrierGroupClassList(Lists.newArrayList(SafetyCarrierGroupClassEnum.GROUP_SPECIAL.getCode(),
                    SafetyCarrierGroupClassEnum.GROUP_VIP.getCode()));
            List<SafetyCarrierGroupDo> specialSafetyCarrierGroupDoList = safetyCarrierGroupRepository.permissionGroupConditionList(permissionQuery);
            if (CollectionUtils.isNotEmpty(specialSafetyCarrierGroupDoList)) {
                permissionAddDto.setHaveSpecial(true);
            }
        } else if (CollectionUtils.isNotEmpty(safetyCarrierGroupDoList)) {
            List<String> safetyCarrierGroupCodeList =
                    safetyCarrierGroupDoList.stream().map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
            PermissionAddDto.PermissionDetailDto permissionDetailDto = new PermissionAddDto.PermissionDetailDto();
            permissionDetailDto.setCarrierGroupCodeList(safetyCarrierGroupCodeList);
            permissionDetailDto.setCarrierGroupClass(SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode());
            permissionDetailDtoList.add(permissionDetailDto);
        }

        permissionAddDto.setPermissionDetailList(permissionDetailDtoList);
        return permissionAddDto;
    }

    private void fillSpecialPermission(List<SafetyCarrierGroupDo> safetyCarrierGroupDoList,
                                       CardTravelRecordDo cardTravelRecordDo,
                                       List<PermissionAddDto.PermissionDetailDto> permissionDetailDtoList,
                                       List<String> existGroupCodeList, PersonInfoModel userInfo) {
        //组装特殊权限
        Set<String> cardGroupCodeSet = new HashSet<>();
        Set<String> carrierGroupCodeSet = new HashSet<>();
        ArrayList<String> cityIdList = Lists.newArrayList(cardTravelRecordDo.getCityIds().split(","));

        //根据uid查询用户的部门信息
        List<DeptDto> deptInfoDtoList = userInfo.getDepts();
        List<String> deptIds = deptInfoDtoList.stream().map(DeptDto::getDeptId).collect(Collectors.toList());
        for (String cityId : cityIdList) {
            cardGroupCodeSet.addAll(travelSafetyRightMappingConfig.getTravelSafetyRightMappingConfigs(deptIds, cityId,
                    userInfo.getAccountType().getValue()));
        }

        if (CollectionUtils.isNotEmpty(cardGroupCodeSet)) {
            //根据权限包code查询查询权限集的集合
            List<CardGroupConfigDo> cardGroupConfigDos = cardGroupConfigRepository.findListByCode(cardGroupCodeSet);
            cardGroupConfigDos.forEach(cardGroupConfigDo -> carrierGroupCodeSet.addAll(cardGroupConfigDo.getCarrierGroupCodeList()));

            //排除已有权限
            List<String> lackCodeList = carrierGroupCodeSet.stream()
                    .filter(carrierGroupCode -> !existGroupCodeList.contains(carrierGroupCode))
                    .collect(Collectors.toList());
            //查询缺失的特殊权限集信息
            List<SafetyCarrierGroupDo> carrierGroupList = safetyCarrierGroupDomainService.findCarrierGroupByCodeList(lackCodeList);

            if (CollectionUtils.isEmpty(carrierGroupList)) {
                return;
            }
            cardTravelRecordDo.setSpecialStatus(CardTravelRecordSpecialStatusEnum.HAVE_SPECIAL.getCode());
            cardTravelRecordRepository.updateSpecialStatusById(cardTravelRecordDo);
            //将特殊权限根据特殊,VIP分开处理
            List<String> specialCodeList = carrierGroupList.stream()
                    .filter(carrierGroup -> SafetyCarrierGroupClassEnum.GROUP_SPECIAL.getCode().equals(carrierGroup.getClassCode()))
                    .map(SafetyCarrierGroupDo::getCarrierGroupCode)
                    .collect(Collectors.toList());
            List<String> vipCodeList = carrierGroupList.stream()
                    .filter(carrierGroup -> SafetyCarrierGroupClassEnum.GROUP_VIP.getCode().equals(carrierGroup.getClassCode()))
                    .map(SafetyCarrierGroupDo::getCarrierGroupCode)
                    .collect(Collectors.toList());

            safetyCarrierGroupDoList.addAll(carrierGroupList.stream()
                    .filter(carrierGroup -> SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode().equals(carrierGroup.getClassCode()))
                    .collect(Collectors.toList()));

            //特殊权限需要设置时间
            if (CollectionUtils.isNotEmpty(specialCodeList)) {
                PermissionAddDto.PermissionDetailDto permissionDetailDto = new PermissionAddDto.PermissionDetailDto();
                permissionDetailDto.setCarrierGroupCodeList(specialCodeList);
                permissionDetailDto.setCarrierGroupClass(SafetyCarrierGroupClassEnum.GROUP_SPECIAL.getCode());
                permissionDetailDto.setStartTime(DateUtils.getCurrentZonedDateTime());
                permissionDetailDto.setEndTime(cardTravelRecordDo.getEndTime().plusDays(SafetyConstants.Card.ONE_YEAR_DAYS));
                permissionDetailDtoList.add(permissionDetailDto);
            }

            if (CollectionUtils.isNotEmpty(vipCodeList)) {
                PermissionAddDto.PermissionDetailDto permissionDetailDto = new PermissionAddDto.PermissionDetailDto();
                permissionDetailDto.setCarrierGroupCodeList(vipCodeList);
                permissionDetailDto.setCarrierGroupClass(SafetyCarrierGroupClassEnum.GROUP_VIP.getCode());
                permissionDetailDto.setStartTime(DateUtils.getCurrentZonedDateTime());
                permissionDetailDto.setEndTime(cardTravelRecordDo.getEndTime().plusDays(SafetyConstants.Card.ONE_YEAR_DAYS));
                permissionDetailDtoList.add(permissionDetailDto);
            }
        }
    }

    @Override
    public PermissionDeleteDto getPermissionAddDtoForRemoveRight(CardTravelRecordDto cardTravelRecordDto) {
        //当前工卡差旅记录对象
        CardTravelRecordDo cardTravelRecordDo = commonCardDtoConverter.toCardTravelRecordDo(cardTravelRecordDto);
        //获取当前用户对应的正式卡或临时卡
        CardInfoDo cardInfoDo = this.getCardInfoForTravel(cardTravelRecordDo);

        PermissionDeleteDto permissionDeleteDto = new PermissionDeleteDto();
        permissionDeleteDto.setCardId(cardInfoDo.getId());
        permissionDeleteDto.setIsTravel(true);
        permissionDeleteDto.setCardType(CardTypeEnum.EMPLOYEE_CARD.getCode());

        List<String> safetyCarrierGroupCodeList = Lists.newArrayList();
        SafetyOperateLogDo safetyOperateLogDo = safetyOperateLogRepository.findById(cardTravelRecordDto.getSafetyOperateLogId());
        if (safetyOperateLogDo != null) {
            OperateLogDto operateLogDto = JacksonUtils.json2Bean(safetyOperateLogDo.getRequestParams(), OperateLogDto.class);
            if (CollectionUtils.isNotEmpty(operateLogDto.getAddGroup())) {
                safetyCarrierGroupCodeList = operateLogDto.getAddGroup().stream().map(SafetyCarrierGroupDto::getCarrierGroupCode).collect(Collectors.toList());
                List<SafetyCarrierGroupDo> safetyCarrierGroupDoList = safetyCarrierGroupRepository.getListByCarrierGroupCodes(safetyCarrierGroupCodeList);
                //过滤当前有效的
                if (CollectionUtils.isNotEmpty(safetyCarrierGroupDoList)) {
                    safetyCarrierGroupCodeList = safetyCarrierGroupDoList.stream().map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
                } else {
                    safetyCarrierGroupCodeList = Lists.newArrayList();
                }
            } else {
                PermissionQuery permissionQuery = new PermissionQuery();
                permissionQuery.setHasAnyAuth(true);
                permissionQuery.setAuthCityIdList(Lists.newArrayList(cardTravelRecordDo.getCityIds().split(",")));
                //判断当前是否有特殊权限
                permissionQuery.setCountryId(cardTravelRecordDo.getCountryId());
                permissionQuery.setCarrierGroupClass(null);
                permissionQuery.setCarrierGroupClassList(Lists.newArrayList(SafetyCarrierGroupClassEnum.GROUP_SPECIAL.getCode(),
                        SafetyCarrierGroupClassEnum.GROUP_VIP.getCode()));
                List<SafetyCarrierGroupDo> specialSafetyCarrierGroupDoList = safetyCarrierGroupRepository.permissionGroupConditionList(permissionQuery);
                if (CollectionUtils.isNotEmpty(specialSafetyCarrierGroupDoList)) {
                    permissionDeleteDto.setHaveSpecial(true);
                }
            }
        }

        permissionDeleteDto.setCarrierGroupCodeList(safetyCarrierGroupCodeList);
        return permissionDeleteDto;
    }


    /**
     * 处理发送权限相关消息的方法* 将工卡差旅记录 Dto 转换为 Do，并添加额外字段
     * <p>
     * 根据状态判断是否发送开通权限消息并进行保存通知操作
     */
    @Override
    public void sendUmsMessageForDealRight(CardTravelRecordDto cardTravelRecordDto) {
        //当前工卡差旅记录对象
        CardTravelRecordDo cardTravelRecordDo = commonCardDtoConverter.toCardTravelRecordDo(cardTravelRecordDto);
        cardTravelRecordDo.putExtField("safetyOperateLogIdList", cardTravelRecordDto.getSafetyOperateLogIdList());
        cardTravelRecordDo.putExtField("destinationMap", cardTravelRecordDto.getDestinationMap());

        if (CardTravelRecordStatusEnum.OPENED_RIGHT.getCode().equals(cardTravelRecordDto.getStatus())
                || CardTravelRecordStatusEnum.NOT_HAVE_RIGHT.getCode().equals(cardTravelRecordDto.getStatus())) {
            //需要发送消息的时候，发送开通权限的消息
            saveUmsNotify(cardTravelRecordDo, SafetyUmsConfigEnum.CARD_TRAVEL_OPEN_RIGHT_EMAIL,
                    SafetyUmsConfigEnum.CARD_TRAVEL_OPEN_RIGHT_LARK, true);
        }
    }

    private void saveUmsNotify(CardTravelRecordDo cardTravelRecordDo, SafetyUmsConfigEnum emailConfig,
                               SafetyUmsConfigEnum larkConfig, Boolean isAdd) {
        cardTravelRecordDo.putExtField("isAdd", isAdd);
        //获取当前的消息发送时间
        ZonedDateTime sendTime = ZonedDateTime.now().withZoneSameInstant(ZoneId.systemDefault()).getHour() > 6 ?
                ZonedDateTime.now() : ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now()).plusHours(6L);

        List<SafetyUmsNotifyDo> safetyUmsNotifyDoList = Lists.newArrayList();

        SafetyUmsNotifyDo safetyUmsNotifyEmailDo = new SafetyUmsNotifyDo();
        safetyUmsNotifyEmailDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyUmsNotifyEmailDo.setConfigId(emailConfig.getCode());
        safetyUmsNotifyEmailDo.setSendStatus(SafetyUmsSendStatusEnum.SEND_WAIT.getStatus());
        safetyUmsNotifyEmailDo.setReceiver(cardTravelRecordDo.getUid());
        safetyUmsNotifyEmailDo.setSendTime(sendTime);
        //组装当前差旅单的消息参数
        Map<String, String> params = cardTravelRecordDomainService.loadUmsNotifyParams(cardTravelRecordDo);
        safetyUmsNotifyEmailDo.setParams(JacksonUtils.bean2Json(params));
        safetyUmsNotifyDoList.add(safetyUmsNotifyEmailDo);

        SafetyUmsNotifyDo safetyUmsNotifylarkDo = new SafetyUmsNotifyDo();
        safetyUmsNotifylarkDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyUmsNotifylarkDo.setConfigId(larkConfig.getCode());
        safetyUmsNotifylarkDo.setSendStatus(SafetyUmsSendStatusEnum.SEND_WAIT.getStatus());
        safetyUmsNotifylarkDo.setReceiver(cardTravelRecordDo.getUid());
        safetyUmsNotifylarkDo.setSendTime(sendTime);
        //组装当前差旅单的消息参数
        safetyUmsNotifylarkDo.setParams(JacksonUtils.bean2Json(params));
        safetyUmsNotifyDoList.add(safetyUmsNotifylarkDo);
        //保存最终的信息
        safetyUmsNotifyRepository.batchCreateNotify(safetyUmsNotifyDoList);
    }

    /**
     * 获取当前用户对应的工卡信息
     *
     * @param cardTravelRecordDo
     * @return com.mi.oa.ee.safety.domain.model.CardInfoDo
     * <AUTHOR>
     * @date 2023/8/14 16:33
     */
    private CardInfoDo getCardInfoForTravel(CardTravelRecordDo cardTravelRecordDo) {
        //获取当前用户对应的正式卡
        CardInfoDo cardInfoDo = cardDomainService.findCardByUidAndCardType(cardTravelRecordDo.getUid(), CardTypeEnum.EMPLOYEE_CARD);
        if (cardInfoDo == null || cardInfoDo.getId() == null) {
            //获取当前uid在临时卡中是否存在
            cardInfoDo = cardDomainService.findCardByUidAndCardType(cardTravelRecordDo.getUid(), CardTypeEnum.TEMP_CARD);
            //正式卡和临时卡都不存在的时候
            if (cardInfoDo == null || cardInfoDo.getId() == null) {
                //更新成不需要处理，然后抛出异常
                cardTravelRecordDo.setStatus(CardTravelRecordStatusEnum.NOT_HAVE_CARD.getCode());
                cardTravelRecordRepository.updateStatusById(cardTravelRecordDo);
                throw new BizException(CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
            }
        }
        //检查当前卡是否有效
        if (cardInfoDo != null) {
            try {
                cardDomainService.checkCardIsActive(cardInfoDo);
            } catch (BizException e) {
                log.warn("---- getCardInfoForTravel card is not active uid : {}", cardInfoDo.getUid());
                //更新成不需要处理，然后抛出异常
                cardTravelRecordDo.setStatus(CardTravelRecordStatusEnum.CARD_IS_NOT_ACTIVE.getCode());
                cardTravelRecordRepository.updateStatusById(cardTravelRecordDo);
                throw e;
            }
        }
        return cardInfoDo;
    }
}
