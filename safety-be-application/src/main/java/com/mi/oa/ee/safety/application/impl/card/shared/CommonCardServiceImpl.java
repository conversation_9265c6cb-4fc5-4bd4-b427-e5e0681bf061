package com.mi.oa.ee.safety.application.impl.card.shared;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.converter.card.CardDtoConverter;
import com.mi.oa.ee.safety.application.converter.card.CommonCardDtoConverter;
import com.mi.oa.ee.safety.application.converter.safety.SafetyCarrierGroupDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.AppletCardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.AppletElectronicCardDto;
import com.mi.oa.ee.safety.application.dto.card.AppletEmployeeInfoDto;
import com.mi.oa.ee.safety.application.dto.card.CardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardBatchPermissionGroupImportDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardBatchPermissionImportDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardBatchReceiveDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardBatchReceiveNoticeDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardGroupAdminPageDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardGroupModifyTimeDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardReceiveDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardReceiveNoticeDto;
import com.mi.oa.ee.safety.application.dto.card.shared.DoBusinessImportDto;
import com.mi.oa.ee.safety.application.dto.card.shared.OperatePageConditionDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionAddDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionAddDto.PermissionDetailDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionDeleteDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionGroupAddDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionGroupCarrierGroupQueryDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionGroupDeleteDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionGroupQueryDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionQueryDto;
import com.mi.oa.ee.safety.application.dto.card.shared.SafetyRightCityTreeDto;
import com.mi.oa.ee.safety.application.dto.card.shared.SafetyRightParkTreeDto;
import com.mi.oa.ee.safety.application.dto.common.AppletCardOperateDto;
import com.mi.oa.ee.safety.application.dto.common.CardPayDto;
import com.mi.oa.ee.safety.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.errorcode.CardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.shared.CommonCardService;
import com.mi.oa.ee.safety.application.service.common.event.ImportEventHandler;
import com.mi.oa.ee.safety.application.service.common.event.model.SafetyImportEvent;
import com.mi.oa.ee.safety.application.service.safety.SafetySupplierEngineService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.AuthLogRedoDto;
import com.mi.oa.ee.safety.common.dto.OperateLogDto;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.dto.SafetyRightDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.ImportStatusEnum;
import com.mi.oa.ee.safety.common.enums.ImportTypeEnum;
import com.mi.oa.ee.safety.common.enums.RocketMqTopicEnum;
import com.mi.oa.ee.safety.common.enums.StatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardClassEnum;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyGroupSourceEnum;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyGroupStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardReceiveNoticeMappingEnum;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.LogOperateType;
import com.mi.oa.ee.safety.common.enums.safety.SafetyCarrierGroupClassEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyCarrierGroupStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyConfigUserTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.common.utils.GsonUtils;
import com.mi.oa.ee.safety.common.utils.PageUtils;
import com.mi.oa.ee.safety.domain.ability.CardInfoAbility;
import com.mi.oa.ee.safety.domain.ability.RecyclableCardAbility;
import com.mi.oa.ee.safety.domain.ability.SafetyPersonAbility;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.AsyncImportTaskDo;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.CardGroupConfigDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.CardPayDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.domain.model.CardReceiptAddressDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyOperateLogDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.model.SafetySpaceParkDo;
import com.mi.oa.ee.safety.domain.model.SafetyUmsNotifyDo;
import com.mi.oa.ee.safety.domain.query.card.CardGroupConfigQuery;
import com.mi.oa.ee.safety.domain.query.card.PermissionQuery;
import com.mi.oa.ee.safety.domain.service.AsyncImportTaskDomainService;
import com.mi.oa.ee.safety.domain.service.CardApplyDomainService;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.domain.service.CardElectronRecordDomainService;
import com.mi.oa.ee.safety.domain.service.CardGroupConfigDomainService;
import com.mi.oa.ee.safety.domain.service.CardPayDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyCarrierGroupDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyRightDomainService;
import com.mi.oa.ee.safety.domain.service.SafetySpaceDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyUmsNotifyDomainService;
import com.mi.oa.ee.safety.infra.config.RecommendedParkConfig;
import com.mi.oa.ee.safety.infra.errorcode.SafetyInfraErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.mq.CommonProducer;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.ee.safety.infra.repository.CardElectronRecordRepository;
import com.mi.oa.ee.safety.infra.repository.CardGroupConfigRepository;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.CardPayRepository;
import com.mi.oa.ee.safety.infra.repository.CardPermissionApplyRepository;
import com.mi.oa.ee.safety.infra.repository.CardReceiptAddressRepository;
import com.mi.oa.ee.safety.infra.repository.CardReissueApplyRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierGroupRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyPersonMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupFuzzyQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.dto.UserInfoDto;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/6/12 10:59
 */
@Service
public class CommonCardServiceImpl implements CommonCardService {

    @Resource
    private SafetySpaceDomainService safetySpaceDomainService;

    @Resource
    private RecommendedParkConfig recommendedParkConfig;

    @Resource
    private CardApplyDomainService cardApplyDomainService;

    @Resource
    private CardDomainService cardDomainService;

    @Resource
    private SafetyUmsNotifyDomainService safetyUmsNotifyDomainService;

    @Resource
    private SafetyRightDomainService safetyRightDomainService;

    @Resource
    private SafetyCarrierGroupDomainService safetyCarrierGroupDomainService;

    @Resource
    private SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Resource
    private CommonCardDtoConverter commonCardDtoConverter;

    @Resource
    private SafetyCarrierGroupDtoConverter safetyCarrierGroupDtoConverter;

    @Resource
    private SpaceSdk spaceSdk;

    @Resource
    CardInfoRepository cardInfoRepository;

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Resource
    private SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Resource
    private SafetyOperateLogRepository safetyOperateLogRepository;

    @Resource
    private SafetyRightRepository safetyRightRepository;

    @Resource
    IdmRemote idmRemote;

    @Resource
    AsyncImportTaskDomainService asyncImportTaskDomainService;

    @Resource
    ImportEventHandler importEventHandler;

    @Resource
    private SafetyPersonDomainService safetyPersonDomainService;

    @Resource
    private CardDtoConverter cardDtoConverter;

    @Resource
    private SafetySupplierEngineService safetySupplierEngineService;

    @Resource
    private CardElectronRecordDomainService cardElectronRecordDomainService;

    @Resource
    private CardReceiptAddressRepository cardReceiptAddressRepository;

    @Resource
    private CardElectronRecordRepository cardElectronRecordRepository;

    @Resource
    private CardReissueApplyRepository cardReissueApplyRepository;

    @Resource
    private SafetyMediumRepository safetyMediumRepository;

    @Resource
    private CardGroupConfigRepository cardGroupConfigRepository;

    @Resource
    private CommonProducer commonProducer;

    @Resource
    private CardPermissionApplyRepository cardPermissionApplyRepository;

    @Resource
    private CardGroupConfigDomainService cardGroupConfigDomainService;

    @Resource
    SafetyPersonAbility safetyPersonAbility;

    @Resource
    CardInfoAbility cardAbility;

    @Resource
    private RecyclableCardAbility recyclableCardAbility;

    @Resource
    private CardPayDomainService cardPayDomainService;

    @Resource
    private CardPayRepository cardPayRepository;

    @Override
    public void disableCard(CardInfoDto cardInfoDto) {
        CardInfoDo cardInfoDo = cardDtoConverter.toDo(cardInfoDto);

        //填装personMediumDo
        cardDomainService.fillSafetyPersonMedium(cardInfoDo);

        //填装对应的操作日志
        SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
        operateLogDo.setOperateTypeEnum(LogOperateType.DISABLE_CARD);
        cardInfoDo.setSafetyOperateLog(operateLogDo);
        cardDomainService.fillSafetyCardOperateLog(cardInfoDo);

        //填装所有的工卡权限信息
        cardDomainService.loadSafetyRightWithBaseInfo(cardInfoDo);

        //处理当前人员介质关系
        if (cardInfoDo.getSafetyPersonMediumDo() != null) {
            SafetyPersonMediumDo safetyPersonMediumDo = cardInfoDo.getSafetyPersonMediumDo();
            //更新同步状态为待同步
            safetyPersonMediumRepository.updateSyncStatusByIdList(
                    Lists.newArrayList(safetyPersonMediumDo.getId()), SafetySyncStatusEnum.WAIT_SYNC, "notifyAdmin");
            //删除当前的人与介质关系
            safetyPersonMediumRepository.deleteByIdList(Lists.newArrayList(safetyPersonMediumDo.getId()));
            //删除介质
            safetyMediumRepository.deleteByMediumCode(cardInfoDo.getMediumCode());
        }

        //人员已经删除了，对应的权限不需要同步给霍尼，可以更新成200
        if (CollectionUtils.isNotEmpty(cardInfoDo.getSafetyRightList())) {
            //更新当前权限的同步状态为待同步
            List<Long> idList = cardInfoDo.getSafetyRightList().stream().map(SafetyRightDo::getId).collect(Collectors.toList());
            safetyRightRepository.updateSyncStatusByIdList(idList, SafetySyncStatusEnum.SUCCESS_SYNC, "notifyAdmin");
        }

        //保存所有的操作日志
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());

        //通知idm
        cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_DELETE);
        cardDomainService.notifyCardNumberChange(cardInfoDo);
    }

    public void disableToCloseCard(CardInfoDto cardInfoDto) {
        CardInfoDo cardInfoDo = cardDtoConverter.toDo(cardInfoDto);
        //填装所有的工卡权限信息
        cardDomainService.loadSafetyRightWithBaseInfo(cardInfoDo);
        //当前权限不为空的时候
        if (CollectionUtils.isNotEmpty(cardInfoDo.getSafetyRightList())) {
            //更新当前权限的同步状态为待同步
            List<Long> idList = cardInfoDo.getSafetyRightList().stream().map(SafetyRightDo::getId).collect(Collectors.toList());
            safetyRightRepository.deleteByIdList(idList);
        }
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);
        cardAbility.fillCardInfo(cardInfoDo, true);
        cardAbility.fillRecyclableCard(cardInfoDo);
        recyclableCardAbility.checkRecyclableCard(cardInfoDo.getRecyclableCardDo());

        recyclableCardAbility.releaseRecyclableCard(cardInfoDo.getRecyclableCardDo());

        //填装对应的操作日志
        SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
        operateLogDo.setOperateTypeEnum(LogOperateType.DISABLE_TO_CLOSE_CARD);
        cardInfoDo.setSafetyOperateLog(operateLogDo);
        cardDomainService.fillSafetyCardOperateLog(cardInfoDo);

        //保存所有的操作日志
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
    }

    @Override
    public void enableCard(CardInfoDto cardInfoDto) {
        CardInfoDo cardInfoDo = cardDtoConverter.toDo(cardInfoDto);

        //尝试将最新的被删除的记录还原
        safetyPersonMediumRepository.restoreByUidAndMediumCode(cardInfoDo.getUid(), cardInfoDo.getMediumCode());

        //恢复介质
        safetyMediumRepository.restoreByMediumCode(cardInfoDo.getMediumCode());

        //填装personMediumDo
        cardDomainService.fillSafetyPersonMedium(cardInfoDo);

        //没有的时候，进行新增
        if (cardInfoDo.getSafetyPersonMediumDo() == null) {
            //填装开始时间
            cardInfoDo = cardDomainService.findCardInfoById(cardInfoDo.getId());

            //组装当前安防人员介质对象
            SafetyPersonMediumDo safetyPersonMediumDo = new SafetyPersonMediumDo();
            safetyPersonMediumDo.setUid(cardInfoDto.getUid());
            safetyPersonMediumDo.setMediumCode(cardInfoDto.getMediumCode());
            safetyPersonMediumDo.setStartTime(cardInfoDto.getStartTime());
            safetyPersonMediumDo.setEndTime(cardInfoDto.getEndTime());
            safetyPersonMediumDo.setIsDeleted(OAUCFCommonConstants.LONG_ZERO);
            safetyPersonMediumDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
            safetyPersonMediumDo.setUpdateTime(ZonedDateTime.now());
            //保存或更新当前的安防人员介质，同步状态为待同步
            safetyPersonMediumRepository.saveOrUpdate(Lists.newArrayList(safetyPersonMediumDo));
            cardInfoDo.setSafetyPersonMediumDo(safetyPersonMediumDo);
        }

        //填装personMediumDo
        cardDomainService.fillSafetyPersonMedium(cardInfoDo);
        //填装所有的工卡权限信息
        cardDomainService.loadSafetyRightWithBaseInfo(cardInfoDo);

        //组装操作日志信息
        SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
        operateLogDo.setOperateTypeEnum(LogOperateType.ENABLE_CARD);
        cardInfoDo.setSafetyOperateLog(operateLogDo);
        cardDomainService.fillSafetyCardOperateLog(cardInfoDo);

        if (CollectionUtils.isNotEmpty(cardInfoDo.getSafetyRightList())) {
            //更新当前权限的同步状态为待同步
            List<Long> idList = cardInfoDo.getSafetyRightList().stream().map(SafetyRightDo::getId).collect(Collectors.toList());
            safetyRightRepository.updateSyncStatusByIdList(idList, SafetySyncStatusEnum.WAIT_SYNC, "notifyAdmin");
        }

        //保存操作日志，包含详情日志
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());

        //通知idm
        cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
        cardDomainService.notifyCardNumberChange(cardInfoDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveNotice(CardReceiveNoticeDto receiveNoticeDto) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(receiveNoticeDto.getApplyId());
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);
        Asserts.assertNotNull(cardApplyDo, CardApplicationErrorCodeEnum.CARD_APPLY_NOT_FOUND);
        SafetyUmsConfigEnum umsConfigEnum = CardReceiveNoticeMappingEnum.getUmsConfig(cardApplyDo.getApplyType());
        //0为制卡领取，1为补卡领取
        if (receiveNoticeDto.getReceiptType() == 1) {
            //临时卡
            if (cardApplyDo.getApplyType() == 2) {
                umsConfigEnum = SafetyUmsConfigEnum.TEMP_REPLACE_RECEIVE;
            } else if (cardApplyDo.getApplyType() == 3) {
                umsConfigEnum = SafetyUmsConfigEnum.EMP_REPLACE_RECEIVE;
            }
        }
        Asserts.assertNotNull(umsConfigEnum, CardApplicationErrorCodeEnum.CARD_APPLY_TYPE_ERROR);
        //更新园区信息
        cardApplyDo.putExtField(SafetyUmsConfigEnum.UMS_CONFIG_ENUM_FIELD, umsConfigEnum);
        cardApplyDo.putExtField(SafetyUmsConfigEnum.RECEIPT_ADDRESS, receiveNoticeDto.getReceiptAddress());
        //通知领取
        String receiptParkCode = StringUtils.isNotEmpty(receiveNoticeDto.getParkCode()) ? receiveNoticeDto.getParkCode() : cardApplyDo.getReceiptParkCode();
        SafetySpaceParkDto safetySpaceParkDto = null;
        if (StringUtils.isNotEmpty(receiptParkCode)) {
            safetySpaceParkDto = spaceSdk.getParkByCode(receiptParkCode);
        }
        //若是临时卡，更新对应的领取地
        if (CardTypeEnum.TEMP_CARD.getNumber().equals(cardApplyDo.getApplyType())) {
            cardApplyDo.setReceiptParkCode(receiveNoticeDto.getParkCode());
            cardApplyDo.setReceiptParkName(Objects.isNull(safetySpaceParkDto) ? StringUtils.EMPTY : safetySpaceParkDto.getParkName());
        }
        cardApplyDomainService.receiveNotice(cardApplyDo);
        //为了发送邮件，补充信息
        cardApplyDo.setReceiptParkCode(receiveNoticeDto.getParkCode());
        cardApplyDo.setReceiptParkName(Objects.isNull(safetySpaceParkDto) ? StringUtils.EMPTY : safetySpaceParkDto.getParkName());
        SafetyUmsNotifyDo safetyUmsNotify = safetyUmsNotifyDomainService.buildCardReceiveNotice(cardApplyDo);
        safetyUmsNotifyDomainService.batchUmsInsert(Lists.newArrayList(safetyUmsNotify));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchReceiveNotice(CardBatchReceiveNoticeDto batchReceiveNoticeDto) {
        List<CardApplyDo> cardApplyList = cardApplyDomainService.findByIdList(batchReceiveNoticeDto.getApplyIdList());
        cardApplyList.forEach(cardApplyDo -> {
            String receiptParkCode = StringUtils.isNotEmpty(batchReceiveNoticeDto.getParkCode()) ?
                    batchReceiveNoticeDto.getParkCode() : cardApplyDo.getReceiptParkCode();
            SafetySpaceParkDto safetySpaceParkDto = null;
            if (StringUtils.isNotEmpty(receiptParkCode)) {
                safetySpaceParkDto = spaceSdk.getParkByCode(receiptParkCode);
            }
            SafetyUmsConfigEnum umsConfigEnum = CardReceiveNoticeMappingEnum.getUmsConfig(cardApplyDo.getApplyType());
            //0为制卡领取，1为补卡领取
            if (batchReceiveNoticeDto.getReceiptType() == 1) {
                //临时卡
                if (cardApplyDo.getApplyType() == 2) {
                    umsConfigEnum = SafetyUmsConfigEnum.TEMP_REPLACE_RECEIVE;
                } else if (cardApplyDo.getApplyType() == 3) {
                    umsConfigEnum = SafetyUmsConfigEnum.EMP_REPLACE_RECEIVE;
                }
            }
            Asserts.assertNotNull(umsConfigEnum, CardApplicationErrorCodeEnum.CARD_APPLY_TYPE_ERROR);
            cardApplyDo.putExtField(SafetyUmsConfigEnum.UMS_CONFIG_ENUM_FIELD, umsConfigEnum);
            cardApplyDo.putExtField(SafetyUmsConfigEnum.RECEIPT_ADDRESS, batchReceiveNoticeDto.getReceiptAddress());
            //若是临时卡，更新对应的领取地
            if (CardTypeEnum.TEMP_CARD.getNumber().equals(cardApplyDo.getApplyType())) {
                cardApplyDo.setReceiptParkName(Objects.isNull(safetySpaceParkDto) ? StringUtils.EMPTY : safetySpaceParkDto.getParkName());
                cardApplyDo.setReceiptParkCode(batchReceiveNoticeDto.getParkCode());
            }
            //更新申请单
            cardApplyDomainService.receiveNotice(cardApplyDo);
            //为了发送邮件，补充信息
            cardApplyDo.setReceiptParkName(Objects.isNull(safetySpaceParkDto) ? StringUtils.EMPTY : safetySpaceParkDto.getParkName());
            cardApplyDo.setReceiptParkCode(batchReceiveNoticeDto.getParkCode());
        });
        List<SafetyUmsNotifyDo> safetyUmsNotifyList = safetyUmsNotifyDomainService.buildCardReceiveNoticeList(cardApplyList);
        safetyUmsNotifyDomainService.batchUmsInsert(Lists.newArrayList(safetyUmsNotifyList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receive(CardReceiveDto cardReceiveDto) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(cardReceiveDto.getApplyId());
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);
        Asserts.assertNotNull(cardApplyDo, CardApplicationErrorCodeEnum.CARD_APPLY_NOT_FOUND);
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoByApplyId(cardApplyDo.getId());
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_APPLY_RECEIVE_STATUS_ERROR);

        //领取前校验卡状态，已销卡不让领取
        cardDomainService.checkCardIsDestroy(cardInfoDo);
        cardApplyDomainService.complete(cardApplyDo);

        //领取前校验人员状态
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonDomainService.fillPersonInfoWithBase(safetyPersonDo);
        safetyPersonDomainService.checkPersonIsActive(safetyPersonDo);

        //领取前校验有效期
        cardDomainService.checkCardIsExpire(cardInfoDo);

        //领取
        cardDomainService.receive(cardInfoDo);
        //若是正式卡，需更新person medium和right sync 为待同步  再同步给供应商
        if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardApplyDo.getApplyType())) {
            //校验新工卡临时卡是否归还
            cardDomainService.checkTempCardIsReturn(cardInfoDo);
            //更新person medium 和 right为待同步
            updatePersonMediumAndRightSync(cardInfoDo);
            //执行一次将数据同步给供应商
            List<SafetyRightDo> safetyRightDoList =
                    safetyRightRepository.findSafetyRightByMediumCodeAndUid(cardInfoDo.getMediumCode(),
                            cardInfoDo.getUid(), null);
            safetySupplierEngineService.asyncSafetyDataToSupplierByUidList(
                    Lists.newArrayList(cardInfoDo.getUid()), safetyRightDomainService.getSupplierCodeList(safetyRightDoList));
        } else if (cardReceiveDto.getIsNeedSyncSupplier()) {
            List<SafetyRightDo> safetyRightDoList =
                    safetyRightRepository.findSafetyRightByMediumCodeAndUid(cardInfoDo.getMediumCode(),
                            cardInfoDo.getUid(), null);
            safetySupplierEngineService.asyncSafetyDataToSupplierByUidList(
                    Lists.newArrayList(cardInfoDo.getUid()), safetyRightDomainService.getSupplierCodeList(safetyRightDoList));
        }
        //通知idm
        cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
        cardDomainService.notifyCardNumberChange(cardInfoDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchReceive(CardBatchReceiveDto batchReceiveDto) {
        List<CardApplyDo> cardApplyList = cardApplyDomainService.findByIdList(batchReceiveDto.getApplyIdList());
        if (CollectionUtils.isEmpty(cardApplyList)) {
            //防御，防止通过接口胡乱输入参数
            return;
        }
        //批量查询工卡记录
        List<CardInfoDo> cardInfoList = cardDomainService.findCardInfoByApplyIdList(batchReceiveDto.getApplyIdList());
        //对比数量，不相等说明存在未绑卡的记录
        if (cardApplyList.size() != cardInfoList.size()) {
            throw new BizException(CardApplicationErrorCodeEnum.CARD_APPLY_RECEIVE_STATUS_ERROR);
        }
        //领取逻辑
        Map<Long, CardInfoDo> cardInfoMap = cardInfoList.stream()
                .collect(Collectors.toMap(CardInfoDo::getCardApplyId, Function.identity(), (a, b) -> a));
        List<CardInfoDo> needSyncCardInfoList = Lists.newArrayList();
        cardApplyList.forEach(cardApplyDo -> {
            CardInfoDo cardInfoDo = cardInfoMap.get(cardApplyDo.getId());
            Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_APPLY_RECEIVE_STATUS_ERROR);
            cardApplyDomainService.complete(cardApplyDo);
            //领取前校验有效期
            cardDomainService.checkCardIsExpire(cardInfoDo);
            cardDomainService.receive(cardInfoDo);
            //若是正式卡，需更新person medium和right sync 为待同步  再同步给供应商
            if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardApplyDo.getApplyType())) {
                //校验新工卡临时卡是否归还
                cardDomainService.checkTempCardIsReturn(cardInfoDo);
                //更新person medium 和 right为待同步
                updatePersonMediumAndRightSync(cardInfoDo);
                needSyncCardInfoList.add(cardInfoDo);
            }
        });
        //推送供应商
        batchAsyncSupplier(needSyncCardInfoList);
    }

    private void batchAsyncSupplier(List<CardInfoDo> cardInfoList) {
        if (CollectionUtils.isNotEmpty(cardInfoList)) {
            List<String> mediumCodeList =
                    cardInfoList.stream().map(CardInfoDo::getMediumCode).collect(Collectors.toList());
            Map<String, CardInfoDo> mediumCodeMap =
                    cardInfoList.stream().collect(Collectors.toMap(CardInfoDo::getMediumCode, Function.identity(), (a, b) -> a));
            List<SafetyRightDo> safetyRightDoList =
                    safetyRightRepository.findNotDeletedListByMediumCodeList(mediumCodeList);
            Map<String, List<SafetyRightDo>> supplierCodeMapList = safetyRightDoList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getSupplierCode()))
                    .collect(Collectors.groupingBy(SafetyRightDo::getSupplierCode));
            //按供应商编码依次执行推送
            for (Map.Entry<String, List<SafetyRightDo>> entry : supplierCodeMapList.entrySet()) {
                List<SafetyRightDo> values = entry.getValue();
                //补充权限uid数据
                values.forEach(item -> {
                    if (StringUtils.isEmpty(item.getUid())) {
                        CardInfoDo cardInfoDo = mediumCodeMap.get(item.getMediumCode());
                        item.setUid(cardInfoDo.getUid());
                    }
                });
                List<String> uidList = values.stream().map(SafetyRightDo::getUid).collect(Collectors.toList());
                safetySupplierEngineService.asyncSafetyDataToSupplierByUidList(uidList, Lists.newArrayList(entry.getKey()));
            }
        }
    }

    private void updatePersonMediumAndRightSync(CardInfoDo cardInfoDo) {
        //更新person medium
        SafetyPersonMediumDo safetyPersonMediumDo = new SafetyPersonMediumDo();
        safetyPersonMediumDo.setMediumCode(cardInfoDo.getMediumCode());
        safetyPersonMediumDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
        safetyPersonMediumRepository.updateSyncByMediumCode(safetyPersonMediumDo);

        //更新right
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setMediumCode(cardInfoDo.getMediumCode());
        safetyRightDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
        safetyRightRepository.batchUpdateSyncByMediumCode(safetyRightDo);
    }

    @Override
    public PageModel<SafetyRightDto> grantPermissionList(PermissionQueryDto queryDto) {
        //根据单号查询到介质编号
        CardInfoDo cardInfoDo;
        if (Objects.isNull(queryDto.getCardId())) {
            cardInfoDo = cardDomainService.findCardInfoByApplyId(queryDto.getApplyId());
        } else {
            cardInfoDo = cardDomainService.findCardInfoById(queryDto.getCardId());
        }
        if (Objects.isNull(cardInfoDo) || StringUtils.isBlank(cardInfoDo.getMediumCode())) {
            return PageModel.build(Lists.newArrayList(), queryDto.getPageSize(), queryDto.getPageNum(), 0L);
        }
        PageModel<SafetyRightDo> page = pagePermissionByQuery(queryDto, cardInfoDo.getMediumCode());
        List<SafetyRightDto> safetyRightDtoList = commonCardDtoConverter.toSafetyRightDtoList(page.getList());
        return PageModel.build(safetyRightDtoList, page.getPageSize(), page.getPageNum(), page.getTotal());
    }

    @Override
    public PageModel<SafetyCarrierGroupDto> restPermissionList(PermissionQueryDto queryDto) {
        //根据单号查询到介质编号
        CardInfoDo cardInfoDo;
        if (Objects.isNull(queryDto.getCardId())) {
            cardInfoDo = cardDomainService.findCardInfoByApplyId(queryDto.getApplyId());
        } else {
            cardInfoDo = cardDomainService.getDetailCardInfo(queryDto.getCardId());
        }
        PermissionQuery permissionQuery = commonCardDtoConverter.toPermissionQuery(queryDto);
        permissionQuery.setCarrierGroupType(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode());
        List<String> existCarrierGroupCodeList = Lists.newArrayList();
        if (Objects.nonNull(cardInfoDo)) {
            List<SafetyCarrierGroupDo> carrierGroupDos = cardDomainService.findCarrierGroupListByMediumCode(cardInfoDo.getMediumCode());
            //查询已经存在的权限
            existCarrierGroupCodeList = carrierGroupDos.stream().map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
        }
        if (queryDto.getIsNeedCheckUcAuth()) {
            cardDomainService.fillQueryByUserUcDataResource(permissionQuery);
        }
        PageModel<SafetyCarrierGroupDo> page = cardDomainService.pageGroupConditionListV3(permissionQuery);
        List<SafetyCarrierGroupDto> carrierGroupList = commonCardDtoConverter.toDtoList(page.getList());
        markHasExist(existCarrierGroupCodeList, carrierGroupList);
        return PageModel.build(carrierGroupList, page.getPageSize(), page.getPageNum(), page.getTotal());
    }

    private void markHasExist(List<String> existCarrierGroupCodeList, List<SafetyCarrierGroupDto> carrierGroupList) {
        if (CollectionUtils.isNotEmpty(existCarrierGroupCodeList) && CollectionUtils.isNotEmpty(carrierGroupList)) {
            carrierGroupList.forEach(item -> {
                if (existCarrierGroupCodeList.contains(item.getCarrierGroupCode())) {
                    item.setHasExist(true);
                }
            });
        }
    }

    /**
     * 构造待添加的安防权限集合
     *
     * @param cardInfoDo 工卡信息
     * @param permissionAddDto 权限信息
     * @return 权限集合
     */
    private List<SafetyRightDo> assemblerSafetyRightDo(CardInfoDo cardInfoDo, PermissionAddDto permissionAddDto) {
        List<SafetyRightDo> safetyRightList = Lists.newArrayList();
        List<String> carrierGroupCodeList = Lists.newArrayList();
        //组装权限载体集领域对象
        for (PermissionDetailDto permissionDetailDto : permissionAddDto.getPermissionDetailList()) {
            carrierGroupCodeList.addAll(permissionDetailDto.getCarrierGroupCodeList());
        }
        List<SafetyCarrierGroupDo> carrierGroupList = safetyCarrierGroupDomainService.findCarrierGroupByCodeList(carrierGroupCodeList);
        //载体集领域转换为map
        Map<String, SafetyCarrierGroupDo> carrierGroupMap = carrierGroupList.stream()
                .collect(Collectors.toMap(SafetyCarrierGroupDo::getCarrierGroupCode, Function.identity(), (a, b) -> a));
        //构造权限集合
        for (PermissionDetailDto permissionDetailDto : permissionAddDto.getPermissionDetailList()) {
            for (String carrierGroupCode : permissionDetailDto.getCarrierGroupCodeList()) {
                SafetyCarrierGroupDo carrierGroupDo = carrierGroupMap.get(carrierGroupCode);
                //如果这个权限组处于禁用状态，则不添加该权限组
                if (carrierGroupDo.getStatus().equals(StatusEnum.BLACK.getCode())){
                    continue;
                }
                Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.SAFETY_CARRIER_GROUP_NOT_EXIST);
                SafetyRightDo safetyRightDo = new SafetyRightDo();
                safetyRightDo.setMediumCode(cardInfoDo.getMediumCode());
                safetyRightDo.setStartTime(permissionDetailDto.getStartTime());
                safetyRightDo.setEndTime(permissionDetailDto.getEndTime());
                safetyRightDo.setCarrierGroupCode(carrierGroupCode);
                safetyRightDo.setUid(cardInfoDo.getUid());
                safetyRightDo.setSupplierCheckCode(cardInfoDo.getMediumPhysicsCode());
                safetyRightDo.setSupplierAccessCode(carrierGroupDo.getSupplierAccessCode());
                safetyRightDo.setSupplierCode(carrierGroupDo.getSupplierCode());
                safetyRightDo.setSafetyCarrierGroup(carrierGroupDo);
                if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfoDo.getCardType())
                        && CardStatusEnum.NOT_ACTIVE.getCode().equals(cardInfoDo.getCardStatus())) {
                    safetyRightDo.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
                } else {
                    safetyRightDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                }
                fillEffectTime(safetyRightDo, permissionDetailDto, cardInfoDo);
                safetyRightList.add(safetyRightDo);
            }
        }
        return safetyRightList;
    }

    private List<SafetyRightDo> assemblerSafetyRightDo(CardInfoDo cardInfoDo, DoBusinessImportDto doBusinessImportDto) {
        List<SafetyRightDo> safetyRightList = Lists.newArrayList();

        List<SafetyCarrierGroupDo> carrierGroupList =
                safetyCarrierGroupDomainService.findCarrierGroupByCodeList(doBusinessImportDto.getCarrierGroupCodeList());
        //载体集领域转换为map
        Map<String, SafetyCarrierGroupDo> carrierGroupMap = carrierGroupList.stream()
                .collect(Collectors.toMap(SafetyCarrierGroupDo::getCarrierGroupCode, Function.identity(), (a, b) -> a));
        //构造权限集合
        for (String carrierGroupCode : doBusinessImportDto.getCarrierGroupCodeList()) {
            SafetyCarrierGroupDo carrierGroupDo = carrierGroupMap.get(carrierGroupCode);
            Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.SAFETY_CARRIER_GROUP_NOT_EXIST);
            SafetyRightDo safetyRightDo = new SafetyRightDo();
            safetyRightDo.setMediumCode(cardInfoDo.getMediumCode());
            safetyRightDo.setCarrierGroupCode(carrierGroupCode);
            safetyRightDo.setUid(cardInfoDo.getUid());
            safetyRightDo.setSupplierCheckCode(cardInfoDo.getMediumPhysicsCode());
            safetyRightDo.setSupplierAccessCode(carrierGroupDo.getSupplierAccessCode());
            safetyRightDo.setSupplierCode(carrierGroupDo.getSupplierCode());
            safetyRightDo.setSafetyCarrierGroup(carrierGroupDo);
            safetyRightDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
            PermissionDetailDto permissionDetailDto = new PermissionDetailDto();
            permissionDetailDto.setStartTime(doBusinessImportDto.getStartTime());
            permissionDetailDto.setEndTime(doBusinessImportDto.getEndTime());
            permissionDetailDto.setCarrierGroupClass(carrierGroupDo.getClassCode());
            fillEffectTime(safetyRightDo, permissionDetailDto, cardInfoDo);
            safetyRightList.add(safetyRightDo);
        }
        return safetyRightList;
    }

    private void fillEffectTime(SafetyRightDo safetyRightDo, PermissionDetailDto permissionDetailDto, CardInfoDo cardInfoDo) {
        if (SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode().equals(permissionDetailDto.getCarrierGroupClass())) {
            safetyRightDo.setStartTime(ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now()));
            //默认2050年结束
            safetyRightDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
        } else {
            //有效期不存在
            Asserts.assertNotNull(permissionDetailDto.getStartTime(), CardApplicationErrorCodeEnum.PERMISSION_TIME_NOT_NULL);
            Asserts.assertNotNull(permissionDetailDto.getEndTime(), CardApplicationErrorCodeEnum.PERMISSION_TIME_NOT_NULL);
            if (permissionDetailDto.getStartTime().toLocalDate().isBefore(cardInfoDo.getStartTime().toLocalDate())) {
                permissionDetailDto.setStartTime(cardInfoDo.getStartTime());
            }
            if (permissionDetailDto.getEndTime().toLocalDate().isAfter(cardInfoDo.getEndTime().toLocalDate())) {
                permissionDetailDto.setEndTime(cardInfoDo.getEndTime());
            }
            safetyRightDo.setStartTime(permissionDetailDto.getStartTime());
            safetyRightDo.setEndTime(permissionDetailDto.getEndTime());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPermission(PermissionAddDto permissionAddDto) {
        if (CollectionUtils.isEmpty(permissionAddDto.getPermissionDetailList())) {
            return;
        }
        CardInfoDo cardInfoDo;
        if (Objects.nonNull(permissionAddDto.getApplyId())) {
            cardInfoDo = cardDomainService.findCardInfoByApplyId(permissionAddDto.getApplyId());
        } else {
            cardInfoDo = cardDomainService.findCardInfoById(permissionAddDto.getCardId());
        }
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        List<SafetyRightDo> safetyRightList = assemblerSafetyRightDo(cardInfoDo, permissionAddDto);
        if (permissionAddDto.getIsTravel()) {
            cardInfoDo.putExtField("isTravel", true);
            cardInfoDo.putExtField("cardTravelRecordId", permissionAddDto.getCardTravelRecordId());
        } else if (permissionAddDto.getCheckPermission()) {
            //不是差旅的时候需要校验权限
            cardDomainService.checkUserPermissionWithUcRole(safetyRightList.stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList()));
        }
        //添加时如果有特殊权限 更新卡信息
        List<String> groupClass = permissionAddDto.getPermissionDetailList().stream()
                .map(PermissionDetailDto::getCarrierGroupClass).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(groupClass) && (groupClass.contains(SafetyCarrierGroupClassEnum.GROUP_SPECIAL.getCode())
                || groupClass.contains(SafetyCarrierGroupClassEnum.GROUP_VIP.getCode()))) {
            cardInfoDo.setHasSpecialAuth(OAUCFCommonConstants.INT_ONE);
            cardInfoRepository.updateStatusById(cardInfoDo);
        }
        cardDomainService.addPermissionV2(cardInfoDo, safetyRightList);
        //实体卡为正式卡且存在已开通的电子工卡 电子卡新增权限
        addPermissionForElectronCard(cardInfoDo, safetyRightList);
    }

    private void addPermissionForElectronCard(CardInfoDo cardInfoDo, List<SafetyRightDo> safetyRightList) {
        if (CollectionUtils.isEmpty(safetyRightList)) {
            return;
        }
        //实体卡为正式卡且存在已开通的电子工卡 电子卡新增权限
        CardElectronRecordDo cardElectronRecordDo = new CardElectronRecordDo();
        cardElectronRecordDo.setCardId(cardInfoDo.getId());
        cardElectronRecordDo.setUid(cardInfoDo.getUid());
        List<CardElectronRecordDo> openedElectronList =
                cardElectronRecordDomainService.findOpenedElectronCardByCardIdAndUid(cardElectronRecordDo);
        if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfoDo.getCardType()) && CollectionUtils.isNotEmpty(openedElectronList)) {
            Map<String, List<CardElectronRecordDo>> electronMap =
                    openedElectronList.stream().collect(Collectors.groupingBy(CardElectronRecordDo::getMediumCode));
            //更新right的medium_code
            for (Map.Entry<String, List<CardElectronRecordDo>> entry : electronMap.entrySet()) {
                List<CardElectronRecordDo> electronRecordList = entry.getValue();
                if (electronRecordList != null && !electronRecordList.isEmpty()) {
                    CardElectronRecordDo electronRecordDo = electronRecordList.get(0);
                    addPermission(cardInfoDo, safetyRightList, electronRecordDo);
                }
            }
        }
    }

    private void addPermission(CardInfoDo cardInfoDo, List<SafetyRightDo> safetyRightList, CardElectronRecordDo finalCardElectronRecordDo) {
        //如果是跟实体卡一个介质 不需要更新
        if (finalCardElectronRecordDo.getMediumCode().equals(cardInfoDo.getMediumCode())) {
            return;
        }
        List<SafetyRightDo> electronSafetyRightList = safetyRightList.stream().map(safetyRightDo -> {
            safetyRightDo.setMediumCode(finalCardElectronRecordDo.getMediumCode());
            safetyRightDo.setId(null);
            return safetyRightDo;
        }).collect(Collectors.toList());
        cardElectronRecordDomainService.groupSafetyRightForAddPermission(finalCardElectronRecordDo, electronSafetyRightList);
        //批量插入介质与权限的关系
        safetyRightRepository.batchSaveWithIds(finalCardElectronRecordDo.getAddSafetyRightList());
        safetyRightRepository.batchUpdate(finalCardElectronRecordDo.getUpdateSafetyRightList());
        cardElectronRecordDomainService.generateOperateLogByAddPermission(finalCardElectronRecordDo, electronSafetyRightList);
        //保存日志
        safetyOperateLogRepository.saveOrUpdate(finalCardElectronRecordDo.getSafetyOperateLog());
    }

    @Override
    public void removePermission(PermissionDeleteDto permissionDeleteDto) {
        if (CollectionUtils.isEmpty(permissionDeleteDto.getCarrierGroupCodeList())) {
            return;
        }
        CardInfoDo cardInfoDo;
        if (Objects.nonNull(permissionDeleteDto.getApplyId())) {
            cardInfoDo = cardDomainService.findCardInfoByApplyId(permissionDeleteDto.getApplyId());
        } else {
            cardInfoDo = cardDomainService.findCardInfoById(permissionDeleteDto.getCardId());
        }
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        if (permissionDeleteDto.getIsTravel()) {
            cardInfoDo.putExtField("isTravel", true);
            cardInfoDo.putExtField("cardTravelRecordId", permissionDeleteDto.getCardTravelRecordId());
        } else {
            //不是差旅的时候需要校验权限
            cardDomainService.checkUserPermissionWithUcRole(permissionDeleteDto.getCarrierGroupCodeList());
        }
        cardInfoDo.setDelGroupCodes(permissionDeleteDto.getCarrierGroupCodeList());
        cardDomainService.removePermission(cardInfoDo);
        //移除完权限后 检验是否有特殊权限并更新卡状态
        cardDomainService.checkHasSpecialAuthAndUpdate(cardInfoDo);

        //实体卡为正式卡且存在已开通的电子工卡 电子卡删除权限
        removePermissionForElectronCard(cardInfoDo);
    }

    private void removePermissionForElectronCard(CardInfoDo cardInfoDo) {
        CardElectronRecordDo cardElectronRecordDo = new CardElectronRecordDo();
        cardElectronRecordDo.setCardId(cardInfoDo.getId());
        cardElectronRecordDo.setUid(cardInfoDo.getUid());
        List<CardElectronRecordDo> openedElectronList =
                cardElectronRecordDomainService.findOpenedElectronCardByCardIdAndUid(cardElectronRecordDo);
        if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfoDo.getCardType()) && CollectionUtils.isNotEmpty(openedElectronList)) {
            Map<String, List<CardElectronRecordDo>> electronMap =
                    openedElectronList.stream().collect(Collectors.groupingBy(CardElectronRecordDo::getMediumCode));
            //更新right的medium_code
            for (Map.Entry<String, List<CardElectronRecordDo>> entry : electronMap.entrySet()) {
                List<CardElectronRecordDo> electronRecordList = entry.getValue();
                if (electronRecordList != null && !electronRecordList.isEmpty()) {
                    CardElectronRecordDo electronRecordDo = electronRecordList.get(0);
                    removeRight(cardInfoDo, electronRecordDo);
                }
            }
        }
    }

    private void removeRight(CardInfoDo cardInfoDo, CardElectronRecordDo cardElectronRecordDo) {
        //如果是跟实体卡一个介质 不需要更新
        if (cardElectronRecordDo.getMediumCode().equals(cardInfoDo.getMediumCode())) {
            return;
        }
        cardElectronRecordDo.setCardInfoDo(cardInfoDo);
        cardElectronRecordDomainService.fillBeforeRemovePermission(cardElectronRecordDo);
        //更新要删除权限的同步状态 并移除权限
        safetyRightRepository.updateWaitSyncAndDeleteByIdList(cardElectronRecordDo.getSafetyRightList());
        //保存日志
        safetyOperateLogRepository.saveOrUpdate(cardElectronRecordDo.getSafetyOperateLog());
    }

    @Override
    public PageModel<OperateLogDto> pageListOperateLogs(OperatePageConditionDto conditionDto) {
        return cardDomainService.pageListOperateLogs(conditionDto.getOperateStatus(), conditionDto.getOperateType(),
                (long) conditionDto.getPageNum(), (long) conditionDto.getPageSize(), conditionDto.getCardId());
    }

    @Override
    public List<SafetyRightCityTreeDto> accessTreeCityByMedium(String mediumCode) {
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setMediumCode(mediumCode);
        List<SafetyRightDo> safetyRightList = safetyRightDomainService.queryListByMediumCodeWithCarrierGroupInfo(safetyRightDo);
        return buildTreeList(safetyRightList);
    }

    private List<SafetyRightCityTreeDto> buildTreeList(List<SafetyRightDo> safetyRightList) {
        if (CollectionUtils.isEmpty(safetyRightList)) {
            return Lists.newArrayList();
        }
        List<SafetyRightCityTreeDto> res = Lists.newArrayList();
        //按照城市分组
        Map<String, List<SafetyRightDo>> cityMap = safetyRightList.stream()
                .filter(safetyRightDo -> Objects.nonNull(safetyRightDo.getSafetyCarrierGroup()))
                .collect(Collectors.groupingBy(safetyRightDo -> safetyRightDo.getSafetyCarrierGroup().getCityId()));
        for (Map.Entry<String, List<SafetyRightDo>> cityEntry : cityMap.entrySet()) {
            List<SafetyRightParkTreeDto> carrierGroupParkTreeDtos = Lists.newArrayList();
            List<SafetyRightDo> cityGroupSafetyCarrierGroup = cityEntry.getValue();
            //按照园区分组
            Map<String, List<SafetyRightDo>> cityParkMap = cityGroupSafetyCarrierGroup.stream()
                    .collect(Collectors.groupingBy(safetyRightDo -> safetyRightDo.getSafetyCarrierGroup().getParkCode()));
            for (Map.Entry<String, List<SafetyRightDo>> cityParkEntry : cityParkMap.entrySet()) {
                SafetyRightParkTreeDto safetyRightParkTreeDto = new SafetyRightParkTreeDto();
                safetyRightParkTreeDto.setId(cityParkEntry.getKey());
                safetyRightParkTreeDto.setCityName(cityEntry.getValue().get(0).getSafetyCarrierGroup().getCityName());
                safetyRightParkTreeDto.setParkCode(cityParkEntry.getKey());
                safetyRightParkTreeDto.setParkName(cityParkEntry.getValue().get(0).getSafetyCarrierGroup().getParkName());
                safetyRightParkTreeDto.setChildren(commonCardDtoConverter.toSafetyRightDtoList(cityParkEntry.getValue()));
                carrierGroupParkTreeDtos.add(safetyRightParkTreeDto);
            }
            SafetyRightCityTreeDto safetyRightCityTreeDto = new SafetyRightCityTreeDto();
            safetyRightCityTreeDto.setCityId(cityEntry.getKey());
            safetyRightCityTreeDto.setId(cityEntry.getKey());
            safetyRightCityTreeDto.setCityName(cityEntry.getValue().get(0).getSafetyCarrierGroup().getCityName());
            safetyRightCityTreeDto.setChildren(carrierGroupParkTreeDtos);
            res.add(safetyRightCityTreeDto);
        }
        return res;

    }

    @Override
    public void authorityRedo(AuthLogRedoDto authLogRedoDto) {
        cardDomainService.authorityRedo(authLogRedoDto);
    }

    @Override
    public List<SafetyCarrierGroupDto> fuzzyGroupList(String name) {
        SafetyCarrierGroupFuzzyQuery query = SafetyCarrierGroupFuzzyQuery.builder()
                .carrierGroupName(name).carrierType(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode())
                .build();
        List<SafetyCarrierGroupDo> safetyCarrierGroupDoList = safetyCarrierGroupRepository.fuzzyGroupList(query);
        return safetyCarrierGroupDtoConverter.toDtoList(safetyCarrierGroupDoList);
    }

    @Override
    public void batchImportAddPermission(CardBatchPermissionImportDto importDto) {
        AsyncImportTaskDo asyncImportRecordDo = initAsyncImportRecord(importDto, ImportTypeEnum.CARD_BATCH_PERMISSION_ADD_IMPORT);
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        asyncImportRecordDo.setOperator(userInfoDto.getUid());
        asyncImportRecordDo.setOperatorName(userInfoDto.getUserName());
        asyncImportTaskDomainService.save(asyncImportRecordDo);
        importEventHandler.handleImportEvent(new SafetyImportEvent(asyncImportRecordDo, asyncImportRecordDo.getBatchId()));
    }

    @Override
    public void batchImportDeletePermission(CardBatchPermissionImportDto importDto) {
        AsyncImportTaskDo asyncImportRecordDo = initAsyncImportRecord(importDto, ImportTypeEnum.CARD_BATCH_PERMISSION_DELETE_IMPORT);
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        asyncImportRecordDo.setOperator(userInfoDto.getUid());
        asyncImportRecordDo.setOperatorName(userInfoDto.getUserName());
        asyncImportTaskDomainService.save(asyncImportRecordDo);
        importEventHandler.handleImportEvent(new SafetyImportEvent(asyncImportRecordDo, asyncImportRecordDo.getBatchId()));
    }

    @Override
    public PageModel<SafetyRightDto> pagePermissionList(PermissionQueryDto queryDto) {
        //查询对应的安防载体集
        PermissionQuery permissionQuery = commonCardDtoConverter.toPermissionQuery(queryDto);
        permissionQuery.setCarrierGroupType(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode());
        PageModel<SafetyCarrierGroupDo> page;
        if (Objects.nonNull(queryDto.getIsNeedFilterEmptyCarrier()) && queryDto.getIsNeedFilterEmptyCarrier()) {
            //批量添加权限
            page = safetyCarrierGroupRepository.pageGroupConditionListV3(permissionQuery);
        } else {
            //批量删除权限
            page = safetyCarrierGroupRepository.pageGroupConditionListV2(permissionQuery);
        }
        //填装安防权限中的安防载体集
        if (CollectionUtils.isNotEmpty(page.getList())) {
            safetyCarrierGroupDomainService.batchFillSafetyCarrierGroupDo(page.getList());
            Map<String, SafetyRightDo> safetyRightDoMap = Maps.newHashMap();
            List<SafetyRightDo> safetyRightList = page.getList().stream().map(safetyCarrierGroupDo -> {
                SafetyRightDo safetyRightDo = safetyRightDoMap.getOrDefault(safetyCarrierGroupDo.getCarrierGroupCode(), new SafetyRightDo());
                if (safetyRightDo != null) {
                    safetyRightDo.setSafetyCarrierGroup(safetyCarrierGroupDo);
                }
                return safetyRightDo;
            }).collect(Collectors.toList());
            return PageModel.build(commonCardDtoConverter.toSafetyRightDtoList(safetyRightList), page.getPageSize(), page.getPageNum(), page.getTotal());
        }
        return PageUtils.emptyPage(queryDto.getPageSize(), queryDto.getPageNum());
    }

    private PageModel<SafetyRightDo> pagePermissionByQuery(PermissionQueryDto queryDto, String mediumCode) {
        //查询当前介质对应的所有安防权限
        List<SafetyRightDo> nowSafetyRightList = safetyRightRepository.findSafetyRightByMediumCodeAndUid(mediumCode, null, null);
        //当前介质对应的载体集编码
        List<String> carrierGroupCodeList = nowSafetyRightList.stream()
                .map(SafetyRightDo::getCarrierGroupCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(carrierGroupCodeList)) {
            return PageUtils.emptyPage(queryDto.getPageSize(), queryDto.getPageNum());
        }
        Map<String, SafetyRightDo> safetyRightMap = nowSafetyRightList.stream()
                .collect(Collectors.toMap(SafetyRightDo::getCarrierGroupCode, safetyRightDo -> safetyRightDo, (k1, k2) -> k1));

        //查询对应的安防载体集
        PermissionQuery permissionQuery = commonCardDtoConverter.toPermissionQuery(queryDto);
        permissionQuery.setCarrierGroupType(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode());
        permissionQuery.setMediumCode(mediumCode);
        permissionQuery.setInCarrierGroupCodeList(carrierGroupCodeList);
        PageModel<SafetyCarrierGroupDo> page = safetyCarrierGroupRepository.pageGroupConditionListV2(permissionQuery);
        safetyCarrierGroupDomainService.batchFillSafetyCarrierGroupDo(page.getList());

        //填装安防权限中的安防载体集
        Map<String, SafetyRightDo> finalSafetyRightMap = safetyRightMap;
        List<SafetyRightDo> safetyRightList = page.getList().stream().map(safetyCarrierGroupDo -> {
            SafetyRightDo safetyRightDo = finalSafetyRightMap.getOrDefault(safetyCarrierGroupDo.getCarrierGroupCode(), new SafetyRightDo());
            if (safetyRightDo != null) {
                safetyRightDo.setSafetyCarrierGroup(safetyCarrierGroupDo);
            }
            return safetyRightDo;
        }).collect(Collectors.toList());
        return PageModel.build(safetyRightList, page.getPageNum(), page.getPageSize(), page.getTotal());

    }

    @Override
    public void batchAddPermissionGroup(DoBusinessImportDto businessParam, Map<String, String> accountEmpNoMap) {
        if (CollectionUtils.isNotEmpty(businessParam.getCardGroupCodeList())) {
            //获取权限组code
            List<String> carrierGroupCodeList = findCarrierGroupCodeList(new HashSet<>(businessParam.getCardGroupCodeList()));
            if (CollectionUtils.isNotEmpty(carrierGroupCodeList)) {
                //carrierGroupCodeList去重
                carrierGroupCodeList = carrierGroupCodeList.stream().distinct().collect(Collectors.toList());
                businessParam.setCarrierGroupCodeList(carrierGroupCodeList);
            }
            if (SafetyConstants.Card.CARD_GROUP_PARENT_TYPE_SPECIAL_CODE.equals(businessParam.getParentControlType())) {
                businessParam.setCarrierGroupClass(SafetyCarrierGroupClassEnum.GROUP_SPECIAL.getCode());
            } else {
                businessParam.setCarrierGroupClass(SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode());
            }
            batchAddPermission(businessParam, accountEmpNoMap);
        }
    }

    @Override
    public void batchDeletePermissionGroup(DoBusinessImportDto businessParam, Map<String, String> accountEmpNoMap) {
        if (CollectionUtils.isNotEmpty(businessParam.getCardGroupCodeList())) {
            //获取权限组code
            List<String> carrierGroupCodeList = findCarrierGroupCodeList(new HashSet<>(businessParam.getCardGroupCodeList()));
            businessParam.setCarrierGroupCodeList(carrierGroupCodeList);
            batchDeletePermission(businessParam, accountEmpNoMap);
        }
    }

    @Override
    public String pay(CardPayDto cardPayDto) {
        CardPayDo cardPayDo = commonCardDtoConverter.toCardPayDo(cardPayDto);
        //创建前填充
        cardPayDomainService.fillInfoBeforeCreate(cardPayDo);
        //支付前检查
        cardPayDomainService.checkPayBeforeCreate(cardPayDo);
        //创建支付单
        cardPayDomainService.createPayOrder(cardPayDo);
        //持久化支付单
        cardPayRepository.savePayOrder(cardPayDo);
        return cardPayDo.getPayUrl();
    }

    @Override
    public void checkCardIsConsistent(Long cardId, String mediumPhysicsCode, String mediumEncryptCode) {
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(cardId);
        Asserts.assertNotNull(cardInfoDo, CardInfoDomainErrorCodeEnum.CARD_IS_NOT_EXISTS);
        checkPhysicAndEncryptCardNum(mediumPhysicsCode, mediumEncryptCode, cardInfoDo);
    }

    private void checkPhysicAndEncryptCardNum(String mediumPhysicsCode, String mediumEncryptCode, CardInfoDo cardInfoDo) {
        if (!cardInfoDo.getMediumPhysicsCode().equalsIgnoreCase(mediumPhysicsCode)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_PHYSIC_NUM_NOT_CONSISTENT);
        }
        if (!cardInfoDo.getMediumEncryptCode().equalsIgnoreCase(mediumEncryptCode)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_ENCRYPT_NUM_NOT_CONSISTENT);
        }
    }

    @Override
    public void batchAddPermission(DoBusinessImportDto doBusinessImportDto, Map<String, String> accountEmpNoMap) {
        SafetyPersonDo safetyPersonDo = buildPersonByPhoneOrAccount(doBusinessImportDto);
        if (checkIsRepeat(doBusinessImportDto, accountEmpNoMap, safetyPersonDo)) {
            return;
        }

        CardInfoDo cardInfoDo = findCard(safetyPersonDo);
        Asserts.assertNotNull(cardInfoDo.getId(), CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        //校验卡是否在使用中
        cardDomainService.checkCardIsUsing(cardInfoDo);

        //添加时如果有特殊权限 更新卡信息
        if (SafetyCarrierGroupClassEnum.GROUP_SPECIAL.getCode().equals(doBusinessImportDto.getCarrierGroupClass())
                || SafetyCarrierGroupClassEnum.GROUP_VIP.getCode().equals(doBusinessImportDto.getCarrierGroupClass())) {
            cardInfoDo.setHasSpecialAuth(OAUCFCommonConstants.INT_ONE);
            cardInfoRepository.updateStatusById(cardInfoDo);

            //权限是特殊或者vip 检查有效期是否超过合作卡/临时卡有效期
            cardInfoDo.setStartTime(doBusinessImportDto.getStartTime());
            cardInfoDo.setEndTime(doBusinessImportDto.getEndTime());
            cardDomainService.checkPermissionIsExceedCardValidateTime(cardInfoDo);
            doBusinessImportDto.setEndTime(cardInfoDo.getEndTime());
        }

        if (CollectionUtils.isNotEmpty(doBusinessImportDto.getCardGroupCodeList())) {
            //处理人与权限包关系新增
            PermissionGroupAddDto permissionGroupAddDto = new PermissionGroupAddDto();
            permissionGroupAddDto.setUid(safetyPersonDo.getUid());
            initPermissionGroupDetailList(permissionGroupAddDto, doBusinessImportDto);
            List<CardPermissionApplyGroupDo> applyGroupDoList = initCardPermissionApplyGroupDoList(permissionGroupAddDto);
            cardPermissionApplyRepository.batchSaveRelation(applyGroupDoList);
        }

        //组装权限
        List<SafetyRightDo> safetyRightList = assemblerSafetyRightDo(cardInfoDo, doBusinessImportDto);

        //本地添加权限
        cardInfoDo.putExtField("operateUid", doBusinessImportDto.getOperateUid());
        cardDomainService.addPermissionV2(cardInfoDo, safetyRightList);

        //实体卡为正式卡且存在已开通的电子工卡 电子卡新增权限
        addPermissionForElectronCard(cardInfoDo, safetyRightList);
    }

    private void initPermissionGroupDetailList(PermissionGroupAddDto permissionGroupAddDto, DoBusinessImportDto doBusinessImportDto) {
        List<PermissionGroupAddDto.PermissionGroupDetailDto> permissionDetailList = Lists.newArrayList();
        PermissionGroupAddDto.PermissionGroupDetailDto detailDto =
                new PermissionGroupAddDto.PermissionGroupDetailDto();
        detailDto.setCardGroupCodeList(doBusinessImportDto.getCardGroupCodeList());
        detailDto.setParentControlType(doBusinessImportDto.getParentControlType());
        detailDto.setStartTime(doBusinessImportDto.getStartTime());
        detailDto.setEndTime(doBusinessImportDto.getEndTime());
        permissionDetailList.add(detailDto);
        permissionGroupAddDto.setPermissionDetailList(permissionDetailList);
    }

    private CardInfoDo findCard(SafetyPersonDo safetyPersonDo) {
        CardInfoDo cardInfoDo = cardDomainService.findCardByUidAndCardType(safetyPersonDo.getUid(),
                CardTypeEnum.EMPLOYEE_CARD);
        //正式卡为空 找临时卡
        if (Objects.isNull(cardInfoDo) || Objects.isNull(cardInfoDo.getId())) {
            cardInfoDo = cardDomainService.findCardByUidAndCardType(safetyPersonDo.getUid(), CardTypeEnum.TEMP_CARD);
            //临时卡为空 找合作卡
            if (Objects.isNull(cardInfoDo) || Objects.isNull(cardInfoDo.getId())) {
                cardInfoDo = cardDomainService.findCardByUidAndCardType(safetyPersonDo.getUid(), CardTypeEnum.COOPERATION_CARD);
                //合作卡为空 找物业卡
                if (Objects.isNull(cardInfoDo) || Objects.isNull(cardInfoDo.getId())) {
                    cardInfoDo = cardDomainService.findCardByUidAndCardType(safetyPersonDo.getUid(), CardTypeEnum.PROPERTY_CARD_APPLY);
                }
            }
        }
        return cardInfoDo;
    }

    private Boolean checkIsRepeat(DoBusinessImportDto doBusinessImportDto, Map<String, String> accountEmpNoMap,
                                  SafetyPersonDo safetyPersonDo) {
        // 优先检查账号，如果账号存在，直接进行账号检查并返回结果
        if (StringUtils.isNotEmpty(doBusinessImportDto.getUserName())) {
            if (SafetyConstants.SUCCESS.equals(accountEmpNoMap.get(doBusinessImportDto.getUserName()))) {
                return true;
            }
        }
        // 如果没有账号，但有工号，进行工号检查
        else if (StringUtils.isNotEmpty(doBusinessImportDto.getEmployeeNo())) {
            if (SafetyConstants.SUCCESS.equals(accountEmpNoMap.get(doBusinessImportDto.getEmployeeNo()))) {
                return true;
            }
        }
        // 如果没有账号和工号，但有手机号，进行手机号检查
        else if (StringUtils.isNotEmpty(doBusinessImportDto.getPhone())) {
            if (SafetyConstants.SUCCESS.equals(accountEmpNoMap.get(doBusinessImportDto.getPhone()))) {
                return true;
            }
        }

        // 如果都没有重复，则将信息加入到map中
        accountEmpNoMap.put(safetyPersonDo.getUserName(), SafetyConstants.SUCCESS);
        accountEmpNoMap.put(safetyPersonDo.getEmployeeId(), SafetyConstants.SUCCESS);
        accountEmpNoMap.put(safetyPersonDo.getMobile(), SafetyConstants.SUCCESS);

        // 设置导入数据对象的属性
        doBusinessImportDto.setEmployeeNo(safetyPersonDo.getEmployeeId());
        doBusinessImportDto.setUserName(safetyPersonDo.getUserName());
        doBusinessImportDto.setPhone(safetyPersonDo.getMobile());

        return false;
    }

    @Override
    public void batchDeletePermission(DoBusinessImportDto businessParam, Map<String, String> accountEmpNoMap) {
        SafetyPersonDo safetyPersonDo = buildPersonByPhoneOrAccount(businessParam);
        if (checkIsRepeat(businessParam, accountEmpNoMap, safetyPersonDo)) {
            return;
        }

        CardInfoDo cardInfoDo = findCard(safetyPersonDo);
        Asserts.assertNotNull(cardInfoDo.getId(), CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        //校验卡是否在使用中
        cardDomainService.checkCardIsUsing(cardInfoDo);
        if (CollectionUtils.isNotEmpty(businessParam.getCardGroupCodeList())) {
            //处理人与权限包关系删除
            List<CardPermissionApplyGroupDo> cardPermissionApplyDoList =
                    buildRemoveApplyGroupList(businessParam.getCardGroupCodeList(), safetyPersonDo.getUid());
            cardPermissionApplyRepository.batchDeleteRelation(cardPermissionApplyDoList);
        }

        //检查该卡是否存在所有要删除的权限
        cardInfoDo.setDelGroupCodes(businessParam.getCarrierGroupCodeList());
        cardDomainService.checkIsExistAllRight(cardInfoDo);

        //删除权限
        cardInfoDo.putExtField("operateUid", businessParam.getOperateUid());
        cardDomainService.removePermission(cardInfoDo);

        //移除完权限后 检验是否有特殊权限并更新卡状态
        cardDomainService.checkHasSpecialAuthAndUpdate(cardInfoDo);

        //实体卡为正式卡且存在已开通的电子工卡 电子卡删除权限
        removePermissionForElectronCard(cardInfoDo);
    }

    private SafetyPersonDo buildPersonByPhoneOrAccount(DoBusinessImportDto businessParam) {
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        if (StringUtils.isNotEmpty(businessParam.getUserName())) {
            safetyPersonDo.setUserName(businessParam.getUserName());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
        } else if (StringUtils.isNotEmpty(businessParam.getEmployeeNo())) {
            safetyPersonDo.setEmployeeId(businessParam.getEmployeeNo());
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByEmpNoAndOrgCode(safetyPersonDo);
        } else if (StringUtils.isNotEmpty(businessParam.getPhone())) {
            safetyPersonDo.setMobile(businessParam.getPhone());
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByPhoneAndOrgCode(safetyPersonDo);
        }
        return safetyPersonDo;
    }

    private void syncSupplierRights(List<String> uidList) {
        List<String> errorList = safetySupplierEngineService.syncSupplierRightToSupplierByUidList(uidList);
        if (CollectionUtils.isNotEmpty(errorList)) {
            throw new BizException(SafetyInfraErrorCodeEnum.RIGHT_SYNC_SUPPLIER_FAILED);
        }
    }

    private AsyncImportTaskDo initAsyncImportRecord(Object importDto, ImportTypeEnum importTypeEnum) {
        AsyncImportTaskDo asyncImportTaskDo = new AsyncImportTaskDo();
        String seq = CodeUtils.getUUID();
        asyncImportTaskDo.setBatchId(seq);
        if (importDto instanceof CardBatchPermissionImportDto) {
            CardBatchPermissionImportDto cardBatchPermissionImportDto = (CardBatchPermissionImportDto) importDto;
            asyncImportTaskDo.setOperator(cardBatchPermissionImportDto.getOperator());
            asyncImportTaskDo.setFileName(cardBatchPermissionImportDto.getFileName());
            asyncImportTaskDo.setOperatorName(cardBatchPermissionImportDto.getOperatorName());
            asyncImportTaskDo.setUrl(cardBatchPermissionImportDto.getUrl());
        } else if (importDto instanceof CardBatchPermissionGroupImportDto) {
            CardBatchPermissionGroupImportDto cardBatchPermissionGroupImportDto = (CardBatchPermissionGroupImportDto) importDto;
            asyncImportTaskDo.setOperator(cardBatchPermissionGroupImportDto.getOperator());
            asyncImportTaskDo.setFileName(cardBatchPermissionGroupImportDto.getFileName());
            asyncImportTaskDo.setOperatorName(cardBatchPermissionGroupImportDto.getOperatorName());
            asyncImportTaskDo.setUrl(cardBatchPermissionGroupImportDto.getUrl());
        }
        asyncImportTaskDo.setOpTime(ZonedDateTime.now());
        asyncImportTaskDo.setOpTypeEnum(importTypeEnum);
        asyncImportTaskDo.setExpireTime(ZonedDateTime.now().toEpochSecond() + SafetyConstants.Common.QUART_HOUR);
        asyncImportTaskDo.setStatusEnum(ImportStatusEnum.TO_EXECUTE);
        asyncImportTaskDo.setExtendInfo(JacksonUtils.bean2Json(importDto));
        return asyncImportTaskDo;
    }

    @Override
    public AppletEmployeeInfoDto cardInfo(String uid) {
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(uid);
        safetyPersonDomainService.fillPersonInfoWithBase(safetyPersonDo);
        AppletEmployeeInfoDto appletEmployeeInfoDto = commonCardDtoConverter.toAppletEmployeeInfoDto(safetyPersonDo);
        //构建正式卡信息
        buildEmpCardDto(appletEmployeeInfoDto);
        //构建临时卡信息
        buildTempCardDto(appletEmployeeInfoDto);
        return appletEmployeeInfoDto;
    }

    private void buildTempCardDto(AppletEmployeeInfoDto appletEmployeeInfoDto) {
        CardInfoDo tempCardInfo = cardDomainService.findCardByUidAndCardType(appletEmployeeInfoDto.getUid(), CardTypeEnum.TEMP_CARD);
        //临时卡在使用中或者已过期状态下将临时卡数据返回给小程序
        boolean flag = Objects.nonNull(tempCardInfo) && (CardStatusEnum.USING.getCode().equals(tempCardInfo.getCardStatus())
                || CardStatusEnum.EXPIRED.getCode().equals(tempCardInfo.getCardStatus()) ||
                CardStatusEnum.LOSS.getCode().equals(tempCardInfo.getCardStatus()));
        if (flag) {
            cardDomainService.fillValidateTime(tempCardInfo);
            appletEmployeeInfoDto.setTempCardInfo(commonCardDtoConverter.toAppletCardInfoDto(tempCardInfo));
            buildPermission(appletEmployeeInfoDto.getTempCardInfo());
        }

    }

    private void buildPermission(AppletCardInfoDto cardInfoDto) {
        if (Objects.isNull(cardInfoDto) || StringUtils.isBlank(cardInfoDto.getMediumCode())) {
            return;
        }
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setMediumCode(cardInfoDto.getMediumCode());
        List<SafetyRightDo> safetyRightList = safetyRightDomainService
                .queryListByMediumCodeWithCarrierGroupInfo(safetyRightDo);
        List<String> grantedParkList = safetyRightList.stream()
                .map(SafetyRightDo::getSafetyCarrierGroup)
                .map(SafetyCarrierGroupDo::getParkName).distinct().collect(Collectors.toList());
        cardInfoDto.setGrantedParkList(grantedParkList);
    }

    private void buildEmpCardDto(AppletEmployeeInfoDto appletEmployeeInfoDto) {
        //正式卡需要返回全部数据
        CardApplyDo cardApplyDO = cardApplyRepository.findCardByUidAndCardType(appletEmployeeInfoDto.getUid(), CardTypeEnum.EMPLOYEE_CARD);
        if (Objects.isNull(cardApplyDO) || CardApplyStatusEnum.REFUSED.getCode().equals(cardApplyDO.getApplyStatus())
                || CardApplyStatusEnum.CANCELED.getCode().equals(cardApplyDO.getApplyStatus())) {
            return;
        }
        CardReceiptAddressDo cardReceiptAddressDo = null;
        CardInfoDo empCardInfo = cardDomainService.findCardByUidAndCardType(appletEmployeeInfoDto.getUid(), CardTypeEnum.EMPLOYEE_CARD);
        if (CardApplyStatusEnum.WAIT_PHOTO.getCode().equals(cardApplyDO.getApplyStatus())) {
            cardReceiptAddressDo = cardReceiptAddressRepository.findCardReceiptAddressByParkCode(cardApplyDO.getParkCode());
        }
        appletEmployeeInfoDto.setEmpCardInfo(commonCardDtoConverter.toAppletCardInfoDto(empCardInfo, cardApplyDO, cardReceiptAddressDo));
        appletEmployeeInfoDto.setPhotoUrl(cardApplyDO.getPhotoUrl());
        buildPermission(appletEmployeeInfoDto.getEmpCardInfo());
    }

    @Override
    public List<AppletElectronicCardDto> electronCardInfo(String uid) {
        CardElectronRecordDo cardElectronRecordDo = new CardElectronRecordDo();
        cardElectronRecordDo.setUid(uid);
        List<CardElectronRecordDo> cardElectronRecordList = cardElectronRecordRepository.getOpenedListByUid(cardElectronRecordDo);
        return commonCardDtoConverter.toAppletElectronicCardDtoList(cardElectronRecordList);
    }

    @Override
    public void lossCard(AppletCardOperateDto appletCardOperateDto) {
        //装载工卡信息，查询使用中的工卡
        CardInfoDo cardInfo = cardDomainService.findCardInfoById(appletCardOperateDto.getCardId());
        //验证是否可以操作该工卡信息
        checkPermission(appletCardOperateDto, cardInfo);
        cardDomainService.lossCard(cardInfo);
        //通知idm
        cardInfo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_DELETE);
        cardDomainService.notifyCardNumberChange(cardInfo);
    }

    @Override
    public void removeLossCard(AppletCardOperateDto appletCardOperateDto) {
        //装载工卡信息，查询使用中的工卡
        CardInfoDo cardInfo = cardDomainService.findCardInfoById(appletCardOperateDto.getCardId());
        //验证是否可以操作该工卡信息
        checkPermission(appletCardOperateDto, cardInfo);
        //存在进行中的补卡申请单
        boolean hasActiveApply = cardReissueApplyRepository.hasActiveApplyByApplyId(cardInfo.getCardApplyId());
        if (hasActiveApply) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_STATUS_HAS_REISSUE_APPLY);
        }
        cardDomainService.removeLossCard(cardInfo);
        //通知idm
        cardInfo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
        cardDomainService.notifyCardNumberChange(cardInfo);
    }

    @Override
    public List<SafetySpaceParkDto> findRecommendedParkList() {
        List<String> parkCodeList = recommendedParkConfig.getParkCodeList();
        List<SafetySpaceParkDo> filterList = null;
        if (CollectionUtils.isNotEmpty(parkCodeList)) {
            List<SafetySpaceParkDo> parkDoList = Lists.newArrayList();
            parkCodeList.forEach(parkCode -> {
                SafetySpaceParkDo safetySpaceParkDo = new SafetySpaceParkDo();
                safetySpaceParkDo.setParkCode(parkCode);
                safetySpaceDomainService.fillSpaceInfoByParkCode(safetySpaceParkDo);
                parkDoList.add(safetySpaceParkDo);
            });
            filterList = parkDoList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getParkName())
                            && OAUCFCommonConstants.INT_ONE.equals(item.getEnable()))
                    .collect(Collectors.toList());
        }
        return commonCardDtoConverter.toParkDtoList(filterList);
    }

    @Override
    public void addGroupPermission(PermissionGroupAddDto permissionGroupAddDto) {
        //查询工卡信息
        CardInfoDo cardInfoDo;
        if (Objects.nonNull(permissionGroupAddDto.getCardId())) {
            cardInfoDo = cardDomainService.findCardInfoById(permissionGroupAddDto.getCardId());
        } else {
            cardInfoDo = cardDomainService.findCardInfoByApplyId(permissionGroupAddDto.getApplyId());
        }
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        Set<String> cardGroupConfigCodeList = permissionGroupAddDto.getPermissionDetailList().stream()
                .flatMap(detail -> detail.getCardGroupCodeList().stream())
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(cardGroupConfigCodeList)) {
            return;
        }
        //保存人权限包关系
        List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList =
                initCardPermissionApplyGroupDoList(permissionGroupAddDto);
        cardPermissionApplyRepository.batchSaveRelation(cardPermissionApplyGroupDoList);
        //添加权限
        List<String> carrierGroupCodeList = findCarrierGroupCodeList(cardGroupConfigCodeList);
        List<SafetyCarrierGroupDo> safetyCarrierGroupDoList = safetyCarrierGroupDomainService.findCarrierGroupByCodeList(carrierGroupCodeList);
        if (CollectionUtils.isNotEmpty(safetyCarrierGroupDoList)) {
            List<SafetyCarrierGroupDo> enableGroupList = safetyCarrierGroupDoList.stream()
                    .filter(item -> SafetyCarrierGroupStatusEnum.GROUP_STATUS_ENABLE.getCode().equals(item.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(enableGroupList)) {
                return;
            }
            PermissionAddDto permissionAddDto = new PermissionAddDto();
            permissionAddDto.setCardId(cardInfoDo.getId());
            List<PermissionDetailDto> permissionDetailDtoList = Lists.newArrayList();

            Map<String, List<String>> carrierCodeList = enableGroupList.stream()
                    .collect(Collectors.groupingBy(SafetyCarrierGroupDo::getClassCode,
                            Collectors.mapping(SafetyCarrierGroupDo::getCarrierGroupCode, Collectors.toList())));
            for (Map.Entry<String, List<String>> entry : carrierCodeList.entrySet()) {
                PermissionDetailDto permissionDetailDto = getPermissionDetailDto(entry, permissionGroupAddDto);
                permissionDetailDtoList.add(permissionDetailDto);
            }
            permissionAddDto.setPermissionDetailList(permissionDetailDtoList);
            commonProducer.sendDelay(RocketMqTopicEnum.ADD_PERMISSION.getTopicName(), GsonUtils.toJsonFilterNullField(permissionAddDto), 2);
        }
    }

    private List<CardPermissionApplyGroupDo> initCardPermissionApplyGroupDoList(PermissionGroupAddDto permissionGroupAddDto) {
        List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList = Lists.newArrayList();
        for (PermissionGroupAddDto.PermissionGroupDetailDto permissionGroupDetailDto : permissionGroupAddDto.getPermissionDetailList()) {
            for (String cardGroupCode : permissionGroupDetailDto.getCardGroupCodeList()) {
                CardPermissionApplyGroupDo cardPermissionApplyGroupDo = new CardPermissionApplyGroupDo();
                cardPermissionApplyGroupDo.setUid(permissionGroupAddDto.getUid());
                cardPermissionApplyGroupDo.setCardGroupCode(cardGroupCode);
                cardPermissionApplyGroupDo.setCardPermissionStatus(CardPermissionApplyGroupStatusEnum.AUDIT_COMPLETED.getCode());
                cardPermissionApplyGroupDo.setSource(CardPermissionApplyGroupSourceEnum.PERMISSION_GROUP_FOR_ADMIN.getCode());
                ZonedDateTime startTime = permissionGroupDetailDto.getStartTime() == null
                        ? SafetyConstants.Card.DEFAULT_START_TIME : permissionGroupDetailDto.getStartTime();
                ZonedDateTime endTime = permissionGroupDetailDto.getEndTime() == null
                        ? SafetyConstants.Card.DEFAULT_END_TIME : permissionGroupDetailDto.getEndTime();
                cardPermissionApplyGroupDo.setStartTime(startTime);
                cardPermissionApplyGroupDo.setEndTime(endTime);
                cardPermissionApplyGroupDoList.add(cardPermissionApplyGroupDo);
            }
        }
        return cardPermissionApplyGroupDoList;
    }

    private List<String> findCarrierGroupCodeList(Set<String> cardGroupConfigCodeList) {
        List<CardGroupConfigDo> cardGroupConfigDoList = cardGroupConfigRepository.findListByCode(cardGroupConfigCodeList);
        List<String> carrierGroupCodeList = Lists.newArrayList();
        for (CardGroupConfigDo cardGroupConfigDo : cardGroupConfigDoList) {
            carrierGroupCodeList.addAll(cardGroupConfigDo.getCarrierGroupCodeList());
        }
        return carrierGroupCodeList;
    }

    private PermissionDetailDto getPermissionDetailDto(Map.Entry<String, List<String>> entry, PermissionGroupAddDto permissionGroupAddDto) {
        PermissionDetailDto permissionDetailDto = new PermissionDetailDto();
        permissionDetailDto.setCarrierGroupClass(entry.getKey());
        permissionDetailDto.setCarrierGroupCodeList(entry.getValue());
        if (CardClassEnum.GROUP_NORMAL.getCode().equals(entry.getKey())) {
            permissionDetailDto.setStartTime(SafetyConstants.Card.DEFAULT_START_TIME);
            permissionDetailDto.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
        } else {
            Map<String, List<PermissionGroupAddDto.PermissionGroupDetailDto>> typeMap =
                    permissionGroupAddDto.getPermissionDetailList()
                            .stream()
                            .collect(Collectors.groupingBy(PermissionGroupAddDto.PermissionGroupDetailDto::getParentControlType,
                                    Collectors.toList()));
            List<PermissionGroupAddDto.PermissionGroupDetailDto> permissionGroupDetailDtoList =
                    typeMap.get(SafetyConstants.Card.CARD_GROUP_PARENT_TYPE_SPECIAL_CODE);
            Asserts.assertNotEmpty(permissionGroupDetailDtoList, ApplicationErrorCodeEnum.SPECIAL_CARD_GROUP_CAN_NOT_EMPTY);
            PermissionGroupAddDto.PermissionGroupDetailDto permissionGroupDetailDto = permissionGroupDetailDtoList.get(0);
            Asserts.assertNotNull(permissionGroupDetailDto.getStartTime(), ApplicationErrorCodeEnum.CARD_GROUP_START_TIME_CAN_NOT_EMPTY);
            permissionDetailDto.setStartTime(permissionGroupDetailDto.getStartTime());
            Asserts.assertNotNull(permissionGroupDetailDto.getEndTime(), ApplicationErrorCodeEnum.CARD_GROUP_END_TIME_CAN_NOT_EMPTY);
            permissionDetailDto.setEndTime(permissionGroupDetailDto.getEndTime());
        }
        return permissionDetailDto;
    }

    @Override
    public void removeGroupPermission(PermissionGroupDeleteDto permissionGroupDeleteDto) {
        if (CollectionUtils.isEmpty(permissionGroupDeleteDto.getCardGroupCodeList())) {
            return;
        }
        List<String> carrierGroupDoList = findCarrierGroupCodeList(new HashSet<>(permissionGroupDeleteDto.getCardGroupCodeList()));
        PermissionDeleteDto permissionDeleteDto = new PermissionDeleteDto();
        permissionDeleteDto.setApplyId(permissionGroupDeleteDto.getApplyId());
        permissionDeleteDto.setCardId(permissionGroupDeleteDto.getCardId());
        permissionDeleteDto.setCarrierGroupCodeList(carrierGroupDoList);
        //删除权限包
        List<CardPermissionApplyGroupDo> cardPermissionApplyDoList =
                buildRemoveApplyGroupList(permissionGroupDeleteDto.getCardGroupCodeList(), permissionGroupDeleteDto.getUid());
        cardPermissionApplyRepository.batchDeleteRelation(cardPermissionApplyDoList);
        //删除权限
        removePermission(permissionDeleteDto);
    }

    private List<CardPermissionApplyGroupDo> buildRemoveApplyGroupList(List<String> cardGroupCodeList, String uid) {
        List<CardPermissionApplyGroupDo> cardPermissionApplyDoList = Lists.newArrayList();
        for (String cardGroupCode : cardGroupCodeList) {
            CardPermissionApplyGroupDo cardPermissionApplyGroupDo = new CardPermissionApplyGroupDo();
            cardPermissionApplyGroupDo.setCardGroupCode(cardGroupCode);
            cardPermissionApplyGroupDo.setUid(uid);
            cardPermissionApplyDoList.add(cardPermissionApplyGroupDo);
        }
        return cardPermissionApplyDoList;
    }

    @Override
    public PageModel<CardGroupAdminPageDto> pageCardGroupList(PermissionGroupQueryDto queryDto) {
        //获取当前人员已有权限包
        List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList =
                cardPermissionApplyRepository.findPermissionApplyGroupListByUidAndSourceAndCode(queryDto.getUid(),
                        null, null);
        CardGroupConfigQuery query = commonCardDtoConverter.toCardGroupAdminPageQuery(queryDto);
        if (CollectionUtils.isNotEmpty(cardPermissionApplyGroupDoList)) {
            query.setCardGroupCodeList(cardPermissionApplyGroupDoList.stream()
                    .map(CardPermissionApplyGroupDo::getCardGroupCode).collect(Collectors.toList()));
        }
        //条件分页获取权限包列表
        PageModel<CardGroupConfigDo> page = cardGroupConfigRepository.page(query);
        cardGroupConfigDomainService.fillCardGroupConfigList(page.getList());
        //已申请权限包填充有效期
        List<CardGroupAdminPageDto> resList = commonCardDtoConverter.toCardGroupAdminPageDtoList(page.getList());
        if (Objects.nonNull(queryDto.getIsQueryRest()) && !queryDto.getIsQueryRest()) {
            fillCardGroupApplyTime(resList, cardPermissionApplyGroupDoList);
        }
        if (CollectionUtils.isNotEmpty(cardPermissionApplyGroupDoList) && CollectionUtils.isNotEmpty(resList)) {
            List<String> existCardGroupCodeList = cardPermissionApplyGroupDoList.stream()
                    .map(CardPermissionApplyGroupDo::getCardGroupCode).collect(Collectors.toList());
            resList.forEach(item -> {
                if (existCardGroupCodeList.contains(item.getCardGroupCode())) {
                    item.setHasExist(Boolean.TRUE);
                }
            });
        }
        return PageModel.build(resList, page.getPageSize(), page.getPageNum(), page.getTotal());
    }

    private void fillCardGroupApplyTime(List<CardGroupAdminPageDto> resList,
                                        List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList) {
        if (CollectionUtils.isNotEmpty(cardPermissionApplyGroupDoList) && CollectionUtils.isNotEmpty(resList)) {
            Map<String, CardPermissionApplyGroupDo> cardGroupCodeMap = cardPermissionApplyGroupDoList.stream()
                    .collect(Collectors.toMap(CardPermissionApplyGroupDo::getCardGroupCode,
                            Function.identity(), (v1, v2) -> v1));
            resList.forEach(item -> {
                CardPermissionApplyGroupDo cardPermissionApplyGroupDo = cardGroupCodeMap.get(item.getCardGroupCode());
                if (Objects.nonNull(cardPermissionApplyGroupDo)) {
                    item.setStartTime(cardPermissionApplyGroupDo.getStartTime());
                    item.setEndTime(cardPermissionApplyGroupDo.getEndTime());
                }
            });
        }
    }

    @Override
    public PageModel<SafetyCarrierGroupDto> pageCardGroupCarrierGroup(PermissionGroupCarrierGroupQueryDto queryDto) {
        PermissionQuery permissionQuery = new PermissionQuery();
        permissionQuery.setInCarrierGroupCodeList(queryDto.getCarrierGroupCodeList());
        PageModel<SafetyCarrierGroupDo> page = cardDomainService.pageGroupConditionListV3(permissionQuery);
        List<SafetyCarrierGroupDto> carrierGroupList = commonCardDtoConverter.toDtoList(page.getList());
        return PageModel.build(carrierGroupList, page.getPageSize(), page.getPageNum(), page.getTotal());
    }

    @Override
    public void batchModifyGroupTime(CardGroupModifyTimeDto queryDto) {
        CardInfoDo cardInfoDo;
        if (Objects.nonNull(queryDto.getCardId())) {
            cardInfoDo = cardDomainService.findCardInfoById(queryDto.getCardId());
        } else {
            cardInfoDo = cardDomainService.findCardInfoByApplyId(queryDto.getApplyId());
        }
        Asserts.assertNotNull(cardInfoDo, CardInfoDomainErrorCodeEnum.CARD_NOT_EXIST);
        if (ZonedDateTimeUtils.compare(queryDto.getEndTime(), cardInfoDo.getEndTime()) > OAUCFCommonConstants.INT_ZERO) {
            queryDto.setEndTime(cardInfoDo.getEndTime());
        }
        List<String> needDealCarrierGroupCodeList = findNeedDealCarrierGroupCodeList(queryDto);
        //处理权限有效期
        if (CollectionUtils.isNotEmpty(needDealCarrierGroupCodeList)) {
            //获取当前要处理的权限组有效期  并更新
            updateSafetyRightTime(cardInfoDo, needDealCarrierGroupCodeList, queryDto);
        }
    }

    @Override
    public void batchImportAddPermissionGroup(CardBatchPermissionGroupImportDto importDto) {
        AsyncImportTaskDo asyncImportRecordDo = initAsyncImportRecord(importDto, ImportTypeEnum.CARD_BATCH_PERMISSION_GROUP_ADD_IMPORT);
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        asyncImportRecordDo.setOperator(userInfoDto.getUid());
        asyncImportRecordDo.setOperatorName(userInfoDto.getUserName());
        asyncImportTaskDomainService.save(asyncImportRecordDo);
        importEventHandler.handleImportEvent(new SafetyImportEvent(asyncImportRecordDo, asyncImportRecordDo.getBatchId()));
    }

    @Override
    public void batchImportDeletePermissionGroup(CardBatchPermissionGroupImportDto importDto) {
        AsyncImportTaskDo asyncImportRecordDo = initAsyncImportRecord(importDto, ImportTypeEnum.CARD_BATCH_PERMISSION_GROUP_DELETE_IMPORT);
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        asyncImportRecordDo.setOperator(userInfoDto.getUid());
        asyncImportRecordDo.setOperatorName(userInfoDto.getUserName());
        asyncImportTaskDomainService.save(asyncImportRecordDo);
        importEventHandler.handleImportEvent(new SafetyImportEvent(asyncImportRecordDo, asyncImportRecordDo.getBatchId()));
    }

    private void updateSafetyRightTime(CardInfoDo cardInfoDo, List<String> needDealCarrierGroupCodeList,
                                       CardGroupModifyTimeDto queryDto) {
        List<SafetyRightDo> safetyRightDos = safetyRightRepository.findNotDeletedListByUidAndGroupCodes(cardInfoDo.getUid(),
                needDealCarrierGroupCodeList);
        safetyRightDomainService.fillCarrierGroupList(safetyRightDos);
        //更新有效期
        List<SafetyRightDo> needUpdateRightList = safetyRightDos.stream().filter(item ->
                        CardClassEnum.GROUP_SPECIAL.getCode().equals(item.getSafetyCarrierGroup().getClassCode())
                                || CardClassEnum.GROUP_VIP.getCode().equals(item.getSafetyCarrierGroup().getClassCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needUpdateRightList)) {
            needUpdateRightList.forEach(item -> {
                if (checkIsNeedResync(queryDto, item)) {
                    item.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                }
                item.setStartTime(queryDto.getStartTime());
                item.setEndTime(queryDto.getEndTime());
            });
            safetyRightRepository.batchUpdate(safetyRightDos);
        }
    }

    private boolean checkIsNeedResync(CardGroupModifyTimeDto queryDto, SafetyRightDo item) {
        return Objects.isNull(item.getEndTime()) || item.getEndTime().isBefore(queryDto.getStartTime());
    }

    private List<String> findNeedDealCarrierGroupCodeList(CardGroupModifyTimeDto queryDto) {
        List<String> needDealCarrierGroupCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryDto.getCardGroupCodeList())) {
            List<CardPermissionApplyGroupDo> permissionApplyGroupList = cardPermissionApplyRepository
                    .findPermissionApplyGroupListByUidAndSourceAndCode(queryDto.getUid(),
                            null, queryDto.getCardGroupCodeList());
            if (CollectionUtils.isNotEmpty(permissionApplyGroupList)) {
                List<String> cardGroupCodeList = permissionApplyGroupList.stream()
                        .map(CardPermissionApplyGroupDo::getCardGroupCode).collect(Collectors.toList());
                needDealCarrierGroupCodeList = findCarrierGroupCodeList(new HashSet<>(cardGroupCodeList));
                //更新权限包有效期
                permissionApplyGroupList.forEach(item -> {
                    item.setStartTime(queryDto.getStartTime());
                    item.setEndTime(queryDto.getEndTime());
                });
                cardPermissionApplyRepository.updateRelation(permissionApplyGroupList);
            }
        } else {
            needDealCarrierGroupCodeList = queryDto.getCarrierGroupCodeList();
        }
        return needDealCarrierGroupCodeList;
    }

    private void checkPermission(AppletCardOperateDto appletCardOperateDto, CardInfoDo cardInfo) {
        if (Objects.isNull(cardInfo)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_NOT_EXISTS_APPLET);
        }
        if (!StringUtils.equals(cardInfo.getUid(), appletCardOperateDto.getUid()) &&
                !StringUtils.equals(cardInfo.getResponsible(), appletCardOperateDto.getUid())) {
            throw new BizException(ApplicationErrorCodeEnum.APPLICATION_NORMAL, "您没有权限操作该工卡");
        }
    }
}
