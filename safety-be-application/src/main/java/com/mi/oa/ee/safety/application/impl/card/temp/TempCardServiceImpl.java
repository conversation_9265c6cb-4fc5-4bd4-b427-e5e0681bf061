package com.mi.oa.ee.safety.application.impl.card.temp;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.converter.card.CardApplyDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.event.CardPinEvent;
import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyImportDto;
import com.mi.oa.ee.safety.application.dto.card.temp.CardReopenDto;
import com.mi.oa.ee.safety.application.dto.card.temp.CardReturnDto;
import com.mi.oa.ee.safety.application.dto.card.temp.TempCardApplyDto;
import com.mi.oa.ee.safety.application.dto.card.temp.TempCardApplyEditDto;
import com.mi.oa.ee.safety.application.dto.card.temp.TempCardApplyQueryDto;
import com.mi.oa.ee.safety.application.dto.card.temp.TempCardDto;
import com.mi.oa.ee.safety.application.dto.card.temp.TempCardEditDto;
import com.mi.oa.ee.safety.application.dto.card.temp.TempCardManagerQueryDto;
import com.mi.oa.ee.safety.application.errorcode.CardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.temp.TempCardService;
import com.mi.oa.ee.safety.application.service.common.event.ImportEventHandler;
import com.mi.oa.ee.safety.application.service.common.event.model.SafetyImportEvent;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.constants.SafetyConstants.Common;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.enums.ImportStatusEnum;
import com.mi.oa.ee.safety.common.enums.ImportTypeEnum;
import com.mi.oa.ee.safety.common.enums.RocketMqTopicEnum;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardClassEnum;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.common.utils.GsonUtils;
import com.mi.oa.ee.safety.common.utils.PageUtils;
import com.mi.oa.ee.safety.domain.model.AsyncImportTaskDo;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardGroupConfigDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.CardLeaveRecordDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.domain.model.CardTimeValidityDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.query.card.CardApplyQuery;
import com.mi.oa.ee.safety.domain.query.card.TempCardInfoQuery;
import com.mi.oa.ee.safety.domain.service.AsyncImportTaskDomainService;
import com.mi.oa.ee.safety.domain.service.CardApplyDomainService;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.infra.remote.mq.CommonProducer;
import com.mi.oa.ee.safety.infra.remote.sdk.HrodSdk;
import com.mi.oa.ee.safety.infra.repository.CardGroupConfigRepository;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.CardLeaveRecordRepository;
import com.mi.oa.ee.safety.infra.repository.CardPermissionApplyRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyPersonMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.ee.safety.infra.repository.query.CardLeaveRecordQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.dto.UserInfoDto;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/6/7 17:44
 */
@Slf4j
@Service
public class TempCardServiceImpl implements TempCardService {

    @Resource
    private CardApplyDomainService cardApplyDomainService;

    @Resource
    private CardDomainService cardDomainService;

    @Resource
    private AsyncImportTaskDomainService asyncImportTaskDomainService;

    @Resource
    private ImportEventHandler importEventHandler;

    @Resource
    private IdmRemote idmRemote;

    @Resource
    private CardApplyDtoConverter cardApplyDtoConverter;

    @Resource
    private SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Resource
    private SafetyRightRepository safetyRightRepository;

    @Resource
    private CardInfoRepository cardInfoRepository;

    @Resource
    SafetyPersonDomainService safetyPersonDomainService;

    @Resource
    private HrodSdk hrodSdk;

    @Resource
    private CommonProducer commonProducer;

    @Resource
    private CardLeaveRecordRepository cardLeaveRecordRepository;

    @Resource
    private CardPermissionApplyRepository cardPermissionApplyRepository;

    @Resource
    private CardGroupConfigRepository cardGroupConfigRepository;

    @NacosValue(value = "${card.prefix-encrypt-code:201046}", autoRefreshed = true)
    private String prefixEncryptCode;

    @Override
    public PageModel<TempCardApplyDto> pageConditionApply(TempCardApplyQueryDto tempCardApplyQueryDto, Boolean flag) {
        CardApplyQuery query = cardApplyDtoConverter.tempCardApplyDtoToCardApplyDo(tempCardApplyQueryDto);
        query.setCardType(CardTypeEnum.TEMP_CARD.getNumber());
        PageModel<CardApplyDo> cardApplyPage = cardApplyDomainService.pageConditionApplyList(query, flag);
        cardApplyDomainService.fillFullCardApplyV2(cardApplyPage.getList());
        List<TempCardApplyDto> tempCardApplyList = cardApplyDtoConverter.cardApplyDoToTempCardApplyDtoList(cardApplyPage.getList());
        return PageModel.build(tempCardApplyList, cardApplyPage.getPageSize(), cardApplyPage.getPageNum(), cardApplyPage.getTotal());
    }

    @Override
    public PageModel<TempCardDto> pageCondition(TempCardManagerQueryDto queryDto) {
        TempCardInfoQuery query = cardApplyDtoConverter.toTempCardInfoQuery(queryDto);
        query.setCardType(CardTypeEnum.TEMP_CARD.getNumber());
        //过滤门禁对应的人员
        if (StringUtils.isNotBlank(queryDto.getCarrierGroupCode())) {
            SafetyRightDo safetyRightDo = new SafetyRightDo();
            safetyRightDo.setCarrierGroupCode(queryDto.getCarrierGroupCode());
            List<SafetyRightDo> safetyRightDoList = safetyRightRepository.findListByCarrierGroupCode(safetyRightDo);
            if (CollectionUtils.isEmpty(safetyRightDoList)) {
                return PageUtils.emptyPage(query.getPageSize(), query.getPageNum());
            }
            query.setUidList(safetyRightDoList.stream().map(SafetyRightDo::getUid).collect(Collectors.toList()));
        }
        if (Objects.nonNull(queryDto.getEndTimeFrom()) && Objects.nonNull(queryDto.getEndTimeTo())) {
            List<Long> cardIdList = cardDomainService.findCardIdByEffectTime(queryDto.getEndTimeFrom(), queryDto.getEndTimeTo());
            query.setCardIdList(cardIdList);
        }
        if (Objects.nonNull(queryDto.getPreLeaveDateFrom()) && Objects.nonNull(queryDto.getPreLeaveDateTo())) {
            CardLeaveRecordQuery leaveRecordQuery = new CardLeaveRecordQuery();
            leaveRecordQuery.setPreLeaveDateFrom(queryDto.getPreLeaveDateFrom());
            leaveRecordQuery.setPreLeaveDateTo(queryDto.getPreLeaveDateTo());
            List<CardLeaveRecordDo> cardLeaveRecordDos =
                    cardLeaveRecordRepository.queryLeaveRecordList(leaveRecordQuery);
            if (CollectionUtils.isNotEmpty(cardLeaveRecordDos)) {
                query.setPreLeaveUidList(cardLeaveRecordDos.stream().map(CardLeaveRecordDo::getUid).collect(Collectors.toList()));
            } else {
                query.setPreLeaveUidList(Lists.newArrayList(SafetyConstants.Card.EMPTY_LIST_STR));
            }
        }
        PageModel<CardInfoDo> cardInfoPage = cardDomainService.pageConditionList(query);
        List<TempCardDto> tempCardDtoList = fillFullCardInfo(cardInfoPage.getList());
        return PageModel.build(tempCardDtoList, queryDto.getPageSize(), queryDto.getPageNum(), cardInfoPage.getTotal());
    }

    /**
     * 填充信息
     *
     * @param list
     */
    private List<TempCardDto> fillFullCardInfo(List<CardInfoDo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        //查询工卡申请信息
        List<Long> applyIdList = list.stream().map(CardInfoDo::getCardApplyId).collect(Collectors.toList());
        List<CardApplyDo> cardApplyDoList = cardApplyDomainService.findByIdList(applyIdList);
        //转换为map
        Map<Long, CardApplyDo> cardApplyMap = cardApplyDoList.stream()
                .collect(Collectors.toMap(CardApplyDo::getId, cardApplyDo -> cardApplyDo));
        //填充申请单
        cardApplyDomainService.fillFullCardApplyV2(cardApplyDoList);
        //查询离职信息
        List<String> uidList = list.stream().map(CardInfoDo::getUid).collect(Collectors.toList());
        List<CardLeaveRecordDo> cardLeaveRecordDoList = cardDomainService.findLeaveRecordByUidList(uidList);
        Map<String, CardLeaveRecordDo> leaveRecordDoMap = cardLeaveRecordDoList.stream().collect(Collectors
                .toMap(CardLeaveRecordDo::getUserName, Function.identity(), (key1, key2) -> key1));
        //调人事查询离职日期 并转换为map 若没有离职记录可能为空
        Map<String, PersonInfoModel> accountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(cardLeaveRecordDoList)) {
            List<String> accounts = cardLeaveRecordDoList.stream().map(CardLeaveRecordDo::getUserName).collect(Collectors.toList());
            List<PersonInfoModel> personList = hrodSdk.getLeaveDate(accounts);
            //转map
            accountMap = personList.stream().collect(Collectors
                    .toMap(PersonInfoModel::getAccountName, Function.identity(), (key1, key2) -> key1));
        }
        //查询生效时间
        List<Long> cardIdList = list.stream().map(CardInfoDo::getId).collect(Collectors.toList());
        List<CardTimeValidityDo> validateTimeList = cardDomainService.findValidateTimeByCardIdList(cardIdList);
        //转换为map
        Map<Long, CardTimeValidityDo> validateTimeMap = validateTimeList.stream()
                .collect(Collectors.toMap(CardTimeValidityDo::getCardId, Function.identity(), (a, b) -> a));
        //更新到tempCardDto
        Map<String, PersonInfoModel> finalAccountMap = accountMap;
        return list.stream().map(cardInfoDo -> {
            TempCardDto tempCardDto = cardApplyDtoConverter.toTempCardDto(cardInfoDo);
            tempCardDto.setCardStatusName(CardStatusEnum.getDesc(cardInfoDo.getCardStatus()));
            CardApplyDo cardApplyDo = cardApplyMap.get(cardInfoDo.getCardApplyId());
            if (Objects.nonNull(cardApplyDo)) {
                //填充申请单信息
                tempCardDto.setUserName(cardApplyDo.getPersonInfo().getUserName());
                tempCardDto.setUid(cardApplyDo.getPersonInfo().getUid());
                tempCardDto.setDisplayName(cardApplyDo.getPersonInfo().getDisplayName());
                tempCardDto.setEmployeeNo(cardApplyDo.getPersonInfo().getEmployeeId());
                tempCardDto.setEmployeeType(cardApplyDo.getPersonInfo().getType());
                tempCardDto.setEmployeeTypeDesc(cardApplyDo.getPersonInfo().getCnType());
                tempCardDto.setFirstDeptName(cardApplyDo.getPersonInfo().getFirstDeptName());
                tempCardDto.setSecondDeptName(cardApplyDo.getPersonInfo().getSecondDeptName());
                tempCardDto.setThirdDeptName(cardApplyDo.getPersonInfo().getThirdDeptName());
                tempCardDto.setFourthDeptName(cardApplyDo.getPersonInfo().getFourthDeptName());
                tempCardDto.setReceiptParkName(cardApplyDo.getReceiptParkName());
                tempCardDto.setParkName(cardApplyDo.getParkName());
                tempCardDto.setWorkCity(cardApplyDo.getCityName());
                PersonInfoModel nowPerson = finalAccountMap.get(cardApplyDo.getPersonInfo().getUserName());
                CardLeaveRecordDo nowRecordDo = leaveRecordDoMap.get(cardApplyDo.getPersonInfo().getUserName());
                if (Objects.nonNull(nowPerson)) {
                    tempCardDto.setLeaveDate(nowPerson.getLeaveDate());
                    tempCardDto.setPreLeaveDate(nowPerson.getPreLeaveDate());
                } else if (Objects.nonNull(nowRecordDo)) {
                    tempCardDto.setLeaveDate(nowRecordDo.getLeaveDate());
                    tempCardDto.setPreLeaveDate(nowRecordDo.getPreLeaveDate());
                }
            }
            //填充生效时间
            CardTimeValidityDo cardTimeValidityDo = validateTimeMap.get(cardInfoDo.getId());
            if (Objects.nonNull(cardTimeValidityDo)) {
                tempCardDto.setStartTime(cardTimeValidityDo.getStartTime());
                tempCardDto.setEndTime(cardTimeValidityDo.getEndTime());
            }
            return tempCardDto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCardApply(TempCardApplyEditDto editDto) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(editDto.getApplyId());
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);
        Asserts.assertNotNull(cardApplyDo, CardApplicationErrorCodeEnum.CARD_APPLY_NOT_FOUND);
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoByApplyId(cardApplyDo.getId());
        fillCardApplyEditInfo(cardApplyDo, editDto);
        cardApplyDomainService.updateCardApply(cardApplyDo);

        if (Objects.isNull(cardInfoDo)) {
            //初次开卡
            cardInfoDo = initCardInfoOnOpenCard(cardApplyDo, editDto);
            cardDomainService.checkIsRepeatOpen(cardInfoDo);
            //填充人员信息
            SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
            safetyPersonDo.setUid(cardInfoDo.getUid());
            safetyPersonDomainService.fillPersonInfoWithBase(safetyPersonDo);

            //初次开卡校验人员状态是否可用
            safetyPersonDomainService.checkPersonIsActive(safetyPersonDo);

            //开临时卡前校验是否存在 使用中、已过期、已销卡的正式卡
            CardInfoDo query = new CardInfoDo();
            query.setUid(cardApplyDo.getUid());
            query.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
            query.putExtField("statusList", CardStatusEnum.exceptNotActive());
            cardDomainService.checkEmpCardIsExist(query);
            //保存工卡记录
            cardInfoDo.putExtField("isInitStartTime", true);
            cardDomainService.openCard(cardInfoDo);
            //更新生效时间
            cardDomainService.saveValidateTime(cardInfoDo);
        } else {
            cardDomainService.fillSafetyRight(cardInfoDo);
            cardDomainService.fillSafetyPersonMedium(cardInfoDo);
            //更新卡信息
            if (Objects.isNull(cardInfoDo.getEndTime()) ||
                    !editDto.getEndTime().toLocalDate().equals(cardInfoDo.getEndTime().toLocalDate())) {
                //卡有效期时间缩短 更新权限包有效期
                if (editDto.getEndTime().toLocalDate().isBefore(cardInfoDo.getEndTime().toLocalDate())) {
                    cardInfoDo.setEndTime(editDto.getEndTime());
                    updatePermissionGroupTimes(cardInfoDo);
                }
                //更新生效时间
                fillValidatePeriodEditInfo(cardInfoDo, editDto);
                cardDomainService.updateValidateTime(cardInfoDo);
            }
            //如果物理卡号或者加密卡号发生变化，则更新卡信息
            if (!StringUtils.equalsIgnoreCase(cardInfoDo.getMediumPhysicsCode(), editDto.getMediumPhysicsCode()) ||
                    !StringUtils.equalsIgnoreCase(cardInfoDo.getMediumEncryptCode(), editDto.getMediumEncryptCode())) {
                fillCardInfoEditInfo(cardInfoDo, editDto);
                cardInfoDo.putExtField("isInitStartTime", true);
                cardDomainService.editCard(cardInfoDo);
                //通知idm卡号变更
                cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
                cardDomainService.notifyCardNumberChange(cardInfoDo);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCardInfo(TempCardEditDto cardEditDto) {
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(cardEditDto.getCardId());
        cardDomainService.fillSafetyRight(cardInfoDo);
        cardDomainService.fillSafetyPersonMedium(cardInfoDo);
        if (!cardEditDto.getEndTime().toLocalDate().equals(cardInfoDo.getEndTime().toLocalDate())) {
            //卡有效期时间缩短 更新权限包有效期
            if (cardEditDto.getEndTime().toLocalDate().isBefore(cardInfoDo.getEndTime().toLocalDate())) {
                cardInfoDo.setEndTime(cardEditDto.getEndTime());
                updatePermissionGroupTimes(cardInfoDo);
            }
            fillValidatePeriodEditInfo(cardInfoDo, cardEditDto);
            //更新生效时间
            cardDomainService.updateValidateTime(cardInfoDo);
            //如果物理卡号未变，但是结束时间有改变
            updateSyncStatusForOnlyExtension(cardInfoDo, cardEditDto);
            //若当前编辑的时候卡的状态是已过期
            if (CardStatusEnum.EXPIRED.getCode().equals(cardInfoDo.getCardStatus())) {
                //更新卡的状态为使用中
                cardInfoDo.setCardStatus(CardStatusEnum.USING.getCode());
                cardInfoRepository.updateStatusById(cardInfoDo);
            }
        }
        if (!StringUtils.equalsIgnoreCase(cardInfoDo.getMediumPhysicsCode(), cardEditDto.getMediumPhysicsCode()) ||
                !StringUtils.equalsIgnoreCase(cardInfoDo.getMediumEncryptCode(), cardEditDto.getMediumEncryptCode())) {
            fillCardEditInfo(cardInfoDo, cardEditDto);
            cardDomainService.editCard(cardInfoDo);
            //通知idm卡号变更
            cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
            cardDomainService.notifyCardNumberChange(cardInfoDo);
        }
    }

    private void updatePermissionGroupTimes(CardInfoDo cardInfoDo) {
        List<CardPermissionApplyGroupDo> permissionApplyGroupList = cardPermissionApplyRepository
                .findPermissionApplyGroupListByUidAndSourceAndCode(cardInfoDo.getUid(),
                        null, null);
        if (CollectionUtils.isNotEmpty(permissionApplyGroupList)) {
            List<String> cardGroupCodeList = permissionApplyGroupList.stream()
                    .map(CardPermissionApplyGroupDo::getCardGroupCode).collect(Collectors.toList());
            List<CardGroupConfigDo> groupConfigList =
                    cardGroupConfigRepository.findListByCode(new HashSet<>(cardGroupCodeList));
            Map<String, CardGroupConfigDo> configDoMap =
                    groupConfigList.stream().collect(Collectors.toMap(CardGroupConfigDo::getCardGroupCode,
                            Function.identity(), (oldValue, newValue) -> oldValue));
            permissionApplyGroupList.removeIf(item -> CardClassEnum.NORMAL.getCode()
                    .equals(configDoMap.get(item.getCardGroupCode()).getControlType()) ||
                    ZonedDateTimeUtils.compare(item.getEndTime(), cardInfoDo.getEndTime()) < OAUCFCommonConstants.INT_ZERO);
            //更新特殊权限包有效期
            permissionApplyGroupList.forEach(item -> item.setEndTime(cardInfoDo.getEndTime()));
            cardPermissionApplyRepository.updateRelation(permissionApplyGroupList);
        }
    }

    /**
     * 更新同步时间
     *
     * @param cardInfoDo
     * @param cardEditDto
     * @return void
     * <AUTHOR>
     * @date 2023/7/3 15:10
     */
    private void updateSyncStatusForOnlyExtension(CardInfoDo cardInfoDo, TempCardEditDto cardEditDto) {
        //卡不变，单纯的延长时间的时候
        if (StringUtils.equalsIgnoreCase(cardInfoDo.getMediumPhysicsCode(), cardEditDto.getMediumPhysicsCode()) &&
                StringUtils.equalsIgnoreCase(cardInfoDo.getMediumEncryptCode(), cardEditDto.getMediumEncryptCode())) {
            //更新当前人和介质关系的状态和时间
            SafetyPersonMediumDo safetyPersonMediumDo =
                    safetyPersonMediumRepository.getSafetyPersonMediumByMediumCodeAndUid(cardInfoDo.getMediumCode(), cardInfoDo.getUid());
            if (safetyPersonMediumDo != null) {
                safetyPersonMediumDo.setEndTime(cardEditDto.getEndTime());
                safetyPersonMediumDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                safetyPersonMediumRepository.saveOrUpdate(Lists.newArrayList(safetyPersonMediumDo));
                safetyPersonMediumRepository.restorePersonMediumById(safetyPersonMediumDo);
            }
            //更新当前权限的状态和时间
            List<SafetyRightDo> safetyRightDoList =
                    safetyRightRepository.findSafetyRightByMediumCodeAndUid(cardInfoDo.getMediumCode(), cardInfoDo.getUid(), null);
            if (CollectionUtils.isNotEmpty(safetyRightDoList)) {
                //权限不需要更新endTime
                safetyRightDoList.stream().forEach(item -> {
                    item.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                    item.setOperateTime(ZonedDateTime.now());
                });
                safetyRightRepository.batchSaveOrUpdate(safetyRightDoList, false);
            }
        }
    }

    private void fillValidatePeriodEditInfo(CardInfoDo cardInfoDo, TempCardEditDto editDto) {
        cardInfoDo.setStartTime(editDto.getStartTime());
        cardInfoDo.setEndTime(editDto.getEndTime());
        if (cardInfoDo.getSafetyPersonMediumDo() != null) {
            cardInfoDo.getSafetyPersonMediumDo().setEndTime(editDto.getEndTime());
        }
    }

    private void fillCardEditInfo(CardInfoDo cardInfoDo, TempCardEditDto editDto) {
        if (prefixEncryptCode.equals(editDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = editDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(editDto.getCardNum());
        cardInfoDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardInfoDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
    }

    private CardInfoDo initCardInfoOnOpenCard(CardApplyDo cardApplyDo, TempCardApplyEditDto editDto) {
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardApplyId(cardApplyDo.getId());
        cardInfoDo.setCardType(CardTypeEnum.TEMP_CARD.getNumber());
        cardInfoDo.setCardStatus(CardStatusEnum.NOT_ACTIVE.getCode());
        if (prefixEncryptCode.equals(editDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = editDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
        cardInfoDo.setUid(cardApplyDo.getUid());
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(editDto.getCardNum());
        cardInfoDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
        cardInfoDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardInfoDo.setStartTime(editDto.getStartTime());
        cardInfoDo.setEndTime(editDto.getEndTime());
        return cardInfoDo;
    }

    private void fillValidatePeriodEditInfo(CardInfoDo cardInfoDo, TempCardApplyEditDto editDto) {
        cardInfoDo.setStartTime(editDto.getStartTime());
        cardInfoDo.setEndTime(editDto.getEndTime());
    }

    private void fillCardInfoEditInfo(CardInfoDo cardInfoDo, TempCardApplyEditDto editDto) {
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(editDto.getCardNum());
        cardInfoDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardInfoDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
        if (prefixEncryptCode.equals(editDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = editDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
    }

    private void fillCardApplyEditInfo(CardApplyDo cardApplyDo, TempCardApplyEditDto editDto) {
        cardApplyDo.setStartTime(editDto.getStartTime());
        cardApplyDo.setEndTime(editDto.getEndTime());
        cardApplyDo.setCardNum(editDto.getCardNum());
        cardApplyDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardApplyDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
        cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_RECEIVE.getCode());
    }

    @Override
    public void returnCard(CardReturnDto cardReturnDto) {
        //查询工卡
        CardInfoDo cardInfoDo = cardDomainService.getDetailCardInfo(cardReturnDto.getCardId());
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        //更新工
        cardDomainService.returnCard(cardInfoDo);
        //通知idm
        cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_DELETE);
        cardDomainService.notifyCardNumberChange(cardInfoDo);
        //发送时间，通知处理离职申请、补卡申请终止、权限同步等逻辑
        CardPinEvent cardPinEvent = CardPinEvent.builder().uid(cardInfoDo.getUid()).cardId(cardInfoDo.getId()).build();
        commonProducer.send(RocketMqTopicEnum.PIN_CARD.getTopicName(), GsonUtils.toJsonFilterNullField(cardPinEvent));
    }

    @Override
    public void reopen(CardReopenDto cardReopenDto) {
        //查询工卡
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(cardReopenDto.getCardId());
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        //校验正式卡申请单状态, 如果正式卡申请记录已经存在，且状态是待领取，则不允许重新开卡
        CardApplyDo empCardInfoDo = cardApplyDomainService.findCardByUidAndCardType(cardInfoDo.getUid(), CardTypeEnum.EMPLOYEE_CARD);
        if (Objects.nonNull(empCardInfoDo)
                && (CardApplyStatusEnum.WAIT_RECEIVE.getCode().equals(empCardInfoDo.getApplyStatus())
                || CardApplyStatusEnum.COMPLETED.getCode().equals(empCardInfoDo.getApplyStatus()))) {
            throw new BizException(CardApplicationErrorCodeEnum.TEMP_CARD_REOPEN_ERROR_EMP_CARD_ACTIVE);
        }
        //恢复前校验人员状态是否可用
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonDomainService.fillPersonInfoWithBase(safetyPersonDo);
        safetyPersonDomainService.checkPersonIsActive(safetyPersonDo);

        //卡恢复
        cardDomainService.reopen(cardInfoDo);
        //通知idm
        cardInfoDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
        cardDomainService.notifyCardNumberChange(cardInfoDo);
    }

    @Override
    public TempCardApplyDto detail(Long applyId) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(applyId);
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);
        Asserts.assertNotNull(cardApplyDo, CardApplicationErrorCodeEnum.CARD_APPLY_NOT_FOUND);
        cardApplyDomainService.fillFullCardApplyV2(cardApplyDo);
        TempCardApplyDto tempCardApplyDto = cardApplyDtoConverter.cardApplyDoToTempCardApplyDto(cardApplyDo);
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoByApplyId(applyId);
        Optional.ofNullable(cardInfoDo).ifPresent(item -> {
            tempCardApplyDto.setCardNum(item.getCardNum());
            tempCardApplyDto.setMediumEncryptCode(item.getMediumEncryptCode());
            tempCardApplyDto.setMediumPhysicsCode(item.getMediumPhysicsCode());
        });
        return tempCardApplyDto;
    }

    @Override
    public TempCardDto cardDetail(Long cardId) {
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(cardId);
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(cardInfoDo.getCardApplyId());
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);
        Asserts.assertNotNull(cardApplyDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        cardApplyDomainService.fillFullCardApplyV2(cardApplyDo);
        TempCardDto tempCardDto = cardApplyDtoConverter.cardApplyDoToTempCardDto(cardApplyDo);
        tempCardDto.setId(cardInfoDo.getId());
        tempCardDto.setCardNum(cardInfoDo.getCardNum());
        tempCardDto.setBackTime(cardInfoDo.getBackTime());
        tempCardDto.setMediumEncryptCode(cardInfoDo.getMediumEncryptCode());
        tempCardDto.setMediumPhysicsCode(cardInfoDo.getMediumPhysicsCode());
        tempCardDto.setCardStatus(cardInfoDo.getCardStatus());
        tempCardDto.setCardStatusName(CardStatusEnum.getDesc(cardInfoDo.getCardStatus()));
        tempCardDto.setStartTime(cardInfoDo.getStartTime());
        tempCardDto.setEndTime(cardInfoDo.getEndTime());
        tempCardDto.setMediumCode(cardInfoDo.getMediumCode());
        return tempCardDto;
    }

    @Override
    public void batchImportTempCardApply(CardApplyImportDto importDto) {
        AsyncImportTaskDo asyncImportRecordDo = initAsyncImportRecord(importDto);
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        asyncImportRecordDo.setOperator(userInfoDto.getUid());
        asyncImportRecordDo.setOperatorName(userInfoDto.getUserName());
        asyncImportTaskDomainService.save(asyncImportRecordDo);
        importEventHandler.handleImportEvent(new SafetyImportEvent(asyncImportRecordDo, asyncImportRecordDo.getBatchId()));
    }

    private AsyncImportTaskDo initAsyncImportRecord(CardApplyImportDto importDto) {
        AsyncImportTaskDo asyncImportTaskDo = new AsyncImportTaskDo();
        String seq = CodeUtils.getUUID();
        asyncImportTaskDo.setOperator(importDto.getOperator());
        asyncImportTaskDo.setBatchId(seq);
        asyncImportTaskDo.setFileName(importDto.getFileName());
        asyncImportTaskDo.setOperatorName(importDto.getOperatorName());
        asyncImportTaskDo.setOpTime(ZonedDateTime.now());
        asyncImportTaskDo.setOpTypeEnum(ImportTypeEnum.TEMP_CARD_APPLY_IMPORT);
        asyncImportTaskDo.setExpireTime(Instant.now().getEpochSecond() + Common.QUART_HOUR);
        asyncImportTaskDo.setUrl(importDto.getUrl());
        asyncImportTaskDo.setStatusEnum(ImportStatusEnum.TO_EXECUTE);
        return asyncImportTaskDo;
    }
}
