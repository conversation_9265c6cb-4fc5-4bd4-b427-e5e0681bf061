package com.mi.oa.ee.safety.application.impl.common.enums;

import com.mi.oa.ee.safety.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.impl.common.export.CardApplyUploadPhotoBatchImportExecutorImpl;
import com.mi.oa.ee.safety.application.impl.common.export.CardBatchPermissionAddImportExecutorImpl;
import com.mi.oa.ee.safety.application.impl.common.export.CardBatchPermissionDeleteImportExecutorImpl;
import com.mi.oa.ee.safety.application.impl.common.export.CardBatchPermissionGroupAddImportExecutorImpl;
import com.mi.oa.ee.safety.application.impl.common.export.CardBatchPermissionGroupDeleteImportExecutorImpl;
import com.mi.oa.ee.safety.application.impl.common.export.CarrierGroupImportExecutorImpl;
import com.mi.oa.ee.safety.application.impl.common.export.CoopCardApplyImportExecutorImpl;
import com.mi.oa.ee.safety.application.impl.common.export.EmpCardApplyImportExecutorImpl;
import com.mi.oa.ee.safety.application.impl.common.export.TempCardApplyImportExecutorImpl;
import com.mi.oa.ee.safety.application.service.common.ImportExecutor;
import com.mi.oa.ee.safety.common.enums.ImportTypeEnum;
import com.mi.oa.ee.safety.common.utils.SpringBeanTool;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导入任务enum
 *
 * <AUTHOR>
 * @date 2023/3/3 11:30
 */
@Getter
@AllArgsConstructor
public enum ImportTaskEnum {
    TEMP_CARD_APPLY_IMPORT(ImportTypeEnum.TEMP_CARD_APPLY_IMPORT.getType(),
            TempCardApplyImportExecutorImpl.SERVICE_NAME),
    EMP_CARD_APPLY_IMPORT(ImportTypeEnum.EMP_CARD_APPLY_IMPORT.getType(),
            EmpCardApplyImportExecutorImpl.SERVICE_NAME),
    CARD_BATCH_PERMISSION_ADD_IMPORT(ImportTypeEnum.CARD_BATCH_PERMISSION_ADD_IMPORT.getType(),
            CardBatchPermissionAddImportExecutorImpl.SERVICE_NAME),
    CARD_BATCH_PERMISSION_DELETE_IMPORT(ImportTypeEnum.CARD_BATCH_PERMISSION_DELETE_IMPORT.getType(),
            CardBatchPermissionDeleteImportExecutorImpl.SERVICE_NAME),
    COOP_CARD_APPLY_IMPORT(ImportTypeEnum.COOP_CARD_APPLY_IMPORT.getType(),
            CoopCardApplyImportExecutorImpl.SERVICE_NAME),
    // 权限组导入
    CARRIER_GROUP_IMPORT(ImportTypeEnum.CARRIER_GROUP_IMPORT.getType(),
            CarrierGroupImportExecutorImpl.SERVICE_NAME),
    // 物业卡列表导入
    PROPERTY_CARD_IMPORT(ImportTypeEnum.PROPERTY_CARD_IMPORT.getType(),
            CoopCardApplyImportExecutorImpl.SERVICE_NAME),
    //权限包批量添加
    CARD_PERMISSION_GROUP_ADD_IMPORT(ImportTypeEnum.CARD_BATCH_PERMISSION_GROUP_ADD_IMPORT.getType(),
            CardBatchPermissionGroupAddImportExecutorImpl.SERVICE_NAME),
    //权限组批量删除
    CARD_PERMISSION_GROUP_DELETE_IMPORT(ImportTypeEnum.CARD_BATCH_PERMISSION_GROUP_DELETE_IMPORT.getType(),
            CardBatchPermissionGroupDeleteImportExecutorImpl.SERVICE_NAME),
    //批量上传照片
    CARD_APPLY_PHOTO_BATCH_IMPORT(ImportTypeEnum.CARD_APPLY_BATCH_IMPORT_PHOTO.getType(),
            CardApplyUploadPhotoBatchImportExecutorImpl.SERVICE_NAME);

    private static final Map<Integer, ImportTaskEnum> CACHE = Arrays.stream(values())
            .collect(Collectors.toMap(ImportTaskEnum::getType, Function.identity()));

    private Integer type;

    private String serviceName;

    public static ImportExecutor getImportExecutor(Integer type) {
        ImportTaskEnum importTaskEnum = CACHE.get(type);
        if (null == importTaskEnum) {
            throw new BizException(ApplicationErrorCodeEnum.APPLICATION_NORMAL, "{{{操作类型未定义}}}");
        }
        return SpringBeanTool.getBean(importTaskEnum.getServiceName(), ImportExecutor.class);
    }
}
