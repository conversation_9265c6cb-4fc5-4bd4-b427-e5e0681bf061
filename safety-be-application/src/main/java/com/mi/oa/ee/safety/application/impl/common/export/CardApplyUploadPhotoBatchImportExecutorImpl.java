package com.mi.oa.ee.safety.application.impl.common.export;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.application.converter.common.ImportConverter;
import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyPhotoUploadImportDto;
import com.mi.oa.ee.safety.application.dto.common.AsyncImportTaskDto;
import com.mi.oa.ee.safety.application.impl.common.export.data.CardApplyUploadPhotoImportFunction;
import com.mi.oa.ee.safety.application.service.card.coop.CardApplyService;
import com.mi.oa.ee.safety.application.service.common.ImportExecutor;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.FileTypeEnum;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.common.enums.ImportStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.excel.ExcelStarter;
import com.mi.oa.ee.safety.common.excel.exception.ExcelStarterException;
import com.mi.oa.ee.safety.common.excel.function.ImportFunction;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.AsyncImportTaskDo;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.service.AsyncImportTaskDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.infra.remote.fds.FdsRemote;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogRepository;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.fds.utils.FDSUtils;
import com.xiaomi.infra.galaxy.fds.model.FDSObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2025/1/15 10:56
 */
@Slf4j
@Service(CardApplyUploadPhotoBatchImportExecutorImpl.SERVICE_NAME)
public class CardApplyUploadPhotoBatchImportExecutorImpl implements ImportExecutor {

    public static final String SERVICE_NAME = "cardApplyUploadPhotoBatchImportExecutor";

    private static final Set<String> SUPPORTED_IMAGE_EXTENSIONS = Sets.newHashSet(".jpg", ".jpeg", ".png", ".gif");

    private static final String MACOSX_DIR = "__MACOSX/";

    private static final int BUFFER_SIZE = 8192;

    @Resource
    private AsyncImportTaskDomainService asyncImportTaskDomainService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ImportConverter importConverter;

    @Resource
    private FdsRemote fdsRemote;

    @Resource
    private SafetyPersonDomainService safetyPersonDomainService;

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Resource
    private CardApplyUploadPhotoImportFunction cardApplyUploadPhotoImportFunction;

    @Resource
    private SafetyOperateLogRepository safetyOperateLogRepository;

    @Resource
    private CardApplyService cardApplyService;

    private String getFileName() {
        return NeptuneClient.getInstance().parseEntryTemplate("{{{批量上传照片导入结果}}}") + SafetyConstants.Common.UNDER_LINE
                + CodeUtils.generateVisitorCode(6, true) + SafetyConstants.Common.POINT + SafetyConstants.Common.EXCEL_SUFFIX;
    }

    @Override
    public void batchImport(AsyncImportTaskDto asyncImportTaskDto) {
        AsyncImportTaskDo asyncImportTaskDo = importConverter.dtoToDoExt(asyncImportTaskDto);
        //更新导入进度状态
        startProcess(asyncImportTaskDo);
        //读取excel
        byte[] excelBytes = downloadFile(asyncImportTaskDto.getUrl());
        if (ArrayUtils.isEmpty(excelBytes)) {
            processFailed(asyncImportTaskDo, "{{{导入文件下载失败}}}");
            return;
        }
        List<CardApplyPhotoUploadImportDto> cardApplyImportExcelDtoList;
        try {
            cardApplyImportExcelDtoList = preProcessZipFile(excelBytes);
        } catch (ExcelStarterException e) {
            processFailed(asyncImportTaskDo, e.getMessage());
            return;
        } catch (Exception e) {
            log.error("读取文件异常. 任务信息:{}", asyncImportTaskDto, e);
            processFailed(asyncImportTaskDo, "{{{读取文件异常，请检查文件格式}}}");
            return;
        } finally {
            getImportFunction().clear();
        }
        if (CollectionUtils.isEmpty(cardApplyImportExcelDtoList)) {
            processFailed(asyncImportTaskDo, "{{{没有需要处理的数据}}}");
            return;
        }
        ImportStatusEnum statusEnum = doBusiness(cardApplyImportExcelDtoList, asyncImportTaskDo);
        //写导入结果
        String resultUrl;
        byte[] excelByte;
        //上传到fds
        try (ByteArrayOutputStream excelOutputStream = new ByteArrayOutputStream()) {
            ExcelStarter.exportBuilder(excelOutputStream, getFileName(), getClassType())
                    .exportResponse(cardApplyImportExcelDtoList);
            excelByte = excelOutputStream.toByteArray();
        } catch (Exception e) {
            log.error("正式卡申请导入失败，生成导入结果失败，记录id: {} ", asyncImportTaskDto.getBatchId(), e);
            uploadResultFailed(asyncImportTaskDo, statusEnum, "{{{生成导入结果失败}}}");
            return;
        }
        //output流转为input流
        try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(excelByte)) {
            //上传到fds
            resultUrl = uploadToFds(getFileName(), byteArrayInputStream);
        } catch (Exception e) {
            log.error("正式卡申请导入失败，上传导入结果失败，记录id: {} ", asyncImportTaskDto.getBatchId(), e);
            uploadResultFailed(asyncImportTaskDo, statusEnum, "{{{上传导入结果失败}}}");
            return;
        }
        //更新导入状态
        finishProcess(asyncImportTaskDo, statusEnum, resultUrl);
    }

    /**
     * 解压缩
     * @param excelBytes
     * @return
     */
    private List<CardApplyPhotoUploadImportDto> preProcessZipFile(byte[] excelBytes) {
        List<CardApplyPhotoUploadImportDto> importList = Lists.newArrayList();
        try (ByteArrayInputStream byteStream = new ByteArrayInputStream(excelBytes);
             ZipInputStream zipStream = new ZipInputStream(byteStream)) {
            ZipEntry entry;
            while ((entry = zipStream.getNextEntry()) != null) {
                CardApplyPhotoUploadImportDto dto = createImageDto(entry);
                importList.add(dto);
                if (!isValidImageEntry(entry)) {
                    dto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.FAIL.getResult()));
                    dto.setException(NeptuneClient.getInstance().parseEntryTemplate(
                            CardApplyDomainErrorCodeEnum.PIC_TYPE_ERROR.getErrDesc()));
                    continue;
                }
                try (ByteArrayInputStream bais = copyToNewInputStream(zipStream)){
                    BufferedImage image = ImageIO.read(bais);
                    bais.reset();
                    if (!isValidImageSize(image)) {
                        dto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.FAIL.getResult()));
                        dto.setException(NeptuneClient.getInstance().parseEntryTemplate(
                                CardApplyDomainErrorCodeEnum.PIC_PIX_NOT_OK.getErrDesc()));
                        continue;
                    }
                    //传入fds
                    String realPhotoUrl = FDSUtils.putFile(bais,
                            FDSUtils.buildNewFileName(dto.getUserName()) + ".jpg", null);
                    dto.setPhotoUrl(realPhotoUrl);
                } catch (Exception e) {
                    log.error("pro process file error:{}", dto.getUserName(), e);
                    dto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.FAIL.getResult()));
                    dto.setException(NeptuneClient.getInstance().parseEntryTemplate(
                            CardApplyDomainErrorCodeEnum.PIC_TYPE_ERROR.getErrDesc()));
                }
            }
        } catch (Exception e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.ZIP_FILE_CONTENT_ERROR);
        }
        return importList;
    }

    /**
     * 将输入流内容复制到新的ByteArrayInputStream
     */
    private ByteArrayInputStream copyToNewInputStream(InputStream input) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[BUFFER_SIZE];
        int bytesRead;
        int totalSize = 0;
        while ((bytesRead = input.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, bytesRead);
            totalSize += bytesRead;
        }
        buffer.flush();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(buffer.toByteArray());
        byteArrayInputStream.mark(totalSize);
        return byteArrayInputStream;
    }

    private boolean isValidImageSize(BufferedImage image) throws IOException {
        int width = image.getWidth();
        int height = image.getHeight();
        return SafetyConstants.Card.PIC_PIX.equals(width) && SafetyConstants.Card.PIC_PIX.equals(height);
    }

    /**
     * 创建图片上传DTO
     */
    private CardApplyPhotoUploadImportDto createImageDto(ZipEntry entry) {
        CardApplyPhotoUploadImportDto dto = new CardApplyPhotoUploadImportDto();
        dto.setUserName(getAccountName(entry.getName()));
        return dto;
    }

    /**
     * 检查是否是有效的图片条目
     */
    private boolean isValidImageEntry(ZipEntry entry) {
        if (entry.isDirectory()) {
            return false;
        }
        String fileName = entry.getName();
        if (fileName.startsWith(MACOSX_DIR) || StringUtils.isEmpty(fileName)) {
            return false;
        }
        return SUPPORTED_IMAGE_EXTENSIONS.stream().anyMatch(fileName.toLowerCase()::endsWith);
    }

    private ImportStatusEnum doBusiness(List<CardApplyPhotoUploadImportDto> cardApplyImportExcelDtoList, AsyncImportTaskDo asyncImportTaskDo) {
        ImportStatusEnum statusEnum = ImportStatusEnum.UNDEFINED;
        int count = 0;
        for (CardApplyPhotoUploadImportDto empCardApplyImportExcelDto : cardApplyImportExcelDtoList) {
            if (count % 1000 == 0) {
                updateProcess(asyncImportTaskDo);
            }
            try {
                if (checkImportExcelDto(empCardApplyImportExcelDto, asyncImportTaskDo)) {
                    statusEnum = (statusEnum == ImportStatusEnum.FAILED || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                            ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.SUCCESS);
                    empCardApplyImportExcelDto.setResult(SafetyConstants.SUCCESS);
                } else {
                    statusEnum = (statusEnum == ImportStatusEnum.SUCCESS || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                            ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.FAILED);
                }
            } catch (Exception e) {
                log.error("批量照片修改失败, batchId:{}, tempCardApplyImportExcelDto:{} ",
                        asyncImportTaskDo.getBatchId(), empCardApplyImportExcelDto);
                empCardApplyImportExcelDto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.FAIL.getResult()));
                empCardApplyImportExcelDto.setException(StringUtils.substring(e.getMessage(), 0, 200));
                statusEnum = (statusEnum == ImportStatusEnum.SUCCESS || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                        ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.FAILED);
            }
            count++;
        }
        return statusEnum;
    }

    private boolean checkImportExcelDto(CardApplyPhotoUploadImportDto empCardApplyImportExcelDto,
                                        AsyncImportTaskDo asyncImportTaskDo) throws Exception {
        //标记为预处理失败，或者图片地址为空，直接返回失败
        if (StringUtils.isNoneBlank(empCardApplyImportExcelDto.getResult())
                || StringUtils.isBlank(empCardApplyImportExcelDto.getPhotoUrl())) {
            return false;
        }

        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUserName(empCardApplyImportExcelDto.getUserName());
        safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
        if (StringUtils.isEmpty(safetyPersonDo.getUid())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.FILE_NAME_NOT_CORRECT);
        }
        CardTypeEnum cardTypeEnum = CardTypeEnum.ofCode(asyncImportTaskDo.getMemo());
        if (Objects.isNull(cardTypeEnum)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_TYPE_PARAM_ERROR);
        }
        CardApplyDo cardApplyDo = cardApplyRepository.findCardByUidAndCardType(safetyPersonDo.getUid(), cardTypeEnum);
        if (Objects.isNull(cardApplyDo) || Objects.isNull(cardApplyDo.getId())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_NOT_EXIST);
        }
        if (!CardApplyStatusEnum.canUploadPhotoList().contains(cardApplyDo.getApplyStatus())) {
            throw new Exception(CardApplyDomainErrorCodeEnum.NOT_SUPPLIER_UPLOAD_PHOTO_ERROR.getErrDesc());
        }
        if (CardApplyStatusEnum.WAIT_PHOTO.getCode().equals(cardApplyDo.getApplyStatus())) {
            cardApplyService.uploadPhotoBySupplier(cardApplyDo.getId(), empCardApplyImportExcelDto.getPhotoUrl());
        } else {
            cardApplyService.uploadPhoto(cardApplyDo.getId(), empCardApplyImportExcelDto.getPhotoUrl());
        }
        return true;
    }


    private ImportFunction<CardApplyPhotoUploadImportDto> getImportFunction() {
        return cardApplyUploadPhotoImportFunction;
    }

    private Class<CardApplyPhotoUploadImportDto> getClassType() {
        return CardApplyPhotoUploadImportDto.class;
    }

    private String uploadToFds(String fileName, InputStream input) {
        FDSObjectMetadata metadata = new FDSObjectMetadata();
        metadata.setContentType(FileTypeEnum.EXCEL.getContentType());
        return fdsRemote.uploadFile(fileName, input, metadata);
    }

    private byte[] downloadFile(String url) {
        ResponseEntity<byte[]> responseEntity = restTemplate.getForEntity(url, byte[].class);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            return responseEntity.getBody();
        }
        return new byte[0];
    }

    private void startProcess(AsyncImportTaskDo asyncExportRecordDo) {
        asyncExportRecordDo.setStatusEnum(ImportStatusEnum.PROCESSING);
        asyncExportRecordDo.setExpireTime(Instant.now().getEpochSecond() + SafetyConstants.Common.QUART_HOUR);
        asyncImportTaskDomainService.updateStatusByBatchId(asyncExportRecordDo);
    }

    public void updateProcess(AsyncImportTaskDo asyncExportRecordDo) {
        asyncExportRecordDo.setStatusEnum(ImportStatusEnum.PROCESSING);
        asyncExportRecordDo.setExpireTime(Instant.now().getEpochSecond() + SafetyConstants.Common.QUART_HOUR);
        asyncImportTaskDomainService.updateStatusByBatchId(asyncExportRecordDo);
    }

    private void uploadResultFailed(AsyncImportTaskDo asyncExportRecordDo, ImportStatusEnum statusEnum, String message) {
        asyncExportRecordDo.setStatusEnum(statusEnum);
        asyncExportRecordDo.setCompleteTime(ZonedDateTime.now());
        asyncExportRecordDo.setExpireTime(0L);
        asyncExportRecordDo.setMessage(message);
        asyncImportTaskDomainService.updateStatusByBatchId(asyncExportRecordDo);
    }

    private void finishProcess(AsyncImportTaskDo asyncExportRecordDo, ImportStatusEnum statusEnum, String resultUrl) {
        asyncExportRecordDo.setStatusEnum(statusEnum);
        asyncExportRecordDo.setCompleteTime(ZonedDateTime.now());
        asyncExportRecordDo.setMessage(StringUtils.EMPTY);
        asyncExportRecordDo.setResultUrl(resultUrl);
        asyncExportRecordDo.setExpireTime(0L);
        asyncImportTaskDomainService.updateStatusByBatchId(asyncExportRecordDo);
    }

    private void processFailed(AsyncImportTaskDo asyncExportRecordDo, String msg) {
        asyncExportRecordDo.setStatusEnum(ImportStatusEnum.IMPORT_FAILED);
        asyncExportRecordDo.setCompleteTime(ZonedDateTime.now());
        asyncExportRecordDo.setMessage(StringUtils.substring(msg, 0, 200));
        asyncExportRecordDo.setExpireTime(0L);
        asyncImportTaskDomainService.updateStatusByBatchId(asyncExportRecordDo);
    }

    private static @NotNull String getAccountName(String fileName) {
        String[] split = fileName.split("/");
        String suffixFileName = split[split.length - 1];
        int lastDotIndex = suffixFileName.lastIndexOf('.');
        String userName;
        if (lastDotIndex > 0) {
            userName = suffixFileName.substring(0, lastDotIndex);
        } else {
            userName = suffixFileName;
        }
        return userName;
    }

}
