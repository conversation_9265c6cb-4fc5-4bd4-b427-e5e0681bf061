package com.mi.oa.ee.safety.application.impl.common.export;

import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.application.dto.card.shared.CarrierGroupImportExcelDto;
import com.mi.oa.ee.safety.application.impl.common.exception.ImportErrorException;
import com.mi.oa.ee.safety.application.impl.common.export.data.CarrierGroupImportFunction;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.common.enums.ImportStatusEnum;
import com.mi.oa.ee.safety.common.excel.function.ImportFunction;
import com.mi.oa.ee.safety.domain.model.AsyncImportTaskDo;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;

public abstract class CarrierGroupAbstractImportExecutor extends AsyncImportAbstractExecutor<CarrierGroupImportExcelDto> {

    @Resource
    private CarrierGroupImportFunction carrierGroupImportFunction;

    @Override
    protected Class<CarrierGroupImportExcelDto> getClassType() {
        return CarrierGroupImportExcelDto.class;
    }

    @Override
    protected ImportFunction<CarrierGroupImportExcelDto> getImportFunction() {
        return carrierGroupImportFunction;
    }

    @Override
    protected ImportStatusEnum doBusiness(List<CarrierGroupImportExcelDto> importExcelDtoList, AsyncImportTaskDo asyncImportTaskDo) {
        ImportStatusEnum statusEnum = ImportStatusEnum.UNDEFINED;
        int count = 0;
        for (CarrierGroupImportExcelDto carrierGroupImportExcelDto : importExcelDtoList) {
            if (count % 1000 == 0) {
                updateProcess(asyncImportTaskDo);
            }
            try {
                if (checkImportExcelDto(carrierGroupImportExcelDto)) {
                    doBusinessForCard(carrierGroupImportExcelDto);
                    statusEnum = (statusEnum == ImportStatusEnum.FAILED || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                            ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.SUCCESS);
                    carrierGroupImportExcelDto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.SUCCESS.getResult())
                            .replace("{", "").replace("}", ""));
                } else {
                    //失败
                    carrierGroupImportExcelDto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.FAIL.getResult())
                            .replace("{", "").replace("}", ""));
                    statusEnum = (statusEnum == ImportStatusEnum.SUCCESS || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                            ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.FAILED);
                }
            } catch (Exception e) {
                carrierGroupImportExcelDto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.FAIL.getResult())
                        .replace("{", "").replace("}", ""));
                carrierGroupImportExcelDto.setException(StringUtils.substring(e.getMessage()
                        .replace("{", "").replace("}", ""), 0, 200));
                statusEnum = (statusEnum == ImportStatusEnum.SUCCESS || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                        ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.FAILED);
            }

            count++;
        }
        // 批量导入
        return statusEnum;
    }

    abstract void doBusinessForCard(CarrierGroupImportExcelDto businessParam) throws ImportErrorException;

    abstract Boolean checkImportExcelDto(CarrierGroupImportExcelDto coopCardApplyImportExcelDto);
}
