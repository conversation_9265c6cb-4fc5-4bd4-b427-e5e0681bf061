package com.mi.oa.ee.safety.application.impl.common.export;

import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyImportExcelDto;
import com.mi.oa.ee.safety.application.dto.card.shared.DoBusinessImportDto;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.common.enums.SafetyPersonStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyConfigUserTypeEnum;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.converter.CardPersonInfoDoConverter;
import com.mi.oa.ee.safety.domain.enums.ImportExceptionEnum;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPersonInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.UserBehaviorRecordDo;
import com.mi.oa.ee.safety.domain.service.CardApplyDomainService;
import com.mi.oa.ee.safety.domain.service.CardPersonInfoDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.ee.safety.infra.repository.UserBehaviorRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 制卡申请导入
 *
 * <AUTHOR>
 * @date 2023/6/12 14:32
 */
@Slf4j
@Service(TempCardApplyImportExecutorImpl.SERVICE_NAME)
public class TempCardApplyImportExecutorImpl extends CardApplyAbstractImportExecutor {

    public static final String SERVICE_NAME = "tempCardApplyImportExecutor";

    @Resource
    private SafetyPersonDomainService safetyPersonDomainService;

    @Resource
    private CardApplyDomainService cardApplyDomainService;

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Resource
    private CardPersonInfoDoConverter cardPersonInfoDoConverter;

    @Autowired
    private CardPersonInfoDomainService cardPersonInfoDomainService;

    @Resource
    private UserBehaviorRecordRepository userBehaviorRecRepository;

    @Override
    public void doBusinessForCard(DoBusinessImportDto doBusinessImportDto, Map<String, String> accountEmpNoMap) {
        //填充申请单数据
        CardApplyDo cardApplyDo = new CardApplyDo();
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        if (StringUtils.isNotEmpty(doBusinessImportDto.getUserName())) {
            safetyPersonDo.setUserName(doBusinessImportDto.getUserName());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
        } else if (doBusinessImportDto.getEmployeeNo() != null && !doBusinessImportDto.getEmployeeNo().equals("")) {
            safetyPersonDo.setEmployeeId(doBusinessImportDto.getEmployeeNo());
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByEmpNoAndOrgCode(safetyPersonDo);
        } else {
            //根据手机号来查
            safetyPersonDo.setMobile(doBusinessImportDto.getPhone());
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByPhoneAndOrgCode(safetyPersonDo);
        }
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardApplyDomainService.fillTempApplyInfo(cardApplyDo);
        cardApplyDomainService.checkBeforeCreate(cardApplyDo);
        cardApplyRepository.save(cardApplyDo);
        //创建人员信息
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(safetyPersonDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);
        //添加白名单
        addUserBehaviorRecord(safetyPersonDo.getUid());
    }

    private void addUserBehaviorRecord(String uid) {
        UserBehaviorRecordDo userBehaviorRecordDo = new UserBehaviorRecordDo();
        userBehaviorRecordDo.setUid(uid);
        userBehaviorRecordDo.setBehaviorCode(SafetyConstants.Common.LARK_WHITE_LIST);
        userBehaviorRecordDo.setBehaviorValue(SafetyConstants.Common.LARK_WHITE_LIST_FLAG);
        userBehaviorRecRepository.addUserBehaviorRecord(userBehaviorRecordDo);
        userBehaviorRecRepository.addUserBehaviorRecord(userBehaviorRecordDo);
    }

    @Override
    Boolean checkImportExcelDto(CardApplyImportExcelDto cardApplyImportExcelDto) {
        if (StringUtils.isBlank(cardApplyImportExcelDto.getUserName())
                && StringUtils.isBlank(cardApplyImportExcelDto.getEmployeeNo())
                && StringUtils.isBlank(cardApplyImportExcelDto.getPhone())) {
            cardApplyImportExcelDto.setResult(ImportResultEnum.FAIL.getResult());
            cardApplyImportExcelDto.setException(ImportExceptionEnum.EMPTY_ACCOUNT_EMPLOYEE_PHONE.getDesc());
            return false;
        }

        String message = "";
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        if (StringUtils.isNotEmpty(cardApplyImportExcelDto.getUserName())) {
            safetyPersonDo.setUserName(cardApplyImportExcelDto.getUserName());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
            if (Objects.isNull(safetyPersonDo.getPersonStatus())) {
                cardApplyImportExcelDto.setResult(ImportResultEnum.FAIL.getResult());
                cardApplyImportExcelDto.setException(ImportExceptionEnum.ACCOUNT_NOT_EXIST.getDesc());
                return false;
            }
            //校验是否有制卡流程中领取状态前的卡
            CardApplyDo cardApplyDo = new CardApplyDo();
            cardApplyDo.setPartnerAccount(cardApplyImportExcelDto.getUserName());
        } else if (StringUtils.isNotEmpty(cardApplyImportExcelDto.getEmployeeNo())) {
            safetyPersonDo.setEmployeeId(cardApplyImportExcelDto.getEmployeeNo());
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByEmpNoAndOrgCode(safetyPersonDo);
            if (Objects.isNull(safetyPersonDo.getPersonStatus())) {
                cardApplyImportExcelDto.setResult(ImportResultEnum.FAIL.getResult());
                cardApplyImportExcelDto.setException(ImportExceptionEnum.EMPLOYEE_NO_NOT_EXIST.getDesc());
                return false;
            }
            //校验是否有制卡流程中领取状态前的卡
            CardApplyDo cardApplyDo = new CardApplyDo();
            cardApplyDo.setPartnerAccount(safetyPersonDo.getUserName());
        } else {
            safetyPersonDo.setMobile(cardApplyImportExcelDto.getPhone());
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByPhoneAndOrgCode(safetyPersonDo);
            if (Objects.isNull(safetyPersonDo.getPersonStatus())) {
                cardApplyImportExcelDto.setResult(ImportResultEnum.FAIL.getResult());
                cardApplyImportExcelDto.setException(ImportExceptionEnum.PHONE_NOT_EXIST.getDesc());
                return false;
            }
            //校验是否有制卡流程中领取状态前的卡
            CardApplyDo cardApplyDo = new CardApplyDo();
            cardApplyDo.setPartnerAccount(safetyPersonDo.getUserName());
        }
        if (StringUtils.isNotBlank(message)) {
            cardApplyImportExcelDto.setResult(ImportResultEnum.FAIL.getResult());
            cardApplyImportExcelDto.setException(message);
            return false;
        }

        if (SafetyPersonStatusEnum.PRE.equals(safetyPersonDo.getPersonStatus())) {
            cardApplyImportExcelDto.setResult(ImportResultEnum.FAIL.getResult());
            cardApplyImportExcelDto.setException(ImportExceptionEnum.ACCOUNT_NOT_ACTIVE.getDesc());
            return false;
        }
        if (SafetyPersonStatusEnum.DISABLE.equals(safetyPersonDo.getPersonStatus()) || SafetyPersonStatusEnum.QUIT.equals(safetyPersonDo.getPersonStatus())) {
            cardApplyImportExcelDto.setResult(ImportResultEnum.FAIL.getResult());
            cardApplyImportExcelDto.setException(ImportExceptionEnum.PERSON_DISABLE.getDesc());
            return false;
        }
        return true;
    }

    @Override
    protected String getFileName() {
        return NeptuneClient.getInstance().parseEntryTemplate("{{{临时卡制卡导入结果}}}") + SafetyConstants.Common.UNDER_LINE
                + CodeUtils.generateVisitorCode(6, true) + SafetyConstants.Common.POINT + SafetyConstants.Common.EXCEL_SUFFIX;
    }
}
