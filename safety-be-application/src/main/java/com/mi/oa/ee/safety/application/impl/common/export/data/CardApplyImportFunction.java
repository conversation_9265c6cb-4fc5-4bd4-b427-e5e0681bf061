package com.mi.oa.ee.safety.application.impl.common.export.data;

import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyImportExcelDto;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.common.excel.entity.ErrorEntity;
import com.mi.oa.ee.safety.common.excel.function.ImportFunction;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/12 15:10
 */
@Service("cardApplyImportFunction")
public class CardApplyImportFunction implements ImportFunction<CardApplyImportExcelDto> {
    private final ThreadLocal<List<CardApplyImportExcelDto>> importResultData = ThreadLocal
            .withInitial(Lists::newArrayList);

    @Override
    public void onSuccess(CardApplyImportExcelDto entity, int rowIndex) {
        entity.setResult(ImportResultEnum.SUCCESS.getResult());
        importResultData.get().add(entity);
    }

    @Override
    public void onError(CardApplyImportExcelDto errorRecord, ErrorEntity errorEntity) {
        errorRecord.setResult(ImportResultEnum.SUCCESS.getResult());
        errorRecord.setException(errorEntity.getErrorMessage());
        importResultData.get().add(errorRecord);

    }

    @Override
    public List<CardApplyImportExcelDto> getImportDate() {
        return importResultData.get();
    }

    @Override
    public void clear() {
        importResultData.remove();
    }
}
