package com.mi.oa.ee.safety.application.impl.common.export.data;

import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyPhotoUploadImportDto;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.common.excel.entity.ErrorEntity;
import com.mi.oa.ee.safety.common.excel.function.ImportFunction;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2025/1/17 18:09
 */
@Service("cardApplyUploadPhotoImportFunction")
public class CardApplyUploadPhotoImportFunction implements ImportFunction<CardApplyPhotoUploadImportDto> {
    private final ThreadLocal<List<CardApplyPhotoUploadImportDto>> importResultData = ThreadLocal
            .withInitial(Lists::newArrayList);

    @Override
    public void onSuccess(CardApplyPhotoUploadImportDto entity, int rowIndex) {
        entity.setResult(ImportResultEnum.SUCCESS.getResult());
        importResultData.get().add(entity);
    }

    @Override
    public void onError(CardApplyPhotoUploadImportDto errorRecord, ErrorEntity errorEntity) {
        errorRecord.setResult(ImportResultEnum.FAIL.getResult());
        errorRecord.setException(errorEntity.getErrorMessage());
        importResultData.get().add(errorRecord);

    }

    @Override
    public List<CardApplyPhotoUploadImportDto> getImportDate() {
        return importResultData.get();
    }

    @Override
    public void clear() {
        importResultData.remove();
    }
}
