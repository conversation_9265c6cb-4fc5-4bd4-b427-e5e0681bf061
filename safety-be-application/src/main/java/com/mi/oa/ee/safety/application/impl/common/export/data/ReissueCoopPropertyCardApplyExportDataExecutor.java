package com.mi.oa.ee.safety.application.impl.common.export.data;

import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.converter.common.ExportConverter;
import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardApplyDto;
import com.mi.oa.ee.safety.application.dto.common.AsyncReissueCardApplyExportQueryDto;
import com.mi.oa.ee.safety.application.dto.common.AsyncReissueCoopPropCardApplyExportExcelDto;
import com.mi.oa.ee.safety.application.service.card.reissue.ReissueCardApplyService;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.enums.ExportTypeEnum;
import com.mi.oa.ee.safety.domain.model.AsyncExportTaskDo;
import com.mi.oa.ee.safety.domain.service.AsyncExportTaskDomainService;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/7/15 15:47
 */
@Service(ReissueCoopPropertyCardApplyExportDataExecutor.SERVICE_NAME)
@Slf4j
public class ReissueCoopPropertyCardApplyExportDataExecutor
        extends
        AbstractExportDataFunction<AsyncReissueCardApplyExportQueryDto, ReissueCardApplyDto, AsyncReissueCoopPropCardApplyExportExcelDto> {
    public static final String SERVICE_NAME = "reissueCoopPropertyCardExportDataExecutor";

    @Resource
    private ReissueCardApplyService reissueCardApplyService;

    @Resource
    private ExportConverter exportConverter;

    @Resource
    private AsyncExportTaskDomainService asyncExportTaskDomainService;

    @Override
    public List<ReissueCardApplyDto> pageQuery(AsyncReissueCardApplyExportQueryDto param, int pageNum, int pageSize) {
        ReissueCardApplyDto reissueCardApplyDto = exportConverter.toReissueCardApplyDto(param);
        reissueCardApplyDto.setPageNum((long) pageNum);
        reissueCardApplyDto.setPageSize((long) pageSize);
        PageModel<ReissueCardApplyDto> pageModel = reissueCardApplyService.page(reissueCardApplyDto);

        Map<String, Integer> nameMap = Maps.newHashMap();
        AsyncExportTaskDo asyncExportTaskDo = asyncExportTaskDomainService.findById(param.getTaskId());
        String empImageFolder;
        if (ExportTypeEnum.REISSUE_COOP_PROPERTY_CARD_APPLY_LIST_EXPORT.getType().equals(asyncExportTaskDo.getOpType())) {
            empImageFolder = "images-reissue-coop" + "-" + CodeUtils.getDateString() + "-" + CodeUtils.getRandomNumCode(4);
            asyncExportTaskDomainService.setExportImageFolder(param.getTaskId(), empImageFolder);
        } else if (ExportTypeEnum.REISSUE_PROP_PROPERTY_CARD_APPLY_LIST_EXPORT.getType().equals(asyncExportTaskDo.getOpType())) {
            empImageFolder = "images-reissue-property" + "-" + CodeUtils.getDateString() + "-" + CodeUtils.getRandomNumCode(4);
            asyncExportTaskDomainService.setExportImageFolder(param.getTaskId(), empImageFolder);
        } else {
            empImageFolder = "images-default" + "-" + CodeUtils.getDateString() + "-" + CodeUtils.getRandomNumCode(4);
            asyncExportTaskDomainService.setExportImageFolder(param.getTaskId(), empImageFolder);
        }
        asyncExportTaskDomainService.setExportImageFolder(param.getTaskId(), empImageFolder);

        if (CollectionUtils.isNotEmpty(pageModel.getList())) {
            pageModel.getList().forEach(item -> {
                if (StringUtils.isEmpty(item.getPhotoUrl())) {
                    log.info("下载{}失败，照片地址为空", item.getUserName());
                    return;
                }
                //由于拼音会重复，所以需要组建imageName
                String imageName = getImageName(item, nameMap);
                //处理对应的图片
                dealImageV2(item.getPhotoUrl(), imageName, empImageFolder);
                item.setImageName(imageName);
                log.info("下载{}成功", item.getUserName());
            });
            log.info("全部图片下载成功");
        }

        return pageModel.getList();
    }

    @Override
    public AsyncReissueCoopPropCardApplyExportExcelDto convert(ReissueCardApplyDto queryResult) {
        AsyncReissueCoopPropCardApplyExportExcelDto result = exportConverter.toReissueCoopPropertyCardApplyExportExcel(queryResult);
        // 拼音格式修改
        String surname = capitalizeFirstLetter(queryResult.getFirstNameEn());
        String name = capitalizeFirstLetter(queryResult.getLastNameEn());
        result.setFullPinyinName(name + " " + surname);
        // 图片名称
        String imageName = queryResult.getImageName();
        imageName = (imageName == null || imageName.isEmpty()) ? "" : imageName + ".jpg";
        result.setPhotoName(imageName);
        return result;
    }

    public static String capitalizeFirstLetter(String str) {
        return EmpCardApplyExportDataExecutor.capitalizeFirstLetter(str);
    }

    public void dealImageV2(String imageUrl, String imageName, String urlFolder) {
        CoopCardApplyExportDataExecutor.dealImageV2(imageUrl, imageName, urlFolder);
    }

    private String getImageName(ReissueCardApplyDto reissueCardApplyDto, Map<String, Integer> nameMap) {
        String imageName = "";
        String key = reissueCardApplyDto.getLastNameEn() + reissueCardApplyDto.getFirstNameEn();
        if (ObjectUtils.isNotEmpty(nameMap.get(key))) {
            Integer count = nameMap.get(key);
            nameMap.put(key, ++count);
            reissueCardApplyDto.setFullPinyinName(key + count);
            imageName = key + count;
        } else {
            nameMap.put(key, 0);
            reissueCardApplyDto.setFullPinyinName(key);
            imageName = key;
        }
        return imageName;
    }
}
