package com.mi.oa.ee.safety.application.impl.safety;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.mi.oa.ee.safety.application.converter.safety.SafetyCarrierGroupDtoConverter;
import com.mi.oa.ee.safety.application.converter.safety.SafetyClassDtoConverter;
import com.mi.oa.ee.safety.application.dto.safety.SafetyClassDto;
import com.mi.oa.ee.safety.application.errorcode.SafetyApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.common.event.model.SafetyExportEvent;
import com.mi.oa.ee.safety.application.service.common.event.model.SafetyImportEvent;
import com.mi.oa.ee.safety.application.service.safety.SafetyCarrierGroupService;
import com.mi.oa.ee.safety.application.service.safety.SafetySupplierEngineService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.ImportStatusEnum;
import com.mi.oa.ee.safety.common.enums.ImportTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.ExcelAttributeMapperEnum;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.enums.ExportStatusEnum;
import com.mi.oa.ee.safety.domain.enums.ExportTypeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.query.card.PermissionQuery;
import com.mi.oa.ee.safety.domain.service.*;
import com.mi.oa.ee.safety.infra.common.SafetyLogService;
import com.mi.oa.ee.safety.infra.errorcode.InfraErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.fds.FdsRemote;
import com.mi.oa.ee.safety.infra.repository.*;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.dto.UserInfoDto;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.ZonedDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/9 16:28
 */
@Slf4j
@Service
public class SafetyCarrierGroupServiceImpl extends SafetyLogService implements SafetyCarrierGroupService {

    @Autowired
    SafetyCarrierDomainService safetyCarrierDomainService;

    @Autowired
    SafetyCarrierGroupDomainService safetyCarrierGroupDomainService;

    @Autowired
    SafetyClassDomainService safetyClassDomainService;

    @Autowired
    SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Autowired
    SafetyCarrierGroupClassRepository safetyCarrierGroupClassRepository;

    @Autowired
    SafetyClassRepository safetyClassRepository;

    @Autowired
    SafetyCarrierGroupDtoConverter safetyCarrierGroupDtoConverter;

    @Autowired
    SafetyClassDtoConverter converter;

    @Resource
    SafetySupplierEngineService safetySupplierEngineService;

    @Resource
    IdmRemote idmRemote;

    @Resource
    AsyncExportTaskDomainService asyncExportTaskDomainService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    AsyncImportTaskDomainService asyncImportTaskDomainService;

    @Resource
    FdsRemote fdsRemote;

    @Override
    public SafetyClassDto getSafetyClassByClassCode(String classCode) {
        SafetyClassDo safetyClassDO = new SafetyClassDo();
        safetyClassDO.setClassCode(classCode);
        //填装其余的分类信息
        safetyClassDomainService.fillFullSafetyClassDoByClassCode(safetyClassDO);
        return converter.do2Dto(safetyClassDO, true);
    }

    @Override
    public List<SafetyCarrierGroupDto> getSafetyCarrierGroupByParentClassCode(String parentClassCode) {
        return Lists.newArrayList();
    }

    @Override
    public PageModel<SafetyCarrierGroupDto> listByGroupName(String groupName, Integer pageNum, Integer pageSize) {
        PageModel<SafetyCarrierGroupDo> pageModel =
                safetyCarrierGroupRepository.listByGroupName(groupName, pageNum, pageSize);
        return PageModel.build(converter.safetyCarrierGroupDoList2DtoList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public List<SafetyCarrierGroupDto> listByGroupCodes(List<String> groupCodes) {
        List<SafetyCarrierGroupDto> safetyCarrierGroupDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupCodes)) {
            List<SafetyCarrierGroupDo> list = safetyCarrierGroupRepository.getListByCarrierGroupCodes(groupCodes);
            safetyCarrierGroupDtos = converter.safetyCarrierGroupDoList2DtoList(list);
        }
        return safetyCarrierGroupDtos;
    }

    @Override
    public List<SafetyClassDto> getSafetyClassByParentClassCode(String parentClassCode) {
        List<SafetyClassDo> safetyClassByParentClassCode = safetyClassRepository
                .findSafetyClassByParentClassCode(parentClassCode);
        return converter.safetyClassDoList2DtoList(safetyClassByParentClassCode);
    }

    @Override
    public List<SafetyClassDto> getSafetyClassCondition(SafetyClassDto classDto) {
        SafetyClassDo safetyClassDO = converter.toDo(classDto);
        List<SafetyClassDo> list = safetyClassRepository.topListByConditions(safetyClassDO);
        safetyClassDomainService.fillSubSafetyClassForList(list);
        return converter.safetyClassDoList2DtoList(list);
    }

    @Override
    public List<SafetyClassDto> getSafetyClassCondition(List<String> classCodes) {
        List<SafetyClassDo> list = safetyClassRepository.topListByClassCodes(classCodes);
        return converter.safetyClassDoList2DtoList(list);
    }

    @Override
    public void saveOrUpdate(SafetyCarrierGroupDto safetyCarrierGroupDto) {
        SafetyCarrierGroupDo safetyCarrierGroupDo = safetyCarrierGroupDtoConverter.toDo(safetyCarrierGroupDto);
        if (safetyCarrierGroupDo.getId() == null) {
            safetyCarrierGroupDomainService.checkBeforeCreate(safetyCarrierGroupDo);
        } else {
            safetyCarrierGroupDomainService.checkBeforeUpdate(safetyCarrierGroupDo);
        }
        //填装需要处理的载体集与载体关系列表
        safetyCarrierGroupDomainService.fillBeforeSaveOrUpdate(safetyCarrierGroupDo);
        //id不存在 则保存 否则编辑更新
        safetyCarrierGroupRepository.save(safetyCarrierGroupDo);
        //同步安防载体集到霍尼
        safetySupplierEngineService.asyncSupplierCarrierGroupToSupplier();
    }

    @Override
    public void batchDelete(List<Long> carrierGroupIds) {
        if (CollectionUtils.isEmpty(carrierGroupIds)) {
            throw new BizException(SafetyApplicationErrorCodeEnum.CARRIER_GROUP_ID_LIST_IS_EMPTY);
        }
        //检查当前批量删除是否在使用中
        for (Long id : carrierGroupIds) {
            SafetyCarrierGroupDo safetyCarrierGroupDo = new SafetyCarrierGroupDo();
            safetyCarrierGroupDo.setId(id);
            safetyCarrierGroupDomainService.checkBeforeDelete(safetyCarrierGroupDo);
        }
        safetyCarrierGroupDomainService.batchDeleteByIds(carrierGroupIds);
        //同步安防载体集到霍尼
        safetySupplierEngineService.asyncSupplierCarrierGroupToSupplier();
    }

    @Override
    public void batchUpdate(SafetyCarrierGroupDto reqToDto) {
        if (CollectionUtils.isEmpty(reqToDto.getIdList())) {
            throw new BizException(SafetyApplicationErrorCodeEnum.CARRIER_GROUP_ID_LIST_IS_EMPTY);
        }

        //填装当前的城市ID
        SafetyCarrierGroupDo safetyCarrierGroupDo = new SafetyCarrierGroupDo();
        safetyCarrierGroupDo.setParkCode(reqToDto.getParkCode());
        safetyCarrierGroupDomainService.fillWithCityIdByParkCode(safetyCarrierGroupDo);
        reqToDto.setCityId(safetyCarrierGroupDo.getCityId());
        reqToDto.setProvinceId(String.valueOf(safetyCarrierGroupDo.getProvinceId()));
        reqToDto.setCountryId(String.valueOf(safetyCarrierGroupDo.getCountryId()));


        List<SafetyCarrierGroupDo> safetyCarrierGroupDoList = safetyCarrierGroupRepository.getListByIds(reqToDto.getIdList());
        safetyCarrierGroupDoList.forEach(item -> {
            item.setCityId(reqToDto.getCityId());
            item.setProvinceId(reqToDto.getProvinceId());
            item.setCountryId(reqToDto.getCountryId());
            item.setParkCode(reqToDto.getParkCode());
            item.setClassCode(reqToDto.getClassCode());
        });
        //更新当前权限组对应的园区，城市
        safetyCarrierGroupRepository.batchSaveOrUpdate(safetyCarrierGroupDoList);

        if (StringUtils.isNotEmpty(reqToDto.getClassCode())) {
            //删除对应历史分类
            List<String> carrierGroupCode = safetyCarrierGroupDoList.stream().map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
            safetyCarrierGroupClassRepository.batchDeleteByGroupCodes(carrierGroupCode);
            //添加对应的新分类
            safetyCarrierGroupClassRepository.batchInsertGroupClass(safetyCarrierGroupDoList);
        }
    }

    @Override
    public void updateOne(SafetyCarrierGroupDto reqToDto) {
        //填装当前的城市ID
        SafetyCarrierGroupDo safetyCarrierGroupDo = new SafetyCarrierGroupDo();
        safetyCarrierGroupDo.setParkCode(reqToDto.getParkCode());
        safetyCarrierGroupDomainService.fillWithCityIdByParkCode(safetyCarrierGroupDo);
        reqToDto.setCityId(safetyCarrierGroupDo.getCityId());
        reqToDto.setProvinceId(String.valueOf(safetyCarrierGroupDo.getProvinceId()));
        reqToDto.setCountryId(String.valueOf(safetyCarrierGroupDo.getCountryId()));

        List<Long> idList = Lists.newArrayList();
        idList.add(reqToDto.getId());
        List<SafetyCarrierGroupDo> listByIds = safetyCarrierGroupRepository.getListByIds(idList);
        // 因为只有一个，所以直接取第一个
        SafetyCarrierGroupDo item = listByIds.get(0);

        item.setCityId(reqToDto.getCityId());
        item.setProvinceId(reqToDto.getProvinceId());
        item.setCountryId(reqToDto.getCountryId());
        item.setParkCode(reqToDto.getParkCode());
        item.setClassCode(reqToDto.getClassCode());
        //修改状态
        item.setStatus(reqToDto.getStatus());

        List<SafetyCarrierGroupDo> safetyCarrierGroupDoList = Lists.newArrayList();
        safetyCarrierGroupDoList.add(item);

        //更新当前权限组对应的园区，城市
        safetyCarrierGroupRepository.batchSaveOrUpdate(safetyCarrierGroupDoList);

        if (StringUtils.isNotEmpty(reqToDto.getClassCode())) {
            //删除对应历史分类
            List<String> carrierGroupCode = safetyCarrierGroupDoList.stream().map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
            safetyCarrierGroupClassRepository.batchDeleteByGroupCodes(carrierGroupCode);
            //添加对应的新分类
            safetyCarrierGroupClassRepository.batchInsertGroupClass(safetyCarrierGroupDoList);
        }

    }

    @Override
    public PageVO<SafetyCarrierGroupDto> simplePageCondition(SafetyCarrierGroupDto safetyCarrierGroupDto) {
        SafetyCarrierGroupQuery query = safetyCarrierGroupDtoConverter.toQuery(safetyCarrierGroupDto);
        PageModel<SafetyCarrierGroupDo> pageModel = safetyCarrierGroupRepository.pageCondition(query);
        return PageVO.build(converter.safetyCarrierGroupDoList2DtoList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }


    @Override
    public SafetyCarrierGroupDto getCarrierGroup(String carrierGroupCode) {
        SafetyCarrierGroupDto safetyCarrierGroupDto = null;
        SafetyCarrierGroupDo safetyCarrierGroupDo = safetyCarrierGroupRepository.getCarrierGroup(carrierGroupCode);
        if (safetyCarrierGroupDo != null) {
            safetyCarrierGroupDomainService.fillSafetyCarrierGroupDo(safetyCarrierGroupDo);
            safetyCarrierGroupDto = safetyCarrierGroupDtoConverter.toDto(safetyCarrierGroupDo);
            if (CollectionUtils.isNotEmpty(safetyCarrierGroupDo.getSafetyCarrierDoList())) {
                safetyCarrierGroupDto.setCarrierNum(safetyCarrierGroupDo.getSafetyCarrierDoList().size());
            }
        }
        return safetyCarrierGroupDto;
    }

    @Override
    public PageModel<SafetyCarrierGroupDto> pageCondition(SafetyCarrierGroupDto safetyCarrierGroupDto) {
        SafetyCarrierGroupQuery query = safetyCarrierGroupDtoConverter.toQuery(safetyCarrierGroupDto);
        PageModel<SafetyCarrierGroupDo> pageModel = safetyCarrierGroupRepository.pageCondition(query);
        List<SafetyCarrierGroupDo> safetyCarrierGroupDos = safetyCarrierGroupDomainService
                .batchFillSafetyCarrierGroupDo(pageModel.getList());
        return PageModel.build(converter.safetyCarrierGroupDoList2DtoList(safetyCarrierGroupDos),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public void batchSaveGroups(MultipartFile file) {
        List<SafetyCarrierGroupDo> safetyCarrierGroupDos = Lists.newArrayList();
        parsingExcelForGroup(file, safetyCarrierGroupDos);
        safetyCarrierGroupDomainService.batchSaveGroups(safetyCarrierGroupDos);
    }

    @Override
    public void batchSaveCarrierGroups(MultipartFile file) {
        List<SafetyCarrierGroupCarrierDo> safetyCarrierGroupCarrierDos = Lists.newArrayList();
        parsingExcelForGoupCarrier(file, safetyCarrierGroupCarrierDos);
        safetyCarrierGroupDomainService.batchSaveCarrierGroups(safetyCarrierGroupCarrierDos);
    }

    @Override
    public void asyncExportCarrierGroup(String queryData) {
        AsyncExportTaskDo asyncExportTaskDo = new AsyncExportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initDO(queryData, asyncExportTaskDo, userInfoDto);

        // 导出任务保存到数据库
        asyncExportTaskDomainService.save(asyncExportTaskDo);
        // 发布导出事件
        eventPublisher.publishEvent(new SafetyExportEvent(asyncExportTaskDo, asyncExportTaskDo.getId()));
    }

    @Override
    public void asyncImportCarrierGroup(MultipartFile file) {
        // 异步导入
        // 保存信息到导入数据库
        AsyncImportTaskDo asyncImportTaskDo = new AsyncImportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initImportDO(file, asyncImportTaskDo, userInfoDto);

        asyncImportTaskDomainService.save(asyncImportTaskDo);

        // 发布导入事件
        eventPublisher.publishEvent(new SafetyImportEvent(asyncImportTaskDo, String.valueOf(asyncImportTaskDo.getBatchId())));
    }


    private void initImportDO(MultipartFile file, AsyncImportTaskDo asyncImportTaskDo, UserInfoDto userInfoDto) {
        asyncImportTaskDo.setOpTypeEnum(ImportTypeEnum.CARRIER_GROUP_IMPORT);
        asyncImportTaskDo.setOperator(userInfoDto.getUid());
        asyncImportTaskDo.setOperatorName(userInfoDto.getUserName());
        asyncImportTaskDo.setOpTime(ZonedDateTime.now());
        asyncImportTaskDo.setStatusEnum(ImportStatusEnum.TO_EXECUTE);
        // 导入文件名
        asyncImportTaskDo.setFileName(file.getOriginalFilename());
        //生成batchId
        asyncImportTaskDo.setBatchId(CodeUtils.getUUID());
        // 文件上传到云，获取url
        // file转ByteArrayInputStream
        try {
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";

            if (originalFilename != null && originalFilename.lastIndexOf('.') > 0) {
                // Extract the extension by substring from the last dot.
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf('.'));
            }
            byte[] bytes = file.getBytes();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
            String url = fdsRemote.uploadExcel(CodeUtils.getUUID() + fileExtension, byteArrayInputStream);
            asyncImportTaskDo.setUrl(url);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            asyncImportTaskDo.setStatusEnum(ImportStatusEnum.IMPORT_FAILED);
            asyncImportTaskDo.setMessage(InfraErrorCodeEnum.INFRA_FDS_UPLOAD_ERROR.getErrDesc());
            throw new BizException(InfraErrorCodeEnum.INFRA_FDS_UPLOAD_ERROR);
        }

    }

    private static void initDO(String queryData, AsyncExportTaskDo asyncExportTaskDo, UserInfoDto userInfoDto) {
        asyncExportTaskDo.setOpType(ExportTypeEnum.CARRIER_GROUP_EXPORT.getType());
        asyncExportTaskDo.setUid(userInfoDto.getUid());
        asyncExportTaskDo.setOperatorName(userInfoDto.getUserName());
        asyncExportTaskDo.setOpTime(ZonedDateTime.now());
        asyncExportTaskDo.setStatus(ExportStatusEnum.TO_EXECUTE.getStatus());
        asyncExportTaskDo.setProgress(SafetyConstants.EXPORT_MAX_PAGE_SIZE_INTEGER);
        asyncExportTaskDo.setParam(queryData);
    }

    @Override
    public PageModel<SafetyCarrierGroupDto> pageConditionV1(SafetyCarrierGroupDto carrierGroupDto) {
        PermissionQuery query = safetyCarrierGroupDtoConverter.toPermissionQuery(carrierGroupDto);
        PageModel<SafetyCarrierGroupDo> pageModel = safetyCarrierGroupRepository.pageGroupConditionListV3(query);
        List<SafetyCarrierGroupDo> safetyCarrierGroupDos = safetyCarrierGroupDomainService
                .batchFillSafetyCarrierGroupDo(pageModel.getList());
        return PageModel.build(converter.safetyCarrierGroupDoList2DtoList(safetyCarrierGroupDos),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    private void parsingExcelForGroup(MultipartFile file, List<SafetyCarrierGroupDo> safetyCarrierGroupDos) {
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Map<String, Object>> readAll;
            Map<String, String> map = new LinkedHashMap<>();
            buildExcelMap(map);
            reader.setHeaderAlias(map);
            readAll = reader.read(0, 1, reader.getRowCount());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(readAll)) {
                for (Map<String, Object> stringObjectMap : readAll) {
                    SafetyCarrierGroupDo safetyCarrierGroupDo = new SafetyCarrierGroupDo();
                    for (Map.Entry<String, Object> entry : stringObjectMap.entrySet()) {
                        if (ExcelAttributeMapperEnum.NULL_STRING.getTarget().equals(entry.getKey())) {
                            stringObjectMap.remove(entry.getKey(), entry.getValue());
                        }
                    }
                    BeanUtils.populate(safetyCarrierGroupDo, stringObjectMap);
                    safetyCarrierGroupDos.add(safetyCarrierGroupDo);
                }
            }
        } catch (IOException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.EXCEL_EXPORT_ERROR);
        } catch (IllegalAccessException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.ILLEGAL_ACCESS_ERROR);
        } catch (InvocationTargetException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.INVOCATION_TARGET);
        }
    }

    private void parsingExcelForGoupCarrier(MultipartFile file, List<SafetyCarrierGroupCarrierDo> safetyCarrierGroupCarrierDos) {
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Map<String, Object>> readAll;
            Map<String, String> map = new LinkedHashMap<>();
            buildExcelMapForGroupCarrier(map);
            reader.setHeaderAlias(map);
            readAll = reader.read(0, 1, reader.getRowCount());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(readAll)) {
                for (Map<String, Object> stringObjectMap : readAll) {
                    SafetyCarrierGroupCarrierDo safetyCarrierGroupCarrierDo = new SafetyCarrierGroupCarrierDo();
                    SafetyCarrierGroupDo safetyCarrierGroupDo = new SafetyCarrierGroupDo();
                    SafetyCarrierDo safetyCarrierDo = new SafetyCarrierDo();
                    for (Map.Entry<String, Object> entry : stringObjectMap.entrySet()) {
                        if (ExcelAttributeMapperEnum.NULL_STRING.getTarget().equals(entry.getKey())) {
                            stringObjectMap.remove(entry.getKey(), entry.getValue());
                        }
                    }
                    BeanUtils.populate(safetyCarrierGroupDo, stringObjectMap);
                    BeanUtils.populate(safetyCarrierDo, stringObjectMap);
                    safetyCarrierGroupCarrierDo.setSafetyCarrierDo(safetyCarrierDo);
                    safetyCarrierGroupCarrierDo.setSafetyCarrierGroupDo(safetyCarrierGroupDo);
                    safetyCarrierGroupCarrierDos.add(safetyCarrierGroupCarrierDo);
                }
            }
        } catch (IOException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.EXCEL_EXPORT_ERROR);
        } catch (IllegalAccessException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.ILLEGAL_ACCESS_ERROR);
        } catch (InvocationTargetException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.INVOCATION_TARGET);
        }
    }

    private void buildExcelMap(Map<String, String> map) {
        map.put(ExcelAttributeMapperEnum.CARRIER_GROUP_NAME.getSource(),
                ExcelAttributeMapperEnum.CARRIER_GROUP_NAME.getTarget());
        map.put(ExcelAttributeMapperEnum.GROUP_CLASS_NAME.getSource(), ExcelAttributeMapperEnum.GROUP_CLASS_NAME.getTarget());
        map.put(ExcelAttributeMapperEnum.CITY_ID.getSource(), ExcelAttributeMapperEnum.CITY_ID.getTarget());
        map.put(ExcelAttributeMapperEnum.PARK_CODE.getSource(), ExcelAttributeMapperEnum.PARK_CODE.getTarget());
        map.put(ExcelAttributeMapperEnum.SUPPLIER_ACCESS_CODE.getSource(),
                ExcelAttributeMapperEnum.SUPPLIER_ACCESS_CODE.getTarget());
        map.put(ExcelAttributeMapperEnum.SUPPLIER.getSource(), ExcelAttributeMapperEnum.SUPPLIER.getTarget());
    }

    private void buildExcelMapForGroupCarrier(Map<String, String> map) {
        map.put(ExcelAttributeMapperEnum.CARRIER_SERIAL.getSource(), ExcelAttributeMapperEnum.CARRIER_SERIAL.getTarget());
        map.put(ExcelAttributeMapperEnum.GROUP_SERIAL.getSource(), ExcelAttributeMapperEnum.GROUP_SERIAL.getTarget());
    }
}
