package com.mi.oa.ee.safety.application.impl.safety;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.service.safety.SafetyCardToolService;
import com.mi.oa.ee.safety.application.service.safety.SafetySupplierEngineService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.dto.SafetyCardToolDto;
import com.mi.oa.ee.safety.common.enums.card.CardApplyTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierCodeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.domain.ability.CardPersonInfoAbility;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.CardPersonInfoDo;
import com.mi.oa.ee.safety.domain.model.CardPsParkAdminDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumSupplierDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.model.UserBehaviorRecordDo;
import com.mi.oa.ee.safety.domain.service.CardApplyDomainService;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyRightDomainService;
import com.mi.oa.ee.safety.infra.remote.sdk.HrodSdk;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.CardParkAdminRepository;
import com.mi.oa.ee.safety.infra.repository.CardPersonInfoRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyPersonMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.ee.safety.infra.repository.UserBehaviorRecordRepository;
import com.mi.oa.ee.safety.infra.repository.query.SafetyPersonMediumQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/4/24 15:31
 */
@Slf4j
@Service
public class SafetyToolServiceImpl implements SafetyCardToolService {

    @Autowired
    SafetyRightDomainService safetyRightDomainService;

    @Autowired
    SafetySupplierEngineService safetySupplierEngineService;

    @Autowired
    SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Autowired
    SafetyRightRepository safetyRightRepository;

    @Autowired
    CardInfoRepository cardInfoRepository;

    @Autowired
    CardDomainService cardDomainService;

    @Resource
    private HrodSdk hrodSdk;

    @Resource
    private CardParkAdminRepository cardParkAdminRepository;

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Resource
    private SafetyPersonDomainService safetyPersonDomainService;

    @Resource
    private CardApplyDomainService cardApplyDomainService;

    @Resource
    private CardPersonInfoRepository cardPersonInfoRepository;

    @Resource
    private UserBehaviorRecordRepository userBehaviorRecordRepository;

    @Resource
    private CardPersonInfoAbility cardPersonInfoAbility;

    @Override
    public void syncPersonMedium(SafetyCardToolDto safetyCardToolDto) {
        CardInfoDo cardInfo = cardDomainService.getCardByNums(safetyCardToolDto,
                Lists.newArrayList(CardStatusEnum.USING));
        if (!ObjectUtils.isEmpty(cardInfo) && !StringUtils.isEmpty(cardInfo.getMediumCode())) {
            safetyPersonMediumRepository.syncPersonMediumByCard(cardInfo, safetyCardToolDto.getIsDelete());
        } else {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_NOT_EXIST);
        }
    }

    @Override
    public void syncRight(SafetyCardToolDto safetyCardToolDto) {
        CardInfoDo cardInfo = cardDomainService.getCardByNums(safetyCardToolDto,
                Lists.newArrayList(CardStatusEnum.USING));
        if (!ObjectUtils.isEmpty(cardInfo) && !StringUtils.isEmpty(cardInfo.getMediumCode())) {
            SafetyRightDo safetyRightDo = new SafetyRightDo();
            safetyRightDo.setMediumCode(cardInfo.getMediumCode());
            safetyRightDo.setIsDeleted(Long.valueOf(safetyCardToolDto.getIsDelete()));
            safetyRightRepository.syncRightByMediumCode(safetyRightDo);
        } else {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_NOT_EXIST);
        }
    }

    @Override
    public void updateHollyWellTime(SafetyCardToolDto safetyCardToolDto) {
        List<String> uidList = Lists.newArrayList();
        List<String> meidumCodeList = Lists.newArrayList();
        if (!StringUtils.isEmpty(safetyCardToolDto.getMediumEncryptCode()) || !StringUtils.isEmpty(safetyCardToolDto.getMediumPhysicsCode())) {
            CardInfoDo cardInfo = cardDomainService.getCardByNums(safetyCardToolDto,
                    Lists.newArrayList(CardStatusEnum.USING));
            if (cardInfo != null) {
                uidList.add(cardInfo.getUid());
                meidumCodeList.add(cardInfo.getMediumCode());
            }
        } else {
            List<CardInfoDo> cardInfoDoList = cardInfoRepository.getNeedUpdateTimeList();
            if (CollectionUtils.isNotEmpty(cardInfoDoList)) {
                uidList.addAll(cardInfoDoList.stream().map(CardInfoDo::getUid).collect(Collectors.toList()));
                meidumCodeList.addAll(cardInfoDoList.stream().map(CardInfoDo::getMediumCode).collect(Collectors.toList()));
            }
        }

        //是否有需要同步的人员列表
        if (CollectionUtils.isNotEmpty(uidList)) {
            log.info("----- updateHollyWellTime uidList size : {}", uidList.size());
            SafetyPersonMediumQuery safetyPersonMediumQuery = new SafetyPersonMediumQuery();
            safetyPersonMediumQuery.setUidList(uidList);
            safetyPersonMediumQuery.setMediumCodeList(meidumCodeList);
            safetyPersonMediumQuery.setIsDeleted(OAUCFCommonConstants.LONG_ZERO);
            List<SafetyPersonMediumDo> safetyPersonMediumDoList = safetyPersonMediumRepository.queryListByCondition(safetyPersonMediumQuery);
            if (CollectionUtils.isNotEmpty(safetyPersonMediumDoList)) {
                safetyPersonMediumDoList.stream().forEach(item -> {
                    item.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
                    item.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                    item.setUpdateUser("updateHollyWellTime");
                    item.setUpdateTime(ZonedDateTime.now());
                });
                log.info("----- updateHollyWellTime safetyPersonMediumDoList size : {}", safetyPersonMediumDoList.size());
                safetyPersonMediumRepository.saveOrUpdate(safetyPersonMediumDoList);
                //同步人和介质的关系到供应商
                safetySupplierEngineService.syncSafetyPersonMediumToSupplierByUidList(uidList, null);
            }
        }

    }

    @Override
    public List<Map<String, String>> batchCorrectAddressAndParkByUserName(List<String> userNameList) {
        if (CollectionUtils.isEmpty(userNameList)) {
            return null;
        }
        List<Map<String, String>> result = Lists.newArrayList();
        List<CardApplyDo> cardApplyDoList = Lists.newArrayList();
        userNameList.forEach(userName -> {
            //获取人事报道地址
            PersonInfoModel personInfoModel = hrodSdk.getLocationReportAddressByAccount(userName);
            if (ObjectUtils.isEmpty(personInfoModel) || StringUtils.isEmpty(personInfoModel.getReportAddressId())) {
                Map<String, String> map = Maps.newHashMap();
                map.put(userName, "未查到对应报道地址");
                result.add(map);
            } else {
                CardPsParkAdminDo cardPsParkAdminDo = cardParkAdminRepository.findOneByReportAddressId(personInfoModel.getReportAddressId());
                if (ObjectUtils.isEmpty(cardPsParkAdminDo) || ObjectUtils.isEmpty(cardPsParkAdminDo.getCityId())
                        || StringUtils.isEmpty(cardPsParkAdminDo.getParkCode())) {
                    Map<String, String> map = Maps.newHashMap();
                    map.put(userName, "园区管理未维护对应全部数据");
                    result.add(map);
                } else {
                    CardApplyDo cardApplyDo = new CardApplyDo();
                    cardApplyDo.setCityId(cardPsParkAdminDo.getCityId());
                    cardApplyDo.setPartnerAccount(userName);
                    cardApplyDo.setParkCode(cardPsParkAdminDo.getParkCode());
                    cardApplyDoList.add(cardApplyDo);
                }
            }
        });
        cardApplyRepository.batchUpdateByUsername(cardApplyDoList);
        return result;
    }

    @Override
    public List<Map<String, String>> batchUpdatePersonInfo(List<String> userNameList) {
        if (CollectionUtils.isEmpty(userNameList)) {
            return null;
        }
        List<Map<String, String>> result = Lists.newArrayList();

        userNameList.forEach(userName -> {
            SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
            safetyPersonDo.setUserName(userName);
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
            if (StringUtils.isNotEmpty(safetyPersonDo.getEmployeeId())
                    && StringUtils.isNotEmpty(safetyPersonDo.getUid())) {
                //获取新系统制卡单信息
                CardApplyDo cardApplyDo = new CardApplyDo();
                cardApplyDo.setApplyType(CardApplyTypeEnum.EMPLOYEE_CARD_APPLY.getCode());
                cardApplyDo.setUid(safetyPersonDo.getUid());
                cardApplyDomainService.fillCardApplyByUidAndType(cardApplyDo);
                if (Objects.nonNull(cardApplyDo.getId())) {
                    //更新申请单
                    cardApplyDo.setEmpNo(safetyPersonDo.getEmployeeId());
                    cardApplyDo.setEmpType(safetyPersonDo.getAccountType().getValue());
                    //更新人员信息表
                    CardPersonInfoDo cardPersonInfoDo = new CardPersonInfoDo();
                    cardPersonInfoDo.setUid(safetyPersonDo.getUid());
                    cardPersonInfoDo.setEmployeeNo(safetyPersonDo.getEmployeeId());
                    cardApplyRepository.updateById(cardApplyDo);
                    cardPersonInfoRepository.updatePersonInfoByUid(cardPersonInfoDo);
                } else {
                    Map<String, String> map = Maps.newHashMap();
                    map.put(userName, "未查到对应申请单");
                    result.add(map);
                }
            } else {
                Map<String, String> map = Maps.newHashMap();
                map.put(userName, "未查到对应人员信息");
                result.add(map);
            }
        });
        return result;
    }

    @Override
    public void refreshAvatarCache(List<String> userNameList) {
        if (CollectionUtils.isEmpty(userNameList)) {
            return;
        }
        userNameList.forEach(userName -> {
            if (StringUtils.isNotEmpty(userName)) {
                RedisUtils.delete(userName);
                SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
                safetyPersonDo.setUserName(userName);
                safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
                if (StringUtils.isNotEmpty(safetyPersonDo.getUid())) {
                    RedisUtils.delete(safetyPersonDo.getUid());
                }
            }
        });
    }

    @Override
    public List<Map<String, String>> batchInsertAppWhite(List<String> userNameList) {
        if (CollectionUtils.isEmpty(userNameList)) {
            return null;
        }
        List<Map<String, String>> result = Lists.newArrayList();
        userNameList.forEach(userName -> {
            SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
            safetyPersonDo.setUserName(userName);
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
            if (StringUtils.isNotEmpty(safetyPersonDo.getUid())) {
                UserBehaviorRecordDo userBehaviorRecordDo = new UserBehaviorRecordDo();
                userBehaviorRecordDo.setUid(safetyPersonDo.getUid());
                userBehaviorRecordDo.setBehaviorCode(SafetyConstants.Common.LARK_WHITE_LIST);
                userBehaviorRecordDo.setBehaviorValue(SafetyConstants.Common.LARK_WHITE_LIST_FLAG);
                userBehaviorRecordRepository.addUserBehaviorRecord(userBehaviorRecordDo);
            } else {
                Map<String, String> map = Maps.newHashMap();
                map.put(userName, "未查到对应人员信息");
                result.add(map);
            }
        });
        return result;
    }

    @Override
    public List<Map<String, String>> batchInsertPersonInfo(List<String> userNameList) {
        if (CollectionUtils.isEmpty(userNameList)) {
            return null;
        }
        List<Map<String, String>> result = Lists.newArrayList();
        userNameList.forEach(userName -> {
            SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
            safetyPersonDo.setUserName(userName);
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
            if (StringUtils.isNotEmpty(safetyPersonDo.getUid())) {
                CardPersonInfoDo cardPersonInfoDo = new CardPersonInfoDo();
                cardPersonInfoDo.setUid(safetyPersonDo.getUid());
                cardPersonInfoDo.setDisplayName(safetyPersonDo.getDisplayName());
                cardPersonInfoDo.setPinyinFullName(safetyPersonDo.getPinyinName());
                cardPersonInfoDo.setUserName(safetyPersonDo.getUserName());
                cardPersonInfoDo.setEmployeeNo(safetyPersonDo.getEmployeeId());
                cardPersonInfoDo.setCompanyName(safetyPersonDo.getCompanyName());
                cardPersonInfoDo.setPhotoUrl(safetyPersonDo.getAvatarUrl());
                cardPersonInfoDo.setResponsible(safetyPersonDo.getResponsible());
                cardPersonInfoDo.setZktecoEmployeeId(cardPersonInfoAbility.genZktecoEmployeeId());
                cardPersonInfoRepository.addPersonInfo(cardPersonInfoDo);
            } else {
                Map<String, String> map = Maps.newHashMap();
                map.put(userName, "未查到对应人员信息");
                result.add(map);
            }
        });
        return result;
    }

    @Override
    @Async("asyncServiceExecutor")
    public void initPersonMediumSupplier() {
        SafetyPersonMediumQuery safetyPersonMediumQuery = new SafetyPersonMediumQuery();
        safetyPersonMediumQuery.setPageNum(1L);
        safetyPersonMediumQuery.setPageSize(100L);
        safetyPersonMediumQuery.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
        int count = 0;
        int recycleCount = 0;
        List<SafetyPersonMediumDo> safetyPersonMediumDos;
        do {
            recycleCount++;
            PageModel<SafetyPersonMediumDo> pageList =
                    safetyPersonMediumRepository.pageList(safetyPersonMediumQuery);
            safetyPersonMediumDos = pageList.getList();
            if (CollectionUtils.isNotEmpty(safetyPersonMediumDos)) {
                List<SafetyPersonMediumSupplierDo> safetyPersonMediumSupplierDoList =
                        batchBuildPersonMediumSupplier(safetyPersonMediumDos);
                //调整为批量插入
                try {
                    safetyPersonMediumRepository.batchInsertOrUpdate(safetyPersonMediumSupplierDoList);
                } catch (Exception e) {
                    log.error("initPersonMediumSupplier save error. batch:{}", safetyPersonMediumSupplierDoList, e);
                }
                count += safetyPersonMediumDos.size();
                safetyPersonMediumQuery.setPageNum(safetyPersonMediumQuery.getPageNum() + 1);
            }
            log.info("recycle count is:{}, current init size is:{}", recycleCount, count);
        } while (CollectionUtils.isNotEmpty(safetyPersonMediumDos));
        log.info("initPersonMediumSupplier init person medium supplier data size: {}", count);
    }

    private List<SafetyPersonMediumSupplierDo> batchBuildPersonMediumSupplier(List<SafetyPersonMediumDo> safetyPersonMediumDos) {
        List<SafetyPersonMediumSupplierDo> safetyPersonMediumSupplierDoList = Lists.newArrayList();
        safetyPersonMediumDos.forEach(item -> {
            for (String supplierCode : SafetySupplierCodeEnum.findNeedSyncSupplierCodeList()) {
                SafetyPersonMediumSupplierDo safetyPersonMediumSupplierDo = new SafetyPersonMediumSupplierDo();
                safetyPersonMediumSupplierDo.setUid(item.getUid());
                safetyPersonMediumSupplierDo.setPersonMediumId(item.getId());
                safetyPersonMediumSupplierDo.setMediumCode(item.getMediumCode());
                safetyPersonMediumSupplierDo.setSupplierCode(supplierCode);
                safetyPersonMediumSupplierDo.setSyncStatus(item.getSyncStatus());
                safetyPersonMediumSupplierDoList.add(safetyPersonMediumSupplierDo);
            }
        });
        return safetyPersonMediumSupplierDoList;
    }
}
