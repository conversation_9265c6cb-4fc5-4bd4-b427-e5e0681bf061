package com.mi.oa.ee.safety.application.impl.safety;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.converter.vistor.ApplyDtoConverter;
import com.mi.oa.ee.safety.application.dto.visitor.ApplyDto;
import com.mi.oa.ee.safety.application.errorcode.VisitorApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.safety.SafetyUmsNotifyService;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.dto.SafetyUmsConfigDto;
import com.mi.oa.ee.safety.common.dto.SafetyUmsNotifyDto;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsSendStatusEnum;
import com.mi.oa.ee.safety.common.enums.visitor.VisitorApplyTypeEnum;
import com.mi.oa.ee.safety.common.enums.visitor.VisitorInfoVisitTypeEnum;
import com.mi.oa.ee.safety.common.enums.visitor.VisitorParkingStatusEnum;
import com.mi.oa.ee.safety.common.utils.FreeMarkerUtil;
import com.mi.oa.ee.safety.domain.model.VisitorApplyDO;
import com.mi.oa.ee.safety.domain.model.VisitorParkParkingConfigDo;
import com.mi.oa.ee.safety.domain.service.SafetyConfigUserDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyUmsNotifyDomainService;
import com.mi.oa.ee.safety.domain.service.VisitorApplyDomainService;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.UmsSdk;
import com.mi.oa.ee.safety.infra.repository.SafetyUmsNotifyRepository;
import com.mi.oa.ee.safety.infra.repository.VisitorParkParkingConfigRepository;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.message.MsgUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 安防中台消息处理
 *
 * <AUTHOR>
 * @date 2022/8/26 20:43
 */
@Slf4j
@Service
public class SafetyUmsNotifyServiceImpl implements SafetyUmsNotifyService {

    @Autowired
    SafetyUmsNotifyRepository safetyUmsNotifyRepository;

    @Autowired
    SafetyUmsNotifyDomainService safetyUmsNotifyDomainService;

    @Autowired
    private VisitorParkParkingConfigRepository visitorParkParkingConfigRepository;

    @Autowired
    ApplyDtoConverter applyDtoConverter;

    @Autowired
    SafetyConfigUserDomainService configUserDomainService;

    @Autowired
    VisitorApplyDomainService visitorApplyDomainService;

    @Autowired
    UmsSdk umsSdk;

    @Autowired
    IdmSdk idmSdk;

    @Autowired
    IdmRemote idmRemote;

    @Override
    public List<SafetyUmsNotifyDto> getNowNeedSendNotify(String appCode, ZonedDateTime nowTime) {
        return safetyUmsNotifyRepository.getNowNeedSendNotify(appCode, nowTime);
    }

    @Override
    public Map<Long, SafetyUmsConfigDto> getAllSafetyUmsConfig(String appCode) {
        Map<Long, SafetyUmsConfigDto> map = Maps.newHashMap();
        List<SafetyUmsConfigDto> configDTOS = safetyUmsNotifyRepository.getAllSafetyUmsConfig(appCode);
        if (CollectionUtils.isNotEmpty(configDTOS)) {
            map = configDTOS.stream().collect(Collectors.toMap(SafetyUmsConfigDto::getId, item -> item));
        }
        return map;
    }

    @Override
    public SafetyUmsConfigDto getSafetyUmsConfigById(Long id) {
        return safetyUmsNotifyRepository.getSafetyUmsConfigById(id);
    }

    @Override
    public String preview(ApplyDto applyDto) {
        VisitorApplyDO applyDo = applyDtoConverter.toDo(applyDto);
        if (VisitorApplyTypeEnum.OVERSEAS.getCode().equals(applyDo.getApplyType())) {
            throw new BizException(VisitorApplicationErrorCodeEnum.OVERSEAS_APPLY_NOT_SUPPORT_SMS_PREVIEW);
        }
        // 临时驻场数据完成性校验
        visitorApplyDomainService.checkShortOnsiteData(applyDo);
        SafetyUmsConfigEnum umsEnum;
        if (VisitorApplyTypeEnum.SHORT_ONSITE.getCode().equals(applyDo.getApplyType())) {
            umsEnum = SafetyUmsConfigEnum.SHORT_ONSITE_APPLY_SEND_SMS;
        } else {
            if (ObjectUtil.isNotEmpty(applyDo.getVisitorInfos())
                    && ObjectUtil.isNotEmpty(applyDo.getVisitorInfos().get(0).getPlateNumber())) {
                umsEnum = SafetyUmsConfigEnum.APPLY_PARKING_SEND_SMS;
            } else {
                umsEnum = SafetyUmsConfigEnum.APPLY_SEND_SMS;
            }
        }
        SafetyUmsConfigDto safetyUmsConfigDto = safetyUmsNotifyRepository.getSafetyUmsConfigById(umsEnum.getCode());
        visitorApplyDomainService.fillApplicantInfo(applyDo);
        //3.获取接待人信息
        visitorApplyDomainService.fillReceiverInfo(applyDo);
        //填装原因信息
        visitorApplyDomainService.fillVisitorReasonDO(applyDo);
        VisitorInfoVisitTypeEnum visitTypeEnum = configUserDomainService.isExecutive(idmRemote.getLoginUid())
                ? VisitorInfoVisitTypeEnum.EXECUTIVE
                : VisitorInfoVisitTypeEnum.NORMAL;
        applyDo.setParkingConfig(visitorParkParkingConfigRepository.find(VisitorParkParkingConfigDo.builder()
                .parkCode(applyDo.getParkCode())
                .visitType(visitTypeEnum.getCode())
                .parkingStatus(VisitorParkingStatusEnum.ENABLED.getCode())
                .build()));
        // 填充园区信息
        visitorApplyDomainService.loadParkInfo(applyDo);
        Map<String, Object> paramMap = safetyUmsNotifyDomainService.buildVisitorParamMap(applyDo, umsEnum);

        return FreeMarkerUtil.parseFtlContent(safetyUmsConfigDto.getTemplateContent(), paramMap);
    }

    @Override
    public List<SafetyUmsNotifyDto> sendNotify(List<SafetyUmsNotifyDto> notifyDTOS,
                                               SafetyUmsConfigDto safetyUmsConfigDTO) {
        List<SafetyUmsNotifyDto> errorList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(notifyDTOS)) {
            List<List<SafetyUmsNotifyDto>> notifyLists = Lists.partition(notifyDTOS, 99);
            for (List<SafetyUmsNotifyDto> subNotifyList : notifyLists) {
                List<MsgUser> msgUsers = dtoToModel(subNotifyList, safetyUmsConfigDTO.getMessageType(),
                        safetyUmsConfigDTO.getTemplateContent(), errorList);
                if (CollectionUtils.isNotEmpty(msgUsers)) {
                    boolean reFlag = umsSdk.sendUms(msgUsers, safetyUmsConfigDTO.getMessageType(),
                            safetyUmsConfigDTO.getBotBizId(), safetyUmsConfigDTO.getTemplateBizId());
                    if (!reFlag) {
                        subNotifyList.forEach(item -> item.setSendStatus(
                                SafetyUmsSendStatusEnum.SEND_UMS_ERROR.getStatus()));
                        errorList.addAll(subNotifyList);
                    }
                }
            }
        }
        return errorList;
    }

    @Override
    public void updateSendStatus(List<Long> ids, Integer sendStatus) {
        safetyUmsNotifyRepository.batchUpdateSendStatus(ids, sendStatus);
    }

    private List<MsgUser> dtoToModel(List<SafetyUmsNotifyDto> notifyDTOS, Integer msgType,
                                     String tempContent, List<SafetyUmsNotifyDto> errorList) {
        List<MsgUser> msgUsers = Lists.newArrayList();
        Map<String, String> receiverMap = buildReceiver(notifyDTOS, msgType);
        for (SafetyUmsNotifyDto notify : notifyDTOS) {
            try {
                //获取真实的receiver
                String realReceiver = receiverMap.get(notify.getReceiver());
                if (StringUtils.isNotEmpty(realReceiver)) {
                    Map<String, Object> params = JacksonUtils.json2Bean(notify.getParams(), Map.class);
                    //如果配置表中存在模板，以配置表中模板为主，并默认UMS中配置的是content!
                    if (StringUtils.isNotEmpty(tempContent)) {
                        String nowContent = FreeMarkerUtil.parseFtlContent(tempContent, params);
                        //重新赋值
                        params = Maps.newHashMap();
                        params.put("content", nowContent);
                    }
                    MsgUser msgUser = umsSdk.buildMsgUser(realReceiver, msgType, params);
                    msgUsers.add(msgUser);
                } else {
                    notify.setSendStatus(SafetyUmsSendStatusEnum.SEND_NO_RECEIVER.getStatus());
                    errorList.add(notify);
                    log.error("-----error buildMsgUser no receiver : {}", JacksonUtils.bean2Json(notify));
                }
            } catch (BizException e) {
                log.error("-----error buildMsgUser : {}", JacksonUtils.bean2Json(notify));
                notify.setSendStatus(SafetyUmsSendStatusEnum.SEND_ERROR.getStatus());
                errorList.add(notify);
            }
        }
        return msgUsers;
    }

    /**
     * 转换真实的receiver
     *
     * @param notifyDTOS
     * @param msgType
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2022/8/29 16:37
     */
    private Map<String, String> buildReceiver(List<SafetyUmsNotifyDto> notifyDTOS, Integer msgType) {
        Map<String, String> receiverMap = Maps.newHashMap();
        List<String> uids = Lists.newArrayList();
        for (SafetyUmsNotifyDto notify : notifyDTOS) {
            if (umsSdk.checkReceiverIsUid(notify.getReceiver())) {
                uids.add(notify.getReceiver());
            } else {
                if (StringUtils.isNotEmpty(notify.getReceiver())) {
                    receiverMap.put(notify.getReceiver(), notify.getReceiver());
                }
            }
        }
        //处理所有uid的数据
        if (CollectionUtils.isNotEmpty(uids)) {
            List<PersonInfoModel> reList = idmSdk.getUserInfosByUids(uids);
            if (CollectionUtils.isNotEmpty(reList)) {
                for (PersonInfoModel accountModel : reList) {
                    if (ObjectUtils.isEmpty(accountModel) || StringUtils.isEmpty(accountModel.getUid())) {
                        log.info("-----error buildReceiver no uid : {}", JacksonUtils.bean2Json(accountModel));
                        continue;
                    }
                    if (MessageChannelEnum.MI_WORK.getType() == msgType.byteValue()) {
                        //飞书的时候填装账号
                        receiverMap.put(accountModel.getUid(), accountModel.getAccountName());
                    } else if (MessageChannelEnum.EMAIL.getType() == msgType.byteValue()) {
                        //邮件的时候填装邮箱,优先填装公司邮箱
                        String email = StringUtils.isBlank(accountModel.getEmail())
                                ? accountModel.getPersonalEmail() : accountModel.getEmail();
                        receiverMap.put(accountModel.getUid(), email);
                    } else if (MessageChannelEnum.SMS.getType() == msgType.byteValue()) {
                        //短信的时候填装手机号
                        receiverMap.put(accountModel.getUid(), accountModel.getMobile());
                    }
                }
            }
        }
        return receiverMap;
    }

}
