package com.mi.oa.ee.safety.application.impl.safety.expire;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mi.oa.ee.safety.common.dto.SafetySyncDto;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyModelTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierCodeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierGroupRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.ee.safety.infra.repository.query.CardInfoQuery;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 安防权限有效期处理
 *
 * <AUTHOR>
 * @date 2023/6/14 17:22
 */
@Slf4j
@Service
public class SafetyRightExpireDataService extends SafetyExpireDataAbstractFactory<SafetyRightDo> {

    @Resource
    SafetyRightRepository safetyRightRepository;

    @Resource
    private CardInfoRepository cardInfoRepository;

    @Resource
    private SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @NacosValue(value = "${card.expire.parks}", autoRefreshed = true)
    private String needExpireParkList;

    @Override
    protected void doExpireData(List<SafetyRightDo> allSupplierDoList, String supplierCode, SafetySyncDto<SafetyRightDo> safetySyncDto) {
        if (CollectionUtils.isNotEmpty(allSupplierDoList)) {
            Map<Integer, List<SafetyRightDo>> safetyRightDoMap =
                    allSupplierDoList.stream().collect(Collectors.toMap(SafetyRightDo::getSyncStatus,
                            p -> new ArrayList<>(Arrays.asList(p)), (oldList, newList) -> {
                                oldList.addAll(newList);
                                return oldList;
                            }));
            //更新同步状态
            for (Map.Entry<Integer, List<SafetyRightDo>> entry : safetyRightDoMap.entrySet()) {
                SafetySyncStatusEnum nowEnum = SafetySyncStatusEnum.WAIT_SYNC;
                if (!SafetySyncStatusEnum.SUCCESS_SYNC.getCode().equals(entry.getKey())
                        || SafetySupplierCodeEnum.VISITOR.getSupplierCode().equals(supplierCode)) {
                    //如果状态是未同步成功，或者是访问码，则过期后直接改成不需同步
                    nowEnum = SafetySyncStatusEnum.NOT_NEED_SYNC;
                }
                List<Long> idList = entry.getValue().stream().map(SafetyRightDo::getId).collect(Collectors.toList());
                //更新状态为待同步
                safetyRightRepository.updateSyncStatusByIdList(idList, nowEnum, "ExpireData");
                //删除当前记录
                safetyRightRepository.deleteByIdList(idList);
            }

        }
    }

    @Override
    protected List<SafetyRightDo> prePareDoList(List<SafetyRightDo> allDoList) {
        List<SafetyRightDo> needDealRightList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(allDoList)) {
            //数据准备
            Map<String, List<SafetyRightDo>> mediumCodeMap =
                    allDoList.stream().collect(Collectors.groupingBy(SafetyRightDo::getMediumCode));
            Set<String> mediumCodeList = allDoList.stream()
                    .map(SafetyRightDo::getMediumCode)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());
            Set<String> groupCodeList = allDoList.stream()
                    .map(SafetyRightDo::getCarrierGroupCode)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());
            //获取所有卡信息
            CardInfoQuery cardInfoQuery = new CardInfoQuery();
            cardInfoQuery.setMediumCodeList(new ArrayList<>(mediumCodeList));
            List<CardInfoDo> cardInfoDoList = cardInfoRepository.getListByConditions(cardInfoQuery);
            //获取所有权限组信息
            SafetyCarrierGroupQuery carrierGroupQuery = new SafetyCarrierGroupQuery();
            carrierGroupQuery.setCarrierGroupCodeList(new ArrayList<>(groupCodeList));
            List<SafetyCarrierGroupDo> safetyCarrierGroupDos = safetyCarrierGroupRepository.listByConditions(carrierGroupQuery);
            //数据筛选
            filterData(needDealRightList, mediumCodeMap, cardInfoDoList, safetyCarrierGroupDos);
        }
        return needDealRightList;
    }

    private void filterData(List<SafetyRightDo> needDealRightList, Map<String, List<SafetyRightDo>> mediumCodeMap,
                            List<CardInfoDo> cardInfoDoList, List<SafetyCarrierGroupDo> safetyCarrierGroupDos) {
        //过滤合作卡过期权限
        if (CollectionUtils.isNotEmpty(cardInfoDoList)) {
            for (CardInfoDo cardInfoDo : cardInfoDoList) {
                if (!CardTypeEnum.COOPERATION_CARD.getNumber().equals(cardInfoDo.getCardType())) {
                    needDealRightList.addAll(mediumCodeMap.get(cardInfoDo.getMediumCode()));
                }
            }
        }
        //过滤非灰度园区权限
        if (CollectionUtils.isNotEmpty(safetyCarrierGroupDos) && CollectionUtils.isNotEmpty(needDealRightList)) {
            List<String> parkCodeList = Arrays.asList(needExpireParkList.split(","));
            Map<String, SafetyCarrierGroupDo> groupCodeMap =
                    safetyCarrierGroupDos.stream().collect(Collectors.toMap(SafetyCarrierGroupDo::getCarrierGroupCode,
                    Function.identity(), (k1, k2) -> k1));
            needDealRightList.removeIf(item -> {
                SafetyCarrierGroupDo safetyCarrierGroupDo = groupCodeMap.get(item.getCarrierGroupCode());
                return !parkCodeList.contains(safetyCarrierGroupDo.getParkCode());
            });
        }
    }

    @Override
    protected String buildKeyByDo(SafetyRightDo doItem) {
        return doItem.getUid() + "-" + doItem.getMediumCode() + "-" + doItem.getCarrierGroupCode() + "-" + doItem.getIsDeleted();
    }

    @Override
    protected List<SafetyRightDo> getNowExpireDoList(SafetySyncDto<SafetyRightDo> safetySyncDto) {
        Long expireTime = null;
        if (safetySyncDto.getEndTime() != null) {
            expireTime = safetySyncDto.getEndTime().toEpochSecond();
        } else {
            expireTime = ZonedDateTime.now().toEpochSecond();
        }
        return safetyRightRepository.findNotDeletedExpireList(expireTime, SafetySupplierCodeEnum.CARD.getNeedSyncSupplierList());
    }

    @Override
    protected SafetyModelTypeEnum getSafetyModelType() {
        return SafetyModelTypeEnum.RIGHT;
    }

    @Override
    protected String getSupplierCodeByDo(SafetyRightDo doItem) {
        return doItem.getSupplierCode();
    }
}
