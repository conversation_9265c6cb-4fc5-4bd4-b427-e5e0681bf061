package com.mi.oa.ee.safety.application.impl.safety.sync;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.application.converter.safety.SafetyCarrierGroupDtoConverter;
import com.mi.oa.ee.safety.common.dto.SafetySyncDto;
import com.mi.oa.ee.safety.common.enums.safety.SafetyModelTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierCodeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.service.SafetyCarrierGroupDomainService;
import com.mi.oa.ee.safety.infra.remote.model.supplier.BaseQuery;
import com.mi.oa.ee.safety.infra.remote.model.supplier.SupplierCarrierGroupDto;
import com.mi.oa.ee.safety.infra.remote.sdk.supplier.SafetySupplierStrategySdk;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierGroupRepository;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/4 17:17
 */
@Service
public class SafetySupplierCarrierGroupSyncService
        extends SafetySupplierSyncAbstractFactory<SafetyCarrierGroupDo, SupplierCarrierGroupDto> {

    private static final Logger LOG = LoggerFactory.getLogger(SafetySupplierCarrierGroupSyncService.class);
    @Autowired
    SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Autowired
    SafetyCarrierGroupDomainService safetyCarrierGroupDomainService;

    @Autowired
    SafetyCarrierGroupDtoConverter safetyCarrierGroupDtoConverter;

    @Override
    protected Boolean isNeedSendMessage(List<SafetyCarrierGroupDo> errorList) {
        Boolean flag = false;
        if (CollectionUtils.isNotEmpty(errorList)) {
            for (SafetyCarrierGroupDo item : errorList) {
                if (item.getSyncStatus() != null && SafetySyncStatusEnum.TWELVE_SYNC.getCode().equals(item.getSyncStatus())) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    @Override
    protected void lastUpdateSyncStatusWithAllSupplierDone(List<SafetyCarrierGroupDo> errorDoItemList,
                                                           String supplierCode) {

    }

    @Override
    protected void setSuccessSyncStatus(SafetyCarrierGroupDo doItem) {
        doItem.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
    }

    @Override
    protected void setErrorSyncStatus(SafetyCarrierGroupDo doItem) {
        if (SafetySyncStatusEnum.SUCCESS_SYNC.getCode().equals(doItem.getSyncStatus())) {
            doItem.setSyncStatus(OAUCFCommonConstants.INT_ONE);
        } else {
            doItem.setSyncStatus(doItem.getSyncStatus() + OAUCFCommonConstants.INT_ONE);
        }
    }

    @Override
    protected void updateSyncStatusByDoList(List<SafetyCarrierGroupDo> doList, String supplierCode) {
        safetyCarrierGroupRepository.batchSaveOrUpdate(doList);
    }

    @Override
    protected void updateSyncStatusToSyncing(List<SafetyCarrierGroupDo> doList) {
        List<Long> idList = doList.stream().filter(item -> item.getId() != null).map(SafetyCarrierGroupDo::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idList)) {
            //更新所有成功的同步状态为100
            safetyCarrierGroupRepository.updateSyncStatusByIdList(idList, SafetySyncStatusEnum.SYNCING, "admin");
        }
    }

    @Override
    protected List<Long> getDoIdList(List<SafetyCarrierGroupDo> doList) {
        return Lists.newArrayList();
    }

    @Override
    protected List<SafetyCarrierGroupDo> getWaitSyncDataByPersonMedium(List<SafetyPersonMediumDo> personMediumDOList,
                                                                       SafetySyncDto<SafetyCarrierGroupDo> safetySyncDto) {
        //安防载体集
        return getWaitSyncDoList(safetySyncDto);
    }

    @Override
    protected List<SafetyCarrierGroupDo> preDelWaitSyncDoList(SafetySyncDto<SafetyCarrierGroupDo> safetySyncDto) {
        return safetySyncDto.getWaitSyncData();
    }

    @Override
    protected List<SafetyCarrierGroupDo> getWaitSyncDoList(SafetySyncDto<SafetyCarrierGroupDo> safetySyncDto) {
        SafetyCarrierGroupQuery query = new SafetyCarrierGroupQuery();
        query.setQueryStartTime(safetySyncDto.getStartTime());
        query.setQueryEndTime(safetySyncDto.getEndTime());
        query.setNeedSyncStatusNum(OAUCFCommonConstants.INT_TEN);
        //获取所有的
        return safetyCarrierGroupRepository.listByConditions(query);
    }

    @Override
    protected String buildKeyByDo(SafetyCarrierGroupDo doItem) {
        return doItem.getCarrierGroupCode();
    }

    @Override
    protected List<String> buildKeyByDtoList(List<SupplierCarrierGroupDto> dtoItemList) {
        return dtoItemList.stream().map(item -> item.getCarrierGroupCode()).collect(Collectors.toList());
    }

    @Override
    protected String getSupplierCodeByDo(SafetyCarrierGroupDo doItem) {
        return doItem.getSupplierCode();
    }

    @Override
    protected Long getIsDeletedByDo(SafetyCarrierGroupDo doItem) {
        return doItem.getIsDeleted();
    }

    @Override
    protected List<SupplierCarrierGroupDto> convert(List<SafetyCarrierGroupDo> doList, Boolean isAdd) {
        return safetyCarrierGroupDtoConverter
                .toSupplierCarrierGroupDtoList(doList);
    }

    @Override
    protected List<SupplierCarrierGroupDto> batchSave(BaseQuery<SupplierCarrierGroupDto> dtoQuery,
                                                      SafetySupplierStrategySdk safetySupplierStrategySdk,
                                                      Map<String, SafetyCarrierGroupDo> allDoMap) {
        return safetySupplierStrategySdk.batchSaveSupplierCarrierGroup(dtoQuery);
    }

    @Override
    protected List<SupplierCarrierGroupDto> batchDelete(BaseQuery<SupplierCarrierGroupDto> dtoQuery,
                                                        SafetySupplierStrategySdk safetySupplierStrategySdk) {
        return safetySupplierStrategySdk.batchDeleteSupplierCarrierGroup(dtoQuery);
    }

    @Override
    protected void fillSupplierInfoToDo(Map<String, SafetyCarrierGroupDo> allDoMap, List<SupplierCarrierGroupDto> dtoList) {
        //将供应商侧的访问码填装
        for (SupplierCarrierGroupDto nowDto : dtoList) {
            SafetyCarrierGroupDo nowDo = allDoMap.get(nowDto.getCarrierGroupCode());
            if (StringUtils.isNotEmpty(nowDto.getSupplierId())) {
                nowDo.setSupplierAccessCode(nowDto.getSupplierId());
            }
        }
    }

    @Override
    protected SafetyModelTypeEnum getSafetyModelType() {
        return SafetyModelTypeEnum.CARRIER_GROUP;
    }

    @Override
    protected PageModel<SupplierCarrierGroupDto> queryByCondition(BaseQuery<SupplierCarrierGroupDto> baseQuery,
                                                                  SafetySupplierStrategySdk safetySupplierStrategySdk) {
        return safetySupplierStrategySdk.querySupplierCarrierGroupByCondition(baseQuery);
    }

    @Override
    protected List<SupplierCarrierGroupDto> filterNotExists(List<SupplierCarrierGroupDto> dtoList,
                                                            List<SupplierCarrierGroupDto> existsDoList,
                                                            Map<String, SafetyCarrierGroupDo> allDoMap) {
        List<String> existsKey = existsDoList.stream().map(SupplierCarrierGroupDto::getName).collect(Collectors.toList());
        Map<String, SupplierCarrierGroupDto> existMap = existsDoList.stream().collect(Collectors
                .toMap(SupplierCarrierGroupDto::getName, item -> item, (k1, k2) -> k1));
        Iterator<Map.Entry<String, SafetyCarrierGroupDo>> iterator = allDoMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, SafetyCarrierGroupDo> entry = iterator.next();
            SafetyCarrierGroupDo value = entry.getValue();
            SupplierCarrierGroupDto supplierCarrierGroupDto = existMap.get(value.getName());
            if (existsKey.contains(value.getName())
                    && SafetySupplierCodeEnum.HOLLYWELLV2.getSupplierCode().equals(value.getSupplierCode())
                    && value.getCarrierGroupCode().equals(value.getSupplierAccessCode())) {
                value.setSupplierAccessCode(supplierCarrierGroupDto.getSupplierId());
            } else if (existsKey.contains(value.getName())
                    && SafetySupplierCodeEnum.HOLLYWELLV2.getSupplierCode().equals(value.getSupplierCode())
                    && !value.getSupplierAccessCode().equals(supplierCarrierGroupDto.getSupplierId())) {
                //对allDoMap存在的key和value数据进行过滤
                iterator.remove();
            }
        }
        LOG.info("carrier group filter exist key map:{}", allDoMap);
        return dtoList.stream().filter(item -> !existsKey.contains(item.getName())).collect(Collectors.toList());
    }
}
