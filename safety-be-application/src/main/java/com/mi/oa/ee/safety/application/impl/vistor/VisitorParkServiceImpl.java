package com.mi.oa.ee.safety.application.impl.vistor;

import cn.hutool.core.util.ObjectUtil;
import com.mi.oa.ee.safety.application.converter.safety.SafetyCarrierDtoConverter;
import com.mi.oa.ee.safety.application.converter.vistor.VisitorParkDtoConverter;
import com.mi.oa.ee.safety.application.dto.safety.SafetyCarrierDto;
import com.mi.oa.ee.safety.application.dto.visitor.VisitingParkDto;
import com.mi.oa.ee.safety.application.service.visitor.VisitorParkService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.UcRoleDto;
import com.mi.oa.ee.safety.common.enums.visitor.VisitorApplyTypeEnum;
import com.mi.oa.ee.safety.domain.ability.VisitorApplyAbility;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierDo;
import com.mi.oa.ee.safety.domain.model.VisitorParkDo;
import com.mi.oa.ee.safety.domain.model.VisitorParkParkingConfigDo;
import com.mi.oa.ee.safety.domain.service.SafetyCarrierDomainService;
import com.mi.oa.ee.safety.domain.service.VisitorParkDomainService;
import com.mi.oa.ee.safety.domain.service.VisitorParkingConfigDomainService;
import com.mi.oa.ee.safety.domain.service.VisitorReceptionConfigDomainService;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.UcSdk;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierRepository;
import com.mi.oa.ee.safety.infra.repository.VisitorParkParkingConfigRepository;
import com.mi.oa.ee.safety.infra.repository.VisitorParkRepository;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 来访园区实现类
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/8/28 15:20
 */
@Slf4j
@Service
public class VisitorParkServiceImpl implements VisitorParkService {

    @Autowired
    VisitorParkDomainService service;

    @Autowired
    VisitorParkDtoConverter converter;

    @Autowired
    VisitorParkRepository parkRepository;

    @Autowired
    SafetyCarrierDomainService safetyCarrierDomainService;

    @Autowired
    SafetyCarrierRepository safetyCarrierRepository;

    @Autowired
    VisitorParkDomainService visitorParkDomainService;

    @Autowired
    VisitorApplyAbility ability;

    @Autowired
    SafetyCarrierDtoConverter safetyCarrierDtoConverter;

    @Autowired
    SpaceSdk spaceSdk;

    @Autowired
    UcSdk ucSdk;

    @Autowired
    private VisitorReceptionConfigDomainService configDomainService;

    @Autowired
    private VisitorParkParkingConfigRepository parkingConfigRepository;

    @Autowired
    private VisitorParkingConfigDomainService parkingConfigDomainService;

    @Override
    public List<UcRoleDto> getRoleListByFuzzyName(String name) {
        return ucSdk.getRoleListByFuzzyName(name);
    }

    @Override
    public List<VisitingParkDto> getVisitingParks(VisitingParkDto parkDto) {
        return converter.toDtoList(service.getAssemblyPark(converter.toDo(parkDto)));
    }

    @Override
    public List<VisitingParkDto> getUnableParks() {
        return converter.toDtoList(service.getUnablePark());
    }

    @Override
    public PageModel<VisitingParkDto> pageConditionList(VisitingParkDto req) {
        VisitorParkDo parkDo = converter.toDo(req);
        parkDo.putExtField(SafetyConstants.PAGE_NUM_KEY, req.getPageNum());
        parkDo.putExtField(SafetyConstants.PAGE_SIZE_KEY, req.getPageSize());
        PageModel<VisitorParkDo> page = visitorParkDomainService.queryPageByConditions(parkDo);
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (VisitorParkDo visitorParkDo : page.getList()) {
                //填装园区对应的空间名称
                visitorParkDomainService.fillParkNameWithSpace(visitorParkDo);
                //填装园区对应的管理员
                visitorParkDomainService.loadParkAdminAccounts(visitorParkDo);
                //填装园区默认角色的名称
                visitorParkDomainService.loadRoleDto(visitorParkDo);
            }
        }
        return PageModel.build(converter.toDtoList(page.getList()),
                page.getPageSize(), page.getPageNum(), page.getTotal());
    }

    @Override
    public void createPark(VisitingParkDto toDto) {
        VisitorParkDo parkDo = converter.toDo(toDto);
        visitorParkDomainService.fillSort(parkDo, toDto.getSort());
        //id不为空则为更新
        if (ObjectUtils.isNotEmpty(parkDo.getId())) {
            //开启时，校验闸机是否已绑定
            if (OAUCFCommonConstants.INT_ONE.equals(toDto.getParkStatus())
                    // 高级接待园区无须校验闸机
                    && !VisitorApplyTypeEnum.RECEIVE.getCode().equals(parkDo.getApplyType())) {
                //判断是否有对应闸机
                VisitorParkDo checkParkDo = new VisitorParkDo();
                checkParkDo.setId(parkDo.getId());
                visitorParkDomainService.checkParkIsCanEnable(checkParkDo);
            }
            //有则保存园区
            parkRepository.update(parkDo);
        } else {
            //检查是否创建过
            parkDo = parkRepository.create(parkDo);
        }

        // 更新园区排序
        if (toDto.getSort() > 0) {
            parkRepository.updateSpaceSort(parkDo);
        }
        //更新对应的园区管理员
        if (ObjectUtil.isNotEmpty(toDto.getParkAdminUids())) {
            visitorParkDomainService.syncParkAdmin(parkDo, toDto.getParkAdminUids());
        }
        //高级接待园区禁用时需联动禁用相关接待配置状态
        if (VisitorApplyTypeEnum.RECEIVE.getCode().equals(parkDo.getApplyType())
                && !OAUCFCommonConstants.INT_ONE.equals(parkDo.getParkStatus())) {
            configDomainService.disable(parkDo);
        }
    }

    @Override
    public List<VisitingParkDto> getAllVisitingParks(VisitingParkDto parkDto) {
        //1.后台获取所有未禁用园区信息
        List<VisitorParkDo> parkList = parkRepository.getParkList(converter.toDo(parkDto));
        //2.编排园区楼栋楼层格式
        return converter.toDtoList(ability.getStructuredPark(parkList, true, parkDto.getOrgCode()));
    }

    @Override
    public PageModel<SafetyCarrierDto> getSafetyCarrierByPark(VisitingParkDto dto) {
        //获取对应的载体列表
        SafetyCarrierQuery query = new SafetyCarrierQuery();
        query.setParkCode(dto.getParkCode());
        query.setPageNum(dto.getPageNum());
        query.setPageSize(dto.getPageSize());

        if (ObjectUtil.isNotEmpty(dto.getParkingConfigId())) {
            VisitorParkParkingConfigDo parkingConfigDo = parkingConfigRepository.find(dto.getParkingConfigId());
            parkingConfigDomainService.loadSafetyCarrierGroups(parkingConfigDo);
            query.setCarrierGroupCodeList(parkingConfigDo.getSafetyCarrierGroupCodeList());
        } else {
            //校验园区是否可用
            VisitorParkDo visitorParkDo = converter.toDo(dto);
            visitorParkDomainService.checkParkIsCanEnable(visitorParkDo);
            query.setCarrierGroupCodeList(visitorParkDo.getSafetyCarrierGroupCodeList());
        }

        PageModel<SafetyCarrierDo> pageModel = safetyCarrierRepository.queryPageByCarrierGroupList(query);
        return PageModel.build(safetyCarrierDtoConverter.toDtoList(pageModel.getList()), pageModel.getPageSize(),
                pageModel.getPageNum(), pageModel.getTotal());
    }
}
