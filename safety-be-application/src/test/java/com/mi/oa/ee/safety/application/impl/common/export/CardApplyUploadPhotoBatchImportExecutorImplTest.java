package com.mi.oa.ee.safety.application.impl.common.export;

import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyPhotoUploadImportDto;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.fds.utils.FDSUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CardApplyUploadPhotoBatchImportExecutorImplTest {

    @InjectMocks
    private CardApplyUploadPhotoBatchImportExecutorImpl executor;

    private byte[] createTestZipFile(byte[] imageData, String fileName) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            ZipEntry entry = new ZipEntry(fileName);
            zos.putNextEntry(entry);
            zos.write(imageData);
            zos.closeEntry();
        }
        return baos.toByteArray();
    }

    private byte[] createTestImage(int width, int height) throws Exception {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", baos);
        return baos.toByteArray();
    }

    @Test
    void preProcessZipFile_WithValidImage_ShouldProcessSuccessfully() throws Exception {
        // 准备测试数据
        byte[] imageData = createTestImage(SafetyConstants.Card.PIC_PIX, SafetyConstants.Card.PIC_PIX);
        byte[] zipData = createTestZipFile(imageData, "test.jpg");

        try (MockedStatic<FDSUtils> fdsUtilsMock = Mockito.mockStatic(FDSUtils.class);
             MockedStatic<NeptuneClient> neptuneClientMock = Mockito.mockStatic(NeptuneClient.class)) {
            // 模拟 NeptuneClient
            NeptuneClient mockNeptuneClient = mock(NeptuneClient.class);
            neptuneClientMock.when(NeptuneClient::getInstance).thenReturn(mockNeptuneClient);
            when(mockNeptuneClient.parseEntryTemplate(anyString())).thenReturn("成功");
            // 模拟 FDS 上传返回
            fdsUtilsMock.when(() -> FDSUtils.putFile(any(InputStream.class), anyString(), any()))
                    .thenReturn("http://test-fds-url/test.jpg");
            fdsUtilsMock.when(() -> FDSUtils.buildNewFileName(anyString()))
                    .thenReturn("test");

            // 执行测试
            @SuppressWarnings("unchecked")
            List<CardApplyPhotoUploadImportDto> result = (List<CardApplyPhotoUploadImportDto>) 
                ReflectionTestUtils.invokeMethod(executor, "preProcessZipFile", zipData);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            CardApplyPhotoUploadImportDto dto = result.get(0);
            assertEquals("http://test-fds-url/test.jpg", dto.getPhotoUrl());
            assertNull(dto.getResult());
        }
    }

    @Test
    void preProcessZipFile_WithInvalidImageType_ShouldFail() throws Exception {
        // 准备测试数据
        byte[] invalidData = "not an image".getBytes();
        byte[] zipData = createTestZipFile(invalidData, "test.txt");

        try (MockedStatic<FDSUtils> fdsUtilsMock = Mockito.mockStatic(FDSUtils.class);
             MockedStatic<NeptuneClient> neptuneClientMock = Mockito.mockStatic(NeptuneClient.class)) {
            // 模拟 NeptuneClient
            NeptuneClient mockNeptuneClient = mock(NeptuneClient.class);
            neptuneClientMock.when(NeptuneClient::getInstance).thenReturn(mockNeptuneClient);
            when(mockNeptuneClient.parseEntryTemplate(ImportResultEnum.FAIL.getResult())).thenReturn("失败");
            when(mockNeptuneClient.parseEntryTemplate(CardApplyDomainErrorCodeEnum.PIC_TYPE_ERROR.getErrDesc()))
                    .thenReturn("文件不是图片格式");
            // 执行测试
            BizException exception = assertThrows(BizException.class, () ->
                    ReflectionTestUtils.invokeMethod(executor, "preProcessZipFile", zipData));
        }
    }

    @Test
    void preProcessZipFile_WithInvalidImageSize_ShouldFail() throws Exception {
        // 准备测试数据
        byte[] imageData = createTestImage(100, 100); // 创建一个尺寸过小的图片
        byte[] zipData = createTestZipFile(imageData, "test.jpg");

        try (MockedStatic<FDSUtils> fdsUtilsMock = Mockito.mockStatic(FDSUtils.class);
             MockedStatic<NeptuneClient> neptuneClientMock = Mockito.mockStatic(NeptuneClient.class)) {
            // 模拟 NeptuneClient
            NeptuneClient mockNeptuneClient = mock(NeptuneClient.class);
            neptuneClientMock.when(NeptuneClient::getInstance).thenReturn(mockNeptuneClient);
            when(mockNeptuneClient.parseEntryTemplate(ImportResultEnum.FAIL.getResult())).thenReturn(ImportResultEnum.FAIL.getResult());
            when(mockNeptuneClient.parseEntryTemplate(CardApplyDomainErrorCodeEnum.PIC_PIX_NOT_OK.getErrDesc()))
                    .thenReturn(CardApplyDomainErrorCodeEnum.PIC_PIX_NOT_OK.getErrDesc());
            // 模拟 FDS 上传返回
            fdsUtilsMock.when(() -> FDSUtils.putFile(any(InputStream.class), anyString(), any()))
                    .thenReturn("http://test-fds-url/test.jpg");
            fdsUtilsMock.when(() -> FDSUtils.buildNewFileName(anyString()))
                    .thenReturn("test");
            // 执行测试
            @SuppressWarnings("unchecked")
            List<CardApplyPhotoUploadImportDto> result = (List<CardApplyPhotoUploadImportDto>) 
                ReflectionTestUtils.invokeMethod(executor, "preProcessZipFile", zipData);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            CardApplyPhotoUploadImportDto dto = result.get(0);
            assertEquals("失败", dto.getResult());
            assertEquals(CardApplyDomainErrorCodeEnum.PIC_PIX_NOT_OK.getErrDesc(), dto.getException());
        }
    }
} 