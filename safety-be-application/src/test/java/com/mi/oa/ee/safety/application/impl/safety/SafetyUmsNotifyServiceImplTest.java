package com.mi.oa.ee.safety.application.impl.safety;

import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.dto.SafetyUmsNotifyDto;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.UmsSdk;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SafetyUmsNotifyServiceImplTest {

    @InjectMocks
    private SafetyUmsNotifyServiceImpl safetyUmsNotifyService;

    @Mock
    private IdmRemote idmRemote;

    @Mock
    private IdmSdk idmSdk;

    @Mock
    private UmsSdk umsSdk;

    @Test
    void buildReceiver_WithCompanyEmail_ShouldUseCompanyEmail() {
        // 准备测试数据
        SafetyUmsNotifyDto notifyDto = new SafetyUmsNotifyDto();
        notifyDto.setReceiver("testUid");
        List<SafetyUmsNotifyDto> notifyDtos = new ArrayList<>();
        notifyDtos.add(notifyDto);

        PersonInfoModel personInfoModel = new PersonInfoModel();
        personInfoModel.setUid("testUid");
        personInfoModel.setEmail("<EMAIL>");
        personInfoModel.setPersonalEmail("<EMAIL>");

        // 设置 mock 行为
        when(umsSdk.checkReceiverIsUid("testUid")).thenReturn(true);
        when(idmSdk.getUserInfosByUids(anyList())).thenReturn(Collections.singletonList(personInfoModel));

        // 执行测试
        Map<String, String> receiverMap = ReflectionTestUtils.invokeMethod(
            safetyUmsNotifyService, 
            "buildReceiver", 
            notifyDtos,
                (int) MessageChannelEnum.EMAIL.getType()
        );

        // 验证结果
        assertEquals("<EMAIL>", receiverMap.get("testUid"));
    }

    @Test
    void buildReceiver_WithoutCompanyEmail_ShouldUsePersonalEmail() {
        // 准备测试数据
        SafetyUmsNotifyDto notifyDto = new SafetyUmsNotifyDto();
        notifyDto.setReceiver("testUid");
        List<SafetyUmsNotifyDto> notifyDtos = new ArrayList<>();
        notifyDtos.add(notifyDto);

        PersonInfoModel personInfoModel = new PersonInfoModel();
        personInfoModel.setUid("testUid");
        personInfoModel.setEmail(null);
        personInfoModel.setPersonalEmail("<EMAIL>");

        // 设置 mock 行为
        when(umsSdk.checkReceiverIsUid("testUid")).thenReturn(true);
        when(idmSdk.getUserInfosByUids(anyList())).thenReturn(Collections.singletonList(personInfoModel));

        // 执行测试
        Map<String, String> receiverMap = ReflectionTestUtils.invokeMethod(
            safetyUmsNotifyService, 
            "buildReceiver", 
            notifyDtos,
                (int) MessageChannelEnum.EMAIL.getType()
        );

        // 验证结果
        assertEquals("<EMAIL>", receiverMap.get("testUid"));
    }

    @Test
    void buildReceiver_WithEmptyEmails_ShouldNotAddToMap() {
        // 准备测试数据
        SafetyUmsNotifyDto notifyDto = new SafetyUmsNotifyDto();
        notifyDto.setReceiver("testUid");
        List<SafetyUmsNotifyDto> notifyDtos = new ArrayList<>();
        notifyDtos.add(notifyDto);

        PersonInfoModel personInfoModel = new PersonInfoModel();
        personInfoModel.setUid("testUid");
        personInfoModel.setEmail("");
        personInfoModel.setPersonalEmail("");

        // 设置 mock 行为
        when(umsSdk.checkReceiverIsUid("testUid")).thenReturn(true);
        when(idmSdk.getUserInfosByUids(anyList())).thenReturn(Collections.singletonList(personInfoModel));

        // 执行测试
        Map<String, String> receiverMap = ReflectionTestUtils.invokeMethod(
            safetyUmsNotifyService, 
            "buildReceiver", 
            notifyDtos,
                (int) MessageChannelEnum.EMAIL.getType()
        );

        // 验证结果
        assertFalse(StringUtils.isNotBlank(receiverMap.get("testUid")));
    }

    @Test
    void buildReceiver_WithNullEmails_ShouldNotAddToMap() {
        // 准备测试数据
        SafetyUmsNotifyDto notifyDto = new SafetyUmsNotifyDto();
        notifyDto.setReceiver("testUid");
        List<SafetyUmsNotifyDto> notifyDtos = new ArrayList<>();
        notifyDtos.add(notifyDto);

        PersonInfoModel personInfoModel = new PersonInfoModel();
        personInfoModel.setUid("testUid");
        personInfoModel.setEmail(null);
        personInfoModel.setPersonalEmail(null);

        // 设置 mock 行为
        when(umsSdk.checkReceiverIsUid("testUid")).thenReturn(true);
        when(idmSdk.getUserInfosByUids(anyList())).thenReturn(Collections.singletonList(personInfoModel));

        // 执行测试
        Map<String, String> receiverMap = ReflectionTestUtils.invokeMethod(
            safetyUmsNotifyService, 
            "buildReceiver", 
            notifyDtos,
                (int) MessageChannelEnum.EMAIL.getType()
        );

        // 验证结果
        assertFalse(StringUtils.isNotBlank(receiverMap.get("testUid")));
    }

    @Test
    void buildReceiver_WithBlankCompanyEmail_ShouldUsePersonalEmail() {
        // 准备测试数据
        SafetyUmsNotifyDto notifyDto = new SafetyUmsNotifyDto();
        notifyDto.setReceiver("testUid");
        List<SafetyUmsNotifyDto> notifyDtos = new ArrayList<>();
        notifyDtos.add(notifyDto);

        PersonInfoModel personInfoModel = new PersonInfoModel();
        personInfoModel.setUid("testUid");
        personInfoModel.setEmail("   ");
        personInfoModel.setPersonalEmail("<EMAIL>");

        // 设置 mock 行为
        when(umsSdk.checkReceiverIsUid("testUid")).thenReturn(true);
        when(idmSdk.getUserInfosByUids(anyList())).thenReturn(Collections.singletonList(personInfoModel));

        // 执行测试
        Map<String, String> receiverMap = ReflectionTestUtils.invokeMethod(
            safetyUmsNotifyService, 
            "buildReceiver", 
            notifyDtos,
                (int) MessageChannelEnum.EMAIL.getType()
        );

        // 验证结果
        assertEquals("<EMAIL>", receiverMap.get("testUid"));
    }

    @Test
    void buildReceiver_WithNonUidReceiver_ShouldUseReceiverAsIs() {
        // 准备测试数据
        SafetyUmsNotifyDto notifyDto = new SafetyUmsNotifyDto();
        notifyDto.setReceiver("<EMAIL>");
        List<SafetyUmsNotifyDto> notifyDtos = new ArrayList<>();
        notifyDtos.add(notifyDto);

        // 设置 mock 行为
        when(umsSdk.checkReceiverIsUid("<EMAIL>")).thenReturn(false);

        // 执行测试
        Map<String, String> receiverMap = ReflectionTestUtils.invokeMethod(
            safetyUmsNotifyService, 
            "buildReceiver", 
            notifyDtos,
                (int) MessageChannelEnum.EMAIL.getType()
        );

        // 验证结果
        assertEquals("<EMAIL>", receiverMap.get("<EMAIL>"));
    }

    @Test
    void buildReceiver_WithMiWorkChannel_ShouldUseAccountName() {
        // 准备测试数据
        SafetyUmsNotifyDto notifyDto = new SafetyUmsNotifyDto();
        notifyDto.setReceiver("testUid");
        List<SafetyUmsNotifyDto> notifyDtos = new ArrayList<>();
        notifyDtos.add(notifyDto);

        PersonInfoModel personInfoModel = new PersonInfoModel();
        personInfoModel.setUid("testUid");
        personInfoModel.setAccountName("testAccount");

        // 设置 mock 行为
        when(umsSdk.checkReceiverIsUid("testUid")).thenReturn(true);
        when(idmSdk.getUserInfosByUids(anyList())).thenReturn(Collections.singletonList(personInfoModel));

        // 执行测试
        Map<String, String> receiverMap = ReflectionTestUtils.invokeMethod(
            safetyUmsNotifyService, 
            "buildReceiver", 
            notifyDtos,
                (int) MessageChannelEnum.MI_WORK.getType()
        );

        // 验证结果
        assertEquals("testAccount", receiverMap.get("testUid"));
    }

    @Test
    void buildReceiver_WithSmsChannel_ShouldUseMobile() {
        // 准备测试数据
        SafetyUmsNotifyDto notifyDto = new SafetyUmsNotifyDto();
        notifyDto.setReceiver("testUid");
        List<SafetyUmsNotifyDto> notifyDtos = new ArrayList<>();
        notifyDtos.add(notifyDto);

        PersonInfoModel personInfoModel = new PersonInfoModel();
        personInfoModel.setUid("testUid");
        personInfoModel.setMobile("***********");

        // 设置 mock 行为
        when(umsSdk.checkReceiverIsUid("testUid")).thenReturn(true);
        when(idmSdk.getUserInfosByUids(anyList())).thenReturn(Collections.singletonList(personInfoModel));

        // 执行测试
        Map<String, String> receiverMap = ReflectionTestUtils.invokeMethod(
            safetyUmsNotifyService, 
            "buildReceiver", 
            notifyDtos,
                (int) MessageChannelEnum.SMS.getType()
        );

        // 验证结果
        assertEquals("***********", receiverMap.get("testUid"));
    }
} 