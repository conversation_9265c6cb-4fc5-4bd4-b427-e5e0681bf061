package com.mi.oa.ee.safety.application.dto.card;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/8/9 20:55
 */
@Data
public class CardLeaveRecordDto {
    /**
     * 主键id
     */
    private Long id;

    /**
     * pid
     */
    private String uid;

    /**
     * 人事离职单号
     */
    private String psLeaveNumber;

    /**
     * 预离职日期
     */
    private ZonedDateTime preLeaveDate;

    /**
     * 账号
     */
    private String userName;

    /**
     * 工号
     */
    private String employeeNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
