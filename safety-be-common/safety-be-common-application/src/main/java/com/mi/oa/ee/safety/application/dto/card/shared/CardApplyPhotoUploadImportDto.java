package com.mi.oa.ee.safety.application.dto.card.shared;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.common.excel.annotation.ExportField;
import lombok.Data;

import java.io.ByteArrayInputStream;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2025/1/17 18:11
 */
@Data
public class CardApplyPhotoUploadImportDto {

    @ExportField(columnName = "导入账号(userName)", order = 1)
    private String userName;

    @ExportField(columnName = "导入照片地址(photoUrl)", order = 2)
    private String photoUrl;

    @ExportField(columnName = "导入结果(result)", order = 3)
    private String result;

    @ExportField(columnName = "失败原因(error description)", order = 4)
    private String exception;

    @ExcelIgnore
    private String uid;

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        //使用国际化
        this.result = NeptuneClient.getInstance().parseEntryTemplate(result).replace("{", "").replace("}", "");
    }

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        this.exception = NeptuneClient.getInstance().parseEntryTemplate(exception);
    }
}
