package com.mi.oa.ee.safety.application.dto.common;

import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.common.excel.annotation.ExportField;
import lombok.Data;

@Data
public class CarrierGroupExportExcelDto {
    @ExportField(columnName = "名称(Name)", order = 1)
    private String name;

    @ExportField(columnName = "类型(Type)", order = 2)
    private String type;

    @ExportField(columnName = "地区(Region)", order = 3)
    private String region;

    @ExportField(columnName = "园区(Park)", order = 4)
    private String park;

    @ExportField(columnName = "状态(Status)", order = 5)
    private String status;

    @ExportField(columnName = "供应商(Supplier)", order = 6)
    private String supplier;

    @ExportField(columnName = "门数量(Number of Doors)", order = 7)
    private int numberOfDoors;

    @ExportField(columnName = "操作人(Operator)", order = 8)
    private String operator;

    @ExportField(columnName = "更新时间(Update Time)", order = 9)
    private String updateTime;

    @ExportField(columnName = "备注说明(Notes)", order = 10)
    private String notes;

    public void setType(String type) {
        this.type = NeptuneClient.getInstance().parseEntryTemplate(type);
    }

    public void setStatus(String status) {
        this.status = NeptuneClient.getInstance().parseEntryTemplate(status);
    }

}
