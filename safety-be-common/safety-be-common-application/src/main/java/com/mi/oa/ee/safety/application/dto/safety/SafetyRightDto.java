package com.mi.oa.ee.safety.application.dto.safety;

import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierDataMonitorUmsTypeEnum;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/22 12:16
 */
@Data
public class SafetyRightDto {

    /**
     * 人员ID uid
     */
    private String uid;

    /**
     * 介质编码 medium_code
     */
    private String mediumCode;

    /**
     * 当前权限对应的供应商
     */
    private String supplierCode;

    /**
     * 载体集编码 carrier_group_code
     */
    private String carrierGroupCode;

    /**
     * 供应商侧校验编码 supplier_check_code
     */
    private String supplierCheckCode;

    /**
     * 供应商侧访问码，可以是json等 supplier_access_code
     */
    private String supplierAccessCode;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 开始时间 start_time
     */
    private ZonedDateTime startTime;

    /**
     * 同步状态：0：待同步 1：已同步  2：同步异常 sync_status
     */
    private Integer syncStatus;

    private ZonedDateTime operateTime;
    /**
     * 结束时间 end_time
     */
    private ZonedDateTime endTime;

    private SafetyCarrierGroupDto safetyCarrierGroup;

    private Long isDeleted;

    private SafetyPersonDto safetyPersonDto;

    private SafetyMediumDto safetyMediumDto;

    private SafetySupplierDataMonitorUmsTypeEnum safetySupplierDataMonitorUmsTypeEnum;
}
