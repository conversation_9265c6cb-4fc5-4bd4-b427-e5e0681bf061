package com.mi.oa.ee.safety.application.dto.safety.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/1/29 20:44
 */
@Data
@ColumnWidth(30)
public class CarrierGroupExcelDto {

    @ExcelProperty(value = "载体集名称")
    private String carrierGroupName;

    @ExcelProperty(value = "供应商侧访问码")
    private String supplierAccessCode;

    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    @ExcelProperty(value = "供应商侧载体缺失名称列表")
    private String supplierCarrierLackNameList;

    @ExcelProperty(value = "供应商侧载体缺失编码列表")
    private String supplierCarrierLackCodeList;

    @ExcelProperty(value = "供应商侧载体多出名称列表")
    private String supplierCarrierExtraNameList;

    @ExcelProperty(value = "供应商侧载体多出编码列表")
    private String supplierCarrierExtraCodeList;

    @ExcelProperty(value = "安防侧还是供应商侧 0:安防侧 1:供应商侧")
    private Integer safetyOrSupplier;

    @ExcelProperty(value = "多出还是缺失 0:多出 1:缺失")
    private Integer moreOrLess;
}
