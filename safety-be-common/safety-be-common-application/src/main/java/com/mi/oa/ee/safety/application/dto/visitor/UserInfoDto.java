package com.mi.oa.ee.safety.application.dto.visitor;

import com.mi.oa.ee.safety.common.dto.DeptDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 接待人dto对象
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/8/26 9:52
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoDto {

    /**
     * uid
     */
    private String uid;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户账号
     */
    private String userNameId;

    /**
     * 用户头像地址
     */
    private String avatarUrl;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 部门信息
     */
    List<DeptDto> deptInfo;

    /**
     * 用户电话
     */
    private String phone;
}
