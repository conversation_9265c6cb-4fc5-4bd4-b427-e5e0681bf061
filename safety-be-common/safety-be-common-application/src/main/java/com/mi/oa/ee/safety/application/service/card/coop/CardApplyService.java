package com.mi.oa.ee.safety.application.service.card.coop;

import com.mi.oa.ee.safety.application.dto.card.BatchModifyParkDto;
import com.mi.oa.ee.safety.application.dto.card.CardAvatarRemoteDto;
import com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto;
import com.mi.oa.ee.safety.application.dto.card.coop.CoopCardApplyEditDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyImportDto;
import com.mi.oa.ee.safety.application.dto.visitor.BpmCallBackDto;
import com.mi.oa.ee.safety.common.dto.BatchOperateDto;
import com.mi.oa.ee.safety.common.dto.CardRemoteDto;
import com.mi.oa.ee.safety.common.dto.ImportErrorDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.dto.UpdatePersonDto;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/21 15:42
 */
public interface CardApplyService {

    CardRemoteDto saveCoopCardApplyForOnSite(CardPartnerApplyDto cardPartnerApplyDto);

    void batchImportEmpCardApply(CardApplyImportDto importDto);

    /**
     * 确认申请单
     *
     * @param cardPartnerApplyDto
     * @return void
     * <AUTHOR>
     * @date 2023/5/30 9:23
     */
    void confirmApply(CardPartnerApplyDto cardPartnerApplyDto, String nowUid);

    /**
     * @param cardPartnerApplyDto
     * @return void
     * @desc 保存合作卡申请单
     * <AUTHOR> denghui
     * @date 2022/11/21 16:33
     */
    CardRemoteDto saveCoopCard(CardPartnerApplyDto cardPartnerApplyDto);

    /**
     * 获取当前申请单详情
     *
     * @param id
     * @param nowUid
     * @return com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto
     * <AUTHOR>
     * @date 2023/6/8 16:31
     */
    CardPartnerApplyDto getDetailCardApply(Long id, String nowUid);

    /**
     * @param phone
     * @return com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto
     * @desc 通过电话获取用户信息
     * <AUTHOR> denghui
     * @date 2022/11/23 20:42
     */
    CardPartnerApplyDto getPersonInfoByPhone(String phone);

    /**
     * @param reqToDto
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto>
     * @desc 条件分页查询申请单
     * <AUTHOR> denghui
     * @date 2022/11/24 11:30
     */
    PageModel<CardPartnerApplyDto> pageConditionApply(CardPartnerApplyDto reqToDto, Boolean flag);

    /**
     * @param queryData
     * @return void
     * @desc 批量导入创建申请单
     * <AUTHOR> denghui
     * @date 2022/11/25 12:22
     */
    List<ImportErrorDto> batchSaveApply(MultipartFile queryData);

    /**
     * @param id
     * @param photoUrl
     * @return void
     * @desc 照片上传和更新
     * <AUTHOR> denghui
     * @date 2022/11/28 20:17
     */
    void uploadPhoto(Long id, String photoUrl);

    /**
     * 供应商上传图片
     *
     * @param id
     * @param photoUrl
     * @return void
     * <AUTHOR>
     * @date 2023/6/18 10:54
     */
    void uploadPhotoBySupplier(Long id, String photoUrl);

    /**
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2023/6/24 11:33
     */
    void uploadPhotoBySupplierReSend(Long id);

    /**
     * @param batchOperateDto
     * @return void
     * @desc 批量操作
     * <AUTHOR> denghui
     * @date 2022/11/29 15:22
     */
    void batchOperate(BatchOperateDto batchOperateDto);

    /**
     * @param parkCode
     * @return boolean
     * @desc 判断是否有载体集
     * <AUTHOR> denghui
     * @date 2022/12/1 14:57
     */
    boolean hasCarrierGroups(String parkCode);

    String getReceiptAddress(String parkCode);

    void updateByUid(CardPartnerApplyDto cardPartnerApplyDto, UpdatePersonDto req);

    PageModel<SafetySpaceParkDto> pageDistinctPark(String keyword, Integer pageNum, Integer pageSize);

    List<SafetySpaceParkDto> listByParkCodes(List<String> resourceCodes);

    Integer checkParkCodeExist(String parkCode);

    void createTempApply(String accountName);

    void createEmpApply(String accountName, String employeeNo, String phone, Boolean isAccountActive);

    void selfUpload(Long id);

    void reSendSelfUploadUmsNotify(Long id);

    void openCardByCardApply(CoopCardApplyEditDto editDto);

    List<CardPartnerApplyDto> findAvatarUrlList(CardAvatarRemoteDto dto);

    CardPartnerApplyDto findCoopApplyByUid(String uid);

    void asyncExportCardApply(String queryData);

    void asyncExportCoopCardApply(String queryData);

    void batchSaveApplyV2(MultipartFile file, String applyType);

    void asyncExportPropertyCardApply(String queryData);

    void creatCardApplyByUid(CardApplyDo cardApplyDo);

    void batchImportPhoto(CardApplyImportDto importDto);

    void batchModifyParks(BatchModifyParkDto modifyParkDto);

    void asyncExportPhoto(String queryData);

    void applyBpmCallBack(BpmCallBackDto bpmCallBackDto);
}
