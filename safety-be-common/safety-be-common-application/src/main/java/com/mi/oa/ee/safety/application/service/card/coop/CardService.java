package com.mi.oa.ee.safety.application.service.card.coop;

import com.mi.oa.ee.safety.application.dto.card.CardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.CardLeaveRecordDto;
import com.mi.oa.ee.safety.application.dto.card.EmpCardDto;
import com.mi.oa.ee.safety.application.dto.card.coop.CoopCardEditDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyCarrierDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyRightDto;
import com.mi.oa.ee.safety.common.dto.AccountModel;
import com.mi.oa.ee.safety.common.dto.AuthLogRedoDto;
import com.mi.oa.ee.safety.common.dto.CardPageConditionDto;
import com.mi.oa.ee.safety.common.dto.CarrierGroupCityTreeDto;
import com.mi.oa.ee.safety.common.dto.GroupPageConditionDto;
import com.mi.oa.ee.safety.common.dto.OperateLogDto;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.model.SpecialPageReq;
import com.mi.oa.ee.safety.common.model.SpecialPageVO;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/30 18:25
 */
public interface CardService {

    /**
     * @param parkCode
     * @return java.lang.Object
     * @desc 获取权限组
     * <AUTHOR> denghui
     * @date 2022/12/1 15:55
     */
    List<SafetyCarrierGroupDto> getCarrierGroups(String parkCode, String mediumCode);

    /**
     * @param pageReqToDto
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.application.dto.card.CardInfoDto>
     * @desc 条件分页查询
     * <AUTHOR> denghui
     * @date 2022/12/2 14:24
     */
    PageModel<CardInfoDto> pageConditionList(CardPageConditionDto pageReqToDto, Boolean flag);

    /**
     * @param id
     * @return com.mi.oa.ee.safety.application.dto.card.CardInfoDto
     * @desc 获取工卡列表详情
     * <AUTHOR> denghui
     * @date 2022/12/3 14:09
     */
    CardInfoDto getDetailCardInfo(Long id);

    /**
     * @param carrierGroupCode
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.application.dto.safety.SafetyCarrierDto>
     * @desc 分页获取门禁列表
     * <AUTHOR> denghui
     * @date 2022/12/3 14:59
     */
    PageModel<SafetyCarrierDto> getCarrierList(String carrierGroupCode, String carrierName, Long pageNum,
                                               Long pageSize);

    /**
     * @param operateStatus
     * @param operateType
     * @param pageNum
     * @param pageSize
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.common.dto.OperateLogDto>
     * @desc 条件分页查询操作日志
     * <AUTHOR> denghui
     * @date 2022/12/5 16:06
     */
    PageModel<OperateLogDto> pageListOperateLogs(Integer operateStatus, Integer operateType, Long pageNum,
                                                 Long pageSize, Long cardId);

    /**
     * @param operateType
     * @param id
     * @param uid
     * @return void
     * @desc
     * <AUTHOR> denghui
     * @date 2022/12/6 14:47
     */
    void cardOperate(Integer operateType, Long id, String uid);

    /**
     * @param name
     * @return java.util.List
     * @desc 模糊搜索用户信息
     * <AUTHOR> denghui
     * @date 2022/12/8 9:42
     */
    List<AccountModel> findFuzzyUser(String name, Boolean isResponsible);

    /**
     * @param toDto
     * @return java.util.List<com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto>
     * @desc 条件分页查询权限组
     * <AUTHOR> denghui
     * @date 2022/12/8 17:27
     */
    PageModel<SafetyCarrierGroupDto> pageGroupConditionList(GroupPageConditionDto toDto);

    /**
     * @param mediumCodes
     * @return java.util.List<com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto>
     * @desc 通过批量介质获取权限
     * <AUTHOR> denghui
     * @date 2022/12/12 14:49
     */
    List<SafetyRightDto> getCarrierGroupsByMediums(List<String> mediumCodes);

    /**
     * @param authReqToDto
     * @return void
     * @desc 权限操作日志重执行
     * <AUTHOR> denghui
     * @date 2022/12/12 18:23
     */
    void authorityRedo(AuthLogRedoDto authReqToDto);

    /**
     * @param uid
     * @return com.mi.oa.ee.safety.application.dto.card.CardInfoDto
     * @desc
     * <AUTHOR> denghui
     * @date 2022/12/13 14:21
     */
    CardInfoDto getCardByUid(String uid, List<CardStatusEnum> status);

    void updateStatus(Long id, CardStatusEnum canceled);

    List<CardInfoDto> getNeedExpireList();

    void doExpire(CardInfoDto cardInfoDto);

    List<CardInfoDto> getNeedActiveList();

    void doActive(CardInfoDto cardInfoDto);

    List<CarrierGroupCityTreeDto> getAccessCityByMedium(String mediumCode);

    List<CardInfoDto> getNeedNotifyList();

    void doNotify(List<CardInfoDto> cardInfoDto);

    void deleteTimeAndRight(String pid, Long startTime, Long endTime);

    void batchSavePartnerCard(MultipartFile file);

    List<CardInfoDto> getListCardInfoByPhysicCodes(List<String> mediumPhysicsCodes);

    List<CardInfoDto> getListCardInfoByEncryptCodes(List<String> mediumEncryptCodes);

    List<CardInfoDto> getListCardInfoByUid(List<String> uid);

    PageModel<SafetyCarrierGroupDto> getRestGroup(GroupPageConditionDto reqDto);

    List<CardInfoDto> getListCardInfoByAccounts(List<String> accounts);

    PageModel<EmpCardDto> pageEmpConditionList(CardPageConditionDto empCardDto, Boolean isExport);

    EmpCardDto getEmpCardDetailInfo(Long id);

    void editCard(EmpCardDto empCardDto);

    void pinCard(Long id);

    void restoreCard(Long id);

    void editCardInfo(CoopCardEditDto editDto);

    void createPreLeave(CardLeaveRecordDto cardLeaveRecordDto);

    void cancelLeave(CardLeaveRecordDto cardLeaveRecordDto);

    void reportLeave(Long cardId);

    Boolean checkIsNewCard(String userName);

    SafetyPersonDo findPersonInfoByUserName(String username);


    List<CardInfoDto> getNeedComparePermissionList();

    /**
     * 分页查询工卡信息
     * @param req
     * @return
     */
    SpecialPageVO<CardInfoDto> aggregatePageCardInfo(SpecialPageReq req);


    void asyncExportCardList(String queryData);

    CardInfoDto getCardInfoByUid(String uid, Integer number);

    void asyncExportPropertyCardList(String queryData);
}
