package com.mi.oa.ee.safety.application.service.card.migration;

import com.mi.oa.ee.safety.application.dto.card.config.RecyclableCardDto;
import com.mi.oa.ee.safety.application.dto.card.migration.RepairResult;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/7 17:08
 */
public interface CardMigrationService {

    /**
     * 迁移循环卡
     * @param recyclableCardDto
     */
    void sync(RecyclableCardDto recyclableCardDto);

    /**
     * 查询待处理的循环卡总数
     * @return
     */
    long countRecyclableCard();

    /**
     * id偏移量
     * @param offsetId 记录id
     * @return 满足条件的循环卡记录，只返回1000条
     */
    List<RecyclableCardDto> findRecyclableCard(long offsetId, List<String> cardNumList);

    /**
     * 执行修复逻辑
     * @param all 是否全量更新，全量更新的话 如果卡是使用中状态，但是没有找到使用记录，则会更新卡状态为使用中。
     * @param recyclableCardDto 需要跟新的工卡
     */
    RepairResult repair(RecyclableCardDto recyclableCardDto);

}
