package com.mi.oa.ee.safety.application.service.safety;

import com.mi.oa.ee.safety.application.dto.safety.SafetyClassDto;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 安防载体的查询
 *
 * <AUTHOR>
 * @date 2022/9/14 17:15
 */
public interface SafetyCarrierGroupService {


    /**
     * 根据classCode查询对应的class分类
     *
     * @param classCode
     * @return com.mi.oa.ee.safety.application.dto.safety.SafetyClassDto
     * <AUTHOR>
     * @date 2022/9/15 13:59
     */
    SafetyClassDto getSafetyClassByClassCode(String classCode);


    /**
     * 获取安防载体集的列表
     *
     * @param parentClassCode
     * @return java.util.List<com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto>
     * <AUTHOR>
     * @date 2022/9/14 17:24
     */
    List<SafetyCarrierGroupDto> getSafetyCarrierGroupByParentClassCode(String parentClassCode);

    /**
     * 获取当前安防载体集列表
     *
     * @param groupName
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageModel<SafetyCarrierGroupDto> listByGroupName(String groupName, Integer pageNum, Integer pageSize);

    /**
     * 根据载体集的编码查询对应的载体
     *
     * @param groupCodes
     * @return java.util.List<com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto>
     * <AUTHOR>
     * @date 2022/9/19 14:31
     */
    List<SafetyCarrierGroupDto> listByGroupCodes(List<String> groupCodes);

    /**
     * 获取门的分类
     *
     * @param parentClassCode
     * @return
     */
    List<SafetyClassDto> getSafetyClassByParentClassCode(String parentClassCode);

    List<SafetyClassDto> getSafetyClassCondition(SafetyClassDto classDto);

    List<SafetyClassDto> getSafetyClassCondition(List<String> classCodes);

    /**
     * @param reqToDto
     * @return void
     * @desc 权限组保存或者更新
     * <AUTHOR> denghui
     * @date 2022/12/15 16:45
     */
    void saveOrUpdate(SafetyCarrierGroupDto reqToDto);

    /**
     * @param carrierGroupIds
     * @return void
     * @desc 批量删除权限组
     * <AUTHOR> denghui
     * @date 2022/12/15 20:23
     */
    void batchDelete(List<Long> carrierGroupIds);

    /**
     * 批量更新权限组
     *
     * @param reqToDto
     * @return void
     * <AUTHOR>
     * @date 2023/8/15 15:50
     */
    void batchUpdate(SafetyCarrierGroupDto reqToDto);

    /**
     * 获取安防载体集详情
     *
     * @param carrierGroupCode
     * @return
     */
    SafetyCarrierGroupDto getCarrierGroup(String carrierGroupCode);

    /**
     * 查询安防载体集列表
     *
     * @param safetyCarrierGroupDto
     * @return
     */
    PageModel<SafetyCarrierGroupDto> pageCondition(SafetyCarrierGroupDto safetyCarrierGroupDto);

    void batchSaveGroups(MultipartFile file);

    void batchSaveCarrierGroups(MultipartFile file);

    PageModel<SafetyCarrierGroupDto> pageConditionV1(SafetyCarrierGroupDto query);

    void asyncExportCarrierGroup(String carrierGroupCode);

    void asyncImportCarrierGroup(MultipartFile file);


    void updateOne(SafetyCarrierGroupDto safetyCarrierGroupDto);

    PageVO<SafetyCarrierGroupDto> simplePageCondition(SafetyCarrierGroupDto query);
}
