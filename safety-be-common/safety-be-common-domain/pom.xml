<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>safety-be-parent</artifactId>
        <groupId>com.mi.oa.ee.safety</groupId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>safety-be-common-domain</artifactId>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>com.mi.oa.ee.safety</groupId>
            <artifactId>safety-be-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.oaucf</groupId>
            <artifactId>oaucf-space-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.oaucf</groupId>
            <artifactId>oaucf-organization-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
