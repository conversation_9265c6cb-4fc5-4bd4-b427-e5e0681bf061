package com.mi.oa.ee.safety.domain.ability;

import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.PhotoEditDo;
import com.mi.oa.ee.safety.domain.query.card.CardApplyQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/21 19:45
 */
public interface CardApplyAbility {

    /**
    * 根据uid和applyType填装申请单基本信息
    * @param cardApplyDo
    * @return        void
    * <AUTHOR>
    * @date          2024/1/5 19:45
    */
    void fillBaseInfoByUidAndApplyType(CardApplyDo cardApplyDo);

    /**
    * TODO
    * @param cardApplyDo
    * @return        void
    * <AUTHOR>
    * @date          2024/1/5 19:49
    */
    void checkCompleted(CardApplyDo cardApplyDo);


    void checkNameLength(CardApplyDo cardApplyDo);

    /**
     * 检查当前申请单的地区编码是否正常
     *
     * @param cardApplyDo
     * @return void
     * <AUTHOR>
     * @date 2023/11/20 17:12
     */
    void checkZoneCode(CardApplyDo cardApplyDo);


    /**
     * 检查ID是否存在
     *
     * @param cardApplyDo
     * @return void
     * <AUTHOR>
     * @date 2023/6/18 11:12
     */
    void checkId(CardApplyDo cardApplyDo);

    /**
     * 检查供应商是否能上传
     *
     * @param
     * @return void
     * <AUTHOR>
     * @date 2023/6/18 11:12
     */
    void checkCanSupplierUploadPhoto(CardApplyDo cardApplyDo);

    /**
     * @return void
     * @desc 通过uid检查是否存在
     * <AUTHOR> denghui
     * @date 2022/11/21 19:54
     */
    void checkIsExist(CardApplyDo cardApplyDo, Boolean needExist);

    /**
     * @param cardApplyDo
     * @return void
     * @desc 通过人员对象，填装申请单基础信息
     * <AUTHOR> denghui
     * @date 2022/11/21 20:02
     */
    void buildBaseInfoByPerson(CardApplyDo cardApplyDo);

    /**
     * 填装申请单空间信息
     *
     * @param cardApplyDo
     * @return void
     * <AUTHOR>
     * @date 2024/1/5 11:39
     */
    void fillSpaceInfo(CardApplyDo cardApplyDo);

    /**
     * @param id
     * @return com.mi.oa.ee.safety.domain.model.CardApplyDo
     * @desc 通过id获取申请单
     * <AUTHOR> denghui
     * @date 2022/11/22 17:31
     */
    CardApplyDo getCardApplyById(Long id);

    /**
     * @param cardApplyDo
     * @return void
     * @desc 填充申请单
     * <AUTHOR> denghui
     * @date 2022/11/22 17:39
     */
    void fillFullCardApply(CardApplyDo cardApplyDo);

    /**
     * @param cardApplyDo
     * @return void
     * @desc 创建idm用户
     * <AUTHOR> denghui
     * @date 2022/11/23 18:22
     */
    String createIdm(CardApplyDo cardApplyDo);

    /**
     * @param cardApplyDo
     * @return void
     * @desc 更新申请表单
     * <AUTHOR> denghui
     * @date 2022/11/23 19:59
     */
    void updateWithCardTimeById(CardApplyDo cardApplyDo);


    /**
     * @param cardApplyDo
     * @param pageNum
     * @param pageSize
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.domain.model.CardApplyDo>
     * @desc 分页查询获取申请单
     * <AUTHOR> denghui
     * @date 2022/11/24 12:06
     */
    PageModel<CardApplyDo> pageConditionApplyList(CardApplyDo cardApplyDo, Long pageNum, Long pageSize, List<String> parkCodes);

    /**
     * 分页查询申请单
     *
     * @param cardApplyQuery 查询条件
     * @return
     */
    PageModel<CardApplyDo> pageConditionApplyList(CardApplyQuery cardApplyQuery);

    /**
     * @param cardApplyDos
     * @return void
     * @desc 批量导入填充
     * <AUTHOR> denghui
     * @date 2022/11/28 9:43
     */
    void fillBatchImport(List<CardApplyDo> cardApplyDos, Boolean isHistoryCard);

    /**
     * @param id
     * @param photoUrl
     * @return void
     * @desc 上传/更新照片
     * <AUTHOR> denghui
     * @date 2022/11/28 20:31
     */
    void uploadPhoto(Long id, String photoUrl);

    /**
     * @param ids
     * @return void
     * @desc 批量通过
     * <AUTHOR> denghui
     * @date 2022/11/29 17:16
     */
    void operateApproval(List<Long> ids);

    /**
     * @param ids
     * @return void
     * @desc 批量拒绝
     * <AUTHOR> denghui
     * @date 2022/11/29 21:33
     */
    void operateReject(List<Long> ids);

    /**
     * @param ids
     * @return void
     * @desc 批量发送notify消息
     * <AUTHOR> denghui
     * @date 2022/11/29 22:04
     */
    void notifyOnsiteMessage(List<Long> ids, CardApplyStatusEnum applyStatusEnum);

    /**
     * @param ids
     * @return void
     * @desc 批量打印工卡
     * <AUTHOR> denghui
     * @date 2022/11/29 22:47
     */
    void operatePrint(List<Long> ids);

    /**
     * @param ids
     * @return void
     * @desc 批量开卡
     * <AUTHOR> denghui
     * @date 2022/11/29 22:54
     */
    void operateOpen(List<Long> ids);

    /**
     * @param ids
     * @return void
     * @desc 批量领取
     * <AUTHOR> denghui
     * @date 2022/11/29 22:56
     */
    void operateReceive(List<Long> ids);

    /**
     * @param ids
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardApplyDo>
     * @desc 通过id批量获取申请单
     * <AUTHOR> denghui
     * @date 2022/11/29 23:09
     */
    List<CardApplyDo> getCardApplyByIds(List<Long> ids);

    /**
     * 检查照片是否有空的
     *
     * @param cardApplyDo
     */
    void checkPhotoUrl(CardApplyDo cardApplyDo);

    void fillDeptInfo(CardApplyDo cardApplyDo);

    String getReceiptAddress(String parkCode);

    CardApplyDo getApplyByUidAndType(CardApplyDo applyDo);

    List<CardApplyDo> getApplyByFuzzyName(String name);

    List<CardApplyDo> getListByParkCodes(List<String> resourceCodes);

    void fillParkInfo(CardApplyDo cardApplyDo);

    List<String> getParkCodesByUc(Boolean isCheck);

    List<SafetySpaceParkDto> listSpaceByParkCodes(List<String> resourceCodes);

    Integer checkParkCodeExist(String parkCode);

    void fillTempApplyBaseInfo(CardApplyDo cardApplyDo);

    void checkEmpTypeIsCanCreateApply(CardApplyDo cardApplyDo);

    void fillOfficeAddress(CardApplyDo cardApplyDo);

    void fillEmpApplyInfo(CardApplyDo cardApplyDo);


    /**
     * 批量设置园区信息
     *
     * @param applyDos
     */
    void fillParkInfoBatch(List<CardApplyDo> applyDos);

    /**
     * 查询指定id生效中的工卡申请
     *
     * @param cardApplyDo
     * @return
     */
    void fillCardApplyDoById(CardApplyDo cardApplyDo);

    /**
     * 编辑申请单
     *
     * @param cardApplyDo
     */
    void updateCardApply(CardApplyDo cardApplyDo);

    /**
     * 查询申请单
     *
     * @param applyIdList
     * @return
     */
    List<CardApplyDo> findByIdList(List<Long> applyIdList);

    void checkEnableUpload(CardApplyDo cardApplyDo);


    /**
     * 编辑前校验申请单状态
     *
     * @param cardApplyDo
     */
    void checkOnEditCardApply(CardApplyDo cardApplyDo);

    /**
     * 根据uid和卡类型查询工卡信息
     *
     * @param uid
     * @param cardTypeEnum
     * @return
     */
    CardApplyDo findCardByUidAndCardType(String uid, CardTypeEnum cardTypeEnum);

    void checkIsEmpty(CardApplyDo cardApplyDo);

    void checkIsExpire(CardApplyDo cardApplyDo);

    void checkValidateTimeBeforeUpdate(CardApplyDo cardApplyDo);

    /**
     * 判断是否需要更新idm
     *
     * @param cardApplyDo
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2024/1/4 18:42
     */
    Boolean judgeIsNeedUpdateIdm(CardApplyDo cardApplyDo);

    void checkValidateTimeIsExceedIdmTime(CardApplyDo cardApplyDo);

    List<CardApplyDo> findAvatarUrlByUidList(List<String> uidList);

    List<CardApplyDo> findAvatarUrlByUserNameList(List<String> userNameList);

    void fillCardApplyByUidAndType(CardApplyDo cardApplyDo);

    void checkApplyIsExistBySameUidAndType(CardApplyDo cardApplyDo);

    void checkCardApplyIsRepeat(CardApplyDo cardApplyDo);

    CardApplyDo getCardApplyByUid(String uid);

    void fillCardApplyByUidAndTypes(CardApplyDo cardApplyDo);

    void refreshAvatarUrl(PhotoEditDo photoEditDo);

    void checkIsInternationalWhiteList(CardApplyDo cardApplyDo);

    void checkIsMiHomeDept(CardApplyDo cardApplyDo);

    Map<String, String> checkUcControl();

    void notifyIdmMessage(CardApplyDo cardApplyDo);
    List<CardApplyDo> getPhotoByPhoneLastFour(String phoneLastFour);
}
