package com.mi.oa.ee.safety.domain.ability;

import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;

import java.util.List;

/**
 * 电子工卡能力
 *
 * <AUTHOR>
 * @date 2023/9/18 15:13
 */
public interface CardElectronRecordAbility {

    /**
     * 检查当前人员是否已迁移过
     *
     * @param cardElectronRecordDo
     * @return void
     */
    void checkMigratedByUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 判断是否是迁移过来的电子工卡
     *
     * @param cardElectronRecordDo
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/12/13 16:43
     */
    Boolean judgeIsMigrate(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 判断是否全部可开通电子工卡
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/19 14:18
     */
    Boolean judgeIsAllCanOpen(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 检查当前人员的部门是否可以
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/26 15:19
     */
    Boolean judgePersonDeptIsCanOpen(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 检查状态是否可删除
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/19 20:14
     */
    void checkStatusIsCanForceDelete(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 根据uid获取已开通的电子工卡列表
     *
     * @param cardElectronRecordDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/9/19 20:13
     */
    List<CardElectronRecordDo> getOpenedListByUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 根据uid获取所有可删除的电子工卡列表
     *
     * @param cardElectronRecordDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/9/19 20:13
     */
    List<CardElectronRecordDo> getCanDeleteListByUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 根据电子工卡ID填装电子卡信息
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/19 20:07
     */
    void fillInfoById(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 检查当前电子工卡记录是否完毕
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 15:15
     */
    void checkCompleted(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 根据供应商侧电子卡号检查当前有效的电子工卡是否已存在
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 15:39
     */
    void checkHasEffectiveByElectronCardNum(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 检查当前人下是否有已经开通过电子工卡
     *
     * @param cardElectronRecordDo
     */
    Boolean judgeHasOpenedByUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 根据钱包侧电子卡-卡号填装电子卡信息
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 17:25
     */
    void fillInfoByElectronCardNum(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 根据工卡信息填装安防人员介质关系及安防权限
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 16:08
     */
    void fillWithSafetyPersonMediumByUidAndMediumCode(CardElectronRecordDo cardElectronRecordDo);


    /**
     * 根据实体工卡信息填装电子工卡的安防权限列表
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 19:09
     */
    void fillWithSafetyRightListByCardInfo(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 填装对应的安防日志
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 16:11
     */
    void fillWithSafetyOperateLog(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 检查状态是否开通中
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 19:15
     */
    void checkStatusIsOpening(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 根据钱包侧用户ID和UID判断当前人下是否有已经开通过电子工卡
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 19:42
     */
    Boolean judgeHasOpenedByElectronUserIdAndUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 检查状态是否已开通
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 19:45
     */
    void checkStatusIsOpened(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 获取当前用户的电子工卡列表
     *
     * @param electronRecordDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/12/19 16:20
     */
    List<CardElectronRecordDo> findListByUid(CardElectronRecordDo electronRecordDo);

    /**
     * 获取电子工卡并类聚
     *
     * @param electronRecordDoList
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/12/19 16:20
     */
    List<CardElectronRecordDo> selectAndGroupByType(List<CardElectronRecordDo> electronRecordDoList);

    /**
     * 填装安防介质根据卡信息
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/19 16:21
     */
    void fillWithSafetyMediumByMediumCode(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 检测状态是否删除中
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/19 16:21
     */
    void checkStatusIsDeleting(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 获取所有没被删除的电子工卡记录
     *
     * @param cardElectronRecordDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/9/27 17:20
     */
    List<CardElectronRecordDo> getCanForceDeleteListByUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 构建一个新的电子工卡的基础信息
     *
     * @param electronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/10/19 15:13
     */
    void buildNewBaseInfo(CardElectronRecordDo electronRecordDo);

    /**
     * 组装迁移时的电子工卡信息
     *
     * @param electronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/12 17:12
     */
    void buildNewForMigrate(CardElectronRecordDo electronRecordDo);

    /**
     * 根据当前电子卡状态，构建应用中电子卡状态
     *
     * @param cardElectronRecordDo
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2023/10/19 15:12
     */
    Integer buildCardElectronStatusForAppByStatus(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 获取已开通的电子工卡根据实体卡ID和用户ID
     *
     * @param cardElectronRecordDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/12/19 16:19
     */
    List<CardElectronRecordDo> findOpenedListByCardIdAndUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 填装电子卡别名从小米钱包
     *
     * @param electronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/19 16:19
     */
    void fillWithElectronNameFromPay(CardElectronRecordDo electronRecordDo);

    /**
     *
     * @param cardElectronRecordDo
     * @param existRightList
     * @param toAddRightList
     */
    void groupSafetyRightForAddPermission(CardElectronRecordDo cardElectronRecordDo, List<SafetyRightDo> existRightList,
                                          List<SafetyRightDo> toAddRightList);

    Boolean judgeHasOpenedByElectronUserIdAndUidV2(CardElectronRecordDo cardElectronRecordDo);

    List<CardElectronRecordDo> findOpenedListByUid(CardElectronRecordDo cardElectronRecordDo);
}
