package com.mi.oa.ee.safety.domain.ability;

import com.mi.oa.ee.safety.common.dto.CardPageConditionDto;
import com.mi.oa.ee.safety.common.dto.CardUserRight;
import com.mi.oa.ee.safety.common.dto.OperateLogDto;
import com.mi.oa.ee.safety.common.dto.SafetyCardToolDto;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.CardLeaveRecordDo;
import com.mi.oa.ee.safety.domain.model.CardTimeValidityDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyOperateLogDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.query.card.TempCardInfoQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/1 16:04
 */
public interface CardInfoAbility {

    /**
     * 根据ID填装卡信息
     *
     * @param cardInfoDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/19 19:41
     */
    void fillCardInfoById(CardInfoDo cardInfoDo);

    /**
     * 检查当前卡是否是正式卡
     *
     * @param cardInfoDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/26 14:43
     */
    void checkCardIsEmployee(CardInfoDo cardInfoDo);

    /**
     * 检查卡是否有效
     *
     * @param cardInfoDo
     * @return void
     * <AUTHOR>
     * @date 2023/8/16 16:58
     */
    void checkCardIsActive(CardInfoDo cardInfoDo);

    /**
     * 检查当前人员是否在新工卡系统中有工卡
     *
     * @param cardInfoDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/6 18:01
     */
    void checkIsHaveCardByUid(CardInfoDo cardInfoDo);

    /**
     * @param pageConditionDto
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.domain.model.CardInfoDo>
     * @desc 条件分页查询
     * <AUTHOR> denghui
     * @date 2022/12/2 14:56
     */
    PageModel<CardInfoDo> pageConditionList(CardPageConditionDto pageConditionDto, List<String> parkCodes);

    /**
     * 分页查询工卡信息
     *
     * @param query 查询条件
     * @return 工卡信息
     */
    PageModel<CardInfoDo> pageConditionList(TempCardInfoQuery query);

    void fillCardInfo(CardInfoDo cardInfoDo, Boolean isFillGroups);

    List<CardTimeValidityDo> getValidatePeriod(Long cardId);

    List<SafetyCarrierGroupDo> getListByMediumCode(String mediumCode, String parkCode);

    List<SafetyCarrierGroupDo> getListByMediumCodeandStatus(String mediumCode, String parkCode);

    CardInfoDo getOneById(Long id);

    PageModel<SafetyOperateLogDo> getListOperateLogs(Integer operateStatus, Integer operateType, Long pageNum,
                                                     Long pageSize, Long cardId);

    void fillOperateLog(List<SafetyOperateLogDo> list, List<OperateLogDto> operateLogDtos);

    void deleteById(Long id);

    void fillSafetyCardOperateLog(CardInfoDo cardInfoDo);

    void deletePersonMedium(String uid, String mediumCode, Long id);

    void restorePersonMedium(String uid, String mediumCode);

    void deleteValidateTime(String uid, ZonedDateTime startTime, ZonedDateTime endTime);

    void deleteValidateTime(Long cardId);

    void updateStatus(CardInfoDo cardInfoDo);

    void restoreValidateTime(String uid);

    void restoreValidateTimeByCardId(Long cardId);

    void buildValidateTime(CardInfoDo cardInfoDo);

    void refreshValidateTime(CardInfoDo cardInfoDo);

    void saveTime(CardTimeValidityDo cardValidateDo);

    Long save(CardInfoDo cardInfoDo);

    CardInfoDo getCardByStatus(String uid, List<CardStatusEnum> status);

    void fillCardInfoByUidAndCardType(CardInfoDo cardInfoDo);

    void deleteMedium(String mediumCode);

    void restoreMedium(String mediumCode);

    SafetyMediumDo fillMedium(CardInfoDo cardInfoDo);

    SafetyMediumDo saveMedium(SafetyMediumDo safetyMediumDO);

    List<SafetyRightDo> getCarrierGroupsByMediums(List<String> mediumCodes);

    void updateRightByMediumAndGroups(String mediumCode, Set<String> carrierGroupCodes, Integer isDelete);

    void updateTime(String uid, Long id);

    List<CardInfoDo> getUsingCardList(List<Integer> code);

    List<CardInfoDo> getNotActiveList(List<Integer> status);

    List<CardTimeValidityDo> getTimeByCardIds(List<Long> usingIds);

    void checkMediumExist(String mediumPhysicsCode, String mediumEncryptCode);

    void fillBatchImport(List<CardInfoDo> cardInfoDos, List<CardApplyDo> cardApplyDosWithIds);

    List<CardInfoDo> batchSaveWithIds(List<CardInfoDo> cardInfoDos);

    void fillBatchExcelImportValidateTime(List<CardTimeValidityDo> cardTimeValidityDtos,
                                          List<CardInfoDo> cardInfoDoWithIds, List<CardApplyDo> cardApplyDos);

    void batchSaveValidateTime(List<CardTimeValidityDo> cardTimeValidityDtos);

    List<CardInfoDo> getListCardInfoByPhysicCodes(List<String> mediumPhysicsCodes);

    List<CardInfoDo> getListCardInfoByEncryptCodes(List<String> mediumEncryptCodes);

    List<CardInfoDo> getListCardInfoByUid(List<String> uid);

    List<CardInfoDo> getListCardInfoByAccounts(List<String> accounts);

    void checkIsOccupation(CardInfoDo cardInfoDo);

    /**
     * 验证工卡是否已经被关联
     *
     * @param cardInfoDo
     */
    void checkCardIsOccupation(CardInfoDo cardInfoDo);

    CardInfoDo getCardByNums(SafetyCardToolDto safetyCardToolDto, List<CardStatusEnum> status);

    /**
     * 根据物理卡好查找工卡数据
     *
     * @param mediumPhysicsCode
     * @return
     */
    CardInfoDo getListCardInfoByPhysicCode(String mediumPhysicsCode);

    CardInfoDo findCardInfoByApplyId(Long applyId);

    /**
     * 根据申请单查询工卡信息
     *
     * @param applyIdList
     * @return
     */
    List<CardInfoDo> findCardInfoByApplyIdList(List<Long> applyIdList);

    /**
     * 查询工卡生效时间
     *
     * @param cardIdList
     * @return
     */
    List<CardTimeValidityDo> findValidatePeriodByCardIdList(List<Long> cardIdList);

    /**
     * 有效时间
     *
     * @param cardId
     * @return
     */
    List<CardTimeValidityDo> findValidateTimeByCardId(Long cardId);

    /**
     * 查询失效时间范围内的工卡记录
     *
     * @param endTimeFrom
     * @param endTimeTo
     * @return
     */
    List<Long> findCardIdByEffectTime(ZonedDateTime endTimeFrom, ZonedDateTime endTimeTo);

    /**
     * 校验是否可以重开开卡
     *
     * @param cardInfoDo
     */
    void canReopen(CardInfoDo cardInfoDo);

    /**
     * 加载生效时间
     *
     * @param cardInfoDo
     */
    void fillValidateTimeByCardId(CardInfoDo cardInfoDo);

    /**
     * 校验工卡是否可以编辑
     *
     * @param cardInfoDo
     */
    void editCheck(CardInfoDo cardInfoDo);

    void fillAndSaveCardRights(CardInfoDo cardInfoDo);

    void fillImportCardInfo(CardInfoDo cardInfoDo);

    void fillImportMedium(CardInfoDo cardInfoDo);

    void fillImportValidateTime(CardInfoDo cardInfoDo);

    /**
     * 复制权限到新介质
     *
     * @param cardInfoDo
     */
    void copySafetyRight(CardInfoDo cardInfoDo);

    void checkIsEmpty(CardInfoDo res);

    void checkIsUsing(CardInfoDo cardInfoDo);

    void fillPersonMediumLogDetail(CardInfoDo cardInfoDo);

    void fillSafetyRightLogDetail(CardInfoDo cardInfoDo);

    /**
     * 根据人员和介质查询安防人员介质记录
     *
     * @param uid
     * @param mediumCode
     * @return
     */
    SafetyPersonMediumDo getSafetyPersonMediumByMediumCodeAndUid(String uid, String mediumCode);

    /**
     * 检查物理卡号是否被占用
     *
     * @param cardInfoDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/7 16:39
     */
    void checkPhysicsCodeIsOccupied(CardInfoDo cardInfoDo);

    /**
     * 检查加密卡号是否被占用
     *
     * @param cardInfoDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/7 16:39
     */
    void checkEncryptCodeIsOccupied(CardInfoDo cardInfoDo);

    /**
     * 根据加密卡号填充工卡信息
     *
     * @param cardInfoDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/7 16:40
     */
    void fillInfoByEncryptCode(CardInfoDo cardInfoDo);


    void checkTempCardIsReturn(CardInfoDo cardInfoDo);

    void checkEmpCardIsExist(CardInfoDo cardParam);

    void checkCardIsExpireForReceive(CardInfoDo cardInfoDo);

    void checkIsExistRight(CardInfoDo cardInfoDo);

    void checkPermissionIsExceedCardValidateTime(CardInfoDo cardInfoDo);

    void checkIsExistAllRight(CardInfoDo cardInfoDo);

    void checkCardIsDestroy(CardInfoDo cardInfoDo);

    void checkCardIsUsing(CardInfoDo cardInfoDo);

    void deleteSafetyRightAndUpdateSync(CardInfoDo cardInfoDo);

    void fillCardLeaveRecordBaseInfo(CardInfoDo cardInfoDo);

    void checkLeaveRecordRepeated(CardInfoDo cardInfoDo);

    void checkTempAndEmpCardIsUsing(CardInfoDo cardInfoDo);

    List<CardLeaveRecordDo> findLeaveRecordByUidList(List<String> uidList);

    CardLeaveRecordDo findPreLeaveRecordByUid(String uid);

    void reportLeave(CardInfoDo cardInfoDo);

    void updateValidateTimeByUidAndTime(CardInfoDo cardInfoDo);

    void fillLocationParkCode(CardInfoDo cardInfoDo);

    Boolean checkIsM9ParkCode(CardInfoDo cardInfoDo);

    Boolean checkIsM9Depart(CardInfoDo cardInfoDo);

    boolean checkIsCHN(CardInfoDo cardInfoDo);

    /**
     * 填充临时卡
     *
     * @param cardInfoDo
     */
    void fillRecyclableCard(CardInfoDo cardInfoDo);

    void checkCardIsExistByUidAndType(CardInfoDo cardInfoDo);

    List<CardInfoDo> findListByMediumCodeList(List<String> mediumCodes);

    void fillCardInfoByApplyId(CardInfoDo cardInfoDo);

    /**
     * 校验是否能挂失
     * @param cardInfoDo
     */
    void checkBeforeLossCard(CardInfoDo cardInfoDo);

    /**
     * 挂失工卡
     * @param cardInfoDo
     */
    void lossCard(CardInfoDo cardInfoDo);

    /**
     * 解挂失校验
     * @param cardInfoDo
     */
    void checkBeforeRemoveLossCard(CardInfoDo cardInfoDo);

    /**
     * 家挂失
     * @param cardInfoDo
     */
    void removeLossCard(CardInfoDo cardInfoDo);

    void buildNewSafetyMedium(CardInfoDo cardInfoDo);

    void buildNewSafetyPersonMedium(CardInfoDo cardInfoDo);

    /**
     * 添加权限校验
     * @param cardInfoDo
     */
    void checkBeforeAddPermission(CardInfoDo cardInfoDo);

    /**
     * 补齐授予的权限
     * @param cardInfoDo
     */
    void fillGrantedSafetyRight(CardInfoDo cardInfoDo);

    /**
     * 对添加的权限分组
     * @param cardInfoDo
     * @param safetyRightList
     */
    void groupSafetyRightForAddPermission(CardInfoDo cardInfoDo, List<SafetyRightDo> safetyRightList);

    /**
     * 装载安防介质
     * @param cardInfoDo
     */
    void fillSafetyMedium(CardInfoDo cardInfoDo);

    void checkCardStatusIsCanCreateReissueApply(CardInfoDo cardInfo);


    List<CardUserRight> findOpenedRightList(CardInfoDo cardInfoDo);

    CardInfoDo findUsingCardInfoByUidAndCardType(String uid, CardTypeEnum employeeCard);

    void checkIsRepeatOpen(CardInfoDo cardInfoDo);

    void checkTempCardIsExist(CardInfoDo cardParam);

    PageModel<CardInfoDo> pageConditionListV2(CardPageConditionDto pageConditionDto, List<String> parkCodes);


}
