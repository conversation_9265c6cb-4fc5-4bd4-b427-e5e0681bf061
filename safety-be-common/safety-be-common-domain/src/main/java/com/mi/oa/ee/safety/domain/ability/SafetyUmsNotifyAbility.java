package com.mi.oa.ee.safety.domain.ability;

import com.mi.oa.ee.safety.domain.model.SafetyUmsNotifyDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 21:24
 */
public interface SafetyUmsNotifyAbility {

    /**
     * @param safetyUmsNotifyDo
     * @return void
     * @desc 构建map能力
     * <AUTHOR> denghui
     * @date 2023/6/13 14:55
     */
    void buildMap(SafetyUmsNotifyDo safetyUmsNotifyDo);

    /**
     * 
     *
     * @param safetyUmsNotifyDo
     * @return void
     * <AUTHOR>
     * @date 2023/6/18 11:42
     */
    void buildSupplierSyncUmsNotify(SafetyUmsNotifyDo safetyUmsNotifyDo);

    /**
     * 构建工卡申请单待确认信息
     *
     * @param safetyUmsNotifyDo
     * @return void
     * <AUTHOR>
     * @date 2023/6/18 11:43
     */
    void buildCardApplyWaitConfirmUmsNotify(SafetyUmsNotifyDo safetyUmsNotifyDo);

    void batchCreateNotify(List<SafetyUmsNotifyDo> safetyUmsNotifyDos);

    void batchCreateUmsNotify(List<SafetyUmsNotifyDo> safetyUmsNotifyDos);

    void sendLossOrRemoveLossUms(SafetyUmsNotifyDo safetyUmsNotifyDo);
}
