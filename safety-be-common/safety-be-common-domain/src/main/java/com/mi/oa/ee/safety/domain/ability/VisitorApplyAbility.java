package com.mi.oa.ee.safety.domain.ability;

import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.domain.model.SafetyConfigUserDo;
import com.mi.oa.ee.safety.domain.model.VisitorApplyDO;
import com.mi.oa.ee.safety.domain.model.VisitorApplyVisitUserInfoDo;
import com.mi.oa.ee.safety.domain.model.VisitorApplyVisitorParkingDo;
import com.mi.oa.ee.safety.domain.model.VisitorParkDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.organization.enums.PersonUniqueEnum;
import com.mi.oa.infra.organization.req.PersonEntryReq;

import java.util.List;
import java.util.Map;

/**
 * 申请单 能力接口层
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/8/21 20:34
 */

public interface VisitorApplyAbility {

    /**
     * 申请单取消前检查
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/4/4 17:06
     */
    void checkBeforeCancel(VisitorApplyDO visitorApplyDO);

    /**
     * 个人签出前检查
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/30 11:27
     */
    void checkBeforePersonCheckOut(VisitorApplyDO visitorApplyDO);

    /**
     * 加载当前邀请单下访客列表
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/28 21:09
     */
    void loadVisitorInfoListByApplyId(VisitorApplyDO visitorApplyDO);

    /**
     * 签出前检查
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/28 20:36
     */
    void checkApplyBeforeCheckOut(VisitorApplyDO visitorApplyDO);

    /**
     * 填装当前邀请信息
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/28 20:36
     */
    void fillApplyInfoById(VisitorApplyDO visitorApplyDO);

    /**
     * 检查是否需要检查访客日期
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/24 14:51
     */
    void checkIsNeedCheckVisitorDays(VisitorApplyDO visitorApplyDO);

    /**
     * 检测是否需要检测高频访客
     * @param visitorApplyDO
     * @return
     */
    Boolean checkIsNeedCheckHighFrequency(VisitorApplyDO visitorApplyDO);

    /**
     * 检测高频访客
     * @param visitorApplyDO
     * @param visitUserInfoDo
     * @return
     */
    void checkHighFrequency(VisitorApplyDO visitorApplyDO, VisitorApplyVisitUserInfoDo visitUserInfoDo);

    /**
     * 检查是否访客日期超过了
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/24 14:56
     */
    void checkNowVisitorDaysExceed(VisitorApplyDO visitorApplyDO);

    /**
     * 加载当前人员对应的申请单信息
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/24 18:27
     */
    void loadVisitorApplyDOListByPhoneAndStatusList(VisitorApplyDO visitorApplyDO);

    /**
     * 检查是否访客日期交叉了
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/24 14:57
     */
    void checkNowVisitorDaysCross(VisitorApplyDO visitorApplyDO);

    /**
     * 检查是否受控
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/29 11:57
     */
    void checkControlDays(VisitorApplyDO visitorApplyDO);

    void checkControlPark(VisitorApplyDO visitorApplyDO);

    /**
     * 检查手机号是否重复
     *
     * @param visitorApplyDO
     * @return void
     * <AUTHOR>
     * @date 2023/3/29 11:57
     */
    void checkIdentityRepeated(VisitorApplyDO visitorApplyDO);

    Map<String, Object> buildVisitorParamMap(VisitorApplyDO apply, SafetyUmsConfigEnum eum);

    String buildParkFullName(VisitorApplyDO apply);

    String buildParkAddress(VisitorApplyDO apply);

    void fillBpmCode(VisitorApplyDO applyDo);

    void fillApplyCode(VisitorApplyDO applyDo);

    VisitorApplyDO fillBpmData(String assignee, int status, String businessKey);

    void checkApplyExists(String applyCode, Boolean needExists);

    void checkVisitTimeAhead(VisitorApplyDO applyDo);

    void checkParkingAllowance(VisitorApplyDO applyDo);

    List<VisitorParkDo> getStructuredPark(List<VisitorParkDo> parkList, boolean isConditionFilterBox);

    /**
     * 通过组织编码查询园区
     *
     * @param parkList             园区数据
     * @param isConditionFilterBox 过滤条件
     * @param orgCode              组织编码
     * @return
     */
    List<VisitorParkDo> getStructuredPark(List<VisitorParkDo> parkList, boolean isConditionFilterBox, String orgCode);


    void fillVisitorReasonDO(VisitorApplyDO applyDO);

    void checkVisitApplyIsActive(VisitorApplyDO visitorApplyDO);

    PersonEntryReq fillVisitorInfo(String name, String uniqueValue, PersonUniqueEnum uniqueType);

    List<VisitorApplyDO> selectAndSort(List<VisitorApplyDO> apply);

    void fillParkInfo(VisitorApplyDO visitorApplyDO);

    void checkParkExist(String parkCode, String buildingCode, String floorCode, boolean isNeed);

    void fillVisitorEncryptPhone(VisitorApplyDO applyDo);

    List<SafetyConfigUserDo> getVisitorBlackList(VisitorApplyDO applyDo);

    VisitorApplyVisitUserInfoDo getVisitorByIdAndUid(Long id, String uid);

    void loadSafetyMedium(VisitorApplyVisitUserInfoDo visitUserInfoDo);

    void loadActivePersonMedium(VisitorApplyVisitUserInfoDo visitUserInfoDo);

    void loadVisitorApply(VisitorApplyVisitUserInfoDo visitUserInfoDo);

    void loadVisitorApply(List<VisitorApplyVisitUserInfoDo> visitUserInfoDos);

    void checkReleaseParking(VisitorApplyVisitUserInfoDo visitUserInfoDo);

    void loadVisitorSafetyRecord(List<VisitorApplyVisitUserInfoDo> visitUserInfoDos);

    void fillVisitorTodaySignTime(List<VisitorApplyVisitUserInfoDo> visitUserInfoDos);

    void checkVisitorInfosIsEmpty(VisitorApplyDO visitorApplyDO);

    Boolean checkIsSendToExecutive(VisitorApplyDO visitorApply);

    Boolean checkIsSendToReception(VisitorApplyDO visitorApply);

    Boolean canCheck(VisitorApplyDO visitorApply);

    void checkBeforeReceptionCheckIn(VisitorApplyDO visitorApplyDO);

    PageModel<VisitorApplyDO> loadPageVisitorAndReceptionApplys(VisitorApplyDO visitorApplyDO);

    void checkReasonId(VisitorApplyDO visitorApplyDo);

    void checkAndUpdateReception(VisitorApplyDO visitorApplyDO);

    void loadParkingConfig(List<VisitorApplyVisitUserInfoDo> visitUserInfos);

    void loadVisitorParking(List<VisitorApplyVisitUserInfoDo> visitUserInfos);

    void loadReceptionApply(VisitorApplyDO visitorApplyDo);

    void loadParkingConfig(VisitorApplyVisitUserInfoDo visitUserInfo);

    void fillIsCarVisitor(VisitorApplyVisitUserInfoDo visitUserInfo);

    void loadVisitorParking(VisitorApplyVisitUserInfoDo visitUserInfo);

    void releaseParking(VisitorApplyVisitorParkingDo visitorParking);

    void restoreParkingNumCache(VisitorApplyVisitUserInfoDo visitUserInfoDo);

    String qualityIdNumber(String uid);

    /**
     * 记录打卡次数
     * @param visitUserInfoDo
     */
    void countPrint(VisitorApplyVisitUserInfoDo visitUserInfoDo);
}
