package com.mi.oa.ee.safety.domain.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/22 10:28
 */
@AllArgsConstructor
public enum CardApplyDomainErrorCodeEnum implements DomainErrorCode {
    UID_EMPTY_ERROR(1, "{{{用户uid为空}}}"),
    CARD_APPLY_NOT_EXIST(2, "{{{工卡申请单不存在错误}}}"),
    CARD_APPLY_REPEATED(3, "{{{工卡申请单已存在}}}"),
    EXCEL_EXPORT_ERROR(4, "{{{excel导出失败}}}"),
    ILLEGAL_ACCESS_ERROR(5, "{{{非法访问异常}}}"),
    INVOCATION_TARGET(6, "{{{调用目标异常}}}"),
    RESPONSIBLE_NOT_EXIST_ERROR(7, "{{{责任人不存在错误}}}"),
    IMPORT_PARAM_ERROR(8, "第%s行,导入参数%s错误"),
    PERSON_MEDIUM_NOT_EXIST(9, "{{{当前人与介质关系不存在}}}"),
    BATCH_PHOTO_URL_EMPTY(10, "{{{存在照片地址为空的单子，操作失败}}}"),
    PHONE_EMPTY(11, "{{{电话号码为空,请去用户中心完备信息}}}"),
    NAME_EMPTY(12, "{{{姓名为空,请去用户中心完备信息}}}"),
    SURNAME_EMPTY(13, "{{{姓氏拼音为空,请去用户中心完备信息}}}"),
    PIN_YIN_NAME_EMPTY(14, "{{{名字拼音为空,请去用户中心完备信息}}}"),
    COMPANY_NAME_EMPTY(15, "{{{公司名字为空,请去用户中心完备信息}}}"),
    NATION_EMPTY(16, "{{{国籍为空,请去用户中心完备信息}}}"),
    RESPONSIBLE_EMPTY(17, "{{{责任人为空,请去用户中心完备信息}}}"),
    INNER_STAFF(18, "内部员工，不允许再开通合作卡，若需要额外权限请联系工卡管理人员开通!"),
    PERSON_NOT_EXIST(19, "{{{人员信息不存在,请手动填写}}}"),
    NOT_WHITE_PARK_EMP(20, "非开放园区入职人员：%s"),

    CARD_APPLY_TYPE_NOT_FOUND(21, "{{{工卡申请单类型错误}}}"),

    CARD_APPLY_TYPE_NOT_SUPPORT_NOTICE(22, "{{{工卡申请单不支持领取通知}}}"),
    NOT_EMP(23, "{{{合作伙伴无需走该流程创建}}}"),
    NOT_WAIT_PHOTO_STATUS(24, "当前不是待拍照状态, 不能改为自行上传"),
    CARD_APPLY_EDIT_STATUS_NOT_SUPPORT(25, "{{{工卡申请单已领取，不支持编辑}}}"),
    HROD_GET_WORK_ADDRESS_FAILED(26, "获取报道地址失败，hrod人员数据不存在，或已离职"),
    EMP_TYPE_NOT_NEED_EMP_CARD(27, "{{{当前员工类型无需正式卡}}}"),
    SYNC_ERROR(32, "{{{数据不存在，请先制卡}}}"),
    ID_EMPTY_ERROR(33, "{{{申请单id为空}}}"),
    PHOTO_EMPTY_ERROR(34, "{{{上传的图片为空}}}"),
    NOT_SUPPLIER_UPLOAD_PHOTO_ERROR(35, "{{{不是待拍照的状态，不能上传图片}}}"),
    CARD_APPLY_EXPIRE(36, "{{{卡已过期}}}"),
    CARD_NOT_MATCH_OLD(37, "{{{新工卡和老工卡的物理卡号和加密卡号不一致}}}"),
    TEMP_CARD_EXPIRE(38, "{{{临时卡已过期，请在老系统核实该工卡是否已归还，若需继续使用请修改有效期，重新操作数据同步}}}"),
    PROPERTY_CARD_EXIST(39, "{{{物业卡申请已存在}}}"),
    COOPERATION_CARD_EXIST(40, "{{{合作卡申请已存在}}}"),
    NOT_NATIONALITY_WHITE_LIST_ERROR(41, "{{{非国际白名单}}}"),
    COUNTRY_ID_EMPTY(42, "{{{国家id为空}}}"),
    MI_HOME_NOT_NEED_CARD(43, "{{{非小米之家员工，无需制卡}}}"),
    FILE_NAME_NOT_CORRECT(44, "请填写正确账号名"),
    PHOTO_URL_GENERATE_FAILED(45, "照片地址生成失败"),
    EXCEED_MAX_EXPORT_PHOTO_SIZE(46, "超出最大导出数量"),
    ACCOUNT_EMPLOYEE_NOT_CORRECT(47, "{{{账号或工号未找到对应人员信息}}}"),
    EMP_STR_CODE_EXIST(48, "{{{员工类型:%s 已存在}}}"),
    PIC_PIX_NOT_OK(49, "{{{图片像素非1500*1500}}}"),
    ZIP_FILE_CONTENT_ERROR(50, "{{{上传文件处理失败}}}"),
    PIC_TYPE_ERROR(51, "{{{文件不是图片格式}}}"),
    ;

    private Integer code;

    private String desc;

    @Override
    public int getBizCode() {
        return BizCodeEnum.CARD.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return code;
    }

    @Override
    public String getErrDesc() {
        return desc;
    }
}
