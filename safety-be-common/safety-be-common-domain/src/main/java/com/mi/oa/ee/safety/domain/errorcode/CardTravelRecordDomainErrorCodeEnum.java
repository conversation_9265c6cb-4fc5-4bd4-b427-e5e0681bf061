package com.mi.oa.ee.safety.domain.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.InfraErrorCode;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/28 20:38
 */
@AllArgsConstructor
public enum CardTravelRecordDomainErrorCodeEnum implements InfraErrorCode {

    CITY_ID_IS_EMPTY(1, "cityId为空"),
    UID_IS_EMPTY(2, "UID为空"),
    TRAVEL_APPLY_ID_IS_EMPTY(3, "差旅申请单ID为空"),
    TRAVEL_TIME_IS_EMPTY(4, "差旅的时间为空"),
    TRAVEL_DESTINATION_IS_EMPTY(5, "差旅的目的地为空"),
    TRAVEL_RECODE_IS_EXISTED(6, "当前工卡差旅记录已存在"),
    ADDRESS_IS_EMPTY(7, "地址信息不存在或差旅目的地不是地级市"),
    TRAVEL_IS_STARTED(8, "当前差旅已经开始"),
    TRAVEL_SAFETY_OPERATE_ID_IS_NULL(9, "当前差旅所对应的操作ID为空"),
    TRAVEL_SAFETY_OPERATE_DO_IS_NULL(10, "当前差旅所对应的操作对象为空"),
    TRAVEL_SAFETY_OPERATE_STATUS_IS_NOT_DONE(11, "当前差旅对应的操作还未执行成功"),
    SAFETY_SPACE_LIST_IS_EMPTY(12, "当前安防空间列表信息为空"),
    TRAVEL_QUERY_STATUS_NOT_EMPTY(13, "查询状态不能为空"),
    ;

    private Integer code;

    private String desc;

    @Override
    public int getBizCode() {
        return BizCodeEnum.CARD_TRAVEL_RECORD.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return code;
    }

    @Override
    public String getErrDesc() {
        return desc;
    }
}
