package com.mi.oa.ee.safety.domain.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;

/**
 * <AUTHOR>
 * @date 2022/8/19 11:35
 */
public enum SafetyClassErrorCodeEnum implements DomainErrorCode {

    IS_EMPTY(1, "{{{当前记录为空}}}"),
    CLASS_CODE_IS_EMPTY(2, "{{{安防分类编码为空}}}"),
    CLASS_NOT_EXISTS(3, "当前安防分类不存在 %s");

    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    SafetyClassErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

    @Override
    public int getBizCode() {
        return BizCodeEnum.SAFETY_CLASS.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return errDesc;
    }
}
