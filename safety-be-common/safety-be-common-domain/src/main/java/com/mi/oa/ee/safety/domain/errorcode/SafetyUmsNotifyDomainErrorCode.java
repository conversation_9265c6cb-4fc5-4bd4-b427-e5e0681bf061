package com.mi.oa.ee.safety.domain.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/6/12 21:55
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SafetyUmsNotifyDomainErrorCode implements DomainErrorCode {

    CONFIG_ID_IS_EMPTY(1, "{{{配置id不能为空}}}"),
    APP_CODE_IS_EMPTY(2, "{{{APPCODE不能为空}}}"),
    RECEIVER_IS_EMPTY(3, "{{{接收人不能为空}}}"),
    SEND_TIME_IS_EMPTY(4, "{{{发送时间不能为空}}}");

    private Integer code;

    private String desc;

    @Override
    public int getBizCode() {
        return BizCodeEnum.CARD.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return code;
    }

    @Override
    public String getErrDesc() {
        return desc;
    }
}
