package com.mi.oa.ee.safety.domain.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;

public enum TKSCardInfoDomainErrorEnum implements DomainErrorCode {
    INSERT_TKS_CARD_INFO_ERROR(1, "添加用户信息失败"),
    PERSON_MEDIUM_ERROR(2, "人卡关系不存在"),
    TKS_CARD_INFO_ERROR(3, "用户信息不存在"),
    MEDIUM_IS_EMPTY(4, "卡片介质为空"),
    TKS_CARD_AUTH_ERROR(5, "权限记录缺失"),
    CARRIER_GROUP_CODES_ERROR(6, "权限组记录不存在"),
    EMP_NOT_EXIST(7, "人员组织信息不存在"),
    PEOPLE_IS_XIAOMI_EMPLOYEE(8,"该人员是小米在职员工！"),
    LARK_MEDIUM_PHYSICS_CODE(9, "物理卡号或加密卡号不能为空"),
    CURRENT_CARD_OCCUPIED(10, "当前卡不可用,或已被其他人占用");
    private int errCode;

    private String errDesc;

    TKSCardInfoDomainErrorEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

    @Override
    public int getBizCode() {
        return BizCodeEnum.KING_STORE_CARD.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return errDesc;
    }
}
