package com.mi.oa.ee.safety.domain.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;
import lombok.AllArgsConstructor;

/**
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/9/27 20:44
 */
@AllArgsConstructor
public enum VisitorParkDomainErrorCodeEnum implements DomainErrorCode {

    PARK_NOT_EXIST(1, "{{{园区不存在}}}"),
    PARK_EXISTS(2, "{{{园区已存在，请勿重复添加}}}"),
    PARK_NOT_HAS_CARRIERS(3, "{{{园区没有绑定对应的闸机}}}"),
    PARK_NOT_ENABLE(4, "{{{园区状态未启用}}}"),
    ;
    private Integer code;

    private String desc;

    @Override
    public int getBizCode() {
        return BizCodeEnum.SPACE.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return code;
    }

    @Override
    public String getErrDesc() {
        return desc;
    }
}
