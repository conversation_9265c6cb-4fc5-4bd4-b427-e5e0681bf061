package com.mi.oa.ee.safety.domain.model;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2025/1/16 16:56
 */
@Data
public class CardApplyConfigDo {

    private Long id;

    /**
     * 国家id country_id
     */
    private String countryId;

    private String countryName;

    /**
     * 省份id province_id
     */
    private String provinceId;

    private String provinceName;

    /**
     * 城市id city_id
     */
    private String cityId;

    private String cityName;
    /**
     * 园区编码 park_code
     */
    private String parkCode;

    private String parkName;

    /**
     * 员工类型集 emp_type_str
     */
    private String empTypeStr;

    /**
     * 工卡类型 card_type
     */
    private Integer cardType;

    /**
     * 申请单配置状态0：禁用 1：启用 config_status
     */
    private Integer configStatus;

    /**
     * 制卡单初始状态 apply_initial_status
     */
    private Integer applyInitialStatus;

    /**
     * 备注 remark
     */
    private String remark;

    private String updateUser;

    private ZonedDateTime updateTime;

    private SafetyPersonDo updatePerson;
}
