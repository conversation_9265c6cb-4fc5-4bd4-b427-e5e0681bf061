package com.mi.oa.ee.safety.domain.model;

import com.mi.oa.infra.oaucf.core.domain.DomainObject;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/08/18/03:46
 */
@Data
public class SafetyRightDo extends DomainObject<SafetyRightDo> implements Serializable {

    private static final long serialVersionUID = -1006615765990171952L;
    /**
     * 权限ID
     */
    private Long id;

    /**
     * 人员ID uid
     */
    private String uid;

    /**
     * 介质编码 medium_code
     */
    private String mediumCode;

    /**
     * 载体集编码 carrier_group_code
     */
    private String carrierGroupCode;

    /**
     * 供应商编码 supplier_code
     */
    private String supplierCode;

    /**
     * 供应商侧校验编码 supplier_check_code
     */
    private String supplierCheckCode;

    /**
     * 供应商侧访问码，可以是json等 supplier_access_code
     */
    private String supplierAccessCode;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 最近一次操作时间
     */
    private ZonedDateTime operateTime;

    /**
     * 开始时间 start_time
     */
    private ZonedDateTime startTime;

    /**
     * 同步状态：0：待同步 1：已同步  2：同步异常 sync_status
     */
    private Integer syncStatus;

    /**
     * 结束时间 end_time
     */
    private ZonedDateTime endTime;

    /**
     * 是否删除 非0表示删除
     */
    private Long isDeleted;

    /**
     * 安防人员
     */
    private SafetyPersonDo safetyPersonDo;

    /**
     * 安防介质
     */
    private SafetyMediumDo safetyMediumDo;

    /**
     * 安防载体集
     */
    private SafetyCarrierGroupDo safetyCarrierGroup;

    @Override
    protected boolean sameIdentityAs(SafetyRightDo safetyRightDO) {
        if (this.getMediumCode() != null && this.getCarrierGroupCode() != null &&
                safetyRightDO.getMediumCode() != null && safetyRightDO.getCarrierGroupCode() != null) {
            return this.getMediumCode().equals(safetyRightDO.getMediumCode()) &&
                    this.getCarrierGroupCode().equals(safetyRightDO.getCarrierGroupCode());
        }
        return false;
    }
}