package com.mi.oa.ee.safety.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysSequenceDo {
    /**
     * 编码生成code
     */
    private String code;

    /**
     * 编码前缀
     */
    private String prefix;

    /**
     * 时间格式化
     */
    private String dateFormat;

    /**
     * 编码连接符
     */
    private String concat;

    /**
     * 编码步长
     */
    private Long step;

    /**
     * 编码当前序列
     */
    private Long current;

    /**
     * 序列长度
     */
    private Integer seqLength;
}
