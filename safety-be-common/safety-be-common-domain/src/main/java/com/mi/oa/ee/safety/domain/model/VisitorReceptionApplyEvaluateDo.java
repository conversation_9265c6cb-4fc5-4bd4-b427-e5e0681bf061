package com.mi.oa.ee.safety.domain.model;

import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/5/8 20:44
 */
@Data
public class VisitorReceptionApplyEvaluateDo implements Serializable {

    private static final long serialVersionUID = -1167939380017036475L;
    /**
     * id
     */
    private Long id;

    /**
     * 接待申请ID reception_apply_id
     */
    private Long receptionApplyId;

    /**
     * 评价内容json evaluate_content
     */
    private String evaluateContent;

    /**
     * 是否有效 0: 正常 1: 删除
     */
    private Long isDeleted;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后更新人
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private ZonedDateTime createTime;

    /**
     * 最后更新时间
     */
    private ZonedDateTime updateTime;
}
