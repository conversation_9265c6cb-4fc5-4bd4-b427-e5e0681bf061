package com.mi.oa.ee.safety.domain.query.card;

import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyGroupStatusEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/4/1 22:25
 */
@Data
public class CardGroupConfigGroupApplyUserQuery extends BaseReq {

    private String cardGroupCode;

    private Integer cardPermissionApplyGroupStatus;

    private String uid;

    private Long endTime;
}
