package com.mi.oa.ee.safety.domain.service;

import com.mi.oa.ee.safety.common.dto.BatchOperateDto;
import com.mi.oa.ee.safety.common.dto.ImportErrorDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.dto.UpdatePersonDto;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.domain.model.CardApplyConfigDo;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.query.card.CardApplyQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/21 19:21
 */
public interface CardApplyDomainService {

    /**
     * 填装申请单信息根据账号和申请单类型
     *
     * @param cardApplyDo
     * @return void
     * <AUTHOR>
     * @date 2024/1/25 14:14
     */
    void fillBaseInfoByUidAndApplyType(CardApplyDo cardApplyDo);

    /**
     * 创建前填装
     *
     * @param cardApplyDo
     * @return void
     * <AUTHOR>
     * @date 2024/1/5 19:15
     */
    void fillBeforeCreate(CardApplyDo cardApplyDo);

    /**
     * 创建合作卡前检查
     *
     * @param cardApplyDo
     * @return void
     * <AUTHOR>
     * @date 2024/1/5 19:17
     */
    void checkBeforeCreateForCoopCard(CardApplyDo cardApplyDo);

    /**
     * 驻场合作卡保存前校验
     *
     * @param cardApplyDo
     * @return void
     * <AUTHOR>
     * @date 2024/1/4 17:45
     */
    void checkBeforeCreateForOnSite(CardApplyDo cardApplyDo);

    /**
     * 检查是否供应商可以上传图片
     *
     * @param cardApplyDo
     * @return void
     * <AUTHOR>
     * @date 2023/6/18 10:58
     */
    void checkCanSupplierUploadPhoto(CardApplyDo cardApplyDo);

    /**
     * @param cardApplyDo
     * @return void
     * @desc 通过uid检查是否存在
     * <AUTHOR> denghui
     * @date 2022/11/21 19:22
     */
    Boolean judgeIsExist(CardApplyDo cardApplyDo);

    /**
     * @param id
     * @return com.mi.oa.ee.safety.domain.model.CardApplyDo
     * @desc
     * <AUTHOR> denghui
     * @date 2022/11/22 17:27
     */
    CardApplyDo getDetailCardApply(Long id);

    /**
     * @param cardApplyDo
     * @return void
     * @desc
     * <AUTHOR> denghui
     * @date 2022/11/23 18:38
     */
    Long save(CardApplyDo cardApplyDo, Boolean isOnSite);

    /**
     * @param cardApplyDo
     * @return void
     * @desc 更新申请表单
     * <AUTHOR> denghui
     * @date 2022/11/23 19:58
     */
    void updateWithCardTimeById(CardApplyDo cardApplyDo);


    /**
     * 编辑申请单
     *
     * @param cardApplyDo
     */
    void updateCardApply(CardApplyDo cardApplyDo);

    /**
     * @param cardApplyDo
     * @param pageNum
     * @param pageSize
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.domain.model.CardApplyDo>
     * @desc 条件分页查询列表
     * <AUTHOR> denghui
     * @date 2022/11/24 11:59
     */
    PageModel<CardApplyDo> pageConditionApplyList(CardApplyDo cardApplyDo, Long pageNum, Long pageSize, boolean flag);

    /**
     * @param cardApplyQuery
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.domain.model.CardApplyDo>
     * @desc 条件分页查询列表
     * <AUTHOR> denghui
     * @date 2022/11/24 11:59
     */
    PageModel<CardApplyDo> pageConditionApplyList(CardApplyQuery cardApplyQuery, boolean flag);

    /**
     * 填装申请单全部信息
     *
     * @param applyDo
     * @return void
     * <AUTHOR>
     * @date 2024/1/5 11:48
     */
    void fillFullCardApply(CardApplyDo applyDo);

    /**
     * 补齐工卡申请单信息
     *
     * @param applyDo
     */
    void fillFullCardApplyV2(CardApplyDo applyDo);

    /**
     * 补齐工卡申请单信息
     *
     * @param applyDos
     * @return
     */
    void fillFullCardApplyV2(List<CardApplyDo> applyDos);


    /**
     * @return void
     * @desc 批量导入
     * <AUTHOR> denghui
     * @date 2022/11/25 12:49
     */
    List<ImportErrorDto> batchSave(List<CardApplyDo> cardApplyDos);

    /**
     * @param id
     * @param photoUrl
     * @return void
     * @desc 上传更新照片
     * <AUTHOR> denghui
     * @date 2022/11/28 20:29
     */
    void uploadPhoto(Long id, String photoUrl);

    /**
     * @param batchOperateDto
     * @return void
     * @desc 批量操作
     * <AUTHOR> denghui
     * @date 2022/11/29 15:24
     */
    void batchOperate(BatchOperateDto batchOperateDto);

    /**
     * @param parkCode
     * @return boolean
     * @desc 判断当前园区是否支持工卡
     * <AUTHOR> denghui
     * @date 2022/12/1 15:06
     */
    boolean hasCarrierGroups(String parkCode);

    void checkParams(CardApplyDo cardApplyDo);

    String getReceiptAddress(String parkCode);

    void fillCardApplyByIdmNotify(CardApplyDo toDo, UpdatePersonDto req);

    List<SafetySpaceParkDto> listSpaceByParkCodes(List<String> resourceCodes);

    Integer checkParkCodeExist(String parkCode);

    List<CardApplyDo> queryApplyByFuzzyName(String name);

    void fillTempApplyInfo(CardApplyDo cardApplyDo);

    void checkBeforeCreate(CardApplyDo cardApplyDo);

    void fillEmpApplyInfo(CardApplyDo cardApplyDo);


    /**
     * 根据申请单Id查询申请单信息
     *
     * @param cardApplyDo
     * @return
     */
    void fillCardApplyDoById(CardApplyDo cardApplyDo);

    /**
     * 查询申请单
     *
     * @param applyIdList
     * @return
     */
    List<CardApplyDo> findByIdList(List<Long> applyIdList);

    /**
     * 完成工卡申请
     *
     * @param cardApplyDo
     */
    void complete(CardApplyDo cardApplyDo);

    /**
     * 通知
     *
     * @param cardApplyDo
     */
    void receiveNotice(CardApplyDo cardApplyDo);

    /**
     * 根据uid和卡类型查询工卡申请信息
     *
     * @param uid
     * @param cardTypeEnum
     * @return
     */
    CardApplyDo findCardByUidAndCardType(String uid, CardTypeEnum cardTypeEnum);

    void checkEnableUpload(CardApplyDo cardApplyDo);

    void fillCardApplyWithBaseAndParkById(CardApplyDo cardApplyDo);

    void fillCoopCardApplyByPerson(CardApplyDo cardApplyDo);

    void notifyMessage(CardApplyDo cardApplyDo);

    void checkIsExpire(CardApplyDo cardApplyDo);

    void checkValidateTimeBeforeUpdate(CardApplyDo cardApplyDo);

    Boolean judgeIsNeedUpdateIdm(CardApplyDo cardApplyDo);

    void checkValidateTimeIsExceedIdmTime(CardApplyDo cardApplyDo);

    List<CardApplyDo> findAvatarUrlByUserNameOrUidList(CardApplyDo cardApplyDo);

    void fillCardApplyByUidAndType(CardApplyDo cardApplyDo);

    void checkBeforeCreateForMigrate(CardApplyDo cardApplyDo);

    void doIdmCreatePerson(CardApplyDo cardApplyDo);

    CardApplyDo getInfoByUid(String uid);

    String saveOne(CardApplyDo aDo);

    void fillCardApplyByUidAndTypes(CardApplyDo cardApplyDo);

    void checkBeforeSaveCoopOrPropertyCard(CardApplyDo cardApplyDo);

    List<CardApplyDo> getPhotoByPhoneLastFour(String phoneLastFour);

    void batchFillCardApplyConfigDo(List<CardApplyConfigDo> list);
}
