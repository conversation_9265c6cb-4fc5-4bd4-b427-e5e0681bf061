package com.mi.oa.ee.safety.domain.service;

import com.mi.oa.ee.safety.common.dto.*;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.query.card.PermissionQuery;
import com.mi.oa.ee.safety.domain.query.card.TempCardInfoQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/1 16:04
 */
public interface CardDomainService {

    void loadSafetyRightWithBaseInfo(CardInfoDo cardInfoDo);

    void fillQueryByUserUcDataResource(PermissionQuery permissionQuery);

    void checkUserPermissionWithUcRole(List<String> carrierGroupCodes);

    PageModel<CardInfoDo> pageConditionList(CardPageConditionDto pageConditionDto, Boolean isExport);

    PageModel<CardInfoDo> pageConditionList(TempCardInfoQuery query);

    CardInfoDo getDetailCardInfo(Long id);

    PageModel<OperateLogDto> pageListOperateLogs(Integer operateStatus, Integer operateType, Long pageNum,
                                                 Long pageSize, Long cardId);

    void cardOperate(Integer operateType, Long id, String uid);

    PageModel<SafetyCarrierGroupDo> pageGroupConditionList(GroupPageConditionDto params);

    /**
     * 分页查询
     * @param query
     * @return
     */
    PageModel<SafetyCarrierGroupDo> pageGroupConditionListV2(PermissionQuery query);

    /**
     * 开卡
     * @param cardInfoDo 工卡信息
     */
    void openCard(CardInfoDo cardInfoDo);

    /**
     * 更新工卡记录, 更新工卡卡号、生效日期等信息
     * @param cardInfoDo 工卡领域
     */
    void editCard(CardInfoDo cardInfoDo);

    void saveValidateTime(CardInfoDo cardInfoDo);

    void updateValidateTime(CardInfoDo cardInfoDo);

    List<SafetyRightDo> getCarrierGroupsByMediums(List<String> mediumCodes);

    void authorityRedo(AuthLogRedoDto param);

    CardInfoDo getCardByUid(String uid, List<CardStatusEnum> status);

    CardInfoDo findCardByUidAndCardType(String uid, CardTypeEnum cardTypeEnum);

    void updateStatus(Long id, CardStatusEnum canceled);

    List<CardInfoDo> getNeedExpireList();

    List<CardInfoDo> getNeedActiveList();

    List<CardInfoDo> getNeedNotifyList();


    List<SafetyCarrierGroupDo> getListGroupsByParkCode(String parkCode, String mediumCode);

    void doExpire(CardInfoDo cardInfoDo);

    void doActive(CardInfoDo cardInfoDo);

    List<CarrierGroupCityTreeDto> getAccessCityByMedium(String mediumCode);

    /**
     * 加载有效期
     * @param cardInfoDo
     */
    void fillValidateTime(CardInfoDo cardInfoDo);

    void deleteValidateTime(String partnerUser, ZonedDateTime startTime, ZonedDateTime endTime);

    void deleteSafetyRight(CardInfoDo cardInfoDo);

    void batchSavePartnerCard(List<CardApplyDo> cardApplyDos);

    List<CardTimeValidityDo> getCardTimeByCardIds(List<Long> cardIds);

    List<CardInfoDo> getListCardInfoByPhysicCodes(List<String> mediumPhysicsCodes);

    List<CardInfoDo> getListCardInfoByEncryptCodes(List<String> mediumEncryptCodes);

    List<CardInfoDo> getListCardInfoByUid(List<String> uid);

    void fillCarrierGroups(List<SafetyCarrierGroupDo> list);

    List<CardInfoDo> getListCardInfoByAccounts(List<String> accounts);

    CardInfoDo getCardByNums(SafetyCardToolDto safetyCardToolDto, List<CardStatusEnum> newArrayList);

    /**
     * 根据申请单查询工卡记录
     * @param applyId
     * @return
     */
    CardInfoDo findCardInfoByApplyId(Long applyId);

    /**
     * 领取工卡
     * @param cardInfoDo
     */
    void receive(CardInfoDo cardInfoDo);

    /**
     * 工卡申请单查询工卡信息
     * @param applyIdList
     * @return
     */
    List<CardInfoDo> findCardInfoByApplyIdList(List<Long> applyIdList);

    /**
     * 归还工卡
     * @param cardInfoDo 工卡信息
     */
    void returnCard(CardInfoDo cardInfoDo);


    /**
     * 工卡恢复
     * @param cardInfoDo
     */
    void reopen(CardInfoDo cardInfoDo);

    /**
     * 挂失工卡
     * @param cardInfo 卡片信息
     */
    void lossCard(CardInfoDo cardInfo);

    /**
     * 解挂工卡
     * @param cardInfo 卡片信息
     */
    void removeLossCard(CardInfoDo cardInfo);

    /**
     * 查询工卡有效期
     * @param cardIdList 工卡记录
     * @return
     */
    List<CardTimeValidityDo> findValidateTimeByCardIdList(List<Long> cardIdList);

    /**
     * 查询过期时间内的工卡记录
     * @param endTimeFrom
     * @param endTimeTo
     * @return
     */
    List<Long> findCardIdByEffectTime(ZonedDateTime endTimeFrom, ZonedDateTime endTimeTo);

    /**
     * 根据介质编码查询权限集合
     * @param mediumCode
     * @return
     */
    List<SafetyCarrierGroupDo> findCarrierGroupListByMediumCode(String mediumCode);

    /**
     * 根据工卡id信息查询
     * @param cardId 工卡id
     * @return
     */
    CardInfoDo findCardInfoById(Long cardId);

    /**
     * 添加权限
     * @param cardInfoDo
     */
    void addPermission(CardInfoDo cardInfoDo);

    void addPermissionV2(CardInfoDo cardInfoDo, List<SafetyRightDo> safetyRightList);

    /**
     * 删除权限
     * @param cardInfoDo 工卡信息
     */
    void removePermission(CardInfoDo cardInfoDo);

    void fillImportCardInfoByApply(CardInfoDo cardInfoDo);

    void fillImportMedium(CardInfoDo cardInfoDo);

    void fillImportPersonMedium(CardInfoDo cardInfoDo);

    void fillImportRights(CardInfoDo cardInfoDo);

    void fillImportValidateTimeAndSave(CardInfoDo cardInfoDo);

    /**
     * 根据工卡id查询权限
     * @param cardInfoDo 工卡id
     * @return 装载权限集合
     */
    void fillSafetyRight(CardInfoDo cardInfoDo);

    /**
     * 转载安防人员介质信息
     * @param cardInfoDo
     */
    void fillSafetyPersonMedium(CardInfoDo cardInfoDo);

    void fillSafetyCardOperateLog(CardInfoDo cardInfoDo);

    void getAndFillEmpCardInfo(CardInfoDo cardInfoDo);

    void pinCard(CardInfoDo cardInfoDo);

    void checkHasSpecialAuthAndUpdate(CardInfoDo cardInfoDo);

    PageModel<CardInfoDo> pageEmpCardList(CardPageConditionDto empCardDto);

    void checkCardCodeIsOccupied(CardInfoDo card);

    void checkTempCardIsReturn(CardInfoDo cardInfoDo);

    void checkEmpCardIsExist(CardInfoDo cardParam);

    void checkCardIsExpire(CardInfoDo cardInfoDo);

    void checkCardIsActive(CardInfoDo cardInfoDo);

    void checkIsExistRight(CardInfoDo cardInfoDo);

    void checkPermissionIsExceedCardValidateTime(CardInfoDo cardInfoDo);

    void checkIsExistAllRight(CardInfoDo cardInfoDo);

    void checkCardIsDestroy(CardInfoDo cardInfoDo);

    void checkCardIsUsing(CardInfoDo cardInfoDo);

    void fillCardLeaveRecord(CardInfoDo cardInfoDo);

    void checkBeforeCreateCardLeaveRecord(CardInfoDo cardInfoDo);

    List<CardLeaveRecordDo> findLeaveRecordByUidList(List<String> uidList);

    void reportLeave(CardInfoDo cardInfoDo);

    void updateValidateTimeByUidAndTime(CardInfoDo cardInfoDo);

    void fillInfoBeforeCheckIsNewCard(CardInfoDo cardInfoDo);

    Boolean checkIsNewCard(CardInfoDo cardInfoDo);

    List<CardInfoDo> findListByMediumCodeList(List<String> mediumCodes);

    void checkIsRepeatOpen(CardInfoDo cardInfoDo);

    void checkTempCardIsExist(CardInfoDo cardParam);

    PageModel<SafetyCarrierGroupDo> pageGroupConditionListV3(PermissionQuery permissionQuery);

    CardInfoDo findUsingCardInfoByUidAndCardType(String uid, CardTypeEnum cardTypeEnum);

    void notifyCardNumberChange(CardInfoDo cardInfoDo);

    String createEncryptCode(String code);

}
