package com.mi.oa.ee.safety.domain.service;

import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;

import java.util.List;

/**
 * 电子工卡记录领域
 *
 * <AUTHOR>
 * @date 2023/9/18 14:39
 */
public interface CardElectronRecordDomainService {

    /**
     * 检查当前人员是否可以开通电子卡
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 15:05
     */
    void checkCanOpen(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 创建前检查
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 14:52
     */
    void checkBeforeCreate(CardElectronRecordDo cardElectronRecordDo);

    /**
    * 判断电子工卡卡是否可迁移
    * @param cardElectronRecordDo
    * @return        void
    * <AUTHOR>
    * @date          2023/12/13 15:04
    */
    Boolean judgeIsCanMigrate(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 判断是否有已经开通过其他电子工卡
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/19 10:55
     */
    Boolean judgeHasOtherOpenedElectronRecord(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡开通前填充
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 15:57
     */
    void fillInfoBeforeDeliver(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡开通前检查
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 15:57
     */
    void checkBeforeDeliver(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡创建前填装
     *
     * @param electronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/6 10:33
     */
    void fillBeforeCreate(CardElectronRecordDo electronRecordDo);

    /**
     * 电子工卡开通前填充安防日志
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/19 10:57
     */
    void fillSafetyOperateLogBeforeOpened(CardElectronRecordDo cardElectronRecordDo);

    /**
    * 电子工卡迁移前填充安防日志
    * @param cardElectronRecordDo
    * @return        void
    * <AUTHOR>
    * @date          2023/12/13 15:12
    */
    void fillSafetyOperateLogBeforeMigrate(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡删除前根据电子卡号填充
     *
     * @param
     * @return void
     * <AUTHOR>
     * @date 2023/9/19 15:03
     */
    void fillInfoBeforeDeletingByElectronCardNum(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡删除前根据ID填充
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/10/19 15:06
     */
    void fillInfoBeforeDeletingById(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡完成删除前填充
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/10/19 14:56
     */
    void fillInfoBeforeDeleted(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡完成删除前检查
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/19 15:04
     */
    void checkBeforeDeleted(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡删除前检查
     *
     * @param electronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/10/18 15:27
     */
    void checkBeforeDeleting(CardElectronRecordDo electronRecordDo);

    /**
     * 强制删除前检查
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/18 15:04
     */
    void checkBeforeForceDelete(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 强制删除前填充
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/19 15:18
     */
    void fillInfoBeforeForceDelete(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 获取当前用户下所有的可强制删除的电子卡记录
     *
     * @param cardElectronRecordDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/9/27 17:29
     */
    List<CardElectronRecordDo> getCanForceDeleteElectronCardListByUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 获取当前人员的所有的电子工卡列表
     *
     * @param electronRecordDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/10/18 16:23
     */
    List<CardElectronRecordDo> getElectronCardListByUid(CardElectronRecordDo electronRecordDo);

    /**
     * 根据实体卡ID和uid获取当前人员以开通的电子工卡列表
     *
     * @param cardElectronRecordDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.CardElectronRecordDo>
     * <AUTHOR>
     * @date 2023/12/6 14:31
     */
    List<CardElectronRecordDo> findOpenedElectronCardByCardIdAndUid(CardElectronRecordDo cardElectronRecordDo);

    List<CardElectronRecordDo> findOpenedElectronCardByUid(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 电子工卡添加权限前填充
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/6 14:17
     */
    void groupSafetyRightForAddPermission(CardElectronRecordDo cardElectronRecordDo, List<SafetyRightDo> electronSafetyRightList);

    void generateOperateLogByAddPermission(CardElectronRecordDo cardElectronRecordDo, List<SafetyRightDo> electronSafetyRightList);

    /**
     * 电子工卡删除权限前填充
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/6 14:17
     */
    void fillBeforeRemovePermission(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 检查是否使用新电子工卡
     *
     * @param cardElectronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/6 10:43
     */
    void checkIsUsingNew(CardElectronRecordDo cardElectronRecordDo);

    /**
     * 根据电子卡编号构建小米人应用端电子工卡状态
     *
     * @param cardElectronRecordDo
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2023/12/6 16:36
     */
    Integer buildCardElectronStatusForAppByCardNum(CardElectronRecordDo cardElectronRecordDo);


    /**
     * 电子工卡更新别名前填装
     *
     * @param electronRecordDo
     * @return void
     * <AUTHOR>
     * @date 2023/12/6 16:41
     */
    void fillBeforeUpdateElectronName(CardElectronRecordDo electronRecordDo);


    Boolean judgeHasOtherOpenedElectronRecordV2(CardElectronRecordDo cardElectronRecordDo);
}
