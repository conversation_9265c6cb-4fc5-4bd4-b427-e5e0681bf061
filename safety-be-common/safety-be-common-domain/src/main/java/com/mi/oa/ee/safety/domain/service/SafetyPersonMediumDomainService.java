package com.mi.oa.ee.safety.domain.service;

import com.mi.oa.ee.safety.domain.model.SafetyMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;

import java.util.List;

/**
 * 安防人员和介质的关系领域
 *
 * <AUTHOR>
 * @date 2023/6/14 17:31
 */
public interface SafetyPersonMediumDomainService {

    /**
    * 填装人介关系中的人员信息和介质信息
    * @param safetyPersonMediumDo
    * @return        void
    * <AUTHOR>
    * @date          2023/11/13 15:48
    */ 
    void fillPersonMediumWithPersonAndMediumInfo(SafetyPersonMediumDo safetyPersonMediumDo);

    void fillPersonMediumWithMediumInfo(SafetyPersonMediumDo safetyPersonMediumDo);
}
