package com.mi.oa.ee.safety.domain.service;

import com.mi.oa.ee.safety.domain.model.SafetyRightDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/23 17:49
 */
public interface SafetyRightDomainService {

    /**
     * 填装安防权限
     *
     * @param safetyRightDo
     * @return void
     * <AUTHOR>
     * @date 2023/6/15 14:52
     */
    void fillSafetyRight(SafetyRightDo safetyRightDo);

    /**
     * 查询安防权限列表包含安防载体集信息根据介质编码
     *
     * @param safetyRightDo
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.SafetyRightDo>
     * <AUTHOR>
     * @date 2023/7/24 21:34
     */
    List<SafetyRightDo> queryListByMediumCodeWithCarrierGroupInfo(SafetyRightDo safetyRightDo);

    void fillCarrierGroupList(List<SafetyRightDo> safetyRightDos);

    List<String> getSupplierCodeList(List<SafetyRightDo> safetyRightDoList);
}
