package com.mi.oa.ee.safety.infra.remote.enums;

/**
 * @ClassName ResponseStatus
 * @Description 返回数据状态
 * <AUTHOR>
 * @Date 2019/3/17
 */
public enum ResponseStatus {

    SUCCESS("200", "请求成功!", true),
    FORBIDDEN("403", "服务器拒绝请求！", false),
    ERROR("500", "服务器错误，无法完成请求！", false),
    REPEAT_SUBMIT("409", "请勿重复提交！", false),
    NOT_FOUND("404", "请求参数错误！", false),
    STATUS_NOT_RIGHT("408", "状态只能是使用中或待开卡", false),
    CARD_IS_DONE("409", "正式卡卡数据已存在，请在【工卡管理-正式卡】使用数据同步", false),
    TEMP_CARD_IS_DONE("411", "临时卡数据已存在，请在【工卡管理-临时卡】使用数据同步", false),
    TEMP_CARD_IS_HAVED("411", "临时卡数据在老工卡系统中已存在并在使用中，请在归还后再开卡", false),
    CARD_IS_HAVED("417", "老工卡中的制卡单已存在，请先拒绝老制卡单后再导入", false),
    CARD_NUM_IS_ERROR("410", "当前物理卡号不存在于工作卡，不能进行状态变更！", false),
    ENCRYPT_CARD_NUM_IS_COOP_USED("413", "当前加密卡号已被老工卡系统中工作卡使用，不能进行开卡！", false),
    ENCRYPT_CARD_NUM_IS_EMP_USED("414", "当前加密卡号已被老工卡系统中正式卡使用，不能进行开卡！", false),
    ENCRYPT_CARD_NUM_IS_TEMP_USED("415", "当前加密卡号已被老工卡系统中临时卡使用，不能进行开卡！", false),
    CARD_REFUSE_ERROR("412", "老工卡制卡流程拒绝失败！", false),
    REQUEST_FAILED("400", "请求失败！", false),
    VERIFY_FAILED("406", "验证不通过!", false),
    CARD_DESTROY_FAILED("407", "销卡失败", false),
    SYNC_FAILED("408", "更新失败,未查到匹配的账号", false),
    NEW_CARD_EMPTY("409", "物理卡号为空", false),
    ;
    public String code;

    public String message;

    public boolean success;

    ResponseStatus(String code, String message, boolean success) {
        this.code = code;
        this.message = message;
        this.success = success;
    }
}
