package com.mi.oa.ee.safety.infra.remote.model.supplier;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.ZonedDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/6/18 18:33
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardInfoRemoteDto implements Serializable {

    private static final long serialVersionUID = 3313478646984849897L;

    /**
     * 物理卡号
     */
    private String mediumPhysicsCard;

    /**
     * 加密卡号
     */
    private String mediumEncryptCard;

    /**
     * 卡编号
     */
    private String cardNum;

    /**
     * 用户名(账号)
     */
    private String userName;

    /**
     * 供应商测权限组编码
     */
    private List<String> supplierAccessCodes;

    /**
     * 1:合作卡 2:临时卡 3:正式卡
     */
    private Integer cardType;

    private String endTime;

    private String photoUrl;
}
