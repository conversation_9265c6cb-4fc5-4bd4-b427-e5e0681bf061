package com.mi.oa.ee.safety.infra.repository;

import com.mi.oa.ee.safety.domain.model.CardReissueApplyDo;
import com.mi.oa.ee.safety.domain.model.CardReissuePayDo;
import com.mi.oa.ee.safety.infra.repository.query.CardReissueApplyQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/3/26 17:11
 */
public interface CardReissueApplyRepository {

    void save(CardReissueApplyDo cardReissueApplyDo);

    PageModel<CardReissueApplyDo> page(CardReissueApplyQuery cardReissueApplyQuery);

    CardReissueApplyDo findOneById(Long id);

    void updateById(CardReissueApplyDo cardReissueApplyDo);

    PageModel<CardReissueApplyDo> pageForAdmin(CardReissueApplyQuery query);

    /**
     * 加载补卡申请单信息
     * @param id 补卡申请单id
     * @return
     */
    CardReissueApplyDo findCardReissueApply(Long id);

    /**
     * 保存补卡支付单信息
     * @param cardReissueApplyDo 补卡申请单
     */
    void savePayOrder(CardReissueApplyDo cardReissueApplyDo);

    /**
     * 根据支付单号加载补卡申请单信息
     * @param orderId
     * @return
     */
    CardReissueApplyDo findCardReissueApplyDoByOrderId(String orderId);

    /**
     * 保存补卡申请单和支付申请单信息
     * @param cardReissueApplyDo
     */
    void persistencePayResult(CardReissueApplyDo cardReissueApplyDo);

    /**
     * 保存补卡申请单和支付申请单信息
     * @param cardReissueApplyDo
     */
    void persistencePayRefund(CardReissueApplyDo cardReissueApplyDo);

    /**
     * 根据支付中心退款单id查询补卡申请单
     * @param cardReissueApplyDo
     */
    void persistenceRefundCompleted(CardReissueApplyDo cardReissueApplyDo);

    /**
     * 根据支付中心退款单id查询补卡申请单
     * @param refundId
     */
    CardReissueApplyDo findCardReissueApplyDoByRefundId(String refundId);

    List<CardReissuePayDo> findCardReissuePayListByReissueApplyId(Long id);

    void updateReissueApplyAndPayOrder(CardReissueApplyDo cardReissueApplyDo);

    void saveOrUpdateForOpenCard(CardReissueApplyDo cardReissueApplyDo);

    boolean hasActiveApplyByApplyId(Long applyId);

    List<CardReissueApplyDo> findApplyListByUid(String uid);

    CardReissueApplyDo findOpeningCardReissueApplyByUidAndCardId(String uid, Long cardId);

    CardReissueApplyDo findCardReissueApplyByCardIdOrCardApplyIdAndStatus(CardReissueApplyDo cardReissueApplyDo);

    CardReissueApplyDo findOpeningCardReissueApplyByUidAndStatus(String uid, List<Integer> cardReissueApplyStatusEnum);

    List<CardReissueApplyDo> queryList(CardReissueApplyDo query);
}
