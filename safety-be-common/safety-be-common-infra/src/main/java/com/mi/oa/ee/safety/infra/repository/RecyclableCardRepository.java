package com.mi.oa.ee.safety.infra.repository;

import com.mi.oa.ee.safety.domain.enums.card.RecyclableCardCountryEnum;
import com.mi.oa.ee.safety.domain.model.RecyclableCardDo;
import com.mi.oa.ee.safety.domain.model.RecyclableCardUseLogDo;
import com.mi.oa.ee.safety.infra.repository.query.RecyclableCardLogQuery;
import com.mi.oa.ee.safety.infra.repository.query.RecyclableCardQuery;
import com.mi.oa.ee.safety.infra.repository.query.RecyclableCardSearchQuery;
import com.mi.oa.infra.oaucf.core.dto.PageVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/21 15:02
 */
public interface RecyclableCardRepository {

    /**
     * 批量保存临时卡记录
     * @param saveList
     */
    void batchSave(List<RecyclableCardDo> saveList);

    /**
     * 查询指定归属定序列号最大的记录
     * @param countryEnum 归属地
     * @return
     */
    RecyclableCardDo findMaxSeqRecyclableCard(RecyclableCardCountryEnum countryEnum);

    /**
     * 根据加密卡号查询临时卡记录
     * @param mediumEncryptCode 加密卡号
     * @return
     */
    RecyclableCardDo findByMediumEncryptCode(String mediumEncryptCode);

    /**
     * 根据物理卡号查询临时卡记录
     * @param mediumPhysicsCode 物理卡号
     * @return
     */
    RecyclableCardDo findByMediumPhysicsCode(String mediumPhysicsCode);

    /**
     * 更新临时卡
     * @param recyclableCardDo 临时卡领域信息
     */
    void update(RecyclableCardDo recyclableCardDo);

    /**
     * 分页查询临时卡信息
     * @param query
     * @return
     */
    PageVO<RecyclableCardDo> page(RecyclableCardQuery query);

    /**
     * 通过卡变哈查询临时卡
     * @param cardNum 卡编号
     * @return
     */
    RecyclableCardDo findByCardNum(String cardNum);

    /**
     * 保存日志
     * @param recyclableCardDo
     */
    void saveLog(RecyclableCardDo recyclableCardDo);

    RecyclableCardUseLogDo findUsingCardLog(RecyclableCardDo recyclableCardDo);

    /**
     * 分页查询使用日志
     * @param query
     * @return
     */
    PageVO<RecyclableCardUseLogDo> logPage(RecyclableCardLogQuery query);

    /**
     * 查询工卡
     * @param searchQuery
     * @return
     */
    RecyclableCardDo findUsableRecyclableCard(RecyclableCardSearchQuery searchQuery);
}
