package com.mi.oa.ee.safety.infra.repository;

import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupCarrierDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.query.card.PermissionQuery;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupFuzzyQuery;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/23 18:01
 */
public interface SafetyCarrierGroupRepository {

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<SafetyCarrierGroupCarrierDo> getSafetyCarrierGroupCarrierListByConditions(SafetyCarrierGroupQuery query);


    /**
     * 根据安防载体code获取对应的安防载体集
     *
     * @param query
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo>
     * <AUTHOR>
     * @date 2023/3/23 16:51
     */
    List<SafetyCarrierGroupDo> getSafetyCarrierGroupByCarrierCode(SafetyCarrierGroupQuery query);

    /**
     * 根据条件查询列表
     *
     * @param query
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDO>
     * <AUTHOR>
     * @date 2022/9/15 10:29
     */
    List<SafetyCarrierGroupDo> listByConditions(SafetyCarrierGroupQuery query);

    /**
     * @param classCode
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDO>
     * <AUTHOR>
     * @date 2022/9/15 11:29
     */
    List<SafetyCarrierGroupDo> listByClassCode(String classCode);

    /**
     * 根据载体集名称，查询载体集列表
     *
     * @param groupName
     * @param pageNum
     * @param pageSize
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDO>
     * <AUTHOR>
     * @date 2022/9/15 19:40
     */
    PageModel<SafetyCarrierGroupDo> listByGroupName(String groupName, Integer pageNum, Integer pageSize);

    /**
     * 根据园区code获取安防载体集列表
     *
     * @param parkCode
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo>
     * <AUTHOR>
     * @date 2022/12/12 19:49
     */
    List<SafetyCarrierGroupDo> getListByParkCode(String parkCode);

    /**
     * @param mediumCode
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDO>
     * @desc 通过介质获取载体集
     * <AUTHOR> denghui
     * @date 2022/12/3 12:55
     */
    List<SafetyCarrierGroupDo> getListByMediumCode(String mediumCode, String parkCode);

    /**
     * @param mediumCode
     * @return java.util.List<com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDO>
     * @desc 通过介质和状态获取载体集
     * <AUTHOR> denghui
     * @date 2022/12/3 12:55
     */
    List<SafetyCarrierGroupDo> getListByMediumCodeandStatus(String mediumCode, String parkCode);

    void save(SafetyCarrierGroupDo safetyCarrierGroupDo);

    List<SafetyCarrierGroupDo> getListByIds(List<Long> carrierGroupIds);

    void batchDeleteByIds(List<Long> carrierGroupIds);

    /**
     * 根据安防载体集code获取安防载体集详情
     *
     * @param carrierGroupCode
     * @return
     */
    SafetyCarrierGroupDo getCarrierGroup(String carrierGroupCode);

    PageModel<SafetyCarrierGroupDo> pageCondition(SafetyCarrierGroupQuery query);

    void batchSaveOrUpdate(List<SafetyCarrierGroupDo> safetyCarrierGroupDos);

    void updateSyncStatusByIdList(List<Long> idList, SafetySyncStatusEnum syncStatus, String updateUser);

    List<SafetyCarrierGroupDo> getListBySupplierAccessCodes(List<String> supplierAccessCodes);

    List<SafetyCarrierGroupDo> getListByCarrierGroupCodes(List<String> groupCodes);

    void batchUpdateGroupSync(List<Long> carrierGroupIds);

    PageModel<SafetyCarrierGroupDo> pageGroupWithParkCods(SafetyCarrierGroupQuery query);

    PageModel<SafetyCarrierGroupDo> pageGroupConditionList(SafetyCarrierGroupQuery query);

    /**
     * 根据条件查询安防载体集列表
     *
     * @param query 查询列表
     * @return
     */
    PageModel<SafetyCarrierGroupDo> pageGroupConditionListV2(PermissionQuery query);

    List<SafetyCarrierGroupDo> permissionGroupConditionList(PermissionQuery query);

    /**
     * 模糊搜索安防载体集列表
     *
     * @param query 查询条件
     * @return
     */
    List<SafetyCarrierGroupDo> fuzzyGroupList(SafetyCarrierGroupFuzzyQuery query);

    PageModel<SafetyCarrierGroupDo> pageGroupConditionListV3(PermissionQuery query);

    List<SafetyCarrierGroupDo> findCarrierGroupWithCarrierByCodeList(List<String> carrierGroupCodeList);
}
