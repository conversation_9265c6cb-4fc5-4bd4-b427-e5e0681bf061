package com.mi.oa.ee.safety.infra.repository;


import com.mi.oa.ee.safety.domain.model.UserBehaviorRecordDo;
import com.mi.oa.ee.safety.domain.query.card.UserBehaviorQuery;

/**
 * <AUTHOR>
 * @since 2024年3月25日 20:13:52
 */
public interface UserBehaviorRecordRepository {

    /**
     * 添加用户行为
     *
     * @param userBehaviorRecordDo
     */
    void addUserBehaviorRecord(UserBehaviorRecordDo userBehaviorRecordDo);

    /**
     * 查询用户行为数据
     *
     * @param userBehaviorQueryDo
     * @return
     */
    UserBehaviorRecordDo queryUserBehavior(UserBehaviorQuery userBehaviorQuery);
}
