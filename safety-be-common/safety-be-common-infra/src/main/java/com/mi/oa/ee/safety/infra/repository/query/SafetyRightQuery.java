package com.mi.oa.ee.safety.infra.repository.query;

import com.mi.oa.infra.oaucf.core.dto.BaseQuery;
import lombok.Data;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2023/6/15 14:34
 */
@Data
public class SafetyRightQuery extends BaseQuery {

    /**
     * 开始时间
     */
    Long startTime;

    /**
     * 过期时间
     */
    Long expireTime;

    /**
     * 更新时间的开始时间
     */
    Long queryStartTime;

    /**
     * 更新时间的结束时间
     */
    Long queryEndTime;

    /**
     * 同步状态
     */
    Integer syncStatus;

    /**
     * 需要同步的状态数
     */
    Integer needSyncStatusNum;

    /**
     * 当前安防载体集的编码列表
     */
    List<String> groupCodes;

    /**
     * 当前安防介质的编码列表
     */
    List<String> mediumCodes;

    /**
     * 用户ID列表
     */
    List<String> uidList;

    /**
     * 是否删除 0 不删除  1：删除
     */
    Long isDeleted;

    private List<String> supplierCodeList;

    private String supplierCode;

    private String uid;

    private String mediumCode;

}
