package com.mi.oa.ee.safety.common.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc   行政工作台配置信息
 * @create 2025/4/15 9:59
 */
@Data
@Configuration
@NacosConfigurationProperties(prefix = "oaucf.auth.workbench", dataId = "common-oaucf.yml", type = ConfigType.YAML, groupId = "ee.safety",
        autoRefreshed = true)
public class WorkbenchProperties {
    
    private String code;
}
