package com.mi.oa.ee.safety.common.dto;

import com.mi.oa.ee.safety.common.enums.SafetyAccountStatusEnum;
import com.mi.oa.ee.safety.common.enums.AccountTypeEnum;
import com.mi.oa.ee.safety.common.enums.SafetyPersonStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/19 18:25
 */
@Data
public class PersonInfoModel implements Serializable {

    private static final long serialVersionUID = 6950837578884301050L;

    /**
     * 组织树编码
     */
    private String orgTreeCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 国家
     */
    private String country;

    /**
     * 姓
     */
    private String lastName;

    /**
     * 名
     */
    private String firstName;

    /**
     * 名字拼音
     */
    private String pinyinName;

    /**
     * 姓氏拼音
     */
    private String lastNameEn;

    /**
     * 名字拼音
     */
    private String firstNameEn;

    /**
     * 有效期开始时间
     */
    private ZonedDateTime startTime;

    /**
     * 责任人uid
     */
    private String managerUid;

    /**
     * 身份ID
     */
    private String uid;

    private String sex;

    /**
     * 账号
     */
    private String accountName;

    /**
     * 全名
     */
    private String fullName;

    /**
     * 展示名
     */
    private String displayName;

    private String name;

    /**
     * 国家区域编号
     */
    private String zoneCode;

    /**
     * 电话
     */
    private String mobile;

    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 个人邮箱
     */
    private String personalEmail;

    /**
     * 员工工号
     */
    private String employeeId;

    /**
     * 身份证件号
     */
    private String idCardNumber;

    /**
     * 账号类型
     */
    private AccountTypeEnum accountType;

    /**
     * 账号类型
     */
    private SafetyAccountStatusEnum accountStatus;

    /**
     * 账户过期时间
     */
    private ZonedDateTime expireTime;

    /**
     * 入职时间
     */
    private ZonedDateTime hireDate;

    /**
     * 责任人账号
     */
    private String accountOwner;

    /**
     * 人员状态
     */
    private SafetyPersonStatusEnum personStatus;

    /**
     * 部门信息
     */
    private List<DeptDto> depts;

    /**
     * 办公地址
     */
    private String workAddress;

    /**
     * 报道地址id
     */
    private String reportAddressId;

    /**
     * 报道地址
     */
    private String reportAddress;

    /**
     * 报道详细地址
     */
    private String reportDetailAddress;

    /**
     * 人员所在地编码（用于区分是否海外）
     */
    private String locationCode;

    /**
     * 离职日期
     */
    private ZonedDateTime leaveDate;

    /**
     * 预离职日期
     */
    private ZonedDateTime preLeaveDate;

    private String firstDept;

    private String firstDeptName;

    private String secondDept;

    private String secondDeptName;

    private String thirdDept;

    private String thirdDeptName;

    private String fourthDept;

    private String fourthDeptName;

    private String deptId;

    /**
     * 拓展信息
     */
    private PersonExpandInfo expandInfo;

    /**
     * 人员扩展信息
     */
    @Data
    public static class PersonExpandInfo implements Serializable {

        private static final long serialVersionUID = -1862692219816731537L;

        /**
         * 办公城市编码
         */
        private String officeCityCode;

        /**
         * 办公城市
         */
        private String officeCity;

        /**
         * 办公地区编码
         */
        private String officeNationalityCode;

        /**
         * 办公地区
         */
        private String officeNationality;

        private String reportAddressId;
    }
}
