package com.mi.oa.ee.safety.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ImportTypeEnum {
    UNDEFINED(0, "{{{未定义}}}", ""),
    TEMP_CARD_APPLY_IMPORT(1, "{{{临时工卡申请导入}}}", "card"),
    EMP_CARD_APPLY_IMPORT(2, "{{{正式工卡申请导入}}}", "card"),
    CARD_BATCH_PERMISSION_ADD_IMPORT(5, "{{{卡权限批量添加导入}}}", "card"),
    CARD_BATCH_PERMISSION_DELETE_IMPORT(6, "{{{卡权限批量删除导入}}}", "card"),
    COOP_CARD_APPLY_IMPORT(8, "{{{合作卡申请导入}}}", "card"),

    //权限组导入
    CARRIER_GROUP_IMPORT(9, "{{{安防权限组导入}}}", "carrierGroup"),
    // 物业卡申请导入
    PROPERTY_CARD_IMPORT(10, "{{{物业卡申请导入}}}", "card"),
    CARD_BATCH_PERMISSION_GROUP_ADD_IMPORT(11, "{{{卡权限批量添加导入}}}", "card"),
    CARD_BATCH_PERMISSION_GROUP_DELETE_IMPORT(12, "{{{卡权限批量删除导入}}}", "card"),
    CARD_APPLY_BATCH_IMPORT_PHOTO(13, "{{{批量上传照片}}}", "card"),
    ;

    private static final Map<Integer, ImportTypeEnum> CACHE = Arrays.stream(values())
            .collect(Collectors.toMap(ImportTypeEnum::getType, Function.identity()));

    private final Integer type;

    private final String desc;

    private final String group;

    public static ImportTypeEnum ofType(Integer type) {
        return CACHE.getOrDefault(type, UNDEFINED);
    }

    public static List<Integer> getCardTypeList() {
        // 遍历所有的导入类型，找到所有的工卡相关的导入类型
        return Arrays.stream(values())
                .filter(importTypeEnum -> "card".equals(importTypeEnum.getGroup()))
                .map(ImportTypeEnum::getType)
                .collect(Collectors.toList());
    }

    public static List<Integer> getCarrierGroupTypeList() {
        // 遍历所有的导入类型，找到所有的权限组相关的导入类型
        return Arrays.stream(values())
                .filter(importTypeEnum -> "carrierGroup".equals(importTypeEnum.getGroup()))
                .map(ImportTypeEnum::getType)
                .collect(Collectors.toList());
    }

}
