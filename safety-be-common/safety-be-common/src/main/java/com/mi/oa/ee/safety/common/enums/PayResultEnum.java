package com.mi.oa.ee.safety.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum PayResultEnum {
    UNDEFINED("", "未定义"),

    PAYING("PAYING", "支付中"),

    FAILED("FAILED", "支付失败"),

    CANCELED("CANCELED", "取消支付"),

    TIMEOUT_FAILED("TIMEOUT_FAILED", "超期失败"),

    SUCCESS("SUCCESS", "支付成功");

    private final String code;

    private final String desc;

    public static PayResultEnum ofCode(String code) {
        for (PayResultEnum payResultEnum : values()) {
            if (StringUtils.equalsIgnoreCase(code, payResultEnum.getCode())) {
                return payResultEnum;
            }
        }
        return UNDEFINED;
    }

}
