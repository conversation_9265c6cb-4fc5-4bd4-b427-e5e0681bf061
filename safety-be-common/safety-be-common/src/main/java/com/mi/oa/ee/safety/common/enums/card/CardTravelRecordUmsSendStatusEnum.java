package com.mi.oa.ee.safety.common.enums.card;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/8/9 21:42
 */
@Getter
@AllArgsConstructor
public enum CardTravelRecordUmsSendStatusEnum {
    NOT_NEED_SEND(-1, "不需处理"),
    WAIT_SEND(0, "{{{待发送}}}"),
    SEND_SUCCESS(1, "发送成功"),
    SEND_FAIL(2, "{{{发送失败}}}"),
    ;

    private Integer code;

    private String desc;
}
