package com.mi.oa.ee.safety.common.enums.card;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/28 14:59
 */
@AllArgsConstructor
@Getter
public enum ExcelAttributeMapperEnum {

    //card apply
    ZONE_CODE("国际区号\n（Zone Code）", "zoneCode"),
    PHONE("手机号\n（Phone Number）", "phone"),
    COMPANY("公司名称\n（Company Name）", "companyName"),
    NATION("国籍\n（Nationality）", "nation"),
    DISPLAY_NAME("姓名\n（Full Name）", "displayName"),
    SURNAME("姓氏拼音\n（Last Name）", "surname"),
    PINYIN_NAME("名字拼音\n（Name）", "pinyinName"),
    ID_NUMBER_TYPE("证件类型\n（Document type）", "idNumberType"),
    ID_NUMBER("证件号码\n（Identification Number）", "idNumber"),
    EMAIL("邮箱\n（Email）", "email"),
    PARK_NAME("办公园区\n（Office Park）", "parkName"),
    START_TIME("合作周期-开始时间\n（Cooperation period - starting time）", "startTime"),
    END_TIME("合作周期-结束时间\n（Cooperation period - end time）", "endTime"),
    RESPONSIBLE_ACCOUNT("责任人账号\n（Responsible person's account）", "responsibleAccount"),
    REMARK("备注说明\n（Notes）", "remark"),
    PARTNER_ACCOUNT("{{{合作伙伴账号}}}", "partnerAccount"),
    RESPONSIBLE_NAME("{{{责任人姓名}}}", "responsibleName"),
    RESPONSIBLE_EMAIL("{{{责任人邮箱}}}", "responsibleEmail"),
    NULL_STRING("", ""),
    //safety carrier
    SUPPLIER_CONTROL_SERIAL("{{{序列号}}}", "supplierControlSerial"),
    CITY_ID("cityId", "cityId"),
    PARK_CODE("parkCode", "parkCode"),
    BUILDING_CODE("buildingCode", "buildingCode"),
    FLOOR_CODE("floorCode", "floorCode"),
    CARRIER_NAME("{{{名称}}}", "name"),
    CLASS_NAME("{{{类型}}}", "className"),
    CARRIER_STATUS("{{{状态}}}", "statusDesc"),
    SYSTEM("{{{系统}}}", "supplierName"),
    //safety group
    CARRIER_GROUP_NAME("{{{权限组名称}}}", "name"),
    GROUP_CLASS_NAME("{{{权限组类型}}}", "className"),
    SUPPLIER_ACCESS_CODE("供应商侧ID", "supplierAccessCode"),
    SUPPLIER("{{{供应商}}}", "supplierName"),

    //safety carrier group
    CARRIER_SERIAL("controlSerial", "supplierControlSerial"),
    GROUP_SERIAL("hw_access_right_id", "supplierAccessCode"),
    GROUP_SERIAL_LIST("hw_access_right_id", "supplierAccessCodeList"),
    MEDIUM_PHYSICS_CODE("{{{物理卡号}}}", "mediumPhysicsCode"),
    MEDIUM_ENCRYPT_CODE("{{{加密卡号}}}", "mediumEncryptCode"),
    ;

    private String source;

    private String target;

    public static String getSource(String target) {
        for (ExcelAttributeMapperEnum value : ExcelAttributeMapperEnum.values()) {
            if (value.getTarget().equals(target)) {
                return value.getSource();
            }
        }
        return null;
    }
}
