package com.mi.oa.ee.safety.common.enums.safety;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 供应商枚举
 *
 * <AUTHOR>
 * @date 2022/9/1 14:04
 */
@Getter
@AllArgsConstructor
public enum SafetySupplierCodeEnum {
    VISITOR("visitor", SafetySupplierTypeEnum.VIRTUAL_GATE_PERMISSION.getCode(), "访客", "", 1, Lists.newArrayList()),
    HOLLYWELL("hollywell", SafetySupplierTypeEnum.DOOR_PERMISSION.getCode(), "{{{霍尼韦尔}}}", "hollyWellAdapterSdk", 1, Lists.newArrayList()),
    HOLLYWELLV2("hollywellv2", SafetySupplierTypeEnum.DOOR_PERMISSION.getCode(), "霍尼韦尔2", "hollyWellV2AdapterSdk", 1, Lists.newArrayList()),
    VIFA("vifa", SafetySupplierTypeEnum.DOOR_PERMISSION.getCode(), "{{{威发}}}", "vifaAdapterSdk", 1, Lists.newArrayList()),
    CARD("card", SafetySupplierTypeEnum.CARD_PERMISSION.getCode(), "{{{工卡}}}", "", 1, Lists.newArrayList("hollywell", "hollywellv2", "zkteco")),
    ZYD("zyd", SafetySupplierTypeEnum.GATE_PERMISSION.getCode(), "{{{卓英达}}}", "", 1, Lists.newArrayList()),
    ZKTECO("zkteco", SafetySupplierTypeEnum.DOOR_PERMISSION.getCode(), "{{{熵基}}}", "zkTecoAdapterSdk", 1, Lists.newArrayList()),
    HIKVISION("hikvision", SafetySupplierTypeEnum.DOOR_PERMISSION.getCode(), "{{{海康威视}}}", "hikVisionAdapterSdk", 1, Lists.newArrayList()),
    OBE("obe", SafetySupplierTypeEnum.GATE_PERMISSION.getCode(), "{{{OBE}}}", "", 1, Lists.newArrayList()),
    EZKECO_WH_IT("ezkeco_wh_it", SafetySupplierTypeEnum.DOOR_PERMISSION.getCode(), "{{{中控-武汉信息部临时办公区门禁}}}", "ezkecoAdapterSdk", 1, Lists.newArrayList()),
    ;

    public static final List<String> GUARD_SUPPLIER_LIST = Lists.newArrayList(HOLLYWELL.getSupplierCode(),
            HOLLYWELLV2.getSupplierCode(), VIFA.getSupplierCode(), ZYD.getSupplierCode(), ZKTECO.getSupplierCode());

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商类型
     */
    private Integer supplierType;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商对应sdk的编码
     */
    private String supplierSdkCode;

    /**
     * 权限绑定类型 1 介质和权限绑定  2 人和权限绑定
     */
    private Integer rightBindType;

    /**
     * 需要同步给的供应商
     */
    private List<String> needSyncSupplierList;

    /**
     * 根据供应商code获取枚举
     *
     * @param supplierCode
     * @return com.mi.oa.ee.safety.common.enums.safety.SafetySupplierCodeEnum
     * <AUTHOR>
     * @date 2022/12/7 17:18
     */
    public static SafetySupplierCodeEnum getEnumBySupplierCode(String supplierCode) {
        for (SafetySupplierCodeEnum s : SafetySupplierCodeEnum.values()) {
            if (s.getSupplierCode().equals(supplierCode)) {
                return s;
            }
        }
        return null;
    }

    public static String getSupplierCode(String supplierName) {
        for (SafetySupplierCodeEnum s : SafetySupplierCodeEnum.values()) {
            if (s.getSupplierName().equals(supplierName)) {
                return s.getSupplierCode();
            }
        }
        return null;
    }

    public static List<String> findNeedSyncSupplierCodeList() {
        return Lists.newArrayList(HOLLYWELL.supplierCode, HOLLYWELLV2.supplierCode, VIFA.supplierCode, ZKTECO.supplierCode);
    }
}
