package com.mi.oa.ee.safety.common.excel.common;


import com.mi.oa.ee.safety.common.excel.annotation.ExportField;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 导出对象工具类
 *
 * <AUTHOR>
 * @since 2022年5月30日 15:14:49
 */
public class ExcelBeanUtils {

    /**
     * 获取动态导出列
     *
     * @param clazz      导出类
     * @param fieldNames 需要导出的动态列
     * @return
     */
    public static void dynamicAnnotation(Class<?> clazz, List<String> fieldNames) {
        try {
            if (fieldNames == null) {
                fieldNames = new ArrayList<>();
            }
            Field[] allFields = clazz.getDeclaredFields();
            for (Field field : allFields) {
                ExportField exportField = field.getAnnotation(ExportField.class);
                if (exportField == null || !exportField.dynamic()) {
                    continue;
                }
                InvocationHandler invocationHandler = Proxy.getInvocationHandler(exportField);
                Field declaredField = invocationHandler.getClass().getDeclaredField("memberValues");
                declaredField.setAccessible(true);
                Map<String, Boolean> memberValues = (Map) declaredField.get(invocationHandler);
                if (fieldNames.contains(field.getName())) {
                    memberValues.put("exist", true);
                } else {
                    memberValues.put("exist", false);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("{{{获取动态导出列异常}}}");
        }
    }

}
