package com.mi.oa.ee.safety.common.utils;

import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日期工具转换
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/9/19 20:44
 */
public class DateUtils {

    private DateUtils() {
    }

    private static final String PATTERN = "yyyy-MM-dd";

    private static final String PATTERN_HMS = "yyyy-MM-dd HH:mm:ss";

    public static final DateTimeFormatter Y_M_D_FORMATTER = DateTimeFormatter.ofPattern(PATTERN_HMS);

    /**
     * @param
     * @return java.time.ZonedDateTime
     * @desc 时间戳转zonedDateTime
     * <AUTHOR> denghui
     * @date 2022/11/22 20:02
     */
    public static ZonedDateTime timeStampToZonedDateTime(Long secondTime) {
        Instant instant = Instant.ofEpochSecond(secondTime);
        return ZonedDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static ZonedDateTime stampToZonedDateTime(Long timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat(PATTERN_HMS);
        String strDate = sdf.format(new Date(timeStamp));
        return ZonedDateTimeUtils.toZonedDateTime(strDate);
    }

    public static Long zonedDateTimeToSecond(ZonedDateTime zonedDateTime) {
        if (!ObjectUtils.isEmpty(zonedDateTime)) {
            return zonedDateTime.toEpochSecond();
        }
        return null;
    }


    public static Long getLastDayStamp(LocalDateTime localDateTime, long days) {
        //使用java.time工具类，获取三天前的时间戳
        return localDateTime.minusDays(days).atZone(ZoneId.systemDefault()).toEpochSecond();
    }


    public static String getLastDayDate(LocalDate localDate, long days) {
        LocalDate threeDaysAgo = localDate.minusDays(days);
        return threeDaysAgo.format(DateTimeFormatter.ofPattern(PATTERN));
    }

    public static ZonedDateTime getCurrentZonedDateTime() {
        //获取当前时间
        return ZonedDateTime.now();
    }

    /**
     * 将秒级时间戳转换为ZonedDateTime
     */
    public static ZonedDateTime toZonedBeginDateTime(long epochSecond) {
        Instant instant = Instant.ofEpochSecond(epochSecond);
        return ZonedDateTime.ofInstant(instant, ZoneId.systemDefault());
    }
}
