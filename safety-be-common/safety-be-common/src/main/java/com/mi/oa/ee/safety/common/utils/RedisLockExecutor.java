package com.mi.oa.ee.safety.common.utils;


import com.mi.oa.ee.safety.common.errorcode.LockErrorCodeEnum;
import com.mi.oa.ee.safety.common.exception.LockException;
import com.mi.oa.infra.oaucf.utils.RedisUtils;

public class RedisLockExecutor {


    public static <T> T call(String key, Long expire, Call<T> call) {
        String token = RedisUtils.tryLock(key, expire);
        if (token == null) {
            //加锁失败
            throw new LockException(LockErrorCodeEnum.APPLICATION_REDIS_LOCK_ERROR);
        }
        try {
            return call.call();
        } finally {
            RedisUtils.unlock(key, token);
        }
    }

    public static void execute(String key, Long expire, Executor executor) {
        String token = RedisUtils.tryLock(key, expire);
        if (token == null) {
            //加锁失败
            throw new LockException(LockErrorCodeEnum.APPLICATION_REDIS_LOCK_ERROR);
        }
        try {
            executor.execute();
        } finally {
            RedisUtils.unlock(key, token);
        }
    }

    public interface Executor {
        void execute();
    }

    public interface Call<T> {
        T call();
    }
}
