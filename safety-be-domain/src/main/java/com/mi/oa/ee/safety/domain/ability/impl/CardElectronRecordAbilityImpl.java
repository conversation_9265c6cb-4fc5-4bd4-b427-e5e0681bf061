package com.mi.oa.ee.safety.domain.ability.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.OperateLogDto;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardElectronStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardElectronStatusForAppEnum;
import com.mi.oa.ee.safety.common.enums.card.CardElectronTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyMediumTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyModelTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierCodeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.ability.CardElectronRecordAbility;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.converter.ElectronicCardDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.CardElectronRecordDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyOperateLogDetailDo;
import com.mi.oa.ee.safety.domain.model.SafetyOperateLogDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.infra.remote.sdk.PaySdk;
import com.mi.oa.ee.safety.infra.repository.CardElectronRecordRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierGroupRepository;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/9/19 17:27
 */
@Slf4j
@Service
public class CardElectronRecordAbilityImpl implements CardElectronRecordAbility {

    @Resource
    private CardElectronRecordRepository cardElectronicRecordRepository;

    @Resource
    private SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Resource
    private PaySdk paySdk;

    @Resource
    private ElectronicCardDoConverter converter;

    @Resource
    private CommonDoConverter commonDoConverter;

    @Value("${card.issuerId:59012}")
    private String issuerId;

    @Value("${pay.electron.cardArt:http://cdn.cnbj0.fds.api.mi-img.com/b2c-mioa-res/card_open.png}")
    private String haveCardPhoto;

    private static final String REDIS_KEY = "electron_encrypt_code";

    @NacosValue(value = "${card.electron.isAllOpen:false}", autoRefreshed = true)
    private String isAllOpen;

    @NacosValue(value = "${card.electron.isAllClose:false}", autoRefreshed = true)
    private String isAllClose;

    @Override
    public Boolean judgeIsAllCanOpen(CardElectronRecordDo cardElectronRecordDo) {

        if (OAUCFCommonConstants.STR_TRUE.equals(isAllClose)) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_IS_ALL_CLOSE);
        }

        //所有人都放开
        return OAUCFCommonConstants.STR_TRUE.equals(isAllOpen);
    }

    @Override
    public void checkMigratedByUid(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.UID_IS_EMPTY);
        }
        List<CardElectronRecordDo> list = cardElectronicRecordRepository.findListWithDeletedByUid(cardElectronRecordDo.getUid());
        if (CollectionUtils.isNotEmpty(list)) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARD_MIGRATED);
        }
    }

    @Override
    public Boolean judgeIsMigrate(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getMediumCode())) {
            return false;
        }
        if (cardElectronRecordDo.getCardInfoDo() == null || StringUtils.isEmpty(cardElectronRecordDo.getCardInfoDo().getMediumCode())) {
            return false;
        }
        return cardElectronRecordDo.getMediumCode().equals(cardElectronRecordDo.getCardInfoDo().getMediumCode());
    }


    @Override
    public Boolean judgePersonDeptIsCanOpen(CardElectronRecordDo cardElectronRecordDo) {
        if (cardElectronRecordDo.getSafetyPersonDo() == null) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.PERSON_NOT_EXIST);
        }
        return false;
    }

    @Override
    public void checkStatusIsCanForceDelete(CardElectronRecordDo cardElectronRecordDo) {
        if (!CardElectronStatusEnum.canForceDeleteStatusList.contains(cardElectronRecordDo.getStatus())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.STATUS_CAN_NOT_FORCE_DELETE);
        }
    }

    @Override
    public List<CardElectronRecordDo> getOpenedListByUid(CardElectronRecordDo cardElectronRecordDo) {
        return cardElectronicRecordRepository.getOpenedListByUid(cardElectronRecordDo);
    }

    @Override
    public List<CardElectronRecordDo> getCanDeleteListByUid(CardElectronRecordDo cardElectronRecordDo) {
        return null;
    }

    @Override
    public void fillInfoById(CardElectronRecordDo cardElectronRecordDo) {
        try {
            CardElectronRecordDo nowRecord = cardElectronicRecordRepository.findById(cardElectronRecordDo.getId());
            BeanUtils.copyProperties(cardElectronRecordDo, nowRecord);
        } catch (Exception e) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRONIC_CARD_NOT_EXIST,
                    e.toString());
        }
    }

    @Override
    public void checkCompleted(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronUserId())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_USERID_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronProductId())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_PRODUCTID_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronCardNum())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARDNUM_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronType())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_TYPE_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronModel())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_MODEL_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronDeviceId())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_DEVICEID_IS_EMPTY);
        }
        if (ObjectUtils.isEmpty(cardElectronRecordDo.getUid())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.UID_IS_EMPTY);
        }
        if (ObjectUtils.isEmpty(cardElectronRecordDo.getMediumEncryptCode())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.MEDIUM_ENCRYPTCODE_IS_EMPTY);
        }
    }

    @Override
    public void checkHasEffectiveByElectronCardNum(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronCardNum())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARDNUM_IS_EMPTY);
        }
        CardElectronRecordDo now = cardElectronicRecordRepository.findInfoByElectronCardNum(cardElectronRecordDo.getElectronCardNum());
        if (ObjectUtils.isNotEmpty(now) && (CardElectronStatusEnum.TO_BE_OPEN.getCode().equals(now.getStatus())
                || CardElectronStatusEnum.OPENED.getCode().equals(now.getStatus()))) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARD_EXIST);
        }
    }

    @Override
    public Boolean judgeHasOpenedByUid(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.UID_IS_EMPTY);
        }
        List<CardElectronRecordDo> list = cardElectronicRecordRepository.getOpenedListByUid(cardElectronRecordDo);
        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public void fillInfoByElectronCardNum(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronCardNum())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARDNUM_IS_EMPTY);
        }
        try {
            CardElectronRecordDo cardElectronRecord =
                    cardElectronicRecordRepository.findInfoByElectronCardNum(cardElectronRecordDo.getElectronCardNum());
            BeanUtils.copyProperties(cardElectronRecordDo, cardElectronRecord);
            cardElectronRecordDo.setUpdateTime(ZonedDateTime.now());
        } catch (Exception e) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRONIC_CARD_NOT_EXIST,
                    e.toString());
        }
    }

    @Override
    public void fillWithSafetyPersonMediumByUidAndMediumCode(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.UID_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getMediumCode())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.MEDIUM_CODE_IS_EMPTY);
        }
        SafetyPersonMediumDo safetyPersonMediumDo = getSafetyPersonMediumDo(cardElectronRecordDo);
        cardElectronRecordDo.setSafetyPersonMediumDo(safetyPersonMediumDo);
    }

    private SafetyPersonMediumDo getSafetyPersonMediumDo(CardElectronRecordDo cardElectronRecordDo) {
        SafetyPersonMediumDo safetyPersonMediumDo = new SafetyPersonMediumDo();
        safetyPersonMediumDo.setMediumCode(cardElectronRecordDo.getMediumCode());
        safetyPersonMediumDo.setUid(cardElectronRecordDo.getUid());
        safetyPersonMediumDo.setStartTime(ZonedDateTime.now());
        safetyPersonMediumDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
        safetyPersonMediumDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
        //电子工卡默认是副卡
        safetyPersonMediumDo.setIsMain(OAUCFCommonConstants.INT_ZERO);
        return safetyPersonMediumDo;
    }

    @Override
    public void fillWithSafetyRightListByCardInfo(CardElectronRecordDo cardElectronRecordDo) {
        List<SafetyRightDo> safetyRightList = cardElectronRecordDo.getSafetyRightList();
        //若实体卡权限不为空 更新电子卡的介质编码和同步状态
        if (CollectionUtils.isNotEmpty(safetyRightList)) {
            List<String> groupCodes = safetyRightList.stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
            List<SafetyCarrierGroupDo> carrierGroupDoList = safetyCarrierGroupRepository.getListByCarrierGroupCodes(groupCodes);
            //载体集领域转换为map
            Map<String, SafetyCarrierGroupDo> carrierGroupMap = carrierGroupDoList.stream()
                    .collect(Collectors.toMap(SafetyCarrierGroupDo::getCarrierGroupCode, Function.identity(), (a, b) -> a));
            safetyRightList.forEach(safetyRightDo -> {
                //清除id
                safetyRightDo.setId(null);
                safetyRightDo.setMediumCode(cardElectronRecordDo.getMediumCode());
                safetyRightDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                SafetyCarrierGroupDo carrierGroupDo = carrierGroupMap.get(safetyRightDo.getCarrierGroupCode());
                safetyRightDo.setSafetyCarrierGroup(carrierGroupDo);
            });
        } else {
            //防空
            safetyRightList = Lists.newArrayList();
        }
        cardElectronRecordDo.setSafetyRightList(safetyRightList);
        cardElectronRecordDo.setAddSafetyRightList(safetyRightList);
    }

    @Override
    public void fillWithSafetyOperateLog(CardElectronRecordDo cardElectronRecordDo) {
        Boolean isAddGroups = (Boolean) cardElectronRecordDo.getExtField("isAddGroups");
        Boolean isOpenCard = (Boolean) cardElectronRecordDo.getExtField("isOpenCard");
        OperateLogDto operateLogDto = new OperateLogDto();
        SafetyOperateLogDo safetyOperateLogDo = cardElectronRecordDo.getSafetyOperateLog();
        operateLogDto.setBizId(cardElectronRecordDo.getCardId().toString());
        safetyOperateLogDo.setBizId(cardElectronRecordDo.getCardId().toString());
        operateLogDto.setStartTime(SafetyConstants.Card.DEFAULT_START_TIME);
        operateLogDto.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
        operateLogDto.setUid(cardElectronRecordDo.getUid());
        operateLogDto.setOperateType(safetyOperateLogDo.getOperateTypeEnum().getType());
        if (isOpenCard) {
            operateLogDto.setNewPhysicCard(cardElectronRecordDo.getMediumPhysicsCode());
            operateLogDto.setNewEncryptCard(cardElectronRecordDo.getMediumEncryptCode());
        } else {
            operateLogDto.setOldPhysicCard(cardElectronRecordDo.getMediumPhysicsCode());
            operateLogDto.setOldEncryptCard(cardElectronRecordDo.getMediumEncryptCode());
        }
        if (CollectionUtils.isNotEmpty(cardElectronRecordDo.getSafetyRightList())) {
            if (isAddGroups == null) {
                operateLogDto.setAddGroup(Lists.newArrayList());
                operateLogDto.setDelGroup(Lists.newArrayList());
            } else if (isAddGroups) {
                List<SafetyCarrierGroupDo> carrierGroupDoList = cardElectronRecordDo
                        .getAddSafetyRightList()
                        .stream()
                        .map(SafetyRightDo::getSafetyCarrierGroup).collect(Collectors.toList());
                operateLogDto.setAddGroup(commonDoConverter.toDtoList(carrierGroupDoList));
            } else {
                List<SafetyCarrierGroupDo> carrierGroupDoList = cardElectronRecordDo
                        .getSafetyRightList()
                        .stream()
                        .map(SafetyRightDo::getSafetyCarrierGroup).collect(Collectors.toList());
                operateLogDto.setDelGroup(commonDoConverter.toDtoList(carrierGroupDoList));
            }
        }
        String requestParams = JacksonUtils.bean2Json(operateLogDto);
        safetyOperateLogDo.setOperateDesc(safetyOperateLogDo.getOperateTypeEnum().getDesc());
        safetyOperateLogDo.setOperateStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
        safetyOperateLogDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyOperateLogDo.setRequestParams(requestParams);
        safetyOperateLogDo.setRequestUrl(safetyOperateLogDo.getOperateTypeEnum().getCode());
        safetyOperateLogDo.setResponseParams("{}");
        safetyOperateLogDo.setResponseCode("200");
        //填装日志详情
        List<SafetyOperateLogDetailDo> logDetailDoList = Lists.newArrayList();
        //person medium
        SafetyPersonMediumDo safetyPersonMediumDo = cardElectronRecordDo.getSafetyPersonMediumDo();
        if (ObjectUtils.isNotEmpty(safetyPersonMediumDo) && ObjectUtils.isNotEmpty(safetyPersonMediumDo.getId())) {
            logDetailDoList.add(createSafetyOperateLogDetail(safetyPersonMediumDo.getId(),
                    SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDo)));
        }
        //safety right
        if (CollectionUtils.isNotEmpty(cardElectronRecordDo.getSafetyRightList())) {
            cardElectronRecordDo.getSafetyRightList().forEach(safetyRightDo -> logDetailDoList.add(createSafetyOperateLogDetail(
                    safetyRightDo.getId(), SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(safetyRightDo))));
        }
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(logDetailDoList);
        cardElectronRecordDo.setSafetyOperateLog(safetyOperateLogDo);
    }

    private SafetyOperateLogDetailDo createSafetyOperateLogDetail(Long id, SafetyModelTypeEnum safetyModelTypeEnum,
                                                                  String jsonParams) {
        SafetyOperateLogDetailDo safetyOperateLogDetailDo = new SafetyOperateLogDetailDo();
        safetyOperateLogDetailDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        if (SafetyModelTypeEnum.MEDIUM.equals(safetyModelTypeEnum)) {
            safetyOperateLogDetailDo.setSyncStatus(200);
        } else {
            safetyOperateLogDetailDo.setSyncStatus(OAUCFCommonConstants.INT_ZERO);
        }
        safetyOperateLogDetailDo.setModelParams(jsonParams);
        safetyOperateLogDetailDo.setModelType(safetyModelTypeEnum.getCode());
        safetyOperateLogDetailDo.setModelId(id);
        return safetyOperateLogDetailDo;
    }

    @Override
    public void checkStatusIsOpening(CardElectronRecordDo cardElectronRecordDo) {
        if (!CardElectronStatusEnum.TO_BE_OPEN.getCode().equals(cardElectronRecordDo.getStatus())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARD_STATUS_NOT_OPENING);
        }
    }

    @Override
    public Boolean judgeHasOpenedByElectronUserIdAndUid(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.UID_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronUserId())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_USERID_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronCardNum())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARD_NUMBER_EMPTY);
        }
        List<CardElectronRecordDo> cardElectronRecordList = cardElectronicRecordRepository.findHasOtherOpenedElectronCardByElectronUserId(cardElectronRecordDo);
        //若还有其他已开通的电子卡 返回true
        return CollectionUtils.isNotEmpty(cardElectronRecordList);
    }

    @Override
    public void checkStatusIsOpened(CardElectronRecordDo cardElectronRecordDo) {
        if (!CardElectronStatusEnum.openStatusList.contains(cardElectronRecordDo.getStatus())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.STATUS_CAN_NOT_DELETE);
        }
    }

    @Override
    public List<CardElectronRecordDo> findListByUid(CardElectronRecordDo electronRecordDo) {
        return cardElectronicRecordRepository.findListByUid(electronRecordDo.getUid());
    }

    @Override
    public List<CardElectronRecordDo> selectAndGroupByType(List<CardElectronRecordDo> electronRecordDoList) {
        List<CardElectronRecordDo> res = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(electronRecordDoList)) {
            Map<String, List<CardElectronRecordDo>> electronTypeMap = electronRecordDoList.stream()
                    .collect(Collectors.groupingBy(CardElectronRecordDo::getElectronType));
            //手机
            List<CardElectronRecordDo> phoneList = electronTypeMap.get(CardElectronTypeEnum.MOBILE.getCode());
            res.add(selectOneSortByStatus(phoneList));
            //手表
            List<CardElectronRecordDo> watchList = electronTypeMap.get(CardElectronTypeEnum.WATCH.getCode());
            res.add(selectOneSortByStatus(watchList));
            //手环
            List<CardElectronRecordDo> bandList = electronTypeMap.get(CardElectronTypeEnum.WRISTBAND.getCode());
            res.add(selectOneSortByStatus(bandList));
        }
        return res;
    }

    @Override
    public void fillWithSafetyMediumByMediumCode(CardElectronRecordDo cardElectronRecordDo) {
        SafetyMediumDo safetyMediumDo = new SafetyMediumDo();
        safetyMediumDo.setMediumCode(cardElectronRecordDo.getMediumCode());
        safetyMediumDo.setMediumPhysicsCode(cardElectronRecordDo.getMediumPhysicsCode());
        safetyMediumDo.setMediumEncryptCode(cardElectronRecordDo.getMediumEncryptCode());
        safetyMediumDo.setMediumType(SafetyMediumTypeEnum.CARD.getCode());
        safetyMediumDo.setSupplierCode(SafetySupplierCodeEnum.CARD.getSupplierCode());
        safetyMediumDo.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
        cardElectronRecordDo.setSafetyMediumDo(safetyMediumDo);
    }

    @Override
    public void checkStatusIsDeleting(CardElectronRecordDo cardElectronRecordDo) {
        //退出钱包账号场景会直接 已开通->已删除
        if (!(CardElectronStatusEnum.DELETING.getCode().equals(cardElectronRecordDo.getStatus())
                || CardElectronStatusEnum.DELETE_FAIL.getCode().equals(cardElectronRecordDo.getStatus())
                || CardElectronStatusEnum.OPENED.getCode().equals(cardElectronRecordDo.getStatus()))) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARD_STATUS_NOT_DELETING);
        }
    }

    @Override
    public List<CardElectronRecordDo> getCanForceDeleteListByUid(CardElectronRecordDo cardElectronRecordDo) {
        //Uid必不能为空
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.UID_IS_EMPTY);
        }
        return cardElectronicRecordRepository.getCanForceDeleteListByUid(cardElectronRecordDo.getUid());
    }

    @Override
    public void buildNewBaseInfo(CardElectronRecordDo electronRecordDo) {
        electronRecordDo.setCardType(CardElectronTypeEnum.getNumByCode(electronRecordDo.getElectronType()));
        electronRecordDo.setStatus(CardElectronStatusEnum.TO_BE_OPEN.getCode());
        electronRecordDo.setElectronIssuerId(issuerId);
        electronRecordDo.setElectronPhotoUrl(haveCardPhoto);
        if (StringUtils.isEmpty(electronRecordDo.getElectronCardNum())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARD_NUMBER_EMPTY);
        }
        List<CardElectronRecordDo> hasOtherOpenedElectronCardByElectronUserId
                = cardElectronicRecordRepository.findHasOtherOpenedElectronCardByElectronUserId(electronRecordDo);
        if (CollectionUtils.isNotEmpty(hasOtherOpenedElectronCardByElectronUserId)) {
            electronRecordDo.setMediumEncryptCode(hasOtherOpenedElectronCardByElectronUserId.get(0).getMediumEncryptCode());
            electronRecordDo.setMediumCode(hasOtherOpenedElectronCardByElectronUserId.get(0).getMediumCode());
        } else {
            electronRecordDo.setMediumEncryptCode(buildElectronEncryptCard());
            electronRecordDo.setMediumCode(UUID.randomUUID().toString().replace("-", ""));
        }
        electronRecordDo.setUid(electronRecordDo.getCardInfoDo().getUid());
    }

    public void buildNewForMigrate(CardElectronRecordDo electronRecordDo) {
        electronRecordDo.setCardType(CardElectronTypeEnum.getNumByCode(electronRecordDo.getElectronType()));
        electronRecordDo.setStatus(CardElectronStatusEnum.OPENED.getCode());
        //使用实体卡的介质编码
        electronRecordDo.setMediumCode(electronRecordDo.getCardInfoDo().getMediumCode());
        electronRecordDo.setCardId(electronRecordDo.getCardInfoDo().getId());
        //同步过来的电子工卡不需要权限
        electronRecordDo.setSafetyRightList(Lists.newArrayList());
        electronRecordDo.setAddSafetyRightList(Lists.newArrayList());
    }

    @Override
    public Integer buildCardElectronStatusForAppByStatus(CardElectronRecordDo cardElectronRecordDo) {
        if (ObjectUtils.isEmpty(cardElectronRecordDo.getStatus())) {
            return CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_NOT_DELETE.getOperateType();
        }
        if (CardElectronStatusEnum.canDeleteStatusForPay.contains(cardElectronRecordDo.getStatus())) {
            return CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_CAN_DELETE.getOperateType();
        } else {
            return CardElectronStatusForAppEnum.ELECTRON_CARD_PAY_RESULT_HAS_DELETED.getOperateType();
        }
    }

    @Override
    public List<CardElectronRecordDo> findOpenedListByCardIdAndUid(CardElectronRecordDo cardElectronRecordDo) {
        return cardElectronicRecordRepository.findOpenedElectronCardByCardIdAndUid(cardElectronRecordDo);
    }

    @Override
    public void fillWithElectronNameFromPay(CardElectronRecordDo electronRecordDo) {
        String electronName = paySdk.getElectronName(electronRecordDo);
        electronRecordDo.setElectronName(electronName);
    }

    private String buildElectronEncryptCard() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").withZone(ZoneId.systemDefault());
        String format = ZonedDateTime.now().format(dateTimeFormatter);
        String suffixString = CodeUtils.generateVisitorCode(2, false);
        return format + suffixString;
    }

    private CardElectronRecordDo selectOneSortByStatus(List<CardElectronRecordDo> recordList) {
        //同一个设备号的电子卡状态理论上互斥(只有已删除|强制删除可存在多个)
        //不同设备号状态排序  已开通>待开通>开通异常 >删除中>删除异常>已删除|强制删除
        if (CollectionUtils.isNotEmpty(recordList)) {
            Map<Integer, CardElectronRecordDo> statusMap = recordList
                    .stream()
                    .collect(Collectors.toMap(CardElectronRecordDo::getStatus, Function.identity(), (k1, k2) -> k1));
            List<Integer> nowStatusList =
                    recordList.stream().map(CardElectronRecordDo::getStatus).collect(Collectors.toList());
            if (nowStatusList.contains(CardElectronStatusEnum.OPENED.getCode())) {
                return statusMap.get(CardElectronStatusEnum.OPENED.getCode());
            }
            if (nowStatusList.contains(CardElectronStatusEnum.TO_BE_OPEN.getCode())) {
                return statusMap.get(CardElectronStatusEnum.TO_BE_OPEN.getCode());
            }
            if (nowStatusList.contains(CardElectronStatusEnum.OPEN_FAIL.getCode())) {
                return statusMap.get(CardElectronStatusEnum.OPEN_FAIL.getCode());
            }
            if (nowStatusList.contains(CardElectronStatusEnum.DELETING.getCode())) {
                return statusMap.get(CardElectronStatusEnum.DELETING.getCode());
            }
            if (nowStatusList.contains(CardElectronStatusEnum.DELETED.getCode())) {
                return statusMap.get(CardElectronStatusEnum.DELETED.getCode());
            }
            if (nowStatusList.contains(CardElectronStatusEnum.DELETED_FORCE.getCode())) {
                CardElectronRecordDo cardElectronRecordDo = statusMap.get(CardElectronStatusEnum.DELETED_FORCE.getCode());
                cardElectronRecordDo.setStatus(CardElectronStatusEnum.DELETED.getCode());
                return cardElectronRecordDo;
            }
        }
        return new CardElectronRecordDo();
    }

    @Override
    public void groupSafetyRightForAddPermission(CardElectronRecordDo cardElectronRecordDo, List<SafetyRightDo> existRightList,
                                                 List<SafetyRightDo> toAddRightList) {
        Map<String, SafetyRightDo> authedPermissionMap = existRightList.stream()
                .collect(Collectors.toMap(SafetyRightDo::getCarrierGroupCode, Function.identity(), (a, b) -> a));
        List<SafetyRightDo> addRights = Lists.newArrayList();
        List<SafetyRightDo> updateRights = Lists.newArrayList();
        List<String> addGroupCodeList = Lists.newArrayList();
        List<String> updateGorupCodeList = Lists.newArrayList();
        for (SafetyRightDo safetyRightDo : toAddRightList) {
            if (authedPermissionMap.containsKey(safetyRightDo.getCarrierGroupCode())) {
                SafetyRightDo exist = authedPermissionMap.get(safetyRightDo.getCarrierGroupCode());
                if (exist.getEndTime().isBefore(safetyRightDo.getEndTime())) {
                    exist.setEndTime(safetyRightDo.getEndTime());
                }
                if (exist.getStartTime().isAfter(safetyRightDo.getStartTime())) {
                    exist.setStartTime(safetyRightDo.getStartTime());
                }
                //同步状态根据外部的数据来
                exist.setSyncStatus(safetyRightDo.getSyncStatus());
                updateRights.add(exist);
                updateGorupCodeList.add(exist.getCarrierGroupCode());
                safetyRightDo.setId(exist.getId());
            } else {
                addRights.add(safetyRightDo);
                addGroupCodeList.add(safetyRightDo.getCarrierGroupCode());
            }
        }
        cardElectronRecordDo.setAddSafetyRightList(addRights);
        cardElectronRecordDo.setUpdateSafetyRightList(updateRights);
    }

    @Override
    public Boolean judgeHasOpenedByElectronUserIdAndUidV2(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.UID_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronUserId())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_USERID_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getMediumCode())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.MEDIUM_CODE_IS_EMPTY);
        }
        if (StringUtils.isEmpty(cardElectronRecordDo.getElectronCardNum())) {
            throw new BizException(CardElectronRecordDomainErrorCodeEnum.ELECTRON_CARD_NUMBER_EMPTY);
        }
        return CollectionUtils.isNotEmpty(cardElectronicRecordRepository.findHasOtherOpenedElectronCardByElectronUserIdV2(cardElectronRecordDo));
    }

    @Override
    public List<CardElectronRecordDo> findOpenedListByUid(CardElectronRecordDo cardElectronRecordDo) {
        return cardElectronicRecordRepository.findOpenedElectronCardByUid(cardElectronRecordDo);
    }
}
