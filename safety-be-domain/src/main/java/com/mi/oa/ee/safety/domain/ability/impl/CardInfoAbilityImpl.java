package com.mi.oa.ee.safety.domain.ability.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.*;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.SafetyPersonStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardLeaveRecordStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.YesNoEnum;
import com.mi.oa.ee.safety.common.enums.safety.*;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.ability.CardApplyAbility;
import com.mi.oa.ee.safety.domain.ability.CardInfoAbility;
import com.mi.oa.ee.safety.domain.ability.SafetyCarrierGroupAbility;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardReissueApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.query.card.TempCardInfoQuery;
import com.mi.oa.ee.safety.infra.errorcode.CardApplyErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.sdk.HrodSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.repository.*;
import com.mi.oa.ee.safety.infra.repository.query.CardInfoQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.enums.UserIdTypeEnum;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/1 16:05
 */
@Slf4j
@Service
public class CardInfoAbilityImpl implements CardInfoAbility {

    @Autowired
    CardInfoRepository cardInfoRepository;

    @Autowired
    CardApplyAbility cardApplyAbility;

    @Autowired
    SafetyCarrierGroupAbility safetyCarrierGroupAbility;

    @Autowired
    CommonDoConverter commonDoConverter;

    @Autowired
    SafetyOperateLogRepository logRepository;

    @Autowired
    SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Autowired
    SafetyCarrierGroupCarrierRepository safetyCarrierGroupCarrierRepository;

    @Autowired
    SafetyCarrierGroupClassRepository safetyCarrierGroupClassRepository;

    @Autowired
    SafetyMediumRepository safetyMediumRepository;

    @Autowired
    SafetyRightRepository safetyRightRepository;

    @Autowired
    IdmSdk idmSdk;

    @Resource
    private CardTimeValidateRepository cardTimeValidateRepository;

    @Resource
    private HrodSdk hrodSdk;

    @Resource
    private CardLeaveRecordRepository cardLeaveRecordRepository;

    @Resource
    private CardParkAdminRepository cardParkAdminRepository;

    @Resource(name = "reportParkCodeLocalObjectCache")
    private Cache<String, Object> reportParkCodeLocalObjectCache;

    @Value("${card.parkCode:BJ04}")
    private String parkCode;
    @Value("${card.firstDept:AU}")
    private String firstDept;
    @Value("${card.secondDept:AU12}")
    private String secondDept;
    @Value("${card.thirdDept:AU1201}")
    private String thirdDept;

    @NacosValue(value = "${card.prefix-encrypt-code:201046}", autoRefreshed = true)
    private String prefixEncryptCode;

    @Override
    public void fillCardInfoById(CardInfoDo cardInfoDo) {
        if (cardInfoDo == null || cardInfoDo.getId() == null) {
            return;
        }
        CardInfoDo tempCardInfo = cardInfoRepository.getOneById(cardInfoDo.getId());
        Asserts.assertNotNull(tempCardInfo, CardInfoDomainErrorCodeEnum.CARD_NOT_EXIST);
        commonDoConverter.copyCardInfoDo(tempCardInfo, cardInfoDo);
    }

    @Override
    public void checkCardIsEmployee(CardInfoDo cardInfoDo) {
        if (cardInfoDo == null || cardInfoDo.getCardStatus() == null) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_IS_NOT_EXISTS);
        }
        if (!CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfoDo.getCardType())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_IS_NOT_EMP);
        }
    }

    @Override
    public void checkCardIsActive(CardInfoDo cardInfoDo) {
        if (!CardStatusEnum.USING.getCode().equals(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_IS_NOT_ACTIVE);
        }
    }

    @Override
    public void checkIsHaveCardByUid(CardInfoDo cardInfoDo) {
        if (cardInfoDo == null || StringUtils.isBlank(cardInfoDo.getUid())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.UID_EMPTY);
        }
        CardInfoQuery cardInfoQuery = new CardInfoQuery();
        cardInfoQuery.setUid(cardInfoDo.getUid());
        List<CardInfoDo> cardInfoDoList = cardInfoRepository.getListByConditions(cardInfoQuery);
        if (CollectionUtils.isEmpty(cardInfoDoList)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CURRENT_PERSON_NOT_HAVE_CARD);
        }
    }

    @Override
    public PageModel<CardInfoDo> pageConditionList(CardPageConditionDto pageConditionDto, List<String> parkCodes) {
        return cardInfoRepository.pageConditionList(pageConditionDto, parkCodes);
    }

    @Override
    public PageModel<CardInfoDo> pageConditionListV2(CardPageConditionDto pageConditionDto, List<String> parkCodes) {
        // cardInfoRepository.pageConditionListV2 为临时卡分页查询
        return cardInfoRepository.pageConditionListV3(pageConditionDto, parkCodes);
    }

    @Override
    public PageModel<CardInfoDo> pageConditionList(TempCardInfoQuery query) {
        return cardInfoRepository.pageConditionList(query);
    }

    @Override
    public void fillCardInfo(CardInfoDo cardInfoDo, Boolean isFillGroups) {
        CardApplyDo cardApplyDo = cardApplyAbility.getCardApplyById(cardInfoDo.getCardApplyId());
        if (ObjectUtils.isNotEmpty(cardApplyDo)) {
            cardApplyDo.setPersonInfo(cardInfoDo.getCardApply().getPersonInfo());
            List<CardTimeValidityDo> validatePeriod = Lists.newArrayList();
            if (OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getStartTime().toEpochSecond()) ||
                    OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getEndTime().toEpochSecond())) {
                ZonedDateTime startTime = cardInfoDo.getCardApply().getPersonInfo().getStartTime();
                ZonedDateTime endTime = cardInfoDo.getCardApply().getPersonInfo().getEndTime();
                CardTimeValidityDo cardTimeValidityDo = new CardTimeValidityDo();
                cardTimeValidityDo.setCardId(cardInfoDo.getId());
                cardTimeValidityDo.setStartTime(startTime);
                cardTimeValidityDo.setEndTime(endTime);
                cardTimeValidityDo.setUid(cardInfoDo.getUid());
                validatePeriod.add(cardTimeValidityDo);
            } else {
                validatePeriod = getValidatePeriod(cardInfoDo.getId());
            }
            cardApplyAbility.fillFullCardApply(cardApplyDo);
            cardApplyAbility.fillParkInfo(cardApplyDo);
            cardApplyDo.setMediumCode(cardInfoDo.getMediumCode());
            cardApplyDo.setMediumEncryptCode(cardInfoDo.getMediumEncryptCode());
            cardApplyDo.setMediumPhysicsCode(cardInfoDo.getMediumPhysicsCode());
            cardApplyDo.setId(cardInfoDo.getId());
            cardApplyDo.setCardNum(cardInfoDo.getCardNum());
            try {
                org.springframework.beans.BeanUtils.copyProperties(cardApplyDo, cardInfoDo);
            } catch (Exception e) {
                log.error("---- BeanUtils copyProperties error", e);
            }
            cardInfoDo.setValidatePeriod(validatePeriod);
            cardInfoDo.setCardApply(cardApplyDo);
            if (isFillGroups) {
                List<SafetyCarrierGroupDo> listByMediumCode = getListByMediumCode(cardInfoDo.getMediumCode(), null);
                cardInfoDo.setAccessControl(listByMediumCode);
            }
        }
    }

    @Override
    public List<CardTimeValidityDo> getValidatePeriod(Long cardId) {
        return cardInfoRepository.getValidatePeriod(cardId);
    }

    @Override
    public List<SafetyCarrierGroupDo> getListByMediumCode(String mediumCode, String parkCode) {
        return safetyCarrierGroupAbility.getListByMediumCode(mediumCode, parkCode);
    }

    @Override
    public List<SafetyCarrierGroupDo> getListByMediumCodeandStatus(String mediumCode, String parkCode) {
        return safetyCarrierGroupAbility.getListByMediumCodeandStatus(mediumCode, parkCode);
    }

    @Override
    public CardInfoDo getOneById(Long id) {
        return cardInfoRepository.getOneById(id);
    }

    @Override
    public PageModel<SafetyOperateLogDo> getListOperateLogs(Integer operateStatus, Integer operateType, Long pageNum,
                                                            Long pageSize, Long cardId) {
        return logRepository.pageList(operateStatus, operateType, pageNum, pageSize, cardId);
    }

    @Override
    public void fillOperateLog(List<SafetyOperateLogDo> list, List<OperateLogDto> operateLogDtos) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(safetyOperateLogDo -> {
                OperateLogDto operateLogDto = JacksonUtils.json2Bean(safetyOperateLogDo.getRequestParams(), OperateLogDto.class);
                if (Objects.nonNull(operateLogDto)) {
                    operateLogDto.setOperateStatus(safetyOperateLogDo.getOperateStatus());
                    operateLogDto.setUpdateTime(safetyOperateLogDo.getUpdateTime());
                    AccountModel accountInfo = idmSdk.getAccountInfo(safetyOperateLogDo.getCreateUser(),
                            UserIdTypeEnum.HAVANA_ID);
                    if (ObjectUtils.isNotEmpty(accountInfo)) {
                        operateLogDto.setUpdateUserName(accountInfo.getAccountName());
                        operateLogDto.setUpdateUserDisplayName(accountInfo.getFullName());
                        operateLogDto.setUpdateTime(safetyOperateLogDo.getCreateTime());
                    } else {
                        operateLogDto.setUpdateUser("admin");
                        operateLogDto.setUpdateUserName("admin");
                        operateLogDto.setUpdateUserDisplayName("admin");
                        operateLogDto.setUpdateTime(safetyOperateLogDo.getCreateTime());
                    }
                    operateLogDtos.add(operateLogDto);
                }
            });
            operateLogDtos.sort((o1, o2) -> ZonedDateTimeUtils.compare(o2.getUpdateTime(), o1.getUpdateTime()));
        }
    }

    @Override
    public void deleteById(Long id) {
        cardInfoRepository.deleteById(id);
    }

    @Override
    public void fillSafetyCardOperateLog(CardInfoDo cardInfoDo) {
        Boolean isAddGroups = (Boolean) cardInfoDo.getExtField("isAddGroups");
        String operateUid = (String) cardInfoDo.getExtField("operateUid");
        OperateLogDto operateLogDto = new OperateLogDto();
        SafetyOperateLogDo safetyOperateLogDo = cardInfoDo.getSafetyOperateLog();
        if (ObjectUtils.isNotEmpty(cardInfoDo.getId())) {
            operateLogDto.setBizId(cardInfoDo.getId().toString());
            safetyOperateLogDo.setBizId(cardInfoDo.getId().toString());
        } else {
            String biz = UUID.randomUUID().toString().replace("-", "");
            operateLogDto.setBizId(biz);
            safetyOperateLogDo.setBizId(biz);
        }
        if (CollectionUtils.isNotEmpty(cardInfoDo.getAddRights())) {
            SafetyRightDo safetyRightDo = cardInfoDo.getAddRights().get(0);
            operateLogDto.setStartTime(safetyRightDo.getStartTime());
            operateLogDto.setEndTime(safetyRightDo.getEndTime());
        }
        operateLogDto.setUid(cardInfoDo.getUid());
        operateLogDto.setOperateType(safetyOperateLogDo.getOperateTypeEnum().getType());
        operateLogDto.setOldEncryptCard(cardInfoDo.getMediumEncryptCode());
        operateLogDto.setOldPhysicCard(cardInfoDo.getMediumPhysicsCode());
        if (StringUtils.isNotBlank(cardInfoDo.getNewMediumPhysicsCode())
                || StringUtils.isNotBlank(cardInfoDo.getNewMediumEncryptCode())) {
            operateLogDto.setNewPhysicCard(cardInfoDo.getNewMediumPhysicsCode());
            operateLogDto.setNewEncryptCard(cardInfoDo.getNewMediumEncryptCode());
            cardInfoDo.setMediumPhysicsCode(cardInfoDo.getNewMediumPhysicsCode());
            cardInfoDo.setMediumEncryptCode(cardInfoDo.getNewMediumEncryptCode());
        }
        if (isAddGroups == null) {
            operateLogDto.setAddGroup(Lists.newArrayList());
            operateLogDto.setDelGroup(Lists.newArrayList());
        } else if (isAddGroups) {
            operateLogDto.setAddGroup(commonDoConverter.toDtoList(cardInfoDo.getAccessControl()));
        } else {
            operateLogDto.setDelGroup(commonDoConverter.toDtoList(cardInfoDo.getAccessControl()));
        }
        String requestParams = JacksonUtils.bean2Json(operateLogDto);
        safetyOperateLogDo.setOperateDesc(safetyOperateLogDo.getOperateTypeEnum().getDesc());
        safetyOperateLogDo.setOperateStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
        safetyOperateLogDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyOperateLogDo.setRequestParams(requestParams);
        safetyOperateLogDo.setRequestUrl(safetyOperateLogDo.getOperateTypeEnum().getCode());
        safetyOperateLogDo.setResponseParams("{}");
        safetyOperateLogDo.setResponseCode("200");
        if (CollectionUtils.isEmpty(safetyOperateLogDo.getSafetyOperateLogDetailDoList())) {
            safetyOperateLogDo.setSafetyOperateLogDetailDoList(Lists.newArrayList());
        }
        if (StringUtils.isNotEmpty(operateUid)) {
            safetyOperateLogDo.setCreateUser(operateUid);
            safetyOperateLogDo.setUpdateUser(operateUid);
        }
        cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
    }

    private SafetyOperateLogDetailDo createSafetyOperateLogDetail(Long id, SafetyModelTypeEnum safetyModelTypeEnum,
                                                                  String jsonParams) {
        SafetyOperateLogDetailDo safetyOperateLogDetailDo = new SafetyOperateLogDetailDo();
        safetyOperateLogDetailDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        if (SafetyModelTypeEnum.MEDIUM.equals(safetyModelTypeEnum)) {
            safetyOperateLogDetailDo.setSyncStatus(200);
        } else {
            safetyOperateLogDetailDo.setSyncStatus(OAUCFCommonConstants.INT_ZERO);
        }
        safetyOperateLogDetailDo.setModelParams(jsonParams);
        safetyOperateLogDetailDo.setModelType(safetyModelTypeEnum.getCode());
        safetyOperateLogDetailDo.setModelId(id);
        return safetyOperateLogDetailDo;
    }

    @Override
    public void deletePersonMedium(String uid, String mediumCode, Long id) {
        safetyPersonMediumRepository.deleteByUidAndMediumCode(uid, mediumCode, id);
    }

    @Override
    public void restorePersonMedium(String uid, String mediumCode) {
        safetyPersonMediumRepository.restoreByUidAndMediumCode(uid, mediumCode);
    }

    @Override
    public void deleteValidateTime(String uid, ZonedDateTime startTime, ZonedDateTime endTime) {
        cardInfoRepository.deleteValidateTime(uid, startTime, endTime);
    }

    @Override
    public void deleteValidateTime(Long cardId) {
        cardInfoRepository.deleteValidateTimeByCardId(cardId);
    }

    @Override
    public void updateStatus(CardInfoDo cardInfoDo) {
        cardInfoRepository.updateStatusById(cardInfoDo);
    }

    @Override
    public void restoreValidateTime(String uid) {
        cardInfoRepository.restoreValidateTime(uid);
    }

    @Override
    public void restoreValidateTimeByCardId(Long cardId) {
        cardInfoRepository.restoreValidateTimeByCardId(cardId);
    }

    @Override
    public void buildValidateTime(CardInfoDo cardInfoDo) {
        if (cardInfoDo == null) {
            return;
        }
        CardTimeValidityDo cardTimeValidityDo = new CardTimeValidityDo();
        cardTimeValidityDo.setCardApplyId(cardInfoDo.getCardApplyId());
        cardTimeValidityDo.setStartTime(cardInfoDo.getStartTime());
        cardTimeValidityDo.setEndTime(cardInfoDo.getEndTime());
        cardTimeValidityDo.setUid(cardInfoDo.getUid());
        cardTimeValidityDo.setCardId(cardInfoDo.getId());
        cardInfoDo.setValidatePeriod(Lists.newArrayList(cardTimeValidityDo));
    }

    @Override
    public void refreshValidateTime(CardInfoDo cardInfoDo) {
        Asserts.assertNotEmpty(cardInfoDo.getValidatePeriod(), CardInfoDomainErrorCodeEnum.CARD_VALIDATE_TIME_NOT_EXISTS);
        for (CardTimeValidityDo cardTimeValidityDo : cardInfoDo.getValidatePeriod()) {
            cardTimeValidityDo.setStartTime(cardInfoDo.getStartTime());
            cardTimeValidityDo.setEndTime(cardInfoDo.getEndTime());
        }
        if (CollectionUtils.isNotEmpty(cardInfoDo.getSafetyRightList())) {
            cardInfoDo.getSafetyRightList().forEach(safetyRightDo -> {
                //非普通权限并且有效期早于卡片有效期
                if (!SafetyCarrierGroupClassEnum.isNormal(safetyRightDo.getSafetyCarrierGroup().getClassCode()) &&
                        cardInfoDo.getEndTime().isBefore(safetyRightDo.getEndTime())) {
                    safetyRightDo.setEndTime(cardInfoDo.getEndTime());
                }
            });
        }
        if (Objects.nonNull(cardInfoDo.getSafetyPersonMediumDo())) {
            cardInfoDo.getSafetyPersonMediumDo().setEndTime(cardInfoDo.getEndTime());
        }
    }

    @Override
    public void saveTime(CardTimeValidityDo cardValidateDo) {
        cardInfoRepository.insertTime(cardValidateDo);
    }

    @Override
    public Long save(CardInfoDo cardInfoDo) {
        return cardInfoRepository.save(cardInfoDo);
    }

    @Override
    public CardInfoDo getCardByStatus(String uid, List<CardStatusEnum> cardStatusEnum) {
        List<Integer> status = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cardStatusEnum)) {
            for (CardStatusEnum statusEnum : cardStatusEnum) {
                status.add(statusEnum.getCode());
            }
        }
        return cardInfoRepository.getCardByStatus(uid, status);
    }

    @Override
    public void fillCardInfoByUidAndCardType(CardInfoDo cardInfoDo) {
        if (StringUtils.isEmpty(cardInfoDo.getUid())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.UID_EMPTY);
        }
        if (ObjectUtils.isEmpty(cardInfoDo.getCardType())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_TYPE_IS_EMPTY);
        }
        CardTypeEnum nowEnum = CardTypeEnum.ofNumber(cardInfoDo.getCardType());
        CardInfoDo now = cardInfoRepository.findCardByUidAndCardType(cardInfoDo.getUid(), nowEnum, null);
        if (now != null) {
            commonDoConverter.copyCardInfoDo(now, cardInfoDo);
        }
    }

    @Override
    public void deleteMedium(String mediumCode) {
        cardInfoRepository.deleteMedium(mediumCode);
    }

    @Override
    public void restoreMedium(String mediumCode) {
        cardInfoRepository.restoreMedium(mediumCode);
    }

    @Override
    public SafetyMediumDo fillMedium(CardInfoDo cardInfoDo) {
        SafetyMediumDo safetyMediumDO = new SafetyMediumDo();
        safetyMediumDO.setMediumCode(cardInfoDo.getMediumCode());
        //由工卡生成的介质，用工卡的供应商类型和供应商编码
        safetyMediumDO.setMediumType(SafetyMediumTypeEnum.CARD.getCode());
        safetyMediumDO.setSupplierCode(SafetySupplierCodeEnum.CARD.getSupplierCode());
        safetyMediumDO.setMediumPhysicsCode(cardInfoDo.getMediumPhysicsCode());
        safetyMediumDO.setMediumEncryptCode(cardInfoDo.getMediumEncryptCode());
        return safetyMediumDO;
    }

    @Override
    public SafetyMediumDo saveMedium(SafetyMediumDo safetyMediumDO) {
        return cardInfoRepository.saveMedium(safetyMediumDO);
    }

    @Override
    public List<SafetyRightDo> getCarrierGroupsByMediums(List<String> mediumCodes) {
        return safetyRightRepository.getCarrierGroupsByMediums(mediumCodes);
    }

    @Override
    public void updateRightByMediumAndGroups(String mediumCode, Set<String> carrierGroupCodes, Integer isDelete) {
        safetyRightRepository.batchUpdateSyncStatusToWaitSync(mediumCode, carrierGroupCodes, false, isDelete);
    }

    @Override
    public void updateTime(String uid, Long id) {
        CardTimeValidityDo cardTimeValidityDo = new CardTimeValidityDo();
        cardTimeValidityDo.setCardId(id);
        cardTimeValidityDo.setUid(uid);
        cardInfoRepository.updateTime(cardTimeValidityDo);
    }

    @Override
    public List<CardInfoDo> getUsingCardList(List<Integer> code) {
        return cardInfoRepository.getListByStatus(code);
    }

    @Override
    public List<CardInfoDo> getNotActiveList(List<Integer> code) {
        return cardInfoRepository.getListByStatus(code);
    }

    @Override
    public List<CardTimeValidityDo> getTimeByCardIds(List<Long> usingIds) {
        return cardInfoRepository.getTimeByCardIds(usingIds);
    }

    @Override
    public void checkMediumExist(String mediumPhysicsCode, String mediumEncryptCode) {
        List<SafetyMediumDo> safetyMediumDOList = safetyMediumRepository.getSafetyMediumByPhysicsCodeAndEncryptCode(mediumPhysicsCode, mediumEncryptCode);
        if (CollectionUtils.isNotEmpty(safetyMediumDOList)) {
            CardInfoDo cardInfoDo = cardInfoRepository.getByMediumCodeAndStatus(safetyMediumDOList.get(0).getMediumCode());
            if (ObjectUtils.isNotEmpty(cardInfoDo)) {
                throw new BizException(CardApplyErrorCodeEnum.MEDIUM_REPEAT);
            }
        }
    }

    @Override
    public void fillBatchImport(List<CardInfoDo> cardInfoDos, List<CardApplyDo> cardApplyDosWithIds) {
        if (CollectionUtils.isNotEmpty(cardApplyDosWithIds)) {
            SafetyCarrierGroupDo query = new SafetyCarrierGroupDo();
            //查询所有门禁权限组
            List<SafetyCarrierGroupDo> safetyCarrierGroupDos = safetyCarrierGroupAbility.queryListByParkCode(query);
            Map<String, SafetyCarrierGroupDo> accessCodeMap = safetyCarrierGroupDos.stream().collect(Collectors.toMap(
                    SafetyCarrierGroupDo::getSupplierAccessCode, item -> item, (a, b) -> a));
            for (CardApplyDo cardApplyDo : cardApplyDosWithIds) {
                CardInfoDo cardInfoDo = new CardInfoDo();
                cardInfoDo.setCardStatus(CardStatusEnum.USING.getCode());
                cardInfoDo.setCardApplyId(cardApplyDo.getId());
                cardInfoDo.setUid(cardApplyDo.getUid());
                cardInfoDo.setCardType(CardTypeEnum.COOPERATION_CARD.getNumber());
                cardInfoDo.setMediumCode(CodeUtils.getUUID());
                cardInfoDo.setMediumPhysicsCode(cardApplyDo.getMediumPhysicsCode());
                cardInfoDo.setMediumEncryptCode(cardApplyDo.getMediumEncryptCode());
                cardInfoDo.setRemark("{{{历史合作卡数据同步}}}");
                cardInfoDo.setStartTime(cardApplyDo.getStartTime());
                cardInfoDo.setEndTime(cardApplyDo.getEndTime());
                if (StringUtils.isNotBlank(cardApplyDo.getSupplierAccessCodeList())) {
                    List<String> accessCodes = Arrays.stream(cardApplyDo.getSupplierAccessCodeList().split(",")).collect(Collectors.toList());
                    List<String> addGroupCodes = Lists.newArrayList();
                    for (String accessCode : accessCodes) {
                        SafetyCarrierGroupDo safetyCarrierGroupDo = accessCodeMap.get(accessCode);
                        if (ObjectUtils.isNotEmpty(safetyCarrierGroupDo)) {
                            addGroupCodes.add(safetyCarrierGroupDo.getCarrierGroupCode());
                        }
                    }
                    cardInfoDo.setAddGroupCodes(addGroupCodes);
                }
                cardInfoDos.add(cardInfoDo);
            }
        }
    }

    @Override
    public List<CardInfoDo> batchSaveWithIds(List<CardInfoDo> cardInfoDos) {
        return cardInfoRepository.batchSaveWithIds(cardInfoDos);
    }

    @Override
    public void fillBatchExcelImportValidateTime(List<CardTimeValidityDo> cardTimeValidityDtos,
                                                 List<CardInfoDo> cardInfoDoWithIds, List<CardApplyDo> cardApplyDos) {
        if (CollectionUtils.isNotEmpty(cardApplyDos) && CollectionUtils.isNotEmpty(cardInfoDoWithIds)) {
            Map<Long, CardInfoDo> cardMap =
                    cardInfoDoWithIds.stream().collect(Collectors.toMap(CardInfoDo::getCardApplyId, item -> item, (a, b) -> a));
            for (CardApplyDo cardApplyDo : cardApplyDos) {
                CardTimeValidityDo cardTimeValidityDo = new CardTimeValidityDo();
                CardInfoDo cardInfoDo = cardMap.get(cardApplyDo.getId());
                cardTimeValidityDo.setCardId(cardInfoDo.getId());
                cardTimeValidityDo.setStartTime(cardApplyDo.getStartTime());
                cardTimeValidityDo.setEndTime(cardApplyDo.getEndTime());
                cardTimeValidityDo.setUid(cardApplyDo.getUid());
                cardTimeValidityDo.setRemark("{{{合作伙伴实名制导入数据}}}");
                cardTimeValidityDtos.add(cardTimeValidityDo);
            }
        }
    }

    @Override
    public void batchSaveValidateTime(List<CardTimeValidityDo> cardTimeValidityDtos) {
        cardInfoRepository.batchSaveValidateTime(cardTimeValidityDtos);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByPhysicCodes(List<String> mediumPhysicsCodes) {
        if (CollectionUtils.isEmpty(mediumPhysicsCodes)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.PHYSICS_CODE_EMPTY);
        }
        return cardInfoRepository.getListCardInfoByPhysicCodes(mediumPhysicsCodes);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByEncryptCodes(List<String> mediumEncryptCodes) {
        if (CollectionUtils.isEmpty(mediumEncryptCodes)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.PHYSICS_CODE_EMPTY);
        }
        return cardInfoRepository.getListCardInfoByEncryptCodes(mediumEncryptCodes);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByUid(List<String> uid) {
        if (CollectionUtils.isEmpty(uid)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.UID_EMPTY);
        }
        return cardInfoRepository.getListCardInfoByUid(uid);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByAccounts(List<String> accounts) {
        if (CollectionUtils.isEmpty(accounts)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.ACCOUNT_EMPTY);
        }
        return cardInfoRepository.getListCardInfoByAccounts(accounts);
    }

    @Override
    public void checkIsOccupation(CardInfoDo cardInfoDo) {
        if (StringUtils.isNotBlank(cardInfoDo.getMediumPhysicsCode())) {
            CardInfoDo oldCard = cardInfoRepository.findCardInfoByPhysicsCode(cardInfoDo.getMediumPhysicsCode());
            if (ObjectUtils.isNotEmpty(oldCard) && !oldCard.getUid().equals(cardInfoDo.getUid())) {
                throw new BizException(CardInfoDomainErrorCodeEnum.CURRENT_CARD_OCCUPIED);
            }
        }
    }

    @Override
    public void checkCardIsOccupation(CardInfoDo cardInfoDo) {
        //物理卡号不为空，校验物理卡号是否被占用
        if (StringUtils.isNotEmpty(cardInfoDo.getMediumPhysicsCode())) {
            CardInfoDo oldCard = cardInfoRepository.findCardInfoByPhysicsCode(cardInfoDo.getMediumPhysicsCode());
            Optional.ofNullable(oldCard).ifPresent(item ->
                    Asserts.assertTrue(StringUtils.equalsIgnoreCase(item.getUid(), cardInfoDo.getUid()),
                            CardInfoDomainErrorCodeEnum.CURRENT_CARD_OCCUPIED));
        }
        //加密卡号不为空，校验加密卡号是否被占用
        if (StringUtils.isNotEmpty(cardInfoDo.getMediumEncryptCode())) {
            CardInfoDo oldCard = cardInfoRepository.findCardInfoByEncryptCode(cardInfoDo.getMediumEncryptCode());
            Optional.ofNullable(oldCard).ifPresent(item ->
                    //如果是同一人，则通过
                    Asserts.assertTrue(StringUtils.equalsIgnoreCase(item.getUid(), cardInfoDo.getUid()),
                            CardInfoDomainErrorCodeEnum.CURRENT_CARD_OCCUPIED));
        }
    }

    @Override
    public CardInfoDo getCardByNums(SafetyCardToolDto safetyCardToolDto, List<CardStatusEnum> status) {
        return cardInfoRepository.getCardByNums(safetyCardToolDto, status);
    }

    @Override
    public CardInfoDo getListCardInfoByPhysicCode(String mediumPhysicsCode) {
        return cardInfoRepository.findCardInfoByPhysicsCode(mediumPhysicsCode);
    }

    @Override
    public CardInfoDo findCardInfoByApplyId(Long applyId) {
        if (ObjectUtils.isEmpty(applyId)) {
            return null;
        }
        return cardInfoRepository.findCardInfoByApplyId(applyId);
    }

    @Override
    public List<CardInfoDo> findCardInfoByApplyIdList(List<Long> applyIdList) {
        return cardInfoRepository.findCardInfoByApplyIdList(applyIdList);
    }

    @Override
    public List<CardTimeValidityDo> findValidatePeriodByCardIdList(List<Long> cardIdList) {
        return cardInfoRepository.findValidatePeriodByCardIdList(cardIdList);
    }

    @Override
    public List<CardTimeValidityDo> findValidateTimeByCardId(Long cardId) {
        return cardInfoRepository.findValidateTimeByCardId(cardId);
    }

    @Override
    public List<Long> findCardIdByEffectTime(ZonedDateTime endTimeFrom, ZonedDateTime endTimeTo) {
        return cardInfoRepository.findCardIdByEffectTime(endTimeFrom, endTimeTo);
    }

    @Override
    public void fillAndSaveCardRights(CardInfoDo cardInfoDo) {
        if (CollectionUtils.isNotEmpty(cardInfoDo.getAccessControl())) {
            List<SafetyRightDo> safetyRightDos = Lists.newArrayList();
            for (SafetyCarrierGroupDo safetyCarrierGroupDto : cardInfoDo.getAccessControl()) {
                SafetyRightDo safetyRightDO = new SafetyRightDo();
                safetyRightDO.setUid(cardInfoDo.getUid());
                safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
                safetyRightDO.setCarrierGroupCode(safetyCarrierGroupDto.getCarrierGroupCode());
                safetyRightDO.setStartTime(cardInfoDo.getStartTime());
                safetyRightDO.setEndTime(cardInfoDo.getEndTime());
                safetyRightDO.setSupplierCode(safetyCarrierGroupDto.getSupplierCode());
                safetyRightDO.setSupplierAccessCode(safetyCarrierGroupDto.getSupplierAccessCode());
                safetyRightDO.setSupplierCheckCode(cardInfoDo.getMediumPhysicsCode());
                safetyRightDO.setSyncStatus(OAUCFCommonConstants.INT_ZERO);
                safetyRightDO.setIsDeleted(OAUCFCommonConstants.LONG_ZERO);
                safetyRightDO.setOperateTime(ZonedDateTime.now());
                safetyRightDos.add(safetyRightDO);
            }
            safetyRightRepository.batchSaveOrUpdate(safetyRightDos, true);
        }
    }

    @Override
    public void fillImportCardInfo(CardInfoDo cardInfoDo) {
        //若当前时间大于有效期结束时间
        if (ZonedDateTimeUtils.compare(ZonedDateTime.now(), cardInfoDo.getCardApply().getEndTime()) > OAUCFCommonConstants.INT_ZERO) {
            cardInfoDo.setCardStatus(CardStatusEnum.EXPIRED.getCode());
        } else {
            cardInfoDo.setCardStatus(CardStatusEnum.USING.getCode());
        }
        cardInfoDo.setCardApplyId(cardInfoDo.getCardApply().getId());
        cardInfoDo.setUid(cardInfoDo.getCardApply().getPersonInfo().getUid());
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setMediumPhysicsCode(cardInfoDo.getCardApply().getMediumPhysicsCode());
        cardInfoDo.setMediumEncryptCode(cardInfoDo.getCardApply().getMediumEncryptCode());
        cardInfoDo.setRemark("{{{老工卡数据同步}}}");
        cardInfoDo.setStartTime(ZonedDateTime.now());
        cardInfoDo.setEndTime(cardInfoDo.getCardApply().getEndTime());
    }

    @Override
    public void fillImportMedium(CardInfoDo cardInfoDo) {
        SafetyMediumDo safetyMediumDO = new SafetyMediumDo();
        safetyMediumDO.setMediumCode(cardInfoDo.getMediumCode());
        //由工卡组装而成的介质，供应商是工卡
        safetyMediumDO.setSupplierCode(SafetySupplierCodeEnum.CARD.getSupplierCode());
        safetyMediumDO.setMediumPhysicsCode(cardInfoDo.getMediumPhysicsCode());
        safetyMediumDO.setMediumType(SafetyMediumTypeEnum.CARD.getCode());
        safetyMediumDO.setMediumEncryptCode(cardInfoDo.getMediumEncryptCode());
        safetyMediumDO.setSyncStatus(200);
        safetyMediumDO.setParkCode("");
        safetyMediumDO.setRemark("{{{同步数据}}}");
        cardInfoDo.setSafetyMedium(safetyMediumDO);
    }

    @Override
    public void fillImportValidateTime(CardInfoDo cardInfoDo) {

        CardTimeValidityDo cardTimeValidityDo = new CardTimeValidityDo();
        cardTimeValidityDo.setCardId(cardInfoDo.getId());
        cardTimeValidityDo.setStartTime(cardInfoDo.getStartTime());
        cardTimeValidityDo.setEndTime(cardInfoDo.getEndTime());
        cardTimeValidityDo.setUid(cardInfoDo.getCardApply().getPersonInfo().getUid());
        cardTimeValidityDo.setRemark("{{{导入数据}}}");
        cardInfoDo.setValidatePeriod(Lists.newArrayList(cardTimeValidityDo));
    }

    @Override
    public void canReopen(CardInfoDo cardInfoDo) {
        //状态不是销卡或者已注销的卡不能重启
        if (!CardStatusEnum.end(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.REOPEN_STATUS_VALID);
        }
        //人员不是启用状态
        PersonInfoModel person = idmSdk.findPersonInfo(cardInfoDo.getUid());
        if (!SafetyPersonStatusEnum.ENABLE.equals(person.getPersonStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.PERSON_CLOSE_FORBIDDEN);
        }
        //卡片已经被使用
        CardInfoDo usingCard = cardInfoRepository.findCardInfoByPhysicsCode(cardInfoDo.getMediumPhysicsCode());
        if (Objects.nonNull(usingCard)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.MEDIUM_CODE_OCCUPATION);
        }
        //已经领取了员工卡
        CardInfoDo empCardInfo = cardInfoRepository.findCardByUidAndCardType(cardInfoDo.getUid(),
                CardTypeEnum.EMPLOYEE_CARD, null);
        if (Objects.nonNull(empCardInfo) && CardStatusEnum.usingList().contains(empCardInfo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.TEMP_CARD_REOPEN_ERROR_EMP_CARD_ACTIVE);
        }
    }

    @Override
    public void fillValidateTimeByCardId(CardInfoDo cardInfoDo) {
        if (Objects.nonNull(cardInfoDo)) {
            List<CardTimeValidityDo> cardTimeValidityDtoList = findValidateTimeByCardId(cardInfoDo.getId());
            cardInfoDo.setValidatePeriod(cardTimeValidityDtoList);
            if (CollectionUtils.isNotEmpty(cardTimeValidityDtoList)) {
                for (CardTimeValidityDo cardTimeValidityDto : cardTimeValidityDtoList) {
                    if (isBetween(cardTimeValidityDto.getStartTime(), cardTimeValidityDto.getEndTime())) {
                        //取第一条记录，因为卡片的有效期只有一条记录
                        cardInfoDo.setEndTime(cardTimeValidityDto.getEndTime());
                        cardInfoDo.setStartTime(cardTimeValidityDto.getStartTime());
                        return;
                    }
                }
                //没有在有效期内的记录，取第一条记录
                cardInfoDo.setEndTime(cardTimeValidityDtoList.get(0).getEndTime());
                cardInfoDo.setStartTime(cardTimeValidityDtoList.get(0).getStartTime());
            }
        }
    }

    private boolean isBetween(ZonedDateTime startTime, ZonedDateTime endTime) {
        ZonedDateTime now = ZonedDateTime.now();
        return now.isAfter(startTime) && now.isBefore(endTime);
    }

    @Override
    public void editCheck(CardInfoDo cardInfoDo) {
        if (CardStatusEnum.end(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.AUTH_CHANGE_LOG_DO_FAIL);
        }
    }

    @Override
    public void copySafetyRight(CardInfoDo cardInfoDo) {
        Boolean isEditForApply = (Boolean) cardInfoDo.getExtField("isEditForApply");
        cardInfoDo.getSafetyRightList().forEach(safetyRight -> {
            safetyRight.setId(null);
            //若是正式卡申请单过来的编辑请求 person medium和right同步状态给200
            if (ObjectUtils.isNotEmpty(isEditForApply) && isEditForApply) {
                safetyRight.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
            } else {
                safetyRight.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
            }
            safetyRight.setMediumCode(cardInfoDo.getMediumCode());
            safetyRight.setSupplierCheckCode(cardInfoDo.getMediumPhysicsCode());
            safetyRight.setIsDeleted(YesNoEnum.NO.getCode().longValue());
        });
    }

    @Override
    public void checkIsEmpty(CardInfoDo cardInfoDo) {
        if (org.springframework.util.ObjectUtils.isEmpty(cardInfoDo)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_NOT_EXIST);
        }
    }

    @Override
    public void checkIsUsing(CardInfoDo cardInfoDo) {
        if (CardStatusEnum.end(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CURRENT_CARD_RETURN_STATUS_ERROR);
        }
    }

    @Override
    public void fillPersonMediumLogDetail(CardInfoDo cardInfoDo) {
        if (ObjectUtils.isNotEmpty(cardInfoDo.getSafetyPersonMediumDo())) {
            SafetyOperateLogDetailDo safetyOperateLogDetail = createSafetyOperateLogDetail(cardInfoDo.getSafetyPersonMediumDo().getId(),
                    SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(cardInfoDo.getSafetyPersonMediumDo()));
            cardInfoDo.getSafetyOperateLog().getSafetyOperateLogDetailDoList().add(safetyOperateLogDetail);
        }
    }

    @Override
    public void fillSafetyRightLogDetail(CardInfoDo cardInfoDo) {
        if (CollectionUtils.isNotEmpty(cardInfoDo.getSafetyRightList())) {
            cardInfoDo.getSafetyRightList().stream().forEach(item -> {
                SafetyOperateLogDetailDo safetyOperateLogDetail = createSafetyOperateLogDetail(item.getId(),
                        SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(item));
                cardInfoDo.getSafetyOperateLog().getSafetyOperateLogDetailDoList().add(safetyOperateLogDetail);
            });
        }
    }

    @Override
    public SafetyPersonMediumDo getSafetyPersonMediumByMediumCodeAndUid(String uid, String mediumCode) {
        return safetyPersonMediumRepository.getSafetyPersonMediumByMediumCodeAndUid(mediumCode, uid);
    }

    @Override
    public void checkPhysicsCodeIsOccupied(CardInfoDo cardInfoDo) {
        if (StringUtils.isEmpty(cardInfoDo.getMediumPhysicsCode())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.PHYSICS_CODE_EMPTY);
        }
        CardInfoDo now = cardInfoRepository.findCardInfoByPhysicsCode(cardInfoDo.getMediumPhysicsCode());
        if (Objects.nonNull(now)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.MEDIUM_PHYSICS_CODE_OCCUPATION);
        }
    }

    @Override
    public void checkEncryptCodeIsOccupied(CardInfoDo cardInfoDo) {
        if (StringUtils.isEmpty(cardInfoDo.getMediumEncryptCode())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.ENCRYPT_CODE_EMPTY);
        }
        CardInfoDo now = cardInfoRepository.findCardInfoByEncryptCode(cardInfoDo.getMediumEncryptCode());
        if (Objects.nonNull(now)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.MEDIUM_ENCRYPT_CODE_OCCUPATION);
        }
    }

    @Override
    public void fillInfoByEncryptCode(CardInfoDo cardInfoDo) {
        if (StringUtils.isEmpty(cardInfoDo.getMediumEncryptCode())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.ENCRYPT_CODE_EMPTY);
        }
        CardInfoDo now = cardInfoRepository.findCardInfoByEncryptCode(cardInfoDo.getMediumEncryptCode());
        commonDoConverter.copyCardInfoDo(now, cardInfoDo);
    }


    @Override
    public void checkTempCardIsReturn(CardInfoDo cardInfoDo) {
        CardInfoDo card = cardInfoRepository.findCardByUidAndCardType(cardInfoDo.getUid(), CardTypeEnum.TEMP_CARD, null);
        if (ObjectUtils.isNotEmpty(card) && !CardStatusEnum.RETURNED.getCode().equals(card.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.TEMP_CARD_NOT_RETURN);
        }
    }

    @Override
    public void checkEmpCardIsExist(CardInfoDo cardParam) {
        @SuppressWarnings("unchecked")
        List<Integer> statusList = (List<Integer>) cardParam.getExtField("statusList");
        CardInfoDo card = cardInfoRepository.findCardByUidAndCardType(cardParam.getUid(),
                CardTypeEnum.ofNumber(cardParam.getCardType()), statusList);
        if (ObjectUtils.isNotEmpty(card) && !CardStatusEnum.LOSS.getCode().equals(card.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.EMP_CARD_EXIST);
        }
    }

    @Override
    public void checkCardIsExpireForReceive(CardInfoDo cardInfoDo) {
        if (!CollectionUtils.isEmpty(cardInfoDo.getValidatePeriod())) {
            ZonedDateTime nowTime = ZonedDateTime.now();
            for (CardTimeValidityDo timeValidityDo : cardInfoDo.getValidatePeriod()) {
                //多段有效期 若当前时间没有所属时间段则过期
                if ((ZonedDateTimeUtils.compare(nowTime, timeValidityDo.getStartTime()) >= OAUCFCommonConstants.INT_ZERO &&
                        ZonedDateTimeUtils.compare(nowTime, timeValidityDo.getEndTime()) <= OAUCFCommonConstants.INT_ZERO) ||
                        ZonedDateTimeUtils.compare(nowTime, timeValidityDo.getStartTime()) <= OAUCFCommonConstants.INT_ZERO) {
                    return;
                }
            }
        }
        throw new BizException(CardInfoDomainErrorCodeEnum.CARD_VALIDATE_TIME_EXPIRE);
    }

    @Override
    public void checkIsExistRight(CardInfoDo cardInfoDo) {
        if (CollectionUtils.isNotEmpty(cardInfoDo.getSafetyRightList())) {
            List<String> carrierGroupCodes = cardInfoDo.getSafetyRightList().stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
            for (String addGroupCode : cardInfoDo.getAddGroupCodes()) {
                if (carrierGroupCodes.contains(addGroupCode)) {
                    throw new BizException(CardInfoDomainErrorCodeEnum.EXIST_SPECIAL_PERMISSION);
                }
            }
        }
    }

    @Override
    public void checkPermissionIsExceedCardValidateTime(CardInfoDo cardInfoDo) {
        //合作卡和临时卡需要校验有效期
        if (CardTypeEnum.COOPERATION_CARD.getNumber().equals(cardInfoDo.getCardType()) ||
                CardTypeEnum.TEMP_CARD.getNumber().equals(cardInfoDo.getCardType()) ||
                CardTypeEnum.PROPERTY_CARD_APPLY.getNumber().equals(cardInfoDo.getCardType())) {
            ZonedDateTime now = ZonedDateTime.now();
            //当前时间在某一段有效期内 且权限有效期时间在卡有效期范围内 返回
            for (CardTimeValidityDo cardTimeValidityDo : cardInfoDo.getValidatePeriod()) {
                if (ZonedDateTimeUtils.compare(now, cardTimeValidityDo.getStartTime()) >= OAUCFCommonConstants.INT_ZERO &&
                        ZonedDateTimeUtils.compare(now, cardTimeValidityDo.getEndTime()) <= OAUCFCommonConstants.INT_ZERO &&
                        ZonedDateTimeUtils.compare(cardInfoDo.getStartTime().plusHours(1L),
                                cardTimeValidityDo.getStartTime()) >= OAUCFCommonConstants.INT_ZERO &&
                        ZonedDateTimeUtils.compare(cardInfoDo.getEndTime().minusHours(1L),
                                cardTimeValidityDo.getEndTime()) <= OAUCFCommonConstants.INT_ZERO) {
                    return;
                } else if (ZonedDateTimeUtils.compare(now, cardTimeValidityDo.getStartTime()) >= OAUCFCommonConstants.INT_ZERO &&
                        ZonedDateTimeUtils.compare(now, cardTimeValidityDo.getEndTime()) <= OAUCFCommonConstants.INT_ZERO &&
                        ZonedDateTimeUtils.compare(cardInfoDo.getEndTime(), cardTimeValidityDo.getEndTime()) > OAUCFCommonConstants.INT_ZERO) {
                    cardInfoDo.setEndTime(cardTimeValidityDo.getEndTime());
                    return;
                }
            }
            throw new BizException(CardInfoDomainErrorCodeEnum.PERMISSION_EXCEED_CARD_VALIDATE_TIME);
        }
    }

    @Override
    public void checkIsExistAllRight(CardInfoDo cardInfoDo) {
        if (CollectionUtils.isNotEmpty(cardInfoDo.getSafetyRightList())) {
            List<String> carrierGroupCodes = cardInfoDo.getSafetyRightList().stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
            cardInfoDo.getDelGroupCodes().removeIf(item -> !carrierGroupCodes.contains(item));
        } else {
            cardInfoDo.setDelGroupCodes(Lists.newArrayList());
        }
    }

    @Override
    public void checkCardIsDestroy(CardInfoDo cardInfoDo) {
        if (CardStatusEnum.CANCELED.getCode().equals(cardInfoDo.getCardStatus())
                || CardStatusEnum.RETURNED.getCode().equals(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_HAS_DESTROY);
        }
    }

    @Override
    public void checkCardIsUsing(CardInfoDo cardInfoDo) {
        if (Objects.isNull(cardInfoDo)
                || !CardStatusEnum.USING.getCode().equals(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_STATUS_NOT_USING);
        }
    }

    @Override
    public void deleteSafetyRightAndUpdateSync(CardInfoDo cardInfoDo) {
        List<SafetyRightDo> safetyRightDoList = cardInfoDo.getSafetyRightList();
        if (CollectionUtils.isEmpty(safetyRightDoList)) {
            return;
        }
        for (SafetyRightDo rightDo : safetyRightDoList) {
            cardInfoDo.getSafetyOperateLog().getSafetyOperateLogDetailDoList().add(createSafetyOperateLogDetail(rightDo.getId(),
                    SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(rightDo)));
        }
        //先删除权限
        safetyRightRepository.batchDeleteByMediumCodeAndCarrierGroupCode(Lists.newArrayList(safetyRightDoList));
        //更新介质与权限的同步状态为待同步
        List<String> carrierGroupCodes = safetyRightDoList.stream()
                .map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
        safetyRightRepository.batchUpdateSyncStatusToWaitSync(cardInfoDo.getMediumCode(),
                Sets.newHashSet(carrierGroupCodes), false, OAUCFCommonConstants.INT_ONE);
    }

    @Override
    public void fillCardLeaveRecordBaseInfo(CardInfoDo cardInfoDo) {
        CardLeaveRecordDo cardLeaveRecord = cardInfoDo.getCardLeaveRecord();
        cardLeaveRecord.setStatus(CardLeaveRecordStatusEnum.WAIT_LEAVE.getCode());
        cardLeaveRecord.setUid(cardInfoDo.getCardApply().getPersonInfo().getUid());
        cardInfoDo.setUid(cardInfoDo.getCardApply().getPersonInfo().getUid());
    }

    @Override
    public void checkLeaveRecordRepeated(CardInfoDo cardInfoDo) {
        CardLeaveRecordDo cardLeaveRecordDo =
                cardLeaveRecordRepository.getOneByPsLeaveNumber(cardInfoDo.getCardLeaveRecord().getPsLeaveNumber());
        if (ObjectUtils.isNotEmpty(cardLeaveRecordDo)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_LEAVE_RECORD_REPEATED);
        }
    }

    @Override
    public void checkTempAndEmpCardIsUsing(CardInfoDo cardInfoDo) {
        CardInfoDo oldCard = cardInfoRepository.findTempAndEmpCard(cardInfoDo);
        if (ObjectUtils.isEmpty(oldCard)) {
            //抛异常改为 无卡状态
            cardInfoDo.getCardLeaveRecord().setStatus(OAUCFCommonConstants.INT_TWO);
            //若已销卡或还卡
        } else if (CardStatusEnum.CANCELED.getCode().equals(oldCard.getCardStatus()) ||
                CardStatusEnum.RETURNED.getCode().equals(oldCard.getCardStatus())) {
            //销卡时间小于提交预离职的时间，则直接调人事接口完成工卡交接节点
            if (ZonedDateTimeUtils.compare(oldCard.getUpdateTime(), ZonedDateTime.now())
                    <= OAUCFCommonConstants.INT_ZERO) {
                Boolean isSuccess = hrodSdk.reportLeave(cardInfoDo.getCardLeaveRecord().getEmployeeNo(),
                        cardInfoDo.getCardLeaveRecord().getPsLeaveNumber());
                if (isSuccess) {
                    //若成功 状态设为已离职
                    cardInfoDo.getCardLeaveRecord().setStatus(OAUCFCommonConstants.INT_ONE);
                }
            }
        }
    }

    @Override
    public List<CardLeaveRecordDo> findLeaveRecordByUidList(List<String> uidList) {
        return cardLeaveRecordRepository.findLeaveRecordByUidList(uidList);
    }

    @Override
    public CardLeaveRecordDo findPreLeaveRecordByUid(String uid) {
        return cardLeaveRecordRepository.findPreLeaveRecordByUid(uid);
    }

    @Override
    public void reportLeave(CardInfoDo cardInfoDo) {
        CardLeaveRecordDo cardLeaveRecord = cardInfoDo.getCardLeaveRecord();
        List<PersonInfoModel> personInfoModelList = hrodSdk.getLeaveDate(Lists.newArrayList(cardLeaveRecord.getUserName()));
        PersonInfoModel personInfoModel = new PersonInfoModel();
        if (CollectionUtils.isNotEmpty(personInfoModelList)) {
            personInfoModel = personInfoModelList.get(0);
        }
        Boolean isSuccess = hrodSdk.reportLeave(cardLeaveRecord.getEmployeeNo(), cardLeaveRecord.getPsLeaveNumber());
        if (isSuccess) {
            cardLeaveRecord.setStatus(OAUCFCommonConstants.INT_ONE);
            cardLeaveRecord.setPreLeaveDate(personInfoModel.getPreLeaveDate());
            cardLeaveRecordRepository.updateById(cardLeaveRecord);
        }
    }

    @Override
    public void updateValidateTimeByUidAndTime(CardInfoDo cardInfoDo) {
        CardTimeValidityDo cardTimeValidity = cardInfoDo.getCardTimeValidity();
        if (StringUtils.isEmpty(cardTimeValidity.getUid())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.UID_EMPTY);
        }
        if (ObjectUtils.isEmpty(cardTimeValidity.getStartTime()) || ObjectUtils.isEmpty(cardTimeValidity.getEndTime())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_START_END_TIME_EMPTY);
        }
        //当前时间在有效期范围内的 更新结束时间为当天0点 未来有效期时间段直接删除
        ZonedDateTime now = ZonedDateTime.now();
        if (ZonedDateTimeUtils.compare(now, cardTimeValidity.getStartTime()) >= OAUCFCommonConstants.INT_ZERO
                && ZonedDateTimeUtils.compare(now, cardTimeValidity.getEndTime()) <= OAUCFCommonConstants.INT_ZERO) {
            cardTimeValidateRepository.updateValidateTimeByUidAndTime(cardTimeValidity);
        } else {
            cardTimeValidateRepository.deleteByUidAndTime(cardTimeValidity);
        }
    }

    @Override
    public void fillLocationParkCode(CardInfoDo cardInfoDo) {
        String nowParkCode = null;
        try {
            nowParkCode = (String) reportParkCodeLocalObjectCache.get(cardInfoDo.getPartnerAccount(),
                    () -> getLocationParkCode(cardInfoDo));
            cardInfoDo.setParkCode(nowParkCode);
        } catch (Exception e) {
            log.error("userName:{} fillLocationParkCode", cardInfoDo.getPartnerAccount(), e);
        } finally {
            if (ObjectUtils.isNotEmpty(nowParkCode)) {
                reportParkCodeLocalObjectCache.put(cardInfoDo.getPartnerAccount(), nowParkCode);
            }
        }
    }

    private String getLocationParkCode(CardInfoDo cardInfoDo) {
        String nowParkCode = OAUCFCommonConstants.STR_EMPTY;
        try {
            SafetyPersonDo personInfo = cardInfoDo.getCardApply().getPersonInfo();
            CardPsParkAdminDo cardPsParkAdminDo = cardParkAdminRepository.findOneByReportAddressId(personInfo.getReportAddressId());
            nowParkCode = cardPsParkAdminDo != null ? cardPsParkAdminDo.getParkCode() : null;
        } catch (Exception e) {
            log.error("userName:{} getLocationParkCode failed:{}", cardInfoDo.getPartnerAccount(), e.getMessage());
        }
        return nowParkCode == null ? OAUCFCommonConstants.STR_EMPTY : nowParkCode;
    }

    @Override
    public Boolean checkIsM9ParkCode(CardInfoDo cardInfoDo) {
        List<String> whiteParkList = Arrays.asList(parkCode.split(","));
        if (whiteParkList.contains(cardInfoDo.getParkCode())) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean checkIsM9Depart(CardInfoDo cardInfoDo) {
        SafetyPersonDo personInfo = cardInfoDo.getCardApply().getPersonInfo();
        if (StringUtils.isEmpty(personInfo.getFirstDeptId())
                || StringUtils.isEmpty(personInfo.getSecondDeptId())
                || StringUtils.isEmpty(personInfo.getThirdDeptId())) {
            PersonInfoModel departments = hrodSdk.getDepartments(cardInfoDo.getPartnerAccount());
            if (StringUtils.isEmpty(departments.getFirstDept())
                    || StringUtils.isEmpty(departments.getSecondDept())
                    || StringUtils.isEmpty(departments.getThirdDept())) {
                throw new BizException(CardInfoDomainErrorCodeEnum.GET_THREE_DEPART_FAILED);
            }
            personInfo.setFirstDeptId(departments.getFirstDept());
            personInfo.setSecondDeptId(departments.getSecondDept());
            personInfo.setThirdDeptId(departments.getThirdDept());
        }
        return firstDept.equals(personInfo.getFirstDeptId())
                && secondDept.equals(personInfo.getSecondDeptId())
                && thirdDept.equals(personInfo.getThirdDeptId());
    }

    @Override
    public boolean checkIsCHN(CardInfoDo cardInfoDo) {
        SafetyPersonDo safetyPerson = cardInfoDo.getCardApply().getPersonInfo();
        if (StringUtils.isNotEmpty(safetyPerson.getLocationCode())) {
            return "CHN".equals(safetyPerson.getLocationCode().substring(0, 3));
        }
        return false;
    }

    @Override
    public void fillRecyclableCard(CardInfoDo cardInfoDo) {
        RecyclableCardDo recyclableCardDo = new RecyclableCardDo();
        recyclableCardDo.setCardNum(cardInfoDo.getCardNum());
        recyclableCardDo.setMediumEncryptCode(cardInfoDo.getMediumEncryptCode());
        recyclableCardDo.setMediumPhysicsCode(cardInfoDo.getMediumPhysicsCode());
        recyclableCardDo.setUid(cardInfoDo.getUid());
        recyclableCardDo.setCardTypeEnum(CardTypeEnum.ofNumber(cardInfoDo.getCardType()));
        cardInfoDo.setRecyclableCardDo(recyclableCardDo);
    }

    @Override
    public void checkCardIsExistByUidAndType(CardInfoDo cardInfoDo) {
        //创建前检验临时卡是否已存在
        if (CardTypeEnum.TEMP_CARD.getNumber().equals(cardInfoDo.getCardType())) {
            //临时卡申请单创建前 需校验临时卡是否已存在
            CardInfoDo tempCard = cardInfoRepository.findCardByUidAndCardType(cardInfoDo.getUid(), CardTypeEnum.TEMP_CARD,
                    Lists.newArrayList(CardStatusEnum.usingList()));
            if (!org.springframework.util.ObjectUtils.isEmpty(tempCard)) {
                throw new BizException(CardInfoDomainErrorCodeEnum.TEMP_CARD_NOT_RETURN);
            }
        }
        //临时卡、正式卡申请单创建前 需校验是否有正式卡
        CardInfoDo empCard = cardInfoRepository.findCardByUidAndCardType(cardInfoDo.getUid(),
                CardTypeEnum.EMPLOYEE_CARD, CardStatusEnum.exceptNotActive());
        if (ObjectUtils.isNotEmpty(empCard)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.EMP_CARD_EXIST);
        }
    }

    @Override
    public List<CardInfoDo> findListByMediumCodeList(List<String> mediumCodes) {
        if (CollectionUtils.isEmpty(mediumCodes)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.MEDIUM_CODE_NOT_EMPTY);
        }
        return cardInfoRepository.findListByMediumCodeList(mediumCodes);
    }

    @Override
    public void fillCardInfoByApplyId(CardInfoDo cardInfoDo) {
        if (ObjectUtils.isEmpty(cardInfoDo.getCardApplyId())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_APPLY_ID_IS_EMPTY);
        }
        CardInfoDo nowDo = cardInfoRepository.findCardInfoByApplyId(cardInfoDo.getCardApplyId());
        Asserts.assertNotNull(nowDo, CardInfoDomainErrorCodeEnum.CARD_NOT_EXIST);
        commonDoConverter.copyCardInfoDo(nowDo, cardInfoDo);
    }

    @Override
    public void buildNewSafetyMedium(CardInfoDo cardInfoDo) {
        SafetyMediumDo safetyMediumDO = new SafetyMediumDo();
        safetyMediumDO.setMediumCode(cardInfoDo.getMediumCode());
        //由工卡组装而成的介质，供应商是工卡
        safetyMediumDO.setSupplierCode(SafetySupplierCodeEnum.CARD.getSupplierCode());
        safetyMediumDO.setMediumPhysicsCode(cardInfoDo.getMediumPhysicsCode());
        safetyMediumDO.setMediumType(SafetyMediumTypeEnum.CARD.getCode());
        safetyMediumDO.setMediumEncryptCode(cardInfoDo.getMediumEncryptCode());
        safetyMediumDO.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
        safetyMediumDO.setParkCode("");
        cardInfoDo.setSafetyMedium(safetyMediumDO);
    }

    @Override
    public void buildNewSafetyPersonMedium(CardInfoDo cardInfoDo) {
        SafetyPersonMediumDo safetyPersonMediumDO = new SafetyPersonMediumDo();
        safetyPersonMediumDO.setUid(cardInfoDo.getUid());
        safetyPersonMediumDO.setSyncStatus(OAUCFCommonConstants.INT_ZERO);
        safetyPersonMediumDO.setMediumCode(cardInfoDo.getMediumCode());
        safetyPersonMediumDO.setStartTime(cardInfoDo.getStartTime());
        safetyPersonMediumDO.setEndTime(cardInfoDo.getEndTime());
        cardInfoDo.setSafetyPersonMediumDo(safetyPersonMediumDO);
    }

    @Override
    public void checkBeforeLossCard(CardInfoDo cardInfoDo) {
        if (Objects.isNull(cardInfoDo)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_NOT_EXISTS_APPLET);
        }

        if (!CardStatusEnum.USING.getCode().equals(cardInfoDo.getCardStatus()) &&
                !CardStatusEnum.EXPIRED.getCode().equals(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_IS_NOT_ACTIVE);
        }
    }

    @Override
    public void lossCard(CardInfoDo cardInfoDo) {
        cardInfoDo.setCardStatus(CardStatusEnum.LOSS.getCode());
        cardInfoDo.setBackTime(ZonedDateTime.now());
    }

    @Override
    public void checkBeforeRemoveLossCard(CardInfoDo cardInfoDo) {
        if (Objects.isNull(cardInfoDo)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_NOT_EXISTS_APPLET);
        }

        if (!CardStatusEnum.LOSS.getCode().equals(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_STATUS_NOT_LOSS_APPLET);
        }
    }

    @Override
    public void removeLossCard(CardInfoDo cardInfoDo) {
        //更改工卡信息状态
        cardInfoDo.setCardStatus(CardStatusEnum.USING.getCode());
        cardInfoDo.setBackTime(ZonedDateTimeUtils.getZeroTime());
    }

    @Override
    public void checkBeforeAddPermission(CardInfoDo cardInfoDo) {
        Asserts.assertNotNull(cardInfoDo.getMediumCode(), CardInfoDomainErrorCodeEnum.MEDIUM_CODE_NOT_EMPTY);
    }

    @Override
    public void fillGrantedSafetyRight(CardInfoDo cardInfoDo) {
        List<SafetyRightDo> safetyRightList = safetyRightRepository
                .findSafetyRightByMediumCodeAndUid(cardInfoDo.getMediumCode(), null, null);
        cardInfoDo.setSafetyRightList(safetyRightList);
    }

    @Override
    public void groupSafetyRightForAddPermission(CardInfoDo cardInfoDo, List<SafetyRightDo> safetyRightList) {
        Map<String, SafetyRightDo> authedPermissionMap = cardInfoDo.getSafetyRightList().stream()
                .collect(Collectors.toMap(SafetyRightDo::getCarrierGroupCode, Function.identity(), (a, b) -> a));
        List<SafetyRightDo> addRights = Lists.newArrayList();
        List<SafetyRightDo> updateRights = Lists.newArrayList();
        List<String> addGroupCodeList = Lists.newArrayList();
        List<String> updateGorupCodeList = Lists.newArrayList();
        for (SafetyRightDo safetyRightDo : safetyRightList) {
            if (authedPermissionMap.containsKey(safetyRightDo.getCarrierGroupCode())) {
                SafetyRightDo exist = authedPermissionMap.get(safetyRightDo.getCarrierGroupCode());
                exist.setStartTime(safetyRightDo.getStartTime());
                exist.setEndTime(safetyRightDo.getEndTime());
                //同步状态根据外部的数据来
                exist.setSyncStatus(safetyRightDo.getSyncStatus());
                updateRights.add(exist);
                updateGorupCodeList.add(exist.getCarrierGroupCode());
                safetyRightDo.setId(exist.getId());
            } else {
                addRights.add(safetyRightDo);
                addGroupCodeList.add(safetyRightDo.getCarrierGroupCode());
            }
        }
        cardInfoDo.setAddRights(addRights);
        cardInfoDo.setAddGroupCodes(addGroupCodeList);
        cardInfoDo.setUpdateRights(updateRights);
        cardInfoDo.setUpdateGroupCodes(updateGorupCodeList);
    }

    @Override
    public void fillSafetyMedium(CardInfoDo cardInfoDo) {
        SafetyMediumDo safetyMediumDo = safetyMediumRepository.getByMediumCode(cardInfoDo.getMediumCode());
        cardInfoDo.setSafetyMedium(safetyMediumDo);
    }

    @Override
    public void checkCardStatusIsCanCreateReissueApply(CardInfoDo cardInfo) {
        if (ObjectUtils.isEmpty(cardInfo)
                || (!CardStatusEnum.LOSS.getCode().equals(cardInfo.getCardStatus())
                && !CardStatusEnum.USING.getCode().equals(cardInfo.getCardStatus()))) {
            throw new BizException(CardReissueApplyDomainErrorCodeEnum.CARD_STATUS_NOT_ACTIVE);
        }
    }


    @Override
    public List<CardUserRight> findOpenedRightList(CardInfoDo cardInfoDo) {
        if (StringUtils.isEmpty(cardInfoDo.getUid())) {
            return null;
        }
        return cardInfoRepository.findOpenedRightList(cardInfoDo.getUid());
    }

    @Override
    public CardInfoDo findUsingCardInfoByUidAndCardType(String uid, CardTypeEnum employeeCard) {
        return cardInfoRepository.findCardByUidAndCardType(uid, employeeCard,
                Lists.newArrayList(CardStatusEnum.USING.getCode()));
    }

    @Override
    public void checkIsRepeatOpen(CardInfoDo cardInfoDo) {
        String lockKey = String.format(SafetyConstants.Card.OPEN_CARD_LOCK, cardInfoDo.getUid(),
                cardInfoDo.getMediumPhysicsCode(), cardInfoDo.getMediumEncryptCode());
        String token = RedisUtils.tryLock(lockKey, 300);
        if (StringUtils.isEmpty(token)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.REPEAT_OPEN_CARD);
        }
    }

    @Override
    public void checkTempCardIsExist(CardInfoDo cardInfoDo) {
        CardInfoDo card = cardInfoRepository.findCardByUidAndCardType(cardInfoDo.getUid(), CardTypeEnum.TEMP_CARD, null);
        if (ObjectUtils.isNotEmpty(card)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.TEMP_CARD_EXIST);
        }
    }
}
