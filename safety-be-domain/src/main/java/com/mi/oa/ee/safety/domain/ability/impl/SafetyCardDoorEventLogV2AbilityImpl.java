package com.mi.oa.ee.safety.domain.ability.impl;

import com.mi.oa.ee.safety.common.enums.RocketMqTopicEnum;
import com.mi.oa.ee.safety.common.utils.GsonUtils;
import com.mi.oa.ee.safety.domain.ability.SafetyCardDoorEventLogV2Ability;
import com.mi.oa.ee.safety.domain.enums.CardDoorEventEnum;
import com.mi.oa.ee.safety.domain.enums.SafetyPersonTypeEnum;
import com.mi.oa.ee.safety.domain.model.SafetyCardDoorEventLogV2Do;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.infra.remote.model.event.DeviceEvent;
import com.mi.oa.ee.safety.infra.remote.model.event.PersonnelEvent;
import com.mi.oa.ee.safety.infra.remote.mq.CommonProducer;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyPersonMediumRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Service
public class SafetyCardDoorEventLogV2AbilityImpl implements SafetyCardDoorEventLogV2Ability {
    @Resource
    private CommonProducer commonProducer;
    @Resource
    private SafetyCarrierRepository safetyCarrierRepository;
    @Resource
    private SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Override
    public void notifyDoorEvent(SafetyCardDoorEventLogV2Do safetyCardDoorEventLogV2Do, SafetyCarrierDo safetyCarrierDo) {
        //数据包装
        DeviceEvent event = DeviceEvent.builder().build();
        event.setParkId(safetyCarrierDo.getParkCode());
        event.setDeviceId(safetyCarrierDo.getCarrierCode());
        event.setDeviceName(safetyCardDoorEventLogV2Do.getDeviceDescription());
        event.setEventTime(safetyCardDoorEventLogV2Do.getEventTime().toInstant().getEpochSecond());
        CardDoorEventEnum doorEvent = CardDoorEventEnum.getDoorEvent(safetyCardDoorEventLogV2Do.getEventDescription());

        if (doorEvent == null) {
            log.error("doorEvent eventDescription is null:{}", safetyCardDoorEventLogV2Do.getEventDescription());
            return;
        }

        event.setEventType(doorEvent.getType());
        event.setEventName(doorEvent.getDesc());
        //数据发布
        commonProducer.send(RocketMqTopicEnum.ACCESS_DEVICE.getTopicName(), GsonUtils.toJsonWtihNullField(event));
    }

    @Override
    public void notifyCardEvent(SafetyCardDoorEventLogV2Do safetyCardDoorEventLogV2Do, SafetyCarrierDo safetyCarrierDo) {
        PersonnelEvent event = PersonnelEvent.builder().build();

        event.setParkId(safetyCarrierDo.getParkCode());
        event.setName(safetyCardDoorEventLogV2Do.getName() + "(" + safetyCardDoorEventLogV2Do.getUsername() + ")");

        //根据加密卡号查询对应的uid
        String uid = getUidByCardNumber(safetyCardDoorEventLogV2Do.getCardNumber(), safetyCardDoorEventLogV2Do.getEventTime());

        if (uid == null) {
            return;
        }
        event.setUid(uid);

        event.setDeviceId(safetyCarrierDo.getCarrierCode());
        event.setDeviceName(safetyCardDoorEventLogV2Do.getDeviceDescription());
        event.setEventTime(safetyCardDoorEventLogV2Do.getEventTime().toInstant().getEpochSecond());
        event.setPersonType(SafetyPersonTypeEnum.STAFF.getCode());

        commonProducer.send(RocketMqTopicEnum.ACCESS_PERSONNEL.getTopicName(), GsonUtils.toJsonWtihNullField(event));

    }

    @Override
    public SafetyCarrierDo getParkIdByDeviceId(String accessPointId) {
        return safetyCarrierRepository.getParkCodeBySupplierControlSerial(accessPointId);
    }

    public String getUidByCardNumber(String cardNumber, ZonedDateTime eventTime) {
        List<SafetyPersonMediumDo> safetyPersonMediumDos = safetyPersonMediumRepository.queryListByCardCode(cardNumber);
        if (CollectionUtils.isEmpty(safetyPersonMediumDos)) {
            return null;
        }
        if (safetyPersonMediumDos.size() == 1) {
            return safetyPersonMediumDos.get(0).getUid();
        }

        for (SafetyPersonMediumDo safetyPersonMediumDo : safetyPersonMediumDos) {
            ZonedDateTime createDateTime = ZonedDateTime.parse(safetyPersonMediumDo.getCreateTime(), DateTimeFormatter.ISO_ZONED_DATE_TIME);
            ZonedDateTime dateTime = safetyPersonMediumDo.getStartTime().isAfter(createDateTime) ? safetyPersonMediumDo.getStartTime() : createDateTime;
            safetyPersonMediumDo.setStartTime(dateTime);
        }
        //根据startTime对集合safetyPersonMediumDos做正序排序
        Collections.sort(safetyPersonMediumDos, Comparator.comparing(SafetyPersonMediumDo::getStartTime));

        List<SafetyPersonMediumDo> collect = safetyPersonMediumDos.stream().filter(s -> {
            return s.getStartTime().isBefore(eventTime);
        }).collect(Collectors.toList());
        return collect.get(collect.size() - 1).getUid();
    }
}
