package com.mi.oa.ee.safety.domain.ability.impl;

import com.mi.oa.ee.safety.domain.ability.SafetyOperateLogAbility;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.SafetyOperateLogDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.SafetyOperateLogDetailDo;
import com.mi.oa.ee.safety.domain.model.SafetyOperateLogDo;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogDetailRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogRepository;
import com.mi.oa.ee.safety.infra.repository.query.SafetyOperateLogQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2023/1/12 19:36
 */
@Component
public class SafetyOperateLogAbilityImpl implements SafetyOperateLogAbility {

    @Autowired
    SafetyOperateLogRepository safetyOperateLogRepository;

    @Autowired
    SafetyOperateLogDetailRepository safetyOperateLogDetailRepository;

    @Resource
    CommonDoConverter commonDoConverter;

    @Override
    public List<SafetyOperateLogDo> getSafetyOperateLogDoListByCondition(SafetyOperateLogDo safetyOperateLogDo) {
        SafetyOperateLogQuery safetyOperateLogQuery = new SafetyOperateLogQuery();
        safetyOperateLogQuery.setRequestUrl(safetyOperateLogDo.getRequestUrl());
        safetyOperateLogQuery.setOperateStatus(safetyOperateLogDo.getOperateStatus());
        safetyOperateLogQuery.setAppCode(safetyOperateLogDo.getAppCode());
        safetyOperateLogQuery.setBizId(safetyOperateLogDo.getBizId());
        safetyOperateLogQuery.setCreateTime(safetyOperateLogDo.getCreateTime());
        safetyOperateLogQuery.setUpdateTime(safetyOperateLogDo.getUpdateTime());
        safetyOperateLogQuery.setId(safetyOperateLogDo.getId());
        safetyOperateLogQuery.setSortBy("id");
        safetyOperateLogQuery.setOrderBy("asc");
        return safetyOperateLogRepository.getListByCondition(safetyOperateLogQuery);
    }

    @Override
    public void fillSafetyOperateLogDoWithDetail(SafetyOperateLogDo safetyOperateLogDo) {
        if (safetyOperateLogDo.getId() != null) {
            SafetyOperateLogDetailDo query = new SafetyOperateLogDetailDo();
            query.setLogId(safetyOperateLogDo.getId());
            List<SafetyOperateLogDetailDo> detailDoList = safetyOperateLogDetailRepository.getListByCondition(query);
            safetyOperateLogDo.setSafetyOperateLogDetailDoList(detailDoList);
        }
    }

    @Override
    public PageModel<SafetyOperateLogDo> getListByConditionForPage(SafetyOperateLogDo safetyOperateLogDo) {
        return safetyOperateLogRepository.getListByConditionForPage(safetyOperateLogDo);
    }

    @Override
    public void fillSafetyOperateLogDoById(SafetyOperateLogDo safetyOperateLogDo) {
        if (safetyOperateLogDo.getId() != null) {
            SafetyOperateLogDo now = safetyOperateLogRepository.findById(safetyOperateLogDo.getId());
            if (now != null) {
                //填装属性
                commonDoConverter.copySafetyOperateLogDo(now, safetyOperateLogDo);
            }
        } else {
            throw new BizException(SafetyOperateLogDomainErrorCodeEnum.PARAMS_ID_NOT_HAVE);
        }
    }

    @Override
    public void fillSafetyOperateLogDoForLatestByRequestUrl(SafetyOperateLogDo safetyOperateLogDo) {
        if (StringUtils.isEmpty(safetyOperateLogDo.getRequestUrl())) {
            throw new BizException(SafetyOperateLogDomainErrorCodeEnum.REQUEST_URL_IS_EMPTY);
        }
        SafetyOperateLogQuery safetyOperateLogQuery = new SafetyOperateLogQuery();
        safetyOperateLogQuery.setRequestUrl(safetyOperateLogDo.getRequestUrl());
        safetyOperateLogQuery.setSortBy("id");
        safetyOperateLogQuery.setOrderBy("desc");
        safetyOperateLogQuery.setLimitSize(1);
        List<SafetyOperateLogDo> safetyOperateLogDoList = safetyOperateLogRepository.getListByCondition(safetyOperateLogQuery);
        if (CollectionUtils.isNotEmpty(safetyOperateLogDoList)) {
            //填装属性
            commonDoConverter.copySafetyOperateLogDo(safetyOperateLogDoList.get(0), safetyOperateLogDo);
        }
    }
}
