package com.mi.oa.ee.safety.domain.ability.impl;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.utils.DateUtils;
import com.mi.oa.ee.safety.domain.ability.SafetyCarrierGroupAbility;
import com.mi.oa.ee.safety.domain.ability.SafetyRightAbility;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.SafetyRightErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.TKSCardInfoDomainErrorEnum;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.model.TKSEmpCardInfoDo;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierGroupRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyPersonMediumRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/23 17:55
 */
@Slf4j
@Service
public class SafetyRightAbilityImpl implements SafetyRightAbility {

    @Autowired
    SafetyRightRepository safetyRightRepository;

    @Autowired
    SafetyMediumRepository safetyMediumRepository;

    @Autowired
    SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Autowired
    SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Autowired
    SafetyCarrierGroupAbility safetyCarrierGroupAbility;

    @Autowired
    CommonDoConverter commonDoConverter;

    @Resource
    private IdmRemote idmRemote;

    @Override
    public void fillSafetyRightWithUidByMediumCode(SafetyRightDo safetyRightDo) {
        //判断是否要找到当前介质对应的uid,若不存在则找到对应的uid
        if (StringUtils.isEmpty(safetyRightDo.getUid())) {
            SafetyPersonMediumDo now = safetyPersonMediumRepository.getOneByMediumOrUid(safetyRightDo.getMediumCode(), null);
            if (now != null) {
                safetyRightDo.setUid(now.getUid());
            }
        }
    }

    @Override
    public void checkSafetyRight(SafetyRightDo safetyRightDo) {
        if (safetyRightDo == null) {
            throw new BizException(SafetyRightErrorCodeEnum.IS_EMPTY);
        }
        if (StringUtils.isEmpty(safetyRightDo.getMediumCode())) {
            throw new BizException(SafetyRightErrorCodeEnum.SAFETY_MEDIUM_IS_EMPTY);
        }
        if (StringUtils.isEmpty(safetyRightDo.getCarrierGroupCode())) {
            throw new BizException(SafetyRightErrorCodeEnum.SAFETY_CARRIER_GROUP_IS_EMPTY);
        }
    }

    @Override
    public List<SafetyRightDo> getListByMediumCode(SafetyRightDo safetyRightDO) {
        return safetyRightRepository.getByMediumCodeAndCarrierGroupCodeList(safetyRightDO.getMediumCode(), null);
    }

    @Override
    public List<SafetyRightDo> fillRightByCard(CardInfoDo cardInfoDo, Boolean isHistory) {
        List<SafetyRightDo> safetyRightDOS = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(cardInfoDo) && !CollectionUtils.isEmpty(cardInfoDo.getAddGroupCodes())) {
            List<SafetyCarrierGroupDo> safetyCarrierGroupDos =
                    safetyCarrierGroupAbility.getListByCarrierGroupCodes(cardInfoDo.getAddGroupCodes());
            if (CollectionUtils.isNotEmpty(safetyCarrierGroupDos)) {
                safetyCarrierGroupDos.forEach(carrierGroupCode -> {
                    SafetyRightDo safetyRightDO = new SafetyRightDo();
                    safetyRightDO.setUid(cardInfoDo.getUid());
                    safetyRightDO.setStartTime(cardInfoDo.getStartTime());
                    safetyRightDO.setEndTime(cardInfoDo.getEndTime());
                    safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
                    if (isHistory) {
                        safetyRightDO.setSyncStatus(200);
                    } else {
                        safetyRightDO.setSyncStatus(OAUCFCommonConstants.INT_ZERO);
                    }
                    safetyRightDO.setCarrierGroupCode(carrierGroupCode.getCarrierGroupCode());
                    safetyRightDO.setSupplierCheckCode(cardInfoDo.getMediumPhysicsCode());
                    safetyRightDO.setSupplierAccessCode(carrierGroupCode.getSupplierAccessCode());
                    safetyRightDO.setSupplierCode(carrierGroupCode.getSupplierCode());
                    safetyRightDO.setOperateTime(ZonedDateTime.now());
                    safetyRightDOS.add(safetyRightDO);
                });
            }
        }
        return safetyRightDOS;
    }

    @Override
    public void fillSafetyMediumAndSafetyCarrierGroupDo(SafetyRightDo safetyRightDo) {
        //填装安防介质
        if (StringUtils.isNotEmpty(safetyRightDo.getMediumCode())) {
            SafetyMediumDo safetyMediumDO = safetyMediumRepository.getByMediumCode(safetyRightDo.getMediumCode());
            if (safetyMediumDO != null) {
                safetyRightDo.setSafetyMediumDo(safetyMediumDO);
            }
        }
        //填装安防载体集
        if (StringUtils.isNotEmpty(safetyRightDo.getCarrierGroupCode())) {
            SafetyCarrierGroupDo safetyCarrierGroupDo = safetyCarrierGroupRepository.getCarrierGroup(safetyRightDo.getCarrierGroupCode());
            safetyRightDo.setSafetyCarrierGroup(safetyCarrierGroupDo);
        }
    }

    @Override
    public List<SafetyRightDo> queryListByCarrierGroupCode(SafetyRightDo safetyRightDo) {
        return safetyRightRepository.findListByCarrierGroupCode(safetyRightDo);
    }

    @Override
    public void filterAuthedPermission(List<SafetyRightDo> needAddsafetyRightList, List<SafetyRightDo> exitsafetyRightList) {
        List<String> authedPermission = exitsafetyRightList.stream()
                .map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(authedPermission)) {
            return;
        }
        //safetyRightList中，在authPermission中存在的权限
        needAddsafetyRightList.removeIf(safetyRightDo -> authedPermission.contains(safetyRightDo.getCarrierGroupCode()));
    }

    @Override
    public List<SafetyRightDo> findSafetyRightByMediumCode(String mediumCode) {
        if (StringUtils.isEmpty(mediumCode)) {
            throw new BizException(SafetyRightErrorCodeEnum.SAFETY_MEDIUM_IS_EMPTY);
        }
        return safetyRightRepository.findSafetyRightByMediumCodeAndUid(mediumCode, null, null);
    }

    @Override
    public List<SafetyRightDo> getSafetyRightByMediumCodeAndUid(SafetyRightDo safetyRightDo) {
        return safetyRightRepository.findSafetyRightByMediumCodeAndUid(safetyRightDo.getMediumCode(), safetyRightDo.getUid(), null);
    }

    @Override
    public void fillSafetyRightWithSafetyCarrierGroupForList(List<SafetyRightDo> safetyRightList) {
        if (CollectionUtils.isEmpty(safetyRightList)) {
            return;
        }
        List<String> carrierGroupCodeList = safetyRightList.stream()
                .map(SafetyRightDo::getCarrierGroupCode).distinct().collect(Collectors.toList());
        List<SafetyCarrierGroupDo> carrierGroupList = safetyCarrierGroupAbility.getListByCarrierGroupCodes(carrierGroupCodeList);
        if (CollectionUtils.isEmpty(carrierGroupList)) {
            return;
        }
        safetyCarrierGroupAbility.fillCarrierGroup(carrierGroupList);
        Map<String, SafetyCarrierGroupDo> carrierGroupDoMap = carrierGroupList.stream()
                .collect(Collectors.toMap(SafetyCarrierGroupDo::getCarrierGroupCode, Function.identity(), (k1, k2) -> k1));
        safetyRightList.forEach(safetyRightDo -> {
            SafetyCarrierGroupDo safetyCarrierGroupDo = carrierGroupDoMap.get(safetyRightDo.getCarrierGroupCode());
            safetyRightDo.setSafetyCarrierGroup(safetyCarrierGroupDo);
        });
    }

    @Override
    public void cancelSafetyRightSync(SafetyRightDo safetyRightDO) {
        safetyRightRepository.cancelSafetyRightSync(safetyRightDO);
    }

    @Override
    public void removeSafetyRight(SafetyRightDo safetyRight) {
        safetyRightRepository.cancelSafetyRightSync(safetyRight);
        safetyRightRepository.removeSafetyRight(safetyRight);
    }

    @Override
    public List<SafetyRightDo> findListByMediumCodeAndGroupCodeList(String mediumCode, List<String> needSyncCarrierGroupCodeList) {
        return safetyRightRepository.getByMediumCodeAndCarrierGroupCodeList(mediumCode,
                needSyncCarrierGroupCodeList);
    }

    @Override
    public List<SafetyRightDo> findSafetyRightByMediumCodeInReopenCard(String mediumCode) {
        if (StringUtils.isEmpty(mediumCode)) {
            throw new BizException(SafetyRightErrorCodeEnum.SAFETY_MEDIUM_IS_EMPTY);
        }
        List<SafetyRightDo> safetyRightByMediumCodeAndUid = safetyRightRepository.findSafetyRightByMediumCodeAndUid(mediumCode, null, null);
        safetyRightByMediumCodeAndUid.stream().forEach(safetyRightDo -> {
            fillSafetyMediumAndSafetyCarrierGroupDo(safetyRightDo);
        });
        return safetyRightByMediumCodeAndUid;
    }

    @Override
    public List<SafetyRightDo> fillSafetyRight(TKSEmpCardInfoDo tksEmpCardInfoDo) {
        List<SafetyCarrierGroupDo> carrierGroupList = safetyCarrierGroupAbility.getListByCarrierGroupCodes(tksEmpCardInfoDo.getRuleGroupIds());
        if (CollectionUtils.isEmpty(carrierGroupList)) {
            return Collections.emptyList();
        }
        List<SafetyRightDo> safetyRightDoList = new ArrayList<>();

        for (SafetyCarrierGroupDo safetyCarrierGroupDo : carrierGroupList) {
            SafetyRightDo safetyRightDo = new SafetyRightDo();

            safetyRightDo.setMediumCode(tksEmpCardInfoDo.getMediumCode());
            safetyRightDo.setStartTime(DateUtils.getCurrentZonedDateTime());
            safetyRightDo.setEndTime(tksEmpCardInfoDo.getExpireDate());
            safetyRightDo.setCarrierGroupCode(safetyCarrierGroupDo.getCarrierGroupCode());
            safetyRightDo.setUid(tksEmpCardInfoDo.getUid());
            safetyRightDo.setSupplierCheckCode(tksEmpCardInfoDo.getMediumPhysicsCode());
            safetyRightDo.setSupplierAccessCode(safetyCarrierGroupDo.getSupplierAccessCode());
            safetyRightDo.setSupplierCode(safetyCarrierGroupDo.getSupplierCode());
            safetyRightDo.setSafetyCarrierGroup(safetyCarrierGroupDo);
            safetyRightDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
            safetyRightDo.setTenantCode(SafetyConstants.KINGSOFT_TENANT_CODE);

            safetyRightDoList.add(safetyRightDo);
        }

        return safetyRightDoList;
    }

    @Override
    public void deleteSafetyRight(String mediumCode) {
        if (StringUtils.isBlank(mediumCode)) {
            throw new BizException(TKSCardInfoDomainErrorEnum.MEDIUM_IS_EMPTY);
        }

        safetyRightRepository.deleteSafetyRightByMediumCode(mediumCode);
    }
}
