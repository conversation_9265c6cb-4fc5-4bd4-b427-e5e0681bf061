package com.mi.oa.ee.safety.domain.ability.impl;

import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.dto.AddressInfoDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceBuildingDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceFloorDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.domain.ability.SafetySpaceAbility;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.converter.SafetySpaceDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.SafetySpaceDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.AddressInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetySpaceBuildingDo;
import com.mi.oa.ee.safety.domain.model.SafetySpaceDo;
import com.mi.oa.ee.safety.domain.model.SafetySpaceFloorDo;
import com.mi.oa.ee.safety.domain.model.SafetySpaceParkDo;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/5/31 20:05
 */
@Slf4j
@Service
public class SafetySpaceAbilityImpl implements SafetySpaceAbility {

    @Autowired
    SpaceSdk spaceSdk;

    @Autowired
    AddressSdk addressSdk;

    @Autowired
    SafetySpaceDoConverter spaceConverter;

    @Autowired
    CommonDoConverter commonDoConverter;

    @Autowired
    Cache<String, Object> spaceLocalObjectCache;

    @Override
    public void fillAddressInfoByOldAddressName(SafetySpaceDo safetySpaceDo) {
        if (StringUtils.isEmpty(safetySpaceDo.getAddrName())) {
            throw new BizException(SafetySpaceDomainErrorCodeEnum.ADDRESS_NAME_IS_EMPTY);
        }
        String destination = safetySpaceDo.getAddrName();
        String addressName = safetySpaceDo.getAddrName();
        String countryId = StringUtils.isEmpty(safetySpaceDo.getCountryId()) ? AddressSdk.CHINA : safetySpaceDo.getCountryId();
        //如果是中国保留现有逻辑，如果是国外
        if (StringUtils.equals(countryId, AddressSdk.CHINA)) {
            if (destination.contains("香港")) {
                addressName = "香港";
                countryId = "3385";
            } else if (addressName.contains("台北")) {
                addressName = "台北市";
                countryId = "3386";
            } else {
                addressName = destination + "市";
            }
            //获取当前对应的城市信息
            AddressInfoDto addressInfoDto = addressSdk.getAddressByName(addressName, countryId);
            //地址不存在是直接返回
            if (Objects.isNull(addressInfoDto)) {
                return;
            }
            addressInfoDto.setCountryId(countryId);
            commonDoConverter.copyAddressInfoDto(addressInfoDto, safetySpaceDo);
        }
    }

    public void fillSpaceInfoByAddressId(SafetySpaceDo safetySpaceDo) {
        String code = safetySpaceDo.getAddrId();
        if (StringUtils.isEmpty(code)) {
            return;
        }
        AddressInfoDto now = null;
        try {
            String key = "address" + code;
            now = (AddressInfoDto) spaceLocalObjectCache.get(key,
                    () -> getSafetySpaceAddress(Integer.valueOf(code)));
            commonDoConverter.copyAddressInfoDto(now, safetySpaceDo);
        } catch (Exception e) {
            log.error("----- fillPersonInfoByUid error", e);
        }
    }

    @Override
    public void fillSpaceInfoByCityId(SafetySpaceDo safetySpaceDo) {
        String code = safetySpaceDo.getAddrId();
    }

    @Override
    public void fillSpaceInfoByParkCode(SafetySpaceParkDo safetySpaceParkDo) {
        String code = safetySpaceParkDo.getParkCode();
        if (StringUtils.isEmpty(code)) {
            return;
        }
        SafetySpaceParkDo now = null;
        try {
            now = (SafetySpaceParkDo) spaceLocalObjectCache.get(code,
                    () -> getSafetySpaceParkDo(code));
            commonDoConverter.copySafetySpaceParkDo(now, safetySpaceParkDo);
        } catch (Exception e) {
            log.error("----- fillPersonInfoByUid error", e);
        }
    }

    @Override
    public void fillSpaceInfoByBuildingCode(SafetySpaceBuildingDo safetySpaceBuildingDo) {
        String code = safetySpaceBuildingDo.getBuildingCode();
        if (StringUtils.isEmpty(code)) {
            return;
        }
        SafetySpaceBuildingDo now = null;
        try {
            now = (SafetySpaceBuildingDo) spaceLocalObjectCache.get(code,
                    () -> getSafetySpaceParkDo(code));
            commonDoConverter.copySafetySpaceBuildingDo(now, safetySpaceBuildingDo);
        } catch (Exception e) {
            log.error("----- fillPersonInfoByUid error", e);
        }
    }

    @Override
    public void fillSpaceInfoByFloorCode(SafetySpaceFloorDo safetySpaceFloorDo) {
        String code = safetySpaceFloorDo.getFloorCode();
        if (StringUtils.isEmpty(code)) {
            return;
        }
        SafetySpaceFloorDo now = null;
        try {
            now = (SafetySpaceFloorDo) spaceLocalObjectCache.get(code,
                    () -> getSafetySpaceParkDo(code));
            commonDoConverter.copySafetySpaceFloorDo(now, safetySpaceFloorDo);
        } catch (Exception e) {
            log.error("----- getSpaceInfoByFloorCode error", e);
        }
    }

    private Object getSafetySpaceAddress(Integer addressId) {
        return addressSdk.getAddressById(addressId);
    }

    private Object getSafetySpaceParkDo(String parkCode) {
        List<SafetySpaceParkDto> parkBuildingFloorTree = spaceSdk.getAllParkBuildingFloorTree();
        if (CollectionUtils.isEmpty(parkBuildingFloorTree)) {
            return null;
        }
        for (SafetySpaceParkDto parkDto : parkBuildingFloorTree) {
            spaceLocalObjectCache.put(parkDto.getParkCode(), spaceConverter.toDo(parkDto));
            if (CollectionUtils.isNotEmpty(parkDto.getBuildingList())) {
                for (SafetySpaceBuildingDto buildingDto : parkDto.getBuildingList()) {
                    spaceLocalObjectCache.put(buildingDto.getBuildingCode(), spaceConverter.toDo(buildingDto));
                    if (CollectionUtils.isNotEmpty(buildingDto.getFloorList())) {
                        for (SafetySpaceFloorDto floorDto : buildingDto.getFloorList()) {
                            spaceLocalObjectCache.put(floorDto.getFloorCode(), spaceConverter.toDo(floorDto));
                        }
                    }
                }
            }
        }
        return spaceLocalObjectCache.getIfPresent(parkCode);
    }

    @Override
    public PageModel<SafetySpaceParkDo> pageAllParkBuildingFloorTree(SafetySpaceParkDo spaceParkDo) {
        Integer pageNum = (Integer) spaceParkDo.getExtField("pageNum");
        Integer pageSize = (Integer) spaceParkDo.getExtField("pageSize");
        List<SafetySpaceParkDto> spaceParkDtos = spaceSdk.getAllParkBuildingFloorTree();
        if (CollectionUtils.isEmpty(spaceParkDtos)) {
            return null;
        }
        int totalCount = spaceParkDtos.size();
        List<SafetySpaceParkDo> safetySpaceParkDos = spaceConverter.toDoList(spaceParkDtos);
        int offset = (pageNum - 1) * pageSize;
        if (offset >= totalCount) {
            return PageModel.build(safetySpaceParkDos, pageSize, pageNum, totalCount);
        }
        int toIndex = Math.min(offset + pageSize, totalCount);
        return PageModel.build(safetySpaceParkDos.subList(offset, toIndex), pageSize, pageNum, totalCount);
    }

    @Override
    public List<SafetySpaceParkDo> getParkCodesByFuzzyName(SafetySpaceParkDo spaceParkDo) {
        return spaceConverter.toDoList(spaceSdk.getParkCodesByFuzzyName(spaceParkDo.getParkName()));
    }

    @Override
    public List<AddressInfoDo> findAddressListByIdList(List<String> idList) {
        if (CollectionUtils.isNotEmpty(idList)) {
            List<Integer> idListInt = idList.stream().map(Integer::valueOf).collect(Collectors.toList());
            List<AddressInfoDto> addressInfoDtoList = addressSdk.getAddressById(idListInt);
            return spaceConverter.toAddressDoList(addressInfoDtoList);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<SafetySpaceParkDo> findAllParkList() {
        List<SafetySpaceParkDto> listParks = spaceSdk.getListParks();
        return spaceConverter.toDoList(listParks);
    }

}
