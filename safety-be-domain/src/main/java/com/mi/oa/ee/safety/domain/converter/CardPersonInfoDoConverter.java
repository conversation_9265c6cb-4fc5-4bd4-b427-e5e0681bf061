package com.mi.oa.ee.safety.domain.converter;

import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPersonInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * <AUTHOR>
 * @date 2024/6/11 17:34
 */
@Mapper(componentModel = "spring")
public interface CardPersonInfoDoConverter {

    @Mapping(target = "employeeNo", source = "employeeId")
    @Mapping(target = "userName", source = "accountName")
    @Mapping(target = "responsible", source = "managerUid")
    @Mapping(target = "pinyinFullName", expression = "java(toPinyinFullName(personInfoModel.getLastNameEn(), personInfoModel.getFirstNameEn()))")
    CardPersonInfoDo toCardPersonInfoDo(PersonInfoModel personInfoModel);

    @Mapping(target = "employeeNo", source = "employeeId")
    @Mapping(target = "pinyinFullName", expression = "java(toPinyinFullName(safetyPersonDo.getLastNameEn(), safetyPersonDo.getFirstNameEn()))")
    CardPersonInfoDo toCardPersonInfoDo(SafetyPersonDo safetyPersonDo);

    default CardPersonInfoDo toCardPersonInfoDo(CardApplyDo cardApplyDo) {
        if (cardApplyDo == null) {
            return null;
        }
        CardPersonInfoDo cardPersonInfoDo;
        if (cardApplyDo.getPersonInfo() != null) {
            cardPersonInfoDo = toCardPersonInfoDo(cardApplyDo.getPersonInfo());
            cardPersonInfoDo.setPhotoUrl(cardApplyDo.getPhotoUrl());
        } else {
            cardPersonInfoDo = new CardPersonInfoDo();
            cardPersonInfoDo.setUid(cardApplyDo.getUid());
            cardPersonInfoDo.setDisplayName(cardApplyDo.getDisplayName());
            cardPersonInfoDo.setPinyinFullName(toPinyinFullName(cardApplyDo.getSurname(), cardApplyDo.getPinyinName()));
            cardPersonInfoDo.setUserName(cardApplyDo.getPartnerAccount());
            cardPersonInfoDo.setEmployeeNo(cardApplyDo.getEmpNo());
            cardPersonInfoDo.setCompanyName(cardApplyDo.getCompanyName());
            cardPersonInfoDo.setPhotoUrl(cardApplyDo.getPhotoUrl());
        }
        return cardPersonInfoDo;
    }

    ;


    @Named("toPinyinFullName")
    default String toPinyinFullName(String surname, String pinyinName) {
        return StringUtils.defaultString(surname, "") + StringUtils.defaultString(pinyinName, "");
    }

}
