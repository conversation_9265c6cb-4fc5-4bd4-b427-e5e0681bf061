package com.mi.oa.ee.safety.domain.service.impl;

import com.mi.info.comb.neptune.client.LanguageTypeThreadLocal;
import com.mi.oa.ee.safety.common.enums.ImportTypeEnum;
import com.mi.oa.ee.safety.domain.model.AsyncImportTaskDo;
import com.mi.oa.ee.safety.domain.model.ImportHistoryQueryDo;
import com.mi.oa.ee.safety.domain.service.AsyncImportTaskDomainService;
import com.mi.oa.ee.safety.infra.repository.AsyncImportTaskRepository;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class AsyncImportTaskDomainServiceImpl implements AsyncImportTaskDomainService {

    @Resource
    private AsyncImportTaskRepository asyncImportTaskRepository;

    @Override
    public void save(AsyncImportTaskDo asyncImportTaskDo) {
        asyncImportTaskDo.setLocale(LanguageTypeThreadLocal.getLanguageType().getLocale().toString());
        asyncImportTaskRepository.save(asyncImportTaskDo);
    }

    @Override
    public AsyncImportTaskDo findById(Long id) {
        return asyncImportTaskRepository.findById(id);
    }

    @Override
    public void updateStatusByBatchId(AsyncImportTaskDo asyncImportTaskDo) {
        asyncImportTaskRepository.updateStatusByBatchId(asyncImportTaskDo);
    }

    @Override
    public AsyncImportTaskDo findByBatchId(String batchId) {
        return asyncImportTaskRepository.findByBatchId(batchId);
    }

    @Override
    public PageVO<AsyncImportTaskDo> page(ImportHistoryQueryDo importHistoryQueryDo) {
        return asyncImportTaskRepository.page(importHistoryQueryDo);
    }
}

