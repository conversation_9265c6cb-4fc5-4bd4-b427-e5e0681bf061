package com.mi.oa.ee.safety.domain.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.common.dto.*;
import com.mi.oa.ee.safety.common.enums.AccountTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardApplyOperateTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardReissueApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyConfigUserTypeEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.JsonUtils;
import com.mi.oa.ee.safety.common.utils.PageUtils;
import com.mi.oa.ee.safety.domain.ability.*;
import com.mi.oa.ee.safety.domain.converter.CardPersonInfoDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.query.card.CardApplyQuery;
import com.mi.oa.ee.safety.domain.service.CardApplyDomainService;
import com.mi.oa.ee.safety.domain.service.CardPersonInfoDomainService;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.UcSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.UmsSdk;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.enums.UserIdTypeEnum;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.message.MsgUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/21 19:42
 */
@Slf4j
@Service
public class CardApplyDomainServiceImpl implements CardApplyDomainService {

    @Autowired
    CardApplyAbility cardApplyAbility;

    @Autowired
    CardInfoAbility cardAbility;

    @Autowired
    CardApplyRepository cardApplyRepository;

    @Autowired
    SafetyCarrierGroupAbility safetyCarrierGroupAbility;

    @Autowired
    UmsSdk umsSdk;

    @Autowired
    SpaceSdk spaceSdk;

    @Autowired
    IdmSdk idmSdk;

    @Autowired
    IdmRemote idmRemote;

    @Autowired
    UcSdk ucSdk;

    @Autowired
    SafetySpaceAbility spaceAbility;

    @Autowired
    SafetyPersonAbility safetyPersonAbility;

    @Resource
    private CardReissueApplyAbility cardReissueApplyAbility;
    @Autowired
    private CardPersonInfoAbility cardPersonInfoAbility;
    @Autowired
    private CardPersonInfoDomainService cardPersonInfoDomainService;
    @Resource
    private CardPersonInfoDoConverter cardPersonInfoDoConverter;

    @Value("${ums.bot-biz-id}")
    private String botBizId;

    @Value("${ums.template-id}")
    private String templateId;

    @Resource
    private SafetySpaceAbility safetySpaceAbility;

    @Override
    public void fillBaseInfoByUidAndApplyType(CardApplyDo cardApplyDo) {
        //填装当前申请单信息
        cardApplyAbility.fillBaseInfoByUidAndApplyType(cardApplyDo);
    }

    @Override
    public void fillBeforeCreate(CardApplyDo cardApplyDo) {

        //填装当前人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardApplyDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        cardApplyDo.setPersonInfo(safetyPersonDo);

        //根据人员信息组装申请单信息
        cardApplyAbility.buildBaseInfoByPerson(cardApplyDo);

        //填装老申请单的ID
        CardApplyDo oldCardApplyDo = new CardApplyDo();
        oldCardApplyDo.setUid(cardApplyDo.getUid());
        oldCardApplyDo.setApplyType(cardApplyDo.getApplyType());
        cardApplyAbility.fillBaseInfoByUidAndApplyType(oldCardApplyDo);
        cardApplyDo.setId(oldCardApplyDo.getId());
        cardApplyDo.setApplyStatus(oldCardApplyDo.getApplyStatus());

        //填装对应的工卡信息
        CardInfoDo cardInfoDo = cardAbility.findCardInfoByApplyId(cardApplyDo.getId());
        //填装时间段
        if (ObjectUtils.isEmpty(cardInfoDo)) {
            cardInfoDo = new CardInfoDo();
            cardInfoDo.setUid(cardApplyDo.getUid());
            cardInfoDo.setCardApplyId(oldCardApplyDo.getId());
        }
        cardInfoDo.setStartTime(cardApplyDo.getStartTime());
        cardInfoDo.setEndTime(cardApplyDo.getEndTime());
        cardAbility.buildValidateTime(cardInfoDo);
        cardApplyDo.setCardInfo(cardInfoDo);

        //填充园区编码
        if (StringUtils.isEmpty(cardApplyDo.getParkCode()) && StringUtils.isNotEmpty(cardApplyDo.getParkName())) {
            SafetySpaceParkDo safetySpaceDo = new SafetySpaceParkDo();
            safetySpaceDo.setParkName(cardApplyDo.getParkName());
            List<SafetySpaceParkDo> parkCodesByFuzzyName = safetySpaceAbility.getParkCodesByFuzzyName(safetySpaceDo);
            if (!CollectionUtils.isEmpty(parkCodesByFuzzyName)) {
                cardApplyDo.setParkCode(parkCodesByFuzzyName.get(0).getParkCode());
            }
        }
    }

    @Override
    public void checkBeforeCreateForCoopCard(CardApplyDo cardApplyDo) {

        //检查申请单是否已存在
        cardApplyAbility.checkCardApplyIsRepeat(cardApplyDo);

        //1.检查当前申请单的数据是否完备
        cardApplyAbility.checkCompleted(cardApplyDo);

        //检查是否内部人员
        safetyPersonAbility.checkPersonIsInnerStaff(cardApplyDo.getPersonInfo());

        //检查人员状态是否预入职或关闭
        safetyPersonAbility.checkPersonIsPreEntryAndForbidden(cardApplyDo.getPersonInfo());

    }

    @Override
    public void checkBeforeCreateForOnSite(CardApplyDo cardApplyDo) {
        //检查是否完整
        cardApplyAbility.checkCompleted(cardApplyDo);
        //人员为空时不检查
        if (cardApplyDo.getPersonInfo() == null) {
            return;
        }
        //检查人员是否存在
        safetyPersonAbility.checkIsExist(cardApplyDo.getPersonInfo());
        //检查是否内部人员
        safetyPersonAbility.checkPersonIsInnerStaff(cardApplyDo.getPersonInfo());
        //已关闭合作伙伴 uid为空 不影响重新创建
        cardApplyDo.setUid(cardApplyDo.getPersonInfo().getUid());
        cardApplyDo.setPersonInfo(cardApplyDo.getPersonInfo());
    }


    @Override
    public void checkCanSupplierUploadPhoto(CardApplyDo cardApplyDo) {
        //检查此图片是否可上传
        cardApplyAbility.checkCanSupplierUploadPhoto(cardApplyDo);
    }

    @Override
    public Boolean judgeIsExist(CardApplyDo cardApplyDo) {
        if (StringUtils.isEmpty(cardApplyDo.getUid())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.UID_EMPTY_ERROR);
        }
        try {
            cardApplyAbility.checkIsExist(cardApplyDo, false);
            return false;
        } catch (BizException e) {
            return true;
        }
    }

    @Override
    public CardApplyDo getDetailCardApply(Long id) {
        CardApplyDo cardApplyDo = cardApplyAbility.getCardApplyById(id);
        Asserts.assertNotNull(cardApplyDo, CardApplyDomainErrorCodeEnum.CARD_APPLY_NOT_EXIST);
        CardInfoDo cardInfoDo = cardAbility.findCardInfoByApplyId(cardApplyDo.getId());
        cardApplyDo.setCardInfo(cardInfoDo);

        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardApplyDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        cardApplyDo.setPersonInfo(safetyPersonDo);

        cardApplyAbility.fillFullCardApply(cardApplyDo);
        cardApplyAbility.fillParkInfo(cardApplyDo);
        return cardApplyDo;
    }

    @Override
    public Long save(CardApplyDo cardApplyDo, Boolean isOnSite) {
        cardApplyDo.setEmpType(AccountTypeEnum.PARTNER.getValue());
        //非驻场 申请单不存开始和结束时间 显示从idm查
        cardApplyDo.setStartTime(null);
        cardApplyDo.setEndTime(null);
        cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_PRINT_CARD.getCode());
        return cardApplyRepository.save(cardApplyDo);
    }

    @Override
    public void updateWithCardTimeById(CardApplyDo cardApplyDo) {
        cardApplyAbility.updateWithCardTimeById(cardApplyDo);
    }

    @Override
    public void updateCardApply(CardApplyDo cardApplyDo) {
        cardApplyAbility.checkOnEditCardApply(cardApplyDo);
        CardReissueApplyDo cardReissueApplyDo = new CardReissueApplyDo();
        cardReissueApplyDo.setCardApplyId(cardApplyDo.getId());
        cardReissueApplyDo.setReissueStatus(CardReissueApplyStatusEnum.TO_BE_RECEIVE.getCode());
        cardReissueApplyAbility.fillCardReissueApplyByCardApplyIdAndStatus(cardReissueApplyDo);
        //不存在待领取补卡单 更新制卡单
        if (ObjectUtils.isEmpty(cardReissueApplyDo.getId())) {
            cardApplyAbility.updateCardApply(cardApplyDo);
        }
    }

    @Override
    public PageModel<CardApplyDo> pageConditionApplyList(CardApplyDo cardApplyDo, Long pageNum, Long pageSize, boolean flag) {
        List<String> parkCodes = cardApplyAbility.getParkCodesByUc(flag);
        Map<String, String> photoSupplierInfo = cardApplyAbility.checkUcControl();

        boolean isPhotoSupplier = Boolean.parseBoolean(photoSupplierInfo.get("hasUcControl"));
        if (isPhotoSupplier) {
            String resourceCodesJson = photoSupplierInfo.get("resourceCodes");
            // 从 JSON 字符串中解析出资源代码列表
            List<String> resourceCodes = JsonUtils.toBean(resourceCodesJson, List.class);
            // 映射每个资源代码到对应的状态代码，过滤掉空值
            List<Integer> statusList = resourceCodes.stream()
                    .map(CardApplyStatusEnum::getCodeByName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            cardApplyDo.putExtField("statusList", statusList);
        }
        return cardApplyAbility.pageConditionApplyList(cardApplyDo, pageNum, pageSize, parkCodes);
    }

    @Override
    public PageModel<CardApplyDo> pageConditionApplyList(CardApplyQuery cardApplyQuery, boolean flag) {
        //设置权限
        List<String> parkCodes = cardApplyAbility.getParkCodesByUc(flag);
        Map<String, String> photoSupplierInfo = cardApplyAbility.checkUcControl();
        cardApplyQuery.setParkCodeList(parkCodes);

        boolean isPhotoSupplier = Boolean.parseBoolean(photoSupplierInfo.get("hasUcControl"));
        if (isPhotoSupplier) {
            String resourceCodesJson = photoSupplierInfo.get("resourceCodes");
            // 从 JSON 字符串中解析出资源代码列表
            List<String> resourceCodes = JsonUtils.toBean(resourceCodesJson, List.class);
            // 映射每个资源代码到对应的状态代码，过滤掉空值
            List<Integer> statusList = resourceCodes.stream()
                    .map(CardApplyStatusEnum::getCodeByName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            cardApplyQuery.setStatusList(statusList);
        }
        //物理卡号在cardInfo
        if (StringUtils.isNotEmpty(cardApplyQuery.getMediumPhysicsCode())) {
            CardInfoDo cardInfoDo = cardAbility.getListCardInfoByPhysicCode(cardApplyQuery.getMediumPhysicsCode());
            //物理卡号不存在直接返回
            if (Objects.isNull(cardInfoDo)) {
                return PageUtils.emptyPage(cardApplyQuery.getPageSize(), cardApplyQuery.getPageNum());
            }
            cardApplyQuery.setId(cardInfoDo.getCardApplyId());
        }
        return cardApplyAbility.pageConditionApplyList(cardApplyQuery);
    }

    @Override
    public void fillFullCardApply(CardApplyDo applyDo) {
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(applyDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        applyDo.setPersonInfo(safetyPersonDo);
        cardApplyAbility.fillFullCardApply(applyDo);
        cardApplyAbility.fillParkInfo(applyDo);
    }

    @Override
    public void fillFullCardApplyV2(CardApplyDo applyDo) {
        cardApplyAbility.fillParkInfo(applyDo);
        //构造安防人员//补齐安防人员相关信息
        safetyPersonAbility.fillPersonInfoByUid(applyDo.getPersonInfo());
    }

    @Override
    public void fillFullCardApplyV2(List<CardApplyDo> applyDos) {
        cardApplyAbility.fillParkInfoBatch(applyDos);
        //构造安防人员
        List<SafetyPersonDo> safetyPersonDoList = Lists.newArrayListWithExpectedSize(applyDos.size());
        applyDos.forEach(item -> safetyPersonDoList.add(item.getPersonInfo()));
        //补齐安防人员相关信息
        safetyPersonAbility.fillPersonInfoAndOrgInfoByUidBatch(safetyPersonDoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportErrorDto> batchSave(List<CardApplyDo> cardApplyDos) {
        List<ImportErrorDto> errors = Lists.newArrayList();
        //1.填充
        cardApplyAbility.fillBatchImport(cardApplyDos, false);
        //2.保存
        cardApplyDos.stream().parallel().forEach(cardApplyDo -> {
            ImportErrorDto importErrorDto = new ImportErrorDto();
            Map<Integer, String> errorMessages = Maps.newHashMap();
            StringBuilder message = new StringBuilder();
            SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
            safetyPersonDo.setMobile(cardApplyDo.getPhone());
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonAbility.fillPersonInfoAndOrgInfoByPhoneAndOrgCode(safetyPersonDo);
            //idm不存在才会创建成功
            if (StringUtils.isEmpty(safetyPersonDo.getUid())) {
                //1.idm创建用户
                try {
                    //用户的姓名不嫩超长
                    insert(cardApplyDo);
                } catch (BizException e) {
                    importErrorDto.setRows(cardApplyDo.getExcelRows());
                    message.append("创建人员失败").append(e.getMessage());
                    errorMessages.put(cardApplyDo.getExcelRows(), message.toString());
                }
            } else {
                importErrorDto.setRows(cardApplyDo.getExcelRows());
                message.append(cardApplyDo.getPhone()).append("{{{手机号}}}").append(cardApplyDo.getDisplayName()).append("用户").append("已存在，请使用新建卡创建制卡订单");
                errorMessages.put(cardApplyDo.getExcelRows(), message.toString());
            }
            //行号有值 说明导入有错误
            if (!ObjectUtils.isEmpty(importErrorDto.getRows())) {
                importErrorDto.setErrorMessages(errorMessages);
                errors.add(importErrorDto);
            }
        });
        return errors;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOne(CardApplyDo cardApplyDo) {
        StringBuilder message = new StringBuilder();
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setMobile(cardApplyDo.getPhone());
        safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
        safetyPersonAbility.fillPersonInfoAndOrgInfoByPhoneAndOrgCode(safetyPersonDo);
        //idm不存在才会创建成功
        if (StringUtils.isEmpty(safetyPersonDo.getUid())) {
            //1.idm创建用户
            try {
                insert(cardApplyDo);
            } catch (BizException e) {
                message.append("创建人员失败").append(e.getMessage());
            }
        } else {
            message.append("{{{手机号}}}").append(cardApplyDo.getPhone()).append(cardApplyDo.getDisplayName()).append("用户").append("已存在，请使用新建卡创建制卡订单");
        }
        return message.toString();
    }

    @Override
    public void fillCardApplyByUidAndTypes(CardApplyDo cardApplyDo) {
        cardApplyAbility.fillCardApplyByUidAndTypes(cardApplyDo);
    }

    @Override
    public void checkBeforeSaveCoopOrPropertyCard(CardApplyDo cardApplyDo) {
        // 创建物业卡的时候要校验是否已有合作卡制卡记录，创建合作卡要校验物业卡记录
        // 根据uid和type去cardApply中去找记录 合作卡申请 需要校验物业卡记录
        if (CardTypeEnum.COOPERATION_CARD.getNumber().equals(cardApplyDo.getApplyType())) {
            CardApplyDo cardApplyByUidAndCardType = cardApplyAbility.findCardByUidAndCardType(cardApplyDo.getUid(), CardTypeEnum.PROPERTY_CARD_APPLY);
            if (cardApplyByUidAndCardType != null) {
                throw new BizException(CardApplyDomainErrorCodeEnum.PROPERTY_CARD_EXIST);
            }
        } else if (CardTypeEnum.PROPERTY_CARD_APPLY.getNumber().equals(cardApplyDo.getApplyType())) {
            CardApplyDo cardApplyByUidAndCardType = cardApplyAbility.findCardByUidAndCardType(cardApplyDo.getUid(), CardTypeEnum.COOPERATION_CARD);
            if (cardApplyByUidAndCardType != null) {
                throw new BizException(CardApplyDomainErrorCodeEnum.COOPERATION_CARD_EXIST);
            }
        }
    }

    @Override
    public List<CardApplyDo> getPhotoByPhoneLastFour(String phoneLastFour) {
        return cardApplyAbility.getPhotoByPhoneLastFour(phoneLastFour);
    }

    @Override
    public void batchFillCardApplyConfigDo(List<CardApplyConfigDo> list) {
        //填充地区信息
        //国家批量地址信息
        List<String> countryIdList = list.stream()
                .map(CardApplyConfigDo::getCountryId)
                .distinct().collect(Collectors.toList());
        List<AddressInfoDo> countryList = safetySpaceAbility.findAddressListByIdList(countryIdList);
        Map<String, AddressInfoDo> countryMap = countryList.stream().collect(Collectors.toMap(AddressInfoDo::getAddrId, Function.identity(),
                (key1, key2) -> key1));

        //省份批量地址信息
        List<String> provinceIdList = list.stream()
                .map(CardApplyConfigDo::getProvinceId)
                .distinct().collect(Collectors.toList());
        List<AddressInfoDo> provinceList = safetySpaceAbility.findAddressListByIdList(provinceIdList);
        Map<String, AddressInfoDo> provinceMap = provinceList.stream().collect(Collectors.toMap(AddressInfoDo::getAddrId,
                Function.identity(), (key1, key2) -> key1));

        //城市批量地址信息
        List<String> cityIdList = list.stream()
                .map(CardApplyConfigDo::getCityId)
                .distinct().collect(Collectors.toList());
        List<AddressInfoDo> cityList = safetySpaceAbility.findAddressListByIdList(cityIdList);
        Map<String, AddressInfoDo> cityMap = cityList.stream().collect(Collectors.toMap(AddressInfoDo::getAddrId,
                Function.identity(), (key1, key2) -> key1));

        //查询所有园区
        List<SafetySpaceParkDo> safetySpaceParkDoList = safetySpaceAbility.findAllParkList();
        Map<String, SafetySpaceParkDo> parkMap = safetySpaceParkDoList.stream().collect(Collectors.toMap(SafetySpaceParkDo::getParkCode,
                Function.identity(), (key1, key2) -> key1));

        //填装
        for (CardApplyConfigDo cardApplyConfigDo : list) {
            AddressInfoDo country = countryMap.get(String.valueOf(cardApplyConfigDo.getCountryId()));
            AddressInfoDo province = provinceMap.get(String.valueOf(cardApplyConfigDo.getProvinceId()));
            AddressInfoDo city = cityMap.get(String.valueOf(cardApplyConfigDo.getCityId()));
            SafetySpaceParkDo park = parkMap.get(cardApplyConfigDo.getParkCode());
            if (Objects.nonNull(country)) {
                cardApplyConfigDo.setCountryName(country.getAddrName());
            }
            if (Objects.nonNull(province)) {
                cardApplyConfigDo.setProvinceName(province.getAddrName());
            }
            if (Objects.nonNull(city)) {
                cardApplyConfigDo.setCityName(city.getAddrName());
            }
            if (Objects.nonNull(park)) {
                cardApplyConfigDo.setParkName(park.getParkName());
            }
        }

        //更新人信息填充
        List<SafetyPersonDo> safetyPersonDoList = Lists.newArrayListWithExpectedSize(list.size());
        list.forEach(item -> {
            SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
            safetyPersonDo.setUid(item.getUpdateUser());
            item.setUpdatePerson(safetyPersonDo);
            safetyPersonDoList.add(safetyPersonDo);
        });
        safetyPersonAbility.fillPersonInfoAndOrgInfoByUidBatch(safetyPersonDoList);
    }

    private void insert(CardApplyDo cardApplyDo) {
        String uid = null;
        //用户的姓名不能超长
        cardApplyAbility.checkNameLength(cardApplyDo);
        //校验地区编码
        cardApplyAbility.checkZoneCode(cardApplyDo);
        //创建人员
        uid = cardApplyAbility.createIdm(cardApplyDo);
        cardApplyDo.setUid(uid);
        AccountModel accountInfo = idmSdk.getAccountInfo(cardApplyDo.getResponsibleAccount(), UserIdTypeEnum.ACCOUNT_NAME);
        if (!ObjectUtils.isEmpty(accountInfo)) {
            cardApplyDo.setResponsible(accountInfo.getUid());
            cardApplyDo.setResponsibleName(accountInfo.getFullName());
        }
        cardApplyDo.setApplyType(cardApplyDo.getApplyType());
        // 根据applyType和applyId来判断是否存在
        if (judgeIsExist(cardApplyDo)) {
            updateWithCardTimeById(cardApplyDo);
            CardInfoDo cardInfoDo = cardAbility.findCardInfoByApplyId(cardApplyDo.getId());
            //若工卡存在则新增有效期
            if (!ObjectUtils.isEmpty(cardInfoDo)) {
                //插入新的有效期时间
                cardInfoDo.setStartTime(cardApplyDo.getStartTime());
                cardInfoDo.setEndTime(cardApplyDo.getEndTime());
                cardAbility.buildValidateTime(cardInfoDo);
                cardAbility.saveTime(cardInfoDo.getValidatePeriod().get(0));
            }
        } else {
            //2.本地保存
            cardApplyDo.setPersonInfo(null);
            save(cardApplyDo, false);
        }
        //创建人员信息
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(cardApplyDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);
    }

    @Override
    public void uploadPhoto(Long id, String photoUrl) {
        cardApplyAbility.uploadPhoto(id, photoUrl);
        //同时更新人员信息表照片
        CardApplyDo cardApplyById = cardApplyAbility.getCardApplyById(id);
        cardPersonInfoAbility.updatePhotoUrl(cardApplyById.getUid(), photoUrl);
    }

    @Override
    public void batchOperate(BatchOperateDto batchOperate) {
        if (CardApplyOperateTypeEnum.OPERATE_APPROVAL.getCode().equals(batchOperate.getOperateType())) {
            //1.同意操作 更新表单状态 为待打印
            //同意前需要校验照片参数
            List<CardApplyDo> cardApplyDos = cardApplyAbility.getCardApplyByIds(batchOperate.getIds());
            if (!CollectionUtils.isEmpty(cardApplyDos)) {
                for (CardApplyDo cardApplyDo : cardApplyDos) {
                    if (StringUtils.isEmpty(cardApplyDo.getPhotoUrl())) {
                        throw new BizException(CardApplyDomainErrorCodeEnum.BATCH_PHOTO_URL_EMPTY);
                    }
                }
            }
            cardApplyAbility.operateApproval(batchOperate.getIds());
            cardApplyAbility.notifyOnsiteMessage(batchOperate.getIds(), CardApplyStatusEnum.COMPLETED);
        } else if (CardApplyOperateTypeEnum.OPERATE_REJECT.getCode().equals(batchOperate.getOperateType())) {
            //2.拒绝操作 更新表单  notify写入消息供驻场申请单消费
            cardApplyAbility.operateReject(batchOperate.getIds());
            cardApplyAbility.notifyOnsiteMessage(batchOperate.getIds(), CardApplyStatusEnum.REFUSED);
        } else if (CardApplyOperateTypeEnum.OPERATE_NOTIFY_RECEIVE.getCode().equals(batchOperate.getOperateType())) {
            //3.通知领取操作 飞书给负责人发送消息领卡
            List<CardApplyDo> applyDos = cardApplyAbility.getCardApplyByIds(batchOperate.getIds());
            Map<String, List<CardApplyDo>> uidMap = applyDos.stream().collect(Collectors.groupingBy(CardApplyDo::getResponsible));
            List<MsgUser> msgUsers = Lists.newArrayList();
            for (Map.Entry<String, List<CardApplyDo>> entry : uidMap.entrySet()) {
                List<CardApplyDo> cardApplyDos = entry.getValue();
                Map<String, Object> params = Maps.newHashMap();
                StringBuilder partnerNames = new StringBuilder();
                for (CardApplyDo cardApplyDo : cardApplyDos) {
                    partnerNames.append(cardApplyDo.getDisplayName());
                    partnerNames.append(",");
                }
                List<String> responsibleIds = new ArrayList<>(uidMap.keySet());
                List<PersonInfoModel> userInfosByUids = idmSdk.getUserInfosByUids(responsibleIds);
                partnerNames.deleteCharAt(partnerNames.length() - 1);
                params.put("name", userInfosByUids.get(0).getDisplayName());
                params.put("partners", partnerNames);
                SafetySpaceParkDto spaceParkDto = spaceSdk.getParkByCode(batchOperate.getParkCode());
                String parkName = spaceParkDto != null ? spaceParkDto.getParkName() : "";
                params.put("address", parkName + batchOperate.getReceiptAddress());
                //构建消息内容
                AccountModel accountInfo = idmSdk.getAccountInfo(entry.getKey(), UserIdTypeEnum.HAVANA_ID);
                msgUsers.add(umsSdk.buildMsgUser(accountInfo.getAccountName(), (int) MessageChannelEnum.MI_WORK.getType(), params));
            }
            //发送消息
            umsSdk.sendUms(msgUsers, (int) MessageChannelEnum.MI_WORK.getType(), botBizId, templateId);
        } else if (CardApplyOperateTypeEnum.OPERATE_FINISHED_RECEIVE.getCode().equals(batchOperate.getOperateType())) {
            //4.领取操作 更新表单为完成
            cardApplyAbility.operateReceive(batchOperate.getIds());
        } else if (CardApplyOperateTypeEnum.OPERATE_PRINT.getCode().equals(batchOperate.getOperateType())) {
            //5.打印操作 更新表单为待开卡 todo 需连接打印终端 接入方式待考虑
            //需要先校验照片是否存在
            List<CardApplyDo> cardApplyDos = cardApplyAbility.getCardApplyByIds(batchOperate.getIds());
            if (!CollectionUtils.isEmpty(cardApplyDos)) {
                for (CardApplyDo cardApplyDo : cardApplyDos) {
                    //更新补卡申请单状态
                    CardReissueApplyDo cardReissueApplyDo = new CardReissueApplyDo();
                    cardReissueApplyDo.setCardApplyId(cardApplyDo.getId());
                    cardReissueApplyDo.setReissueStatus(CardReissueApplyStatusEnum.TO_BE_PRINTED.getCode());
                    cardReissueApplyAbility.updateCardReissueApplyReceivedStatus(cardReissueApplyDo);
                    if (!ObjectUtils.isEmpty(cardReissueApplyDo.getId())) {
                        batchOperate.getIds().removeIf(item -> item.equals(cardReissueApplyDo.getCardApplyId()));
                    } else {
                        cardApplyAbility.checkPhotoUrl(cardApplyDo);
                    }
                }
            }
            cardApplyAbility.operatePrint(batchOperate.getIds());
        } else {
            //6.开卡操作 (不支持批量) do nothing 开卡操作跳转到 编辑工卡信息 最终落到保存工卡信息接口
        }
    }

    @Override
    public boolean hasCarrierGroups(String parkCode) {
        SafetyCarrierGroupDo query = new SafetyCarrierGroupDo();
        query.setParkCode(parkCode);
        List<SafetyCarrierGroupDo> carrierGroupDOList = safetyCarrierGroupAbility.queryListByParkCode(query);
        return !CollectionUtils.isEmpty(carrierGroupDOList);

    }

    @Override
    public void checkParams(CardApplyDo cardApplyDo) {
        cardApplyAbility.checkCompleted(cardApplyDo);
    }

    @Override
    public String getReceiptAddress(String parkCode) {
        return cardApplyAbility.getReceiptAddress(parkCode);
    }

    @Override
    public void fillCardApplyByIdmNotify(CardApplyDo cardApplyDo, UpdatePersonDto req) {
        if (!ObjectUtils.isEmpty(req) && !ObjectUtils.isEmpty(cardApplyDo)) {
            cardApplyDo.setDisplayName(req.getDisplayName());
            cardApplyDo.setIdNumber(req.getIdCardNumber());
            cardApplyDo.setPhone(req.getMobile());
            if (!StringUtils.isEmpty(req.getManagerUid()) &&
                    !StringUtils.isEmpty(cardApplyDo.getResponsible()) &&
                    !cardApplyDo.getResponsible().equals(req.getManagerUid())) {
                cardApplyDo.setResponsible(req.getManagerUid());
                cardApplyAbility.fillDeptInfo(cardApplyDo);
            }
        }
    }

    @Override
    public List<SafetySpaceParkDto> listSpaceByParkCodes(List<String> resourceCodes) {
        return cardApplyAbility.listSpaceByParkCodes(resourceCodes);
    }

    @Override
    public Integer checkParkCodeExist(String parkCode) {
        return cardApplyAbility.checkParkCodeExist(parkCode);
    }

    @Override
    public List<CardApplyDo> queryApplyByFuzzyName(String name) {
        return cardApplyAbility.getApplyByFuzzyName(name);
    }

    @Override
    public void fillTempApplyInfo(CardApplyDo cardApplyDo) {
        cardApplyAbility.fillTempApplyBaseInfo(cardApplyDo);
        cardApplyAbility.fillOfficeAddress(cardApplyDo);

    }

    @Override
    public void checkBeforeCreate(CardApplyDo cardApplyDo) {
        //检验员工类型是否可创建正式卡/临时卡申请单
        cardApplyAbility.checkEmpTypeIsCanCreateApply(cardApplyDo);

        //检查是否为米家部门
        cardApplyAbility.checkIsMiHomeDept(cardApplyDo);

        //检查正式卡/临时卡是否已存在
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setUid(cardApplyDo.getPersonInfo().getUid());
        cardInfoDo.setCardType(cardApplyDo.getApplyType());
        cardAbility.checkCardIsExistByUidAndType(cardInfoDo);

        //检查同一人同一类型申请单是否存在
        cardApplyAbility.checkApplyIsExistBySameUidAndType(cardApplyDo);

        //校验是否国际白名单
        cardApplyAbility.checkIsInternationalWhiteList(cardApplyDo);
    }

    @Override
    public void fillEmpApplyInfo(CardApplyDo cardApplyDo) {
        cardApplyAbility.fillOfficeAddress(cardApplyDo);
        cardApplyAbility.fillEmpApplyInfo(cardApplyDo);
    }

    @Override
    public void fillCardApplyDoById(CardApplyDo cardApplyDo) {
        cardApplyAbility.fillCardApplyDoById(cardApplyDo);
    }

    @Override
    public List<CardApplyDo> findByIdList(List<Long> applyIdList) {
        return cardApplyAbility.findByIdList(applyIdList);
    }

    @Override
    public void complete(CardApplyDo cardApplyDo) {
        cardApplyDo.setApplyStatus(CardApplyStatusEnum.COMPLETED.getCode());
        cardApplyAbility.updateCardApply(cardApplyDo);
    }

    @Override
    public void checkEnableUpload(CardApplyDo cardApplyDo) {
        //检查是否可上传
        cardApplyAbility.checkEnableUpload(cardApplyDo);
    }

    @Override
    public void fillCardApplyWithBaseAndParkById(CardApplyDo cardApplyDo) {
        //填装基础信息
        cardApplyAbility.fillCardApplyDoById(cardApplyDo);

        //填装园区信息
        cardApplyAbility.fillParkInfo(cardApplyDo);

        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUserName(cardApplyDo.getPartnerAccount());
        safetyPersonAbility.fillPersonInfoAndOrgInfoByAccount(safetyPersonDo);
        cardApplyDo.setPersonInfo(safetyPersonDo);
    }

    @Override
    public void fillCoopCardApplyByPerson(CardApplyDo cardApplyDo) {
        cardApplyAbility.buildBaseInfoByPerson(cardApplyDo);
        cardApplyAbility.fillSpaceInfo(cardApplyDo);
    }

    @Override
    public void notifyMessage(CardApplyDo cardApplyDo) {
        //通知驻场开卡完成
        cardApplyAbility.notifyOnsiteMessage(Lists.newArrayList(cardApplyDo.getId()), CardApplyStatusEnum.getEnumByCode(cardApplyDo.getApplyStatus()));
        //通知idm卡号变更
        cardApplyAbility.notifyIdmMessage(cardApplyDo);
    }

    @Override
    public void checkIsExpire(CardApplyDo cardApplyDo) {
        cardApplyAbility.checkIsExpire(cardApplyDo);
    }

    @Override
    public void checkValidateTimeBeforeUpdate(CardApplyDo cardApplyDo) {
        cardApplyAbility.checkValidateTimeBeforeUpdate(cardApplyDo);
    }

    @Override
    public Boolean judgeIsNeedUpdateIdm(CardApplyDo cardApplyDo) {
        return cardApplyAbility.judgeIsNeedUpdateIdm(cardApplyDo);
    }

    @Override
    public void checkValidateTimeIsExceedIdmTime(CardApplyDo cardApplyDo) {
        cardApplyAbility.checkValidateTimeIsExceedIdmTime(cardApplyDo);
    }

    @Override
    public void receiveNotice(CardApplyDo cardApplyDo) {
        cardApplyAbility.updateCardApply(cardApplyDo);
    }

    @Override
    public CardApplyDo findCardByUidAndCardType(String uid, CardTypeEnum cardTypeEnum) {
        return cardApplyAbility.findCardByUidAndCardType(uid, cardTypeEnum);
    }

    @Override
    public List<CardApplyDo> findAvatarUrlByUserNameOrUidList(CardApplyDo cardApplyDo) {
        List<String> uidList = (List<String>) cardApplyDo.getExtField("uidList");
        List<String> userNameList = (List<String>) cardApplyDo.getExtField("userNameList");
        List<CardApplyDo> cardApplyDoList;
        if (!CollectionUtils.isEmpty(uidList)) {
            cardApplyDoList = cardApplyAbility.findAvatarUrlByUidList(uidList);
        } else {
            cardApplyDoList = cardApplyAbility.findAvatarUrlByUserNameList(userNameList);
        }
        return cardApplyDoList;
    }

    @Override
    public void fillCardApplyByUidAndType(CardApplyDo cardApplyDo) {
        cardApplyAbility.fillCardApplyByUidAndType(cardApplyDo);
    }

    @Override
    public void checkBeforeCreateForMigrate(CardApplyDo cardApplyDo) {
        //检查正式卡/临时卡是否已存在
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setUid(cardApplyDo.getPersonInfo().getUid());
        cardInfoDo.setCardType(cardApplyDo.getApplyType());
        cardAbility.checkCardIsExistByUidAndType(cardInfoDo);
    }

    @Override
    public void doIdmCreatePerson(CardApplyDo cardApplyDo) {
        //根据手机号填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setMobile(cardApplyDo.getPhone());
        safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
        safetyPersonAbility.fillPersonInfoAndOrgInfoByPhoneAndOrgCode(safetyPersonDo);
        cardApplyDo.setUid(safetyPersonDo.getUid());

        //若uid不存在 则表示本地发起的创建 需推送idm
        if (StringUtils.isEmpty(cardApplyDo.getUid())) {
            //1.idm创建用户
            String uid = cardApplyAbility.createIdm(cardApplyDo);
            //2.本地保存
            cardApplyDo.setUid(uid);
        }
    }

    @Override
    public CardApplyDo getInfoByUid(String uid) {
        return cardApplyAbility.getCardApplyByUid(uid);
    }

}
