package com.mi.oa.ee.safety.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mi.oa.ee.safety.common.config.WorkbenchProperties;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.*;
import com.mi.oa.ee.safety.common.enums.*;
import com.mi.oa.ee.safety.common.enums.card.*;
import com.mi.oa.ee.safety.common.enums.safety.*;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.domain.ability.*;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.SafetyCarrierGroupErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.query.card.PermissionQuery;
import com.mi.oa.ee.safety.domain.query.card.TempCardInfoQuery;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.infra.remote.nacos.WarningWhiteGroupsConfig;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.UcSdk;
import com.mi.oa.ee.safety.infra.repository.*;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCarrierGroupQuery;
import com.mi.oa.ee.safety.infra.seq.generator.SeqGenerator;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import com.mi.oa.infra.uc.common.model.identifier.Account;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/1 16:06
 */
@Slf4j
@Service
public class CardDomainServiceImpl implements CardDomainService {

    @Autowired
    SafetyCarrierGroupAbility groupAbility;

    @Autowired
    SafetyRightAbility safetyRightAbility;

    @Autowired
    CardInfoAbility cardAbility;

    @Autowired
    CardApplyAbility cardApplyAbility;

    @Autowired
    SafetyCarrierAbility safetyCarrierAbility;

    @Autowired
    SafetyCarrierGroupAbility safetyCarrierGroupAbility;

    @Autowired
    SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Autowired
    SafetyOperateLogRepository safetyOperateLogRepository;

    @Autowired
    SafetyRightRepository safetyRightRepository;

    @Autowired
    CardApplyRepository cardApplyRepository;

    @Resource
    private CardInfoRepository cardInfoRepository;

    @Autowired
    SafetyPersonAbility safetyPersonAbility;

    @Autowired
    SafetyMediumAbility safetyMediumAbility;

    @Resource
    private SafetyPersonMediumRepository safetyPersonMediumRepository;

    @Resource
    CommonDoConverter commonDoConverter;

    @Autowired
    IdmSdk idmSdk;

    @Resource
    private IdmRemote idmRemote;

    @Autowired
    private UcSdk ucSdk;

    @Resource
    private WorkbenchProperties workbenchProperties;

    @Resource
    SafetyPersonMediumAbility safetyPersonMediumAbility;

    @Resource
    SafetyCarrierGroupClassAbility safetyCarrierGroupClassAbility;

    @Resource
    CardTravelRecordRepository cardTravelRecordRepository;

    @Resource
    private RecyclableCardAbility recyclableCardAbility;

    @Resource
    private CardTimeValidityAbility cardTimeValidityAbility;

    @Resource
    private CardReissueApplyAbility cardReissueApplyAbility;

    @Resource
    private SafetyUmsNotifyAbility safetyUmsNotifyAbility;

    @NacosValue(value = "${card.travel.chat-id:95c3cc598f7a427a9a60b370f5fd32af}", autoRefreshed = true)
    private String chatId;

    @Resource
    private WarningWhiteGroupsConfig warningWhiteGroupsConfig;

    @Resource
    private SeqGenerator seqGenerator;

    @Override
    public void fillQueryByUserUcDataResource(PermissionQuery permissionQuery) {
        Account loginAccount = idmRemote.getLoginAccount();
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUserName(loginAccount.getValue());
        if (safetyPersonAbility.isUcCardAppSuperAdmin(safetyPersonDo)) {
            // 如果是超管，则无须uc数据权限过滤
            return;
        }

        // 获取用户数据维度权限
        Map<String, List<String>> dimensionResource = ucSdk.getAccountDimensionResource(
                loginAccount.getValue(), loginAccount.getAccountType().name(), workbenchProperties.getCode());

        if (dimensionResource != null) {
            // 根据维度赋值查询条件
            permissionQuery.setAuthSupplierCodeList(dimensionResource.get(UcDataResourceDimensionEnum.SUPPLIER.getCode()));
            permissionQuery.setAuthCarrierGroupCodeList(dimensionResource.get(UcDataResourceDimensionEnum.CARRIER_GROUP.getCode()));
            permissionQuery.setAuthCarrierGroupClassCodeList(dimensionResource.get(UcDataResourceDimensionEnum.CLASS.getCode()));
            /**
             * 空间数据维度权限编码特殊处理
             * 转化规则详见{@link com.mi.oa.ee.safety.api.web.card.CardUcController#spaceDataResource()}
             */
            List<String> spaceDimensionResource = dimensionResource.get(UcDataResourceDimensionEnum.SPACE.getCode());
            if (spaceDimensionResource != null) {
                /**
                 * uc权限编码逆转化
                 * @comment 以前缀为分组标识，并将分组后的值剔除前缀
                 * @example spaceDimensionResource: ["C-123", "C-456", "P-abc", "B-abc-A"]
                 *          =>
                 *          resourceCodeMap: {"C-": ["123", "456"], "P-": ["abc"], "B-": ["abc-A"]}
                 */
                Map<String, List<String>> resourceCodeMap = spaceDimensionResource.stream()
                        .collect(Collectors.groupingBy(
                                code -> StringUtils.substringBefore(code, "-") + "-",
                                Collectors.mapping(code -> StringUtils.substringAfter(code, "-"), Collectors.toList())
                        ));
                // 赋值查询条件
                permissionQuery.setAuthCityIdList(resourceCodeMap.get(SpaceUcDataResourcePrefixEnum.CITY.getPrefix()));
                permissionQuery.setAuthParkCodeList(resourceCodeMap.get(SpaceUcDataResourcePrefixEnum.PARK.getPrefix()));
                permissionQuery.setAuthBuildingCodeList(resourceCodeMap.get(SpaceUcDataResourcePrefixEnum.BUILDING.getPrefix()));
                permissionQuery.setAuthFloorCodeList(resourceCodeMap.get(SpaceUcDataResourcePrefixEnum.FLOOR.getPrefix()));
            }
        }

        permissionQuery.setHasAnyAuth(
                ObjectUtil.isNotEmpty(permissionQuery.getAuthSupplierCodeList())
                        || ObjectUtil.isNotEmpty(permissionQuery.getAuthCarrierGroupCodeList())
                        || ObjectUtil.isNotEmpty(permissionQuery.getAuthCarrierGroupClassCodeList())
                        || ObjectUtil.isNotEmpty(permissionQuery.getAuthCityIdList())
                        || ObjectUtil.isNotEmpty(permissionQuery.getAuthParkCodeList())
                        || ObjectUtil.isNotEmpty(permissionQuery.getAuthBuildingCodeList())
                        || ObjectUtil.isNotEmpty(permissionQuery.getAuthFloorCodeList())
        );
    }

    @Override
    public void checkUserPermissionWithUcRole(List<String> carrierGroupCodes) {
        PermissionQuery permissionQuery = new PermissionQuery();
        fillQueryByUserUcDataResource(permissionQuery);

        List<SafetyCarrierGroupDo> groupDoList = safetyCarrierGroupAbility.permissionGroupConditionList(permissionQuery);
        // 待操作载体集编码校验, 必须在当前用户的权限范围内
        if (!groupDoList.stream()
                .map(SafetyCarrierGroupDo::getCarrierGroupCode)
                .collect(Collectors.toList())
                .containsAll(carrierGroupCodes)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CURRENT_USER_UC_PERMISSION_DENIED);
        }
    }

    @Override
    public PageModel<CardInfoDo> pageConditionList(CardPageConditionDto pageConditionDto, Boolean isExport) {
        List<String> parkCodes = cardApplyAbility.getParkCodesByUc(false);
        //1.条件查询工卡信息
        PageModel<CardInfoDo> pageModel = cardAbility.pageConditionList(pageConditionDto, parkCodes);
        //2.封装
        if (!CollectionUtils.isEmpty(pageModel.getList())) {
            //导出时需填充权限组
            pageModel.getList().stream().parallel().forEach(cardInfoDo -> {
                //填充人员信息
                SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
                safetyPersonDo.setUid(cardInfoDo.getUid());
                safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
                CardApplyDo cardApplyDo = new CardApplyDo();
                cardApplyDo.setPersonInfo(safetyPersonDo);
                cardInfoDo.setCardApply(cardApplyDo);
                cardAbility.fillCardInfo(cardInfoDo, isExport);
            });
        }
        return pageModel;
    }

    @Override
    public PageModel<CardInfoDo> pageConditionList(TempCardInfoQuery query) {
        List<String> parkCodes = cardApplyAbility.getParkCodesByUc(false);
        query.setAuthParkCodeList(parkCodes);
        //1.条件查询工卡信息
        return cardAbility.pageConditionList(query);
    }

    @Override
    public CardInfoDo getDetailCardInfo(Long id) {
        CardInfoDo cardInfoDo = cardAbility.getOneById(id);
        if (ObjectUtils.isEmpty(cardInfoDo)) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_NOT_EXIST);
        }
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);
        cardAbility.fillCardInfo(cardInfoDo, true);
        return cardInfoDo;
    }

    @Override
    public PageModel<OperateLogDto> pageListOperateLogs(Integer operateStatus, Integer operateType, Long pageNum,
                                                        Long pageSize, Long cardId) {
        //1.查询操作日志
        PageModel<SafetyOperateLogDo> pageModel = cardAbility.getListOperateLogs(operateStatus, operateType, pageNum,
                pageSize, cardId);
        //2.填充工卡操作日志
        List<OperateLogDto> operateLogDtos = Lists.newArrayList();
        cardAbility.fillOperateLog(pageModel.getList(), operateLogDtos);
        return PageModel.build(operateLogDtos, pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cardOperate(Integer operateType, Long id, String uid) {
        if (CardOperateTypeEnum.CARD_DESTROY.getCode().equals(operateType)) {
            CardInfoDo cardInfoDo = cardAbility.getOneById(id);
            //使用中才有权限 才能被销卡
            if (!ObjectUtils.isEmpty(cardInfoDo) && (CardStatusEnum.USING.getCode().equals(cardInfoDo.getCardStatus()) ||
                    CardStatusEnum.NOT_ACTIVE.getCode().equals(cardInfoDo.getCardStatus()))) {
                List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
                //填充人员信息
                SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
                safetyPersonDo.setUid(cardInfoDo.getUid());
                safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
                CardApplyDo cardApplyDo = new CardApplyDo();
                cardApplyDo.setPersonInfo(safetyPersonDo);
                cardInfoDo.setCardApply(cardApplyDo);
                cardAbility.fillCardInfo(cardInfoDo, true);
                cardAbility.fillRecyclableCard(cardInfoDo);
                recyclableCardAbility.checkRecyclableCard(cardInfoDo.getRecyclableCardDo());
                //3.校验当前介质与人的关系是否匹配
                SafetyPersonMediumDo safetyPersonMediumDO = safetyPersonMediumAbility.getPersonMedium(cardInfoDo.getUid(),
                        cardInfoDo.getMediumCode());
                if (ObjectUtils.isEmpty(safetyPersonMediumDO)) {
                    throw new BizException(CardInfoDomainErrorCodeEnum.PERSON_MEDIUM_NOT_EXIST);
                } else {
                    safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyPersonMediumDO.getId(),
                            SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDO)));
                }
                //4.删除介质与人的关系（剩下交给安防引擎处理）
                cardAbility.deletePersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode(), safetyPersonMediumDO.getId());
                //5.删除介质与权限的关系
                SafetyRightDo safetyRightDO = new SafetyRightDo();
                safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
                //获取当前介质对应的权限ids  记入日志详情
                List<SafetyRightDo> rightDos = safetyRightAbility.getListByMediumCode(safetyRightDO);
                if (!CollectionUtils.isEmpty(rightDos)) {
                    for (SafetyRightDo rightDo : rightDos) {
                        safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(rightDo.getId(),
                                SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(rightDo)));
                    }
                }
                safetyRightRepository.batchDeleteByMediumCodeAndCarrierGroupCode(Lists.newArrayList(safetyRightDO));
                //更新介质与权限的同步状态为待同步
                if (!StringUtils.isEmpty(cardInfoDo.getMediumCode()) && !CollectionUtils.isEmpty(cardInfoDo.getAccessControl())) {
                    List<String> carrierGroupCodes = cardInfoDo.getAccessControl().stream()
                            .map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
                    safetyRightRepository.batchUpdateSyncStatusToWaitSync(cardInfoDo.getMediumCode(),
                            Sets.newHashSet(carrierGroupCodes), false, OAUCFCommonConstants.INT_ONE);
                } else {
                    log.info(CardInfoDomainErrorCodeEnum.MEDIUM_CARRIER_GROUP.getErrDesc());
                }
                //6.删除卡对应的所有有效时间
                cardAbility.deleteValidateTime(uid, null, null);
                //7.更改工卡信息状态
                cardInfoDo.setCardStatus(CardStatusEnum.CANCELED.getCode());
                cardAbility.updateStatus(cardInfoDo);
                //8.删除介质
                cardAbility.deleteMedium(cardInfoDo.getMediumCode());
                recyclableCardAbility.releaseRecyclableCard(cardInfoDo.getRecyclableCardDo());
                //9.填充工卡操作日志对象、记录销卡操作日志
                SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
                operateLogDo.setOperateTypeEnum(LogOperateType.DESTROY_CARD);
                operateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
                cardInfoDo.setSafetyOperateLog(operateLogDo);
                cardInfoDo.putExtField("isAddGroups", false);
                cardAbility.fillSafetyCardOperateLog(cardInfoDo);
                safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
                //通知idm
                CardApplyDo cardApply = cardInfoDo.getCardApply();
                cardApply.setCardInfo(cardInfoDo);
                cardApply.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_DELETE);
                cardApplyAbility.notifyIdmMessage(cardApply);
            }
        } else if (CardOperateTypeEnum.CARD_RESTORE.getCode().equals(operateType)) {
            CardInfoDo cardInfoDo = cardAbility.getOneById(id);
            //已销卡状态才能恢复
            if (!ObjectUtils.isEmpty(cardInfoDo) && CardStatusEnum.CANCELED.getCode().equals(cardInfoDo.getCardStatus())) {
                //恢复前判断 账号是否启用状态
                PersonInfoModel person = idmSdk.findPersonInfoByPidFromLocalCache(uid);
                if (!SafetyPersonStatusEnum.ENABLE.getCode().equals(person.getPersonStatus().getCode())) {
                    throw new BizException(CardInfoDomainErrorCodeEnum.PERSON_CLOSE_FORBIDDEN);
                }
                //填充人员信息
                SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
                safetyPersonDo.setUid(cardInfoDo.getUid());
                safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
                CardApplyDo cardApplyDo = new CardApplyDo();
                cardApplyDo.setPersonInfo(safetyPersonDo);
                cardInfoDo.setCardApply(cardApplyDo);
                cardAbility.fillCardInfo(cardInfoDo, true);
                cardAbility.fillRecyclableCard(cardInfoDo);
                recyclableCardAbility.checkRecyclableCard(cardInfoDo.getRecyclableCardDo());
                List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
                //1.查询操作日志（权限组）
                SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
                safetyOperateLogDo.setOperateTypeEnum(LogOperateType.DESTROY_CARD);
                cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
                safetyOperateLogDo = safetyOperateLogRepository.getOneByBizId(cardInfoDo);
                OperateLogDto operateLogDto = null;
                if (!ObjectUtils.isEmpty(safetyOperateLogDo)) {
                    operateLogDto = JacksonUtils.json2Bean(safetyOperateLogDo.getRequestParams(), OperateLogDto.class);
                    if (CollectionUtils.isNotEmpty(operateLogDto.getDelGroup())) {
                        //只恢复普通权限
                        List<SafetyCarrierGroupDo> allNeedRestoreGroupList =
                                commonDoConverter.toDoList(operateLogDto.getDelGroup());
                        safetyCarrierGroupAbility.fillCarrierGroupClassList(allNeedRestoreGroupList);
                        List<SafetyCarrierGroupDo> normalGroups = allNeedRestoreGroupList.stream()
                                .filter(item -> SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode().equals(item.getClassCode())
                                        && StatusEnum.WHITE.getCode().equals(item.getStatus()))
                                .collect(Collectors.toList());
                        operateLogDto.setDelGroup(commonDoConverter.toDtoList(normalGroups));
                        cardInfoDo.setAccessControl(normalGroups);
                    }
                }
                //4.校验当前物理卡号是否可用（可能被他人占用）
                cardAbility.checkIsOccupation(cardInfoDo);
                //5.恢复介质与人的关系(剩下交给安防引擎处理)
                cardAbility.restorePersonMedium(uid, cardInfoDo.getMediumCode());
                SafetyPersonMediumDo safetyPersonMediumDO = safetyMediumAbility.getPersonMedium(uid,
                        cardInfoDo.getMediumCode());
                if (!ObjectUtils.isEmpty(safetyPersonMediumDO)) {
                    safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyPersonMediumDO.getId(),
                            SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDO)));
                }
                //6.恢复卡对应有效时间
                cardAbility.restoreValidateTime(uid);
                //7.恢复介质与权限的关系
                List<CardTimeValidityDo> validatePeriod = cardAbility.getValidatePeriod(id);
                //若有多段时间 选取当前有效期内时间塞入权限中 若为空表明 当前没有可用有效时间
                CardTimeValidityDo currentPeriod = null;
                if (!CollectionUtils.isEmpty(validatePeriod)) {
                    validatePeriod.sort(((o1, o2) -> ZonedDateTimeUtils.compare(o1.getStartTime(), o2.getStartTime())));
                    for (CardTimeValidityDo cardTimeValidityDto : validatePeriod) {
                        if (ZonedDateTimeUtils.compare(ZonedDateTime.now(), cardTimeValidityDto.getStartTime()) >= OAUCFCommonConstants.INT_ZERO
                                && ZonedDateTimeUtils.compare(ZonedDateTime.now(), cardTimeValidityDto.getEndTime()) < OAUCFCommonConstants.INT_ZERO) {
                            currentPeriod = cardTimeValidityDto;
                            break;
                        }
                    }
                }
                if (ObjectUtils.isEmpty(currentPeriod)) {
                    throw new BizException(CardInfoDomainErrorCodeEnum.CARD_VALIDATE_TIME_EXPIRE);
                }
                List<SafetyRightDo> safetyRightDOList = Lists.newArrayList();
                if (!ObjectUtils.isEmpty(operateLogDto) && !CollectionUtils.isEmpty(operateLogDto.getDelGroup())) {
                    for (SafetyCarrierGroupDto safetyCarrierGroupDto : operateLogDto.getDelGroup()) {
                        SafetyRightDo safetyRightDO = new SafetyRightDo();
                        safetyRightDO.setUid(cardInfoDo.getUid());
                        safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
                        safetyRightDO.setCarrierGroupCode(safetyCarrierGroupDto.getCarrierGroupCode());
                        safetyRightDO.setStartTime(currentPeriod.getStartTime());
                        safetyRightDO.setEndTime(currentPeriod.getEndTime());
                        safetyRightDO.setSupplierCode(safetyCarrierGroupDto.getSupplierCode());
                        safetyRightDO.setSupplierAccessCode(safetyCarrierGroupDto.getSupplierAccessCode());
                        safetyRightDO.setSupplierCheckCode(cardInfoDo.getMediumPhysicsCode());
                        safetyRightDO.setSyncStatus(OAUCFCommonConstants.INT_ZERO);
                        safetyRightDO.setIsDeleted(OAUCFCommonConstants.LONG_ZERO);
                        safetyRightDO.setOperateTime(ZonedDateTime.now());
                        safetyRightDOList.add(safetyRightDO);
                    }
                    safetyRightRepository.batchSaveOrUpdate(safetyRightDOList, true);
                    //获取权限组ids 记录日志详情
                    List<String> carrierGroupCodeList = operateLogDto.getDelGroup().stream()
                            .map(SafetyCarrierGroupDto::getCarrierGroupCode).collect(Collectors.toList());
                    List<SafetyRightDo> safetyRightWithIds =
                            safetyRightRepository.getByMediumCodeAndCarrierGroupCodeList(cardInfoDo.getMediumCode(), carrierGroupCodeList);
                    if (!CollectionUtils.isEmpty(safetyRightWithIds)) {
                        for (SafetyRightDo safetyRightDoWithId : safetyRightWithIds) {
                            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyRightDoWithId.getId(),
                                    SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(safetyRightDoWithId)));
                        }
                    }
                }
                //8.更改工卡信息状态
                cardInfoDo.setCardStatus(CardStatusEnum.USING.getCode());
                cardAbility.updateStatus(cardInfoDo);
                //9.恢复介质
                cardAbility.restoreMedium(cardInfoDo.getMediumCode());
                recyclableCardAbility.occupyRecyclableCard(cardInfoDo.getRecyclableCardDo());
                //10.填充工卡操作日志对象
                SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
                operateLogDo.setOperateTypeEnum(LogOperateType.RESTORE_CARD);
                operateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
                cardInfoDo.setSafetyOperateLog(operateLogDo);
                cardInfoDo.putExtField("isAddGroups", true);
                cardAbility.fillSafetyCardOperateLog(cardInfoDo);
                safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
                //通知idm
                CardApplyDo cardApply = cardInfoDo.getCardApply();
                cardApply.setCardInfo(cardInfoDo);
                cardApply.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
                cardApplyAbility.notifyIdmMessage(cardApply);
            }
        } else if (CardOperateTypeEnum.CARD_DELETE.getCode().equals(operateType)) {
            //删除操作 针对已销卡状态
            cardAbility.deleteById(id);
        }
    }

    @Override
    public PageModel<SafetyCarrierGroupDo> pageGroupConditionListV2(PermissionQuery query) {
        PageModel<SafetyCarrierGroupDo> res = safetyCarrierGroupAbility.pageGroupConditionListV2(query);
        safetyCarrierGroupAbility.fillCarrierGroup(res.getList());
        return res;
    }

    @Override
    public PageModel<SafetyCarrierGroupDo> pageGroupConditionList(GroupPageConditionDto params) {
        //根据介质编码查询组
        List<SafetyCarrierGroupDo> safetyCarrierGroupDtos = cardAbility.getListByMediumCode(params.getMediumCode(), null);
        List<String> carrierGroupCodes = safetyCarrierGroupDtos.stream().map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
        SafetyCarrierGroupQuery query = new SafetyCarrierGroupQuery();
        query.setPageNum(params.getPageNum());
        query.setPageSize(params.getPageSize());
        query.setCarrierGroupCodeList(carrierGroupCodes);
        query.setParkCodeList(params.getParkCodes());
        query.setIsSelect(params.getIsSelect());
        query.setMediumCode(params.getMediumCode());
        query.setName(params.getCarrierGroupDesc());
        query.setClassCode(params.getTypeCode());
        if (!ObjectUtils.isEmpty(params.getCityId())) {
            query.setCityId(params.getCityId());
        }
        query.setParkCode(params.getParkCode());
        PageModel<SafetyCarrierGroupDo> res = safetyCarrierGroupRepository.pageGroupConditionList(query);
        safetyCarrierGroupAbility.fillCarrierGroup(res.getList());
        return res;
    }

    @Override
    public void openCard(CardInfoDo cardInfoDo) {
        cardAbility.fillRecyclableCard(cardInfoDo);
        recyclableCardAbility.checkRecyclableCard(cardInfoDo.getRecyclableCardDo());
        //校验卡是否可用或者已经被使用
        cardAbility.checkCardIsOccupation(cardInfoDo);
        //保存安防介质
        SafetyMediumDo safetyMediumDO = cardAbility.fillMedium(cardInfoDo);
        SafetyMediumDo mediumDoWithId = cardAbility.saveMedium(safetyMediumDO);
        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
        if (Objects.nonNull(mediumDoWithId)) {
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(mediumDoWithId.getId(),
                    SafetyModelTypeEnum.MEDIUM, JacksonUtils.bean2Json(mediumDoWithId)));
        }
        //保存工卡信息
        cardAbility.save(cardInfoDo);
        SafetyPersonMediumDo safetyPersonMediumDO = safetyMediumAbility.fillPersonMediumByCard(cardInfoDo, false, true);
        //若是正式卡
        if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfoDo.getCardType())) {
            safetyPersonMediumDO.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
        }
        safetyPersonMediumDO = safetyMediumAbility.savePersonMedium(safetyPersonMediumDO);
        safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyPersonMediumDO.getId(),
                SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDO)));

        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setOperateTypeEnum(LogOperateType.OPEN_CARD);
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
        cardInfoDo.putExtField("isAddGroups", true);
        cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);
        //记录开卡日志
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
        recyclableCardAbility.occupyRecyclableCard(cardInfoDo.getRecyclableCardDo());
    }

    @Override
    public void editCard(CardInfoDo cardInfoDo) {
        Boolean isEditForApply = (Boolean) cardInfoDo.getExtField("isEditForApply");
        cardAbility.editCheck(cardInfoDo); // 判断是否可以编辑 card——info中的 status字段
        cardAbility.fillRecyclableCard(cardInfoDo); // 填充临时卡
        recyclableCardAbility.checkRecyclableCard(cardInfoDo.getRecyclableCardDo()); // 验证临时卡
        //原来的记录
        CardInfoDo exist = findCardInfoById(cardInfoDo.getId());
        cardAbility.fillRecyclableCard(exist);
        //校验卡是否可用或者已经被使用
        recyclableCardAbility.checkRecyclableCard(exist.getRecyclableCardDo());
        cardAbility.checkCardIsOccupation(cardInfoDo);
        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();

        //删除旧的介质
        safetyMediumAbility.deleteByMedium(exist.getMediumCode());
        //2.新增介质（已存在则忽略）(有效时间塞以前的)
        SafetyMediumDo safetyMediumDO = cardAbility.fillMedium(cardInfoDo);
        SafetyMediumDo mediumDoWithId = cardAbility.saveMedium(safetyMediumDO);
        if (!ObjectUtils.isEmpty(mediumDoWithId)) {
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(mediumDoWithId.getId(),
                    SafetyModelTypeEnum.MEDIUM, JacksonUtils.bean2Json(mediumDoWithId)));
        }

        //3.删除旧的介质与人的关系
        SafetyPersonMediumDo safetyPersonMediumDO = safetyPersonMediumAbility.getPersonMedium(cardInfoDo.getUid(),
                exist.getMediumCode());
        if (Objects.nonNull(safetyPersonMediumDO)) {
            cardAbility.deletePersonMedium(cardInfoDo.getUid(), exist.getMediumCode(), safetyPersonMediumDO.getId());
        }
        //释放旧卡
        recyclableCardAbility.releaseRecyclableCard(exist.getRecyclableCardDo());

        //4.插入新的介质与人的关系
        safetyPersonMediumDO = safetyMediumAbility.fillPersonMediumByCard(cardInfoDo, false, true);
        //若是正式卡申请单过来的编辑请求 person medium和right同步状态给200
        if (!ObjectUtils.isEmpty(isEditForApply) && isEditForApply) {
            safetyPersonMediumDO.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
        }
        safetyPersonMediumDO = safetyMediumAbility.savePersonMedium(safetyPersonMediumDO);
        safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyPersonMediumDO.getId(),
                SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDO)));
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setMediumCode(exist.getMediumCode());
        //删除安防权限并更新权限状态
        safetyRightAbility.removeSafetyRight(safetyRightDo);
        cardAbility.copySafetyRight(cardInfoDo);
        safetyRightRepository.batchSaveWithIds(cardInfoDo.getSafetyRightList());
        for (SafetyRightDo safetyRightDoWithId : cardInfoDo.getSafetyRightList()) {
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyRightDoWithId.getId(),
                    SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(safetyRightDoWithId)));
        }

        //6.插入新的介质与权限的关系
        CardInfoDo tempDo = new CardInfoDo();
        BeanUtils.copyProperties(cardInfoDo, tempDo);

        //绑定新工卡
        cardAbility.updateStatus(cardInfoDo);

        //记录新卡号和旧卡号
        tempDo.setNewMediumPhysicsCode(cardInfoDo.getMediumPhysicsCode());
        tempDo.setNewMediumEncryptCode(cardInfoDo.getMediumEncryptCode());
        tempDo.setMediumPhysicsCode(exist.getMediumPhysicsCode());
        tempDo.setMediumEncryptCode(exist.getMediumEncryptCode());

        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setOperateTypeEnum(LogOperateType.PHYSIC_CARD_CHANGE);
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
        tempDo.putExtField("isAddGroups", true);
        tempDo.setSafetyOperateLog(safetyOperateLogDo);
        cardAbility.fillSafetyCardOperateLog(tempDo);
        safetyOperateLogRepository.saveOrUpdate(tempDo.getSafetyOperateLog());
        recyclableCardAbility.occupyRecyclableCard(cardInfoDo.getRecyclableCardDo());
    }

    private void fillAccessControl(CardInfoDo cardInfoDo, Boolean isAdd) {
        if (isAdd) {
            if (CollectionUtils.isNotEmpty(cardInfoDo.getAddGroupCodes())) {
                List<SafetyCarrierGroupDo> addGroupDos = groupAbility.getListByCarrierGroupCodes(cardInfoDo.getAddGroupCodes());
                cardInfoDo.setAccessControl(addGroupDos);
            }
        } else {
            if (CollectionUtils.isNotEmpty(cardInfoDo.getDelGroupCodes())) {
                List<SafetyCarrierGroupDo> delGroupCodes = groupAbility.getListByCarrierGroupCodes(cardInfoDo.getDelGroupCodes());
                cardInfoDo.setAccessControl(delGroupCodes);
            }
        }
    }

    @Override
    public void saveValidateTime(CardInfoDo cardInfoDo) {
        List<CardTimeValidityDo> cardTimeValidityDoList =
                cardTimeValidityAbility.findCardValidateListByApplyId(cardInfoDo.getCardApplyId());
        //首次开卡时 若申请单对应有效期为空则插入 否则更新卡id
        if (CollectionUtils.isEmpty(cardTimeValidityDoList)) {
            cardAbility.buildValidateTime(cardInfoDo);
            //后续 需移到service
            cardInfoRepository.saveValidateTime(cardInfoDo);
        } else {
            CardTimeValidityDo cardTimeValidityDo = new CardTimeValidityDo();
            cardTimeValidityDo.setCardApplyId(cardInfoDo.getCardApplyId());
            cardTimeValidityDo.setCardId(cardInfoDo.getId());
            cardTimeValidityAbility.updateCardIdByApplyId(cardTimeValidityDo);
        }
    }

    @Override
    public void updateValidateTime(CardInfoDo cardInfoDo) {
        List<CardTimeValidityDo> cardTimeValidityDoList = cardAbility.getValidatePeriod(cardInfoDo.getId());
        Asserts.assertNotEmpty(cardTimeValidityDoList, CardInfoDomainErrorCodeEnum.CARD_VALIDATE_TIME_NOT_EXISTS);
        cardAbility.refreshValidateTime(cardInfoDo);
        //调用仓储更新有效期
        cardInfoRepository.updateCardTimeValidity(cardInfoDo);
        //再次同步权限
        safetyRightRepository.reSyncSafetyRight(cardInfoDo.getSafetyRightList());
        //更新person_medium有效期 或者状态
        SafetyPersonMediumDo personMediumDo = cardInfoDo.getSafetyPersonMediumDo();
        //若已被删除 先恢复 再更新
        if (!ObjectUtils.isEmpty(personMediumDo) && !OAUCFCommonConstants.LONG_ZERO.equals(personMediumDo.getIsDeleted())) {
            safetyPersonMediumRepository.restorePersonMediumById(personMediumDo);
        }
        safetyPersonMediumRepository.updateValidateTimeById(personMediumDo);
        //更新子表同步状态
        List<SafetyPersonMediumSupplierDo> safetyPersonMediumSupplierDoList =
                safetyPersonMediumAbility.findPersonSupplierListByPersonMediumIdList(Lists.newArrayList(personMediumDo.getId()));
        safetyPersonMediumSupplierDoList.forEach(item -> item.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode()));
        safetyPersonMediumAbility.updatePersonMediumSupplierSync(safetyPersonMediumSupplierDoList);
    }

    @Override
    public List<SafetyRightDo> getCarrierGroupsByMediums(List<String> mediumCodes) {
        return cardAbility.getCarrierGroupsByMediums(mediumCodes);
    }

    @Override
    public void authorityRedo(AuthLogRedoDto param) {
        //权限变更 且未完成的 才能重执行
        if (LogOperateType.AUTHORITY_CHANGE.getType().equals(param.getOperateType()) &&
                !SafetySyncStatusEnum.SUCCESS_SYNC.getCode().equals(param.getOperateStatus())) {
            Set<String> carrierGroupCodes = Sets.newHashSet();
            if (!CollectionUtils.isEmpty(param.getAddStringGroups()) || !CollectionUtils.isEmpty(param.getDelStringGroups())) {
                //更新介质与权限的关系为未同步状态
                carrierGroupCodes.addAll(param.getAddStringGroups());
                carrierGroupCodes.addAll(param.getDelStringGroups());
                cardAbility.updateRightByMediumAndGroups(param.getMediumCode(), carrierGroupCodes,
                        OAUCFCommonConstants.INT_TWO);
                //更新介质与人的关系为未同步
                SafetyPersonMediumDo safetyPersonMediumDo = new SafetyPersonMediumDo();
                safetyPersonMediumDo.setMediumCode(param.getMediumCode());
                safetyPersonMediumDo.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                safetyPersonMediumAbility.updateSyncByMediumCode(safetyPersonMediumDo);
            }
        } else {
            throw new BizException(CardInfoDomainErrorCodeEnum.AUTH_CHANGE_LOG_DO_FAIL);
        }
    }

    @Override
    public CardInfoDo getCardByUid(String uid, List<CardStatusEnum> status) {
        return cardAbility.getCardByStatus(uid, status);
    }

    @Override
    public CardInfoDo findCardByUidAndCardType(String uid, CardTypeEnum cardTypeEnum) {
        CardInfoDo card = new CardInfoDo();
        card.setUid(uid);
        if (cardTypeEnum != null) {
            card.setCardType(cardTypeEnum.getNumber());
        } else {
            card.setCardType(CardTypeEnum.COOPERATION_CARD.getNumber());
        }
        cardAbility.fillCardInfoByUidAndCardType(card);
        return card;
    }

    @Override
    public void updateStatus(Long id, CardStatusEnum canceled) {
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardStatus(canceled.getCode());
        cardInfoDo.setId(id);
        cardAbility.updateStatus(cardInfoDo);
    }

    @Override
    public List<CardInfoDo> getNeedExpireList() {
        //过期的 待激活的流程一致 可统一处理 不同类型卡 快过期处理方式不一致需区分
        List<CardInfoDo> expireList = Lists.newArrayList();
        List<CardInfoDo> usingCardList = cardAbility.getUsingCardList(Lists.newArrayList(CardStatusEnum.USING.getCode()));
        Map<Long, List<CardTimeValidityDo>> mapForUsing = buildCardTimeValidityMap(usingCardList);
        if (!ObjectUtils.isEmpty(mapForUsing)) {
            usingCardList.stream().forEach(usingCard -> {
                List<CardTimeValidityDo> timeValidityDos = mapForUsing.get(usingCard.getId());
                if (checkIsExpire(timeValidityDos)) {
                    expireList.add(usingCard);
                }
            });
        }
        return expireList;
    }

    @Override
    public List<CardInfoDo> getNeedActiveList() {
        List<CardInfoDo> activeList = Lists.newArrayList();
        List<CardInfoDo> notActiveList =
                cardAbility.getNotActiveList(Lists.newArrayList(CardStatusEnum.NOT_ACTIVE_FOR_TIME.getCode(),
                        CardStatusEnum.EXPIRED.getCode(), CardStatusEnum.USING.getCode()));
        //找到进入有效期的卡
        List<Long> notActiveIds = notActiveList.stream().map(CardInfoDo::getId).collect(Collectors.toList());
        List<CardTimeValidityDo> timeForNotActive = cardAbility.getTimeByCardIds(notActiveIds);
        Map<Long, List<CardTimeValidityDo>> mapForNotActive = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(timeForNotActive)) {
            mapForNotActive =
                    timeForNotActive.stream().collect(Collectors.groupingBy(CardTimeValidityDo::getCardId));
        }
        if (!ObjectUtils.isEmpty(mapForNotActive)) {
            Map<Long, List<CardTimeValidityDo>> finalMapForNotActive = mapForNotActive;
            notActiveList.stream().forEach(notActiveCard -> {
                List<CardTimeValidityDo> timeValidityDos = finalMapForNotActive.get(notActiveCard.getId());
                notActiveCard.setValidatePeriod(timeValidityDos);
                if (checkIsActive(notActiveCard)) {
                    activeList.add(notActiveCard);
                }
            });
        }
        return activeList;
    }

    @Override
    public List<CardInfoDo> getNeedNotifyList() {
        List<CardInfoDo> notifyList = Lists.newArrayList();
        List<CardInfoDo> usingCardList = cardAbility.getUsingCardList(Lists.newArrayList(CardStatusEnum.USING.getCode()));
        Map<Long, List<CardTimeValidityDo>> mapForUsing = buildCardTimeValidityMap(usingCardList);
        if (!ObjectUtils.isEmpty(mapForUsing)) {
            usingCardList.stream().forEach(usingCard -> {
                List<CardTimeValidityDo> timeValidityDos = mapForUsing.get(usingCard.getId());
                if (checkIsNotify(timeValidityDos)) {
                    //发送消息需要责任人信息
                    //填充人员信息
                    SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
                    safetyPersonDo.setUid(usingCard.getUid());
                    safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
                    CardApplyDo cardApplyDo = new CardApplyDo();
                    cardApplyDo.setPersonInfo(safetyPersonDo);
                    usingCard.setCardApply(cardApplyDo);
                    cardAbility.fillCardInfo(usingCard, false);
                    notifyList.add(usingCard);
                }
            });
        }
        return notifyList;
    }

    private Map<Long, List<CardTimeValidityDo>> buildCardTimeValidityMap(List<CardInfoDo> usingCardList) {
        //找出使用中 已过期的卡
        List<Long> usingIds = usingCardList.stream().map(CardInfoDo::getId).collect(Collectors.toList());
        List<CardTimeValidityDo> timeForUsing = cardAbility.getTimeByCardIds(usingIds);
        Map<Long, List<CardTimeValidityDo>> mapForUsing =
                timeForUsing.stream().collect(Collectors.groupingBy(CardTimeValidityDo::getCardId));
        return mapForUsing;
    }

    @Override
    public List<SafetyCarrierGroupDo> getListGroupsByParkCode(String parkCode, String mediumCode) {
        return cardAbility.getListByMediumCode(mediumCode, parkCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doExpire(CardInfoDo cardInfoDo) {
        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);
        cardAbility.fillCardInfo(cardInfoDo, true);

        //1.更改工卡状态为已过期
        cardInfoDo.setCardStatus(CardStatusEnum.EXPIRED.getCode());
        cardAbility.updateStatus(cardInfoDo);

        //合作卡才执行权限删除 临时卡 正式卡只需要更新状态
        if (CardTypeEnum.COOPERATION_CARD.getNumber().equals(cardInfoDo.getCardType())) {
            //2.删除前先查询当前有的权限
            List<SafetyRightDo> safetyRightDos = safetyRightAbility.findSafetyRightByMediumCode(cardInfoDo.getMediumCode());

            //3.删除介质与权限的关系
            SafetyRightDo safetyRightDO = new SafetyRightDo();
            safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
            safetyRightRepository.batchDeleteByMediumCodeAndCarrierGroupCode(Lists.newArrayList(safetyRightDO));
            Set<String> carrierGroupCodes =
                    safetyRightDos.stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toSet());
            //3.更新介质与权限关系为未同步(安防引擎捞)
            if (!CollectionUtils.isEmpty(carrierGroupCodes)) {
                cardAbility.updateRightByMediumAndGroups(cardInfoDo.getMediumCode(), carrierGroupCodes,
                        OAUCFCommonConstants.INT_ONE);
            }
            //获取当前介质对应的权限ids  记入日志详情
            if (!CollectionUtils.isEmpty(safetyRightDos)) {
                for (SafetyRightDo rightDo : safetyRightDos) {
                    safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(rightDo.getId(),
                            SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(rightDo)));
                }
            }
            //4.记录过期日志
            SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
            operateLogDo.setOperateTypeEnum(LogOperateType.EXPIRE_CARD);
            operateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
            cardInfoDo.setSafetyOperateLog(operateLogDo);
            cardInfoDo.putExtField("isAddGroups", false);
            cardAbility.fillSafetyCardOperateLog(cardInfoDo);
            safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
        }
    }

    @Override
    @Transactional
    public void doActive(CardInfoDo cardInfoDo) {
        //1.合作卡才执行权限恢复 临时卡 正式卡只需要更新状态
        if (CardTypeEnum.COOPERATION_CARD.getNumber().equals(cardInfoDo.getCardType()) ||
                CardTypeEnum.PROPERTY_CARD_APPLY.getNumber().equals(cardInfoDo.getCardType())) {
            //更新申请表单有效时间段
            CardApplyDo apply = cardApplyAbility.getCardApplyById(cardInfoDo.getCardApplyId());
            if (CardStatusEnum.USING.getCode().equals(cardInfoDo.getCardStatus())) {
                //若是使用中的卡 更新person_medium的有效期时间以及card_apply开始时间结束时间
                SafetyPersonMediumDo safetyPersonMediumDO = safetyMediumAbility.fillPersonMediumByCard(cardInfoDo,
                        false, false);
                safetyMediumAbility.updateValidateTime(safetyPersonMediumDO);
                //非驻场 不更新有效期
                if (!OAUCFCommonConstants.LONG_ZERO.equals(apply.getStartTime().toEpochSecond())
                        && !OAUCFCommonConstants.LONG_ZERO.equals(apply.getEndTime().toEpochSecond())) {
                    apply.setStartTime(cardInfoDo.getStartTime());
                    apply.setEndTime(cardInfoDo.getEndTime());
                    cardApplyAbility.updateCardApply(apply);
                }
                return;
            }
            //非驻场 不更新有效期
            if (!OAUCFCommonConstants.LONG_ZERO.equals(apply.getStartTime().toEpochSecond())
                    && !OAUCFCommonConstants.LONG_ZERO.equals(apply.getEndTime().toEpochSecond())) {
                apply.setStartTime(cardInfoDo.getStartTime());
                apply.setEndTime(cardInfoDo.getEndTime());
                cardApplyAbility.updateCardApply(apply);
            }
            //4.将该段有效期同步到personMedium中
            SafetyPersonMediumDo safetyPersonMediumDO = safetyMediumAbility.fillPersonMediumByCard(cardInfoDo, false,
                    false);
            safetyMediumAbility.updateValidateTime(safetyPersonMediumDO);
            //5.恢复权限
            //5.1.查询操作日志（权限组）
            SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.EXPIRE_CARD);
            cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
            safetyOperateLogDo = safetyOperateLogRepository.getOneByBizId(cardInfoDo);
            OperateLogDto operateLogDto = null;
            if (!ObjectUtils.isEmpty(safetyOperateLogDo)) {
                operateLogDto = JacksonUtils.json2Bean(safetyOperateLogDo.getRequestParams(), OperateLogDto.class);
                cardInfoDo.setAccessControl(commonDoConverter.toDoList(operateLogDto.getDelGroup()));
            } else { //日志未捞到
                List<SafetyRightDo> safetyRightDos = safetyRightAbility.findSafetyRightByMediumCode(cardInfoDo.getMediumCode());
                if (CollectionUtils.isNotEmpty(safetyRightDos)) {
                    List<String> carrierGroupCodes = safetyRightDos.stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
                    List<SafetyCarrierGroupDo> safetyCarrierGroupDoList =
                            safetyCarrierGroupAbility.getListByCarrierGroupCodes(carrierGroupCodes);
                    List<SafetyCarrierGroupDo> normalGroups = safetyCarrierGroupDoList.stream()
                            .filter(item -> SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode().equals(item.getClassCode())).collect(Collectors.toList());
                    cardInfoDo.setAccessControl(normalGroups);
                }
            }
            //5.2保存权限介质
            cardAbility.fillAndSaveCardRights(cardInfoDo);
        }
        //2.更改工卡状态为使用中
        cardInfoDo.setCardStatus(CardStatusEnum.USING.getCode());
        cardAbility.updateStatus(cardInfoDo);
    }

    @Override
    public List<CarrierGroupCityTreeDto> getAccessCityByMedium(String mediumCode) {
        List<SafetyCarrierGroupDo> safetyCarrierGroupDos = cardAbility.getListByMediumCode(mediumCode, null);
        //填充供应商名字 和 分类
        safetyCarrierGroupAbility.fillCarrierGroup(safetyCarrierGroupDos);
        List<CarrierGroupCityTreeDto> res = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(safetyCarrierGroupDos)) {
            Map<String, List<SafetyCarrierGroupDo>> cityMap = safetyCarrierGroupDos.stream().collect(Collectors.groupingBy(SafetyCarrierGroupDo::getCityId));
            for (Map.Entry<String, List<SafetyCarrierGroupDo>> cityEntry : cityMap.entrySet()) {
                List<CarrierGroupParkTreeDto> carrierGroupParkTreeDtos = Lists.newArrayList();
                List<SafetyCarrierGroupDo> cityGroupSafetyCarrierGroup = cityEntry.getValue();
                Map<String, List<SafetyCarrierGroupDo>> cityParkMap = cityGroupSafetyCarrierGroup.stream()
                        .collect(Collectors.groupingBy(SafetyCarrierGroupDo::getParkCode));
                for (Map.Entry<String, List<SafetyCarrierGroupDo>> cityParkEntry : cityParkMap.entrySet()) {
                    CarrierGroupParkTreeDto carrierGroupParkTreeDto = new CarrierGroupParkTreeDto();
                    carrierGroupParkTreeDto.setId(cityParkEntry.getKey());
                    carrierGroupParkTreeDto.setCityName(cityEntry.getValue().get(0).getCityName());
                    carrierGroupParkTreeDto.setParkCode(cityParkEntry.getKey());
                    carrierGroupParkTreeDto.setParkName(cityParkEntry.getValue().get(0).getParkName());
                    carrierGroupParkTreeDto.setChildren(commonDoConverter.toDtoList(cityParkEntry.getValue()));
                    carrierGroupParkTreeDtos.add(carrierGroupParkTreeDto);
                }
                CarrierGroupCityTreeDto carrierGroupCityTreeDto = new CarrierGroupCityTreeDto();
                carrierGroupCityTreeDto.setCityId(cityEntry.getKey());
                carrierGroupCityTreeDto.setId(cityEntry.getKey());
                carrierGroupCityTreeDto.setCityName(cityEntry.getValue().get(0).getCityName());
                carrierGroupCityTreeDto.setChildren(carrierGroupParkTreeDtos);
                res.add(carrierGroupCityTreeDto);
            }
            return res;
        }
        return null;
    }

    @Override
    public void fillValidateTime(CardInfoDo cardInfoDo) {
        cardAbility.fillValidateTimeByCardId(cardInfoDo);
    }

    @Override
    public void deleteValidateTime(String partnerUser, ZonedDateTime startTime, ZonedDateTime endTime) {
        cardAbility.deleteValidateTime(partnerUser, startTime, endTime);
    }

    @Override
    public void deleteSafetyRight(CardInfoDo cardInfoDo) {
        List<SafetyRightDo> safetyRightDoList = safetyRightAbility.findSafetyRightByMediumCode(cardInfoDo.getMediumCode());
        cardInfoDo.setSafetyRightList(safetyRightDoList);
        //1.删除介质与权限的关系
        cardAbility.deleteSafetyRightAndUpdateSync(cardInfoDo);

        //2.填充、保存日志
        cardInfoDo.putExtField("isAddGroups", false);
        if (CollectionUtils.isNotEmpty(safetyRightDoList)) {
            //查询载体集
            List<String> carrierGroupCodes = safetyRightDoList.stream()
                    .map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
            List<SafetyCarrierGroupDo> safetyCarrierGroupDos = safetyCarrierGroupAbility.getListByCarrierGroupCodes(carrierGroupCodes);
            //恢复时 只恢复普通权限
            List<SafetyCarrierGroupDo> normalCarrierGroups = safetyCarrierGroupDos.stream()
                    .filter(item -> SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode().equals(item.getClassCode())).collect(Collectors.toList());
            cardInfoDo.setAccessControl(normalCarrierGroups);
        }
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSavePartnerCard(List<CardApplyDo> cardApplyDos) {
        List<CardInfoDo> cardInfoDos = Lists.newArrayList();
        List<SafetyMediumDo> safetyMediumDOS = Lists.newArrayList();
        List<SafetyPersonMediumDo> safetyPersonMediumDos = Lists.newArrayList();
        List<SafetyRightDo> safetyRightDos = Lists.newArrayList();
        List<CardTimeValidityDo> cardTimeValidityDos = Lists.newArrayList();
        cardApplyAbility.fillBatchImport(cardApplyDos, true);
        //批量保存申请单
        List<CardApplyDo> cardApplyDosWithIds = cardApplyRepository.batchSave(cardApplyDos);
        cardAbility.fillBatchImport(cardInfoDos, cardApplyDosWithIds);
        //批量保存工卡
        List<CardInfoDo> cardInfoDoWithIds = cardAbility.batchSaveWithIds(cardInfoDos);
        //批量保存介质
        safetyMediumAbility.fillSafetyMediumByCard(cardInfoDos, safetyMediumDOS);
        safetyMediumAbility.batchSave(safetyMediumDOS);
        //批量保存介质与人的关系
        for (CardInfoDo cardInfoDo : cardInfoDos) {
            SafetyPersonMediumDo safetyPersonMediumDO = safetyMediumAbility.fillPersonMediumByCard(cardInfoDo, true,
                    true);
            safetyPersonMediumDos.add(safetyPersonMediumDO);
            List<SafetyRightDo> safetyRights = safetyRightAbility.fillRightByCard(cardInfoDo, true);
            safetyRightDos.addAll(safetyRights);
        }
        safetyMediumAbility.savePersonMedium(safetyPersonMediumDos);
        //批量保存介质与权限的关系
        safetyRightRepository.batchSaveOrUpdate(safetyRightDos, false);
        //批量保存有效期时间
        cardAbility.fillBatchExcelImportValidateTime(cardTimeValidityDos, cardInfoDoWithIds, cardApplyDos);
        cardAbility.batchSaveValidateTime(cardTimeValidityDos);
    }

    @Override
    public List<CardTimeValidityDo> getCardTimeByCardIds(List<Long> cardIds) {
        return cardAbility.getTimeByCardIds(cardIds);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByPhysicCodes(List<String> mediumPhysicsCodes) {
        return cardAbility.getListCardInfoByPhysicCodes(mediumPhysicsCodes);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByEncryptCodes(List<String> mediumEncryptCodes) {
        return cardAbility.getListCardInfoByEncryptCodes(mediumEncryptCodes);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByUid(List<String> uid) {
        return cardAbility.getListCardInfoByUid(uid);
    }

    @Override
    public void fillCarrierGroups(List<SafetyCarrierGroupDo> list) {
        safetyCarrierGroupAbility.fillCarrierGroup(list);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByAccounts(List<String> accounts) {
        return cardAbility.getListCardInfoByAccounts(accounts);
    }

    @Override
    public CardInfoDo getCardByNums(SafetyCardToolDto safetyCardToolDto, List<CardStatusEnum> status) {
        return cardAbility.getCardByNums(safetyCardToolDto, status);
    }

    /**
     * @param timeValidityDtos
     * @return boolean
     * @desc 检测是否需要过期处理
     * <AUTHOR> denghui
     * @date 2022/12/28 12:00
     */
    private boolean checkIsExpire(List<CardTimeValidityDo> timeValidityDtos) {
        if (!CollectionUtils.isEmpty(timeValidityDtos)) {
            ZonedDateTime nowTime = ZonedDateTime.now();
            for (CardTimeValidityDo timeValidityDto : timeValidityDtos) {
                //多段有效期 若当前时间没有所属时间段则过期
                if (ZonedDateTimeUtils.compare(nowTime, timeValidityDto.getStartTime()) >= OAUCFCommonConstants.INT_ZERO &&
                        ZonedDateTimeUtils.compare(nowTime, timeValidityDto.getEndTime()) <= OAUCFCommonConstants.INT_ZERO) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @param cardInfoDo
     * @return boolean
     * @desc 检测是否需要激活
     * <AUTHOR> denghui
     * @date 2022/12/28 12:00
     */
    private boolean checkIsActive(CardInfoDo cardInfoDo) {
        List<CardTimeValidityDo> timeValidityDos = cardInfoDo.getValidatePeriod();
        if (!CollectionUtils.isEmpty(timeValidityDos)) {
            ZonedDateTime nowTime = ZonedDateTime.now();
            timeValidityDos.sort((o1, o2) -> ZonedDateTimeUtils.compare(o1.getStartTime(), o2.getStartTime()));
            for (CardTimeValidityDo timeValidityDto : timeValidityDos) {
                //只要当前时间 在其中某一段时间里
                if (ZonedDateTimeUtils.compare(nowTime, timeValidityDto.getStartTime()) >= OAUCFCommonConstants.INT_ZERO &&
                        ZonedDateTimeUtils.compare(nowTime, timeValidityDto.getEndTime()) < OAUCFCommonConstants.INT_ZERO) {
                    //将这段时间存入
                    cardInfoDo.setStartTime(timeValidityDto.getStartTime());
                    cardInfoDo.setEndTime(timeValidityDto.getEndTime());
                    if (!CardStatusEnum.USING.getCode().equals(cardInfoDo.getCardStatus())) {
                        return true;
                    } else {
                        SafetyPersonMediumDo personMedium = safetyPersonMediumAbility.getPersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode());
                        if (!ObjectUtils.isEmpty(personMedium)
                                && (!Objects.equals(ZonedDateTimeUtils.compare(personMedium.getStartTime(),
                                cardInfoDo.getStartTime()), OAUCFCommonConstants.INT_ZERO)
                                || !Objects.equals(ZonedDateTimeUtils.compare(personMedium.getEndTime(),
                                cardInfoDo.getEndTime()), OAUCFCommonConstants.INT_ZERO))
                                && ZonedDateTimeUtils.compare(cardInfoDo.getEndTime(), personMedium.getEndTime()) > OAUCFCommonConstants.INT_ZERO) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * @param timeValidityDos
     * @return boolean
     * @desc 检测是否需要提前7天发送notify
     * <AUTHOR> denghui
     * @date 2022/12/28 12:01
     */
    private boolean checkIsNotify(List<CardTimeValidityDo> timeValidityDos) {
        if (!CollectionUtils.isEmpty(timeValidityDos)) {
            ZonedDateTime afterOneWeek = ZonedDateTime.now().plusDays(7L);
            ZonedDateTime now = ZonedDateTime.now();
            //根据endTime 降序排序
            timeValidityDos.sort((o1, o2) -> ZonedDateTimeUtils.compare(o2.getEndTime(), o1.getEndTime()));
            //最大结束时间小于afterOneWeek 且大于当前时间 则是需要发送ums的
            if (ZonedDateTimeUtils.compare(afterOneWeek,
                    timeValidityDos.get(0).getEndTime()) >= OAUCFCommonConstants.INT_ZERO
                    && ZonedDateTimeUtils.compare(now, timeValidityDos.get(0).getEndTime()) < OAUCFCommonConstants.INT_ZERO) {
                return true;
            }
        }
        return false;
    }

    private SafetyOperateLogDetailDo createSafetyOperateLogDetail(Long id, SafetyModelTypeEnum safetyModelTypeEnum,
                                                                  String jsonParams) {
        SafetyOperateLogDetailDo safetyOperateLogDetailDo = new SafetyOperateLogDetailDo();
        safetyOperateLogDetailDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        if (SafetyModelTypeEnum.MEDIUM.equals(safetyModelTypeEnum)) {
            safetyOperateLogDetailDo.setSyncStatus(200);
        } else {
            safetyOperateLogDetailDo.setSyncStatus(OAUCFCommonConstants.INT_ZERO);
        }
        safetyOperateLogDetailDo.setModelParams(jsonParams);
        safetyOperateLogDetailDo.setModelType(safetyModelTypeEnum.getCode());
        safetyOperateLogDetailDo.setModelId(id);
        return safetyOperateLogDetailDo;
    }

    @Override
    public CardInfoDo findCardInfoByApplyId(Long applyId) {
        CardInfoDo cardInfoDo = cardAbility.findCardInfoByApplyId(applyId);
        cardAbility.fillValidateTimeByCardId(cardInfoDo);
        return cardInfoDo;
    }

    @Override
    public CardInfoDo findCardInfoById(Long cardId) {
        CardInfoDo cardInfoDo = cardAbility.getOneById(cardId);
        cardAbility.fillValidateTimeByCardId(cardInfoDo);
        return cardInfoDo;
    }

    @Override
    public List<CardInfoDo> findCardInfoByApplyIdList(List<Long> applyIdList) {
        return cardAbility.findCardInfoByApplyIdList(applyIdList);
    }

    @Override
    public void receive(CardInfoDo cardInfoDo) {
        List<CardTimeValidityDo> validityDos = cardAbility.findValidateTimeByCardId(cardInfoDo.getId());
        cardInfoDo.setValidatePeriod(validityDos);
        //更新工卡状态为使用中
        if ((CardTypeEnum.COOPERATION_CARD.getNumber().equals(cardInfoDo.getCardType())
                || CardTypeEnum.PROPERTY_CARD_APPLY.getNumber().equals(cardInfoDo.getCardType()))
                && !checkIsActive(cardInfoDo)) {
            cardInfoDo.setCardStatus(CardStatusEnum.NOT_ACTIVE_FOR_TIME.getCode());
        } else {
            cardInfoDo.setCardStatus(CardStatusEnum.USING.getCode());
        }
        cardAbility.updateStatus(cardInfoDo);

        //更新补卡申请单状态
        CardReissueApplyDo cardReissueApplyDo = new CardReissueApplyDo();
        cardReissueApplyDo.setCardId(cardInfoDo.getId());
        cardReissueApplyDo.setReissueStatus(CardReissueApplyStatusEnum.TO_BE_RECEIVE.getCode());
        cardReissueApplyAbility.updateCardReissueApplyReceivedStatus(cardReissueApplyDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnCard(CardInfoDo cardInfoDo) {
        //校验工卡状态为使用中
        if (CardStatusEnum.end(cardInfoDo.getCardStatus())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CURRENT_CARD_RETURN_STATUS_ERROR);
        }
        //更新工卡状态为已归还
        cardInfoDo.setCardStatus(CardStatusEnum.RETURNED.getCode());
        cardInfoDo.setBackTime(ZonedDateTime.now());
        cardAbility.updateStatus(cardInfoDo);
        //清除工卡权限
        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);
        cardAbility.fillCardInfo(cardInfoDo, true);
        cardAbility.fillRecyclableCard(cardInfoDo);
        recyclableCardAbility.checkRecyclableCard(cardInfoDo.getRecyclableCardDo());
        //3.校验当前介质与人的关系是否匹配
        SafetyPersonMediumDo safetyPersonMediumDO = safetyPersonMediumAbility.getPersonMedium(cardInfoDo.getUid(),
                cardInfoDo.getMediumCode());
        if (Objects.nonNull(safetyPersonMediumDO)) {
            //4.删除介质与人的关系（剩下交给安防引擎处理）
            cardAbility.deletePersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode(), safetyPersonMediumDO.getId());
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyPersonMediumDO.getId(),
                    SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDO)));
        }

        // 更新还没有同步的介质状态为已同步
        SafetyRightDo safetyRightDO = new SafetyRightDo();
        safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
        safetyRightAbility.cancelSafetyRightSync(safetyRightDO);
        //删除介质
        cardAbility.deleteMedium(cardInfoDo.getMediumCode());
        //删除卡对应的所有有效时间
        cardAbility.deleteValidateTime(cardInfoDo.getId());
        //更改工卡信息状态
        if (CardTypeEnum.TEMP_CARD.getNumber().equals(cardInfoDo.getCardType())) {
            cardInfoDo.setCardStatus(CardStatusEnum.RETURNED.getCode());
        } else {
            cardInfoDo.setCardStatus(CardStatusEnum.CANCELED.getCode());
        }
        cardAbility.updateStatus(cardInfoDo);
        recyclableCardAbility.releaseRecyclableCard(cardInfoDo.getRecyclableCardDo());
        //记录日志
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setOperateTypeEnum(LogOperateType.DESTROY_CARD);
        cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
        cardInfoDo.putExtField("isAddGroups", false);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reopen(CardInfoDo cardInfoDo) {
        //已销卡状态才能恢复
        cardAbility.canReopen(cardInfoDo);
        //4.校验当前物理卡号是否可用（可能被他人占用）
        cardAbility.checkCardIsOccupation(cardInfoDo);
        cardAbility.fillRecyclableCard(cardInfoDo);
        recyclableCardAbility.checkRecyclableCard(cardInfoDo.getRecyclableCardDo());
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);
        cardAbility.fillCardInfo(cardInfoDo, true);
        //恢复的时候需要过滤状态为启用的权限组
        List<SafetyCarrierGroupDo> accessControl = cardInfoDo.getAccessControl();
        List<SafetyCarrierGroupDo> safetyCarrierGroupDoList = accessControl.stream().filter(safetyCarrierGroupDo ->
                safetyCarrierGroupDo.getStatus().equals(StatusEnum.WHITE.getCode())).collect(Collectors.toList());
        cardInfoDo.setAccessControl(safetyCarrierGroupDoList);

        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
        //5.恢复介质与人的关系(剩下交给安防引擎处理)
        cardAbility.restorePersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode());
        SafetyPersonMediumDo safetyPersonMediumDO = safetyMediumAbility.getPersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode());
        if (!ObjectUtils.isEmpty(safetyPersonMediumDO)) {
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyPersonMediumDO.getId(),
                    SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDO)));
        }
        //6.恢复卡对应有效时间
        cardAbility.restoreValidateTimeByCardId(cardInfoDo.getId());
        //恢复介质
        cardAbility.restoreMedium(cardInfoDo.getMediumCode());
        //更改工卡信息状态
        cardInfoDo.setCardStatus(CardStatusEnum.USING.getCode());
        cardInfoDo.setBackTime(ZonedDateTimeUtils.getZeroTime());
        cardAbility.updateStatus(cardInfoDo);
        //获取工卡权限
        List<SafetyRightDo> safetyRightList = safetyRightAbility.findSafetyRightByMediumCodeInReopenCard(cardInfoDo.getMediumCode());
        List<SafetyRightDo> syncRightList = safetyRightList.stream()
                .filter(safetyRightDo -> StatusEnum.WHITE.getCode().equals(safetyRightDo.getSafetyCarrierGroup().getStatus())).collect(Collectors.toList());
        List<SafetyRightDo> deleteRightList = safetyRightList.stream()
                .filter(safetyRightDo -> StatusEnum.BLACK.getCode().equals(safetyRightDo.getSafetyCarrierGroup().getStatus())).collect(Collectors.toList());
        // 删除禁用权限
        safetyRightRepository.batchDeleteByMediumCodeAndCarrierGroupCode(deleteRightList);
        safetyRightRepository.reSyncSafetyRight(syncRightList);
        for (SafetyRightDo safetyRightDoWithId : syncRightList) {
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyRightDoWithId.getId(),
                    SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(safetyRightDoWithId)));
        }
        recyclableCardAbility.occupyRecyclableCard(cardInfoDo.getRecyclableCardDo());
        //记录恢复操作日志
        SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
        operateLogDo.setOperateTypeEnum(LogOperateType.RESTORE_CARD);
        cardInfoDo.setSafetyOperateLog(operateLogDo);
        cardInfoDo.putExtField("isAddGroups", true);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);
        operateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
        safetyOperateLogRepository.saveOrUpdate(operateLogDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lossCard(CardInfoDo cardInfoDo) {
        //校验工卡状态为使用中
        cardAbility.checkBeforeLossCard(cardInfoDo);
        //挂失工卡
        cardAbility.lossCard(cardInfoDo);
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);
        //清除工卡权限
        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
        cardAbility.fillCardInfo(cardInfoDo, true);
        //3.校验当前介质与人的关系是否匹配
        SafetyPersonMediumDo safetyPersonMediumDO = safetyPersonMediumAbility.getPersonMedium(cardInfoDo.getUid(),
                cardInfoDo.getMediumCode());
        if (Objects.nonNull(safetyPersonMediumDO)) {
            //4.删除介质与人的关系（剩下交给安防引擎处理）
            cardAbility.deletePersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode(), safetyPersonMediumDO.getId());
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyPersonMediumDO.getId(),
                    SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDO)));
        }
        // 更新还没有同步的介质状态为已同步
        SafetyRightDo safetyRightDO = new SafetyRightDo();
        safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
        safetyRightAbility.cancelSafetyRightSync(safetyRightDO);
        //删除介质
        cardAbility.deleteMedium(cardInfoDo.getMediumCode());
        //删除卡对应的所有有效时间
        cardAbility.deleteValidateTime(cardInfoDo.getId());
        //更改工卡信息状态
        cardAbility.updateStatus(cardInfoDo);
        //记录日志
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setOperateTypeEnum(LogOperateType.LOSS_CARD);
        cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
        cardInfoDo.putExtField("isAddGroups", false);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
        //飞书、邮件发送拍照信息
        SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
        Map<String, String> params = new HashMap<>();
        params.put("name", safetyPersonDo.getDisplayName());
        safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.CARD_LOSS_SEND_LARK.getCode());
        safetyUmsNotifyDo.setReceiver(cardInfoDo.getUid());
        safetyUmsNotifyDo.setParams(JacksonUtils.bean2Json(params));
        safetyUmsNotifyAbility.sendLossOrRemoveLossUms(safetyUmsNotifyDo);
    }

    @Override
    public void removeLossCard(CardInfoDo cardInfoDo) {
        //已销卡状态才能恢复
        cardAbility.checkBeforeRemoveLossCard(cardInfoDo);
        cardAbility.removeLossCard(cardInfoDo);
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);

        cardAbility.fillCardInfo(cardInfoDo, true);
        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
        //5.恢复介质与人的关系(剩下交给安防引擎处理)
        cardAbility.restorePersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode());
        SafetyPersonMediumDo safetyPersonMediumDO = safetyMediumAbility.getPersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode());
        if (Objects.nonNull(safetyPersonMediumDO)) {
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyPersonMediumDO.getId(),
                    SafetyModelTypeEnum.PERSON_MEDIUM, JacksonUtils.bean2Json(safetyPersonMediumDO)));
        }
        //6.恢复卡对应有效时间
        cardAbility.restoreValidateTimeByCardId(cardInfoDo.getId());
        //恢复介质
        cardAbility.restoreMedium(cardInfoDo.getMediumCode());
        //获取工卡权限
        List<SafetyRightDo> safetyRightList = safetyRightAbility.findSafetyRightByMediumCode(cardInfoDo.getMediumCode());
        safetyRightRepository.reSyncSafetyRight(safetyRightList);
        for (SafetyRightDo safetyRightDoWithId : safetyRightList) {
            safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyRightDoWithId.getId(),
                    SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(safetyRightDoWithId)));
        }
        //记录恢复操作日志
        SafetyOperateLogDo operateLogDo = new SafetyOperateLogDo();
        operateLogDo.setOperateTypeEnum(LogOperateType.REMOVE_LOSS_CARD);
        cardInfoDo.setSafetyOperateLog(operateLogDo);
        cardInfoDo.putExtField("isAddGroups", true);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);
        cardAbility.updateStatus(cardInfoDo);
        operateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
        safetyOperateLogRepository.saveOrUpdate(operateLogDo);
        //飞书发送解挂通知
        Map<String, String> params = new HashMap<>();
        params.put("name", safetyPersonDo.getDisplayName());
        SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
        safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.CARD_REMOVE_LOSS_SEND_LARK.getCode());
        safetyUmsNotifyDo.setReceiver(cardInfoDo.getUid());
        safetyUmsNotifyDo.setParams(JacksonUtils.bean2Json(params));
        safetyUmsNotifyAbility.sendLossOrRemoveLossUms(safetyUmsNotifyDo);
    }

    @Override
    public List<CardTimeValidityDo> findValidateTimeByCardIdList(List<Long> cardIdList) {
        if (CollectionUtils.isEmpty(cardIdList)) {
            return Lists.newArrayList();
        }
        return cardAbility.findValidatePeriodByCardIdList(cardIdList);
    }

    @Override
    public List<Long> findCardIdByEffectTime(ZonedDateTime endTimeFrom, ZonedDateTime endTimeTo) {
        return cardAbility.findCardIdByEffectTime(endTimeFrom, endTimeTo);
    }

    @Override
    public List<SafetyCarrierGroupDo> findCarrierGroupListByMediumCode(String mediumCode) {
        return cardAbility.getListByMediumCode(mediumCode, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPermission(CardInfoDo cardInfoDo) {
        List<SafetyRightDo> safetyRightList = cardInfoDo.getSafetyRightList();
        Asserts.assertNotNull(cardInfoDo.getMediumCode(), CardInfoDomainErrorCodeEnum.MEDIUM_CODE_NOT_EMPTY);
        //过滤当前用户已有权限的数据，获得需要添加的权限
        List<SafetyRightDo> safetyCarrierGroupList = safetyRightAbility.findSafetyRightByMediumCode(cardInfoDo.getMediumCode());
        safetyRightAbility.filterAuthedPermission(safetyRightList, safetyCarrierGroupList);
        if (CollectionUtils.isEmpty(safetyRightList)) {
            return;
        }
        List<String> addGroupCodeList = safetyRightList.stream()
                .map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
        cardInfoDo.setAddGroupCodes(addGroupCodeList);
        cardInfoDo.setAddRights(safetyRightList);
        //需要添加的霍尼1.0权限
        List<String> hollywell1CarrierGroupCodeList = safetyRightList.stream()
                .filter(item -> SafetySupplierCodeEnum.HOLLYWELL.getSupplierCode().equals(item.getSupplierCode()))
                .map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
        //对于hollywell1.0来说由于只有更新接口，需原有hollywell1权限组更新为未同步状态
        if (CollectionUtils.isNotEmpty(hollywell1CarrierGroupCodeList)) {
            List<String> carrierGroupCodes = safetyCarrierGroupList.stream()
                    .filter(item -> SafetySupplierCodeEnum.HOLLYWELL.getSupplierCode().equals(item.getSupplierCode()))
                    .map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
            //更新1.0的原权限为未同步
            cardAbility.updateRightByMediumAndGroups(cardInfoDo.getMediumCode(),
                    Sets.newHashSet(carrierGroupCodes), OAUCFCommonConstants.INT_ZERO);
        }

        //若是正式卡，并且未激活，重新设置为已同步，待激活的时候再同步
        if (CollectionUtils.isNotEmpty(safetyCarrierGroupList)) {
            if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfoDo.getCardType())
                    && CardStatusEnum.NOT_ACTIVE.getCode().equals(cardInfoDo.getCardStatus())) {
                //正式卡开卡的时候添加的权限是已同步的，待激活的时候再同步
                List<Long> idList = safetyCarrierGroupList.stream().map(SafetyRightDo::getId).collect(Collectors.toList());
                safetyRightRepository.updateSyncStatusByIdList(idList, SafetySyncStatusEnum.SUCCESS_SYNC, idmRemote.getLoginUid());
            }
        }

        List<SafetyOperateLogDetailDo> safetyOperateLogDetailList = Lists.newArrayList();
        //批量插入介质与权限的关系
        safetyRightRepository.batchSaveWithIds(safetyRightList);
        if (!CollectionUtils.isEmpty(safetyRightList)) {
            for (SafetyRightDo safetyRightDoWithId : safetyRightList) {
                safetyOperateLogDetailList.add(createSafetyOperateLogDetail(safetyRightDoWithId.getId(),
                        SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(safetyRightDoWithId)));
            }
        }
        //记录权限变更日志
        fillAccessControl(cardInfoDo, true);
        cardInfoDo.putExtField("isAddGroups", true);
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        if (cardInfoDo.getExtField("isTravel") != null && (Boolean) cardInfoDo.getExtField("isTravel")) {
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_TRAVEL_OPEN_RIGHT);
        } else {
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.AUTHORITY_CHANGE);
        }
        cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);

        //保持对应的操作记录及操作详情记录
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailList);
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
        //若是工卡差旅记录操作，则更新操作ID到record表中
        if (cardInfoDo.getExtField("cardTravelRecordId") != null) {
            CardTravelRecordDo cardTravelRecordDo = new CardTravelRecordDo();
            cardTravelRecordDo.setId((Long) cardInfoDo.getExtField("cardTravelRecordId"));
            cardTravelRecordDo.setSafetyOperateLogId(cardInfoDo.getSafetyOperateLog().getId());
            cardTravelRecordRepository.updateById(cardTravelRecordDo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removePermission(CardInfoDo cardInfoDo) {
        List<String> carrierGroupCodeList = cardInfoDo.getDelGroupCodes();
        if (StringUtils.isEmpty(cardInfoDo.getMediumCode())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.MEDIUM_CODE_NOT_EMPTY);
        }
        if (CollectionUtils.isEmpty(carrierGroupCodeList)) {
            return;
        }
        List<SafetyCarrierGroupDo> safetyCarrierGroupDos = safetyCarrierGroupAbility.getListByCarrierGroupCodes(carrierGroupCodeList);
        if (CollectionUtils.isEmpty(safetyCarrierGroupDos)) {
            throw new BizException(SafetyCarrierGroupErrorCodeEnum.CARRIER_GROUP_IS_NOT_EXISTS);
        }
        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();

        List<String> hollywell1CarrierGroupCodeList = safetyCarrierGroupDos.stream()
                .filter(item -> SafetySupplierCodeEnum.HOLLYWELL.getSupplierCode().equals(item.getSupplierCode()))
                .map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
        List<String> needSyncCarrierGroupCodeList = (List<String>) CollectionUtils.subtract(carrierGroupCodeList, hollywell1CarrierGroupCodeList);
        //对于hollywell1.0来说由于只有更新接口，需原有权限组去除当前删除权限组后更新为未同步状态
        if (CollectionUtils.isNotEmpty(hollywell1CarrierGroupCodeList)) {
            List<SafetyCarrierGroupDo> carrierGroupDtos = cardAbility.getListByMediumCode(cardInfoDo.getMediumCode(),
                    null);
            if (CollectionUtils.isNotEmpty(carrierGroupDtos)) {
                List<String> carrierGroupCodes = carrierGroupDtos.stream()
                        .filter(item -> SafetySupplierCodeEnum.HOLLYWELL.getSupplierCode().equals(item.getSupplierCode()))
                        .map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
                List<String> subCarrierGroupCodes =
                        (List<String>) CollectionUtils.subtract(carrierGroupCodes, cardInfoDo.getDelGroupCodes());
                //将需要同步的安防载体集填装到需要同步的数据中
                needSyncCarrierGroupCodeList.addAll(subCarrierGroupCodes);
            }
        }

        //处理对应的安防权限数据，生成操作详情日志
        cardAbility.updateRightByMediumAndGroups(cardInfoDo.getMediumCode(),
                Sets.newHashSet(needSyncCarrierGroupCodeList), OAUCFCommonConstants.INT_ZERO);
        List<SafetyRightDo> rightDoList = safetyRightRepository.getByMediumCodeAndCarrierGroupCodeList(cardInfoDo.getMediumCode(),
                needSyncCarrierGroupCodeList);
        if (!CollectionUtils.isEmpty(rightDoList)) {
            for (SafetyRightDo safetyRightDoWithId : rightDoList) {
                safetyOperateLogDetailDos.add(createSafetyOperateLogDetail(safetyRightDoWithId.getId(),
                        SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(safetyRightDoWithId)));
            }
        }

        //批量删除介质与权限的关系
        List<SafetyRightDo> safetyRightDos = Lists.newArrayList();
        for (String delGroupCode : cardInfoDo.getDelGroupCodes()) {
            SafetyRightDo safetyRightDO = new SafetyRightDo();
            safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
            safetyRightDO.setCarrierGroupCode(delGroupCode);
            safetyRightDos.add(safetyRightDO);
        }
        safetyRightRepository.batchDeleteByMediumCodeAndCarrierGroupCode(safetyRightDos);
        //记录权限变更日志
        fillAccessControl(cardInfoDo, false);
        cardInfoDo.putExtField("isAddGroups", false);
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        if (cardInfoDo.getExtField("isTravel") != null && (Boolean) cardInfoDo.getExtField("isTravel")) {
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_TRAVEL_REMOVE_RIGHT);
        } else {
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.AUTHORITY_CHANGE);
        }
        cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);

        //保持对应的操作记录及操作详情记录
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());

        //若是工卡差旅记录操作，则更新操作ID到record表中
        if (cardInfoDo.getExtField("cardTravelRecordId") != null) {
            CardTravelRecordDo cardTravelRecordDo = new CardTravelRecordDo();
            cardTravelRecordDo.setId((Long) cardInfoDo.getExtField("cardTravelRecordId"));
            cardTravelRecordDo.setSafetyOperateLogId(cardInfoDo.getSafetyOperateLog().getId());
            cardTravelRecordRepository.updateById(cardTravelRecordDo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPermissionV2(CardInfoDo cardInfoDo, List<SafetyRightDo> safetyRightList) {
        cardAbility.checkBeforeAddPermission(cardInfoDo);
        //过滤当前用户已有权限的数据，获得需要添加的权限
        cardAbility.fillGrantedSafetyRight(cardInfoDo);
        cardAbility.fillSafetyMedium(cardInfoDo);
        cardAbility.groupSafetyRightForAddPermission(cardInfoDo, safetyRightList);

        //若是正式卡，并且未激活，重新设置为已同步，待激活的时候再同步
        if (CollectionUtils.isNotEmpty(safetyRightList)) {
            if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfoDo.getCardType())
                    && CardStatusEnum.NOT_ACTIVE.getCode().equals(cardInfoDo.getCardStatus())) {
                //正式卡开卡的时候添加的权限是已同步的，待激活的时候再同步
                List<Long> idList = safetyRightList.stream().map(SafetyRightDo::getId).collect(Collectors.toList());
                safetyRightRepository.updateSyncStatusByIdList(idList, SafetySyncStatusEnum.SUCCESS_SYNC, idmRemote.getLoginUid());
            }
        }

        List<SafetyOperateLogDetailDo> safetyOperateLogDetailList = Lists.newArrayList();
        //批量插入介质与权限的关系
        safetyRightRepository.batchSaveWithIds(cardInfoDo.getAddRights());
        safetyRightRepository.batchUpdate(cardInfoDo.getUpdateRights());

        if (!CollectionUtils.isEmpty(cardInfoDo.getAddRights())) {
            for (SafetyRightDo safetyRightDoWithId : cardInfoDo.getAddRights()) {
                safetyOperateLogDetailList.add(createSafetyOperateLogDetail(safetyRightDoWithId.getId(),
                        SafetyModelTypeEnum.RIGHT, JacksonUtils.bean2Json(safetyRightDoWithId)));
            }
        }
        //记录权限变更日志
        fillAccessControl(cardInfoDo, true);
        cardInfoDo.putExtField("isAddGroups", true);
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        if (cardInfoDo.getExtField("isTravel") != null && (Boolean) cardInfoDo.getExtField("isTravel")) {
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_TRAVEL_OPEN_RIGHT);
        } else {
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.AUTHORITY_CHANGE);
        }
        cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);

        //保持对应的操作记录及操作详情记录
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailList);
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
        //若是工卡差旅记录操作，则更新操作ID到record表中
        if (cardInfoDo.getExtField("cardTravelRecordId") != null) {
            CardTravelRecordDo cardTravelRecordDo = new CardTravelRecordDo();
            cardTravelRecordDo.setId((Long) cardInfoDo.getExtField("cardTravelRecordId"));
            cardTravelRecordDo.setSafetyOperateLogId(cardInfoDo.getSafetyOperateLog().getId());
            cardTravelRecordRepository.updateById(cardTravelRecordDo);
        }
    }

    @Override
    public void fillImportCardInfoByApply(CardInfoDo cardInfoDo) {
        SafetyCarrierGroupDo query = new SafetyCarrierGroupDo();
        //查询所有门禁权限组
        List<SafetyCarrierGroupDo> safetyCarrierGroupDos = safetyCarrierGroupAbility.queryListByParkCode(query);
        Map<String, SafetyCarrierGroupDo> accessCodeMap = safetyCarrierGroupDos.stream().collect(Collectors.toMap(
                SafetyCarrierGroupDo::getSupplierAccessCode, item -> item, (a, b) -> a));
        @SuppressWarnings("unchecked")
        List<String> hwAccessRightIds = (List<String>) cardInfoDo.getCardApply().getExtField("hwAccessRightIds");
        if (CollectionUtils.isNotEmpty(hwAccessRightIds)) {
            List<SafetyCarrierGroupDo> addSafetyCarrierGroupDoList = Lists.newArrayList();
            for (String accessCode : hwAccessRightIds) {
                SafetyCarrierGroupDo safetyCarrierGroupDo = accessCodeMap.get(accessCode);
                if (!ObjectUtils.isEmpty(safetyCarrierGroupDo)) {
                    addSafetyCarrierGroupDoList.add(safetyCarrierGroupDo);
                }
            }
            List<String> addGroupCodes = addSafetyCarrierGroupDoList.stream().map(SafetyCarrierGroupDo::getCarrierGroupCode).collect(Collectors.toList());
            cardInfoDo.setAddGroupCodes(addGroupCodes);
            //填装特殊权限的标识
            Boolean isHave = safetyCarrierGroupAbility.judgeIsHaveSpecialCarrierGroupByList(addSafetyCarrierGroupDoList);
            cardInfoDo.setHasSpecialAuth(isHave ? OAUCFCommonConstants.INT_ONE : OAUCFCommonConstants.INT_ZERO);
        }
        cardAbility.fillImportCardInfo(cardInfoDo);
    }

    @Override
    public void fillImportMedium(CardInfoDo cardInfoDo) {
        cardAbility.fillImportMedium(cardInfoDo);
    }

    @Override
    public void fillImportPersonMedium(CardInfoDo cardInfoDo) {
        SafetyPersonMediumDo safetyPersonMediumDO = new SafetyPersonMediumDo();
        safetyPersonMediumDO.setUid(cardInfoDo.getUid());
        safetyPersonMediumDO.setSyncStatus(200);
        safetyPersonMediumDO.setMediumCode(cardInfoDo.getMediumCode());
        safetyPersonMediumDO.setStartTime(cardInfoDo.getStartTime());
        safetyPersonMediumDO.setEndTime(cardInfoDo.getEndTime());
        cardInfoDo.setSafetyPersonMediumDo(safetyPersonMediumDO);
    }

    @Override
    public void fillImportRights(CardInfoDo cardInfoDo) {
        List<SafetyRightDo> safetyRightDOS = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(cardInfoDo) && !CollectionUtils.isEmpty(cardInfoDo.getAddGroupCodes())) {
            List<SafetyCarrierGroupDo> safetyCarrierGroupDos =
                    safetyCarrierGroupAbility.getListByCarrierGroupCodes(cardInfoDo.getAddGroupCodes());
            List<SafetyCarrierGroupClassDo> groupClassList =
                    safetyCarrierGroupClassAbility.getCarrierGroupClassByGroupCodes(cardInfoDo.getAddGroupCodes());
            if (CollectionUtils.isNotEmpty(safetyCarrierGroupDos)) {
                List<String> normalGroupCodeList = groupClassList.stream().filter(item ->
                                SafetyCarrierGroupClassEnum.GROUP_NORMAL.getCode().equals(item.getClassCode()))
                        .map(SafetyCarrierGroupClassDo::getCarrierGroupCode).collect(Collectors.toList());
                safetyCarrierGroupDos.forEach(carrierGroupCode -> {
                    SafetyRightDo safetyRightDO = new SafetyRightDo();
                    safetyRightDO.setUid(cardInfoDo.getCardApply().getPersonInfo().getUid());
                    if (CollectionUtils.isNotEmpty(normalGroupCodeList)
                            && normalGroupCodeList.contains(carrierGroupCode.getCarrierGroupCode())) {
                        safetyRightDO.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
                    } else {
                        safetyRightDO.setEndTime(cardInfoDo.getCardApply().getEndTime());
                    }
                    safetyRightDO.setStartTime(cardInfoDo.getCardApply().getStartTime());
                    safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
                    safetyRightDO.setSyncStatus(200);
                    safetyRightDO.setCarrierGroupCode(carrierGroupCode.getCarrierGroupCode());
                    safetyRightDO.setOperateTime(ZonedDateTime.now());
                    safetyRightDO.setSupplierCheckCode(cardInfoDo.getMediumPhysicsCode());
                    safetyRightDO.setSupplierAccessCode(carrierGroupCode.getSupplierAccessCode());
                    safetyRightDO.setSupplierCode(carrierGroupCode.getSupplierCode());
                    safetyRightDOS.add(safetyRightDO);
                });
            }
            cardInfoDo.setSafetyRightList(safetyRightDOS);
        }
    }

    @Override
    public void fillImportValidateTimeAndSave(CardInfoDo cardInfoDo) {
        cardAbility.fillImportValidateTime(cardInfoDo);
        cardAbility.batchSaveValidateTime(cardInfoDo.getValidatePeriod());
    }

    @Override
    public void fillSafetyRight(CardInfoDo cardInfoDo) {
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setMediumCode(cardInfoDo.getMediumCode());
        safetyRightDo.setUid(cardInfoDo.getUid());
        List<SafetyRightDo> safetyRightList = safetyRightAbility.getSafetyRightByMediumCodeAndUid(safetyRightDo);
        //填装安防载体集信息
        safetyRightAbility.fillSafetyRightWithSafetyCarrierGroupForList(safetyRightList);
        cardInfoDo.setSafetyRightList(safetyRightList);
    }

    @Override
    public void loadSafetyRightWithBaseInfo(CardInfoDo cardInfoDo) {
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setMediumCode(cardInfoDo.getMediumCode());
        safetyRightDo.setUid(cardInfoDo.getUid());
        List<SafetyRightDo> safetyRightList = safetyRightAbility.getSafetyRightByMediumCodeAndUid(safetyRightDo);
        cardInfoDo.setSafetyRightList(safetyRightList);
    }

    @Override
    public void fillSafetyPersonMedium(CardInfoDo cardInfoDo) {
        //没有safetyPerson的ID的时候，填装对应的人和介质对象
        if (cardInfoDo.getSafetyPersonMediumDo() == null || cardInfoDo.getSafetyPersonMediumDo().getId() == null) {
            SafetyPersonMediumDo safetyPersonMediumDo = safetyPersonMediumAbility.getPersonMediumWithIsDeleted(cardInfoDo.getUid(),
                    cardInfoDo.getMediumCode());
            cardInfoDo.setSafetyPersonMediumDo(safetyPersonMediumDo);
        }
    }

    @Override
    public void fillSafetyCardOperateLog(CardInfoDo cardInfoDo) {
        //填装工卡的操作日志
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);
        //填装工卡对安防人员介质的操作日志详情
        cardAbility.fillPersonMediumLogDetail(cardInfoDo);
        //填装工卡对安防权限的操作日志详情
        cardAbility.fillSafetyRightLogDetail(cardInfoDo);
    }

    @Override
    public void getAndFillEmpCardInfo(CardInfoDo cardInfoDo) {
        CardInfoDo res = cardAbility.getOneById(cardInfoDo.getId());
        cardAbility.checkIsEmpty(res);
        BeanUtils.copyProperties(res, cardInfoDo);

        CardApplyDo cardApplyDo = cardApplyAbility.getCardApplyById(cardInfoDo.getCardApplyId());
        cardApplyAbility.checkIsEmpty(cardApplyDo);

        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardApplyDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        cardApplyDo.setPersonInfo(safetyPersonDo);

        //包含了城市信息的填充
        cardApplyAbility.fillParkInfo(cardApplyDo);
        cardInfoDo.setCardApply(cardApplyDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pinCard(CardInfoDo cardInfoDo) {
        //使用中才能被销卡
        cardAbility.checkIsUsing(cardInfoDo);
        cardAbility.fillRecyclableCard(cardInfoDo);
        recyclableCardAbility.checkRecyclableCard(cardInfoDo.getRecyclableCardDo());
        //日志初始化
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        List<SafetyOperateLogDetailDo> safetyOperateLogDetailDos = Lists.newArrayList();
        safetyOperateLogDo.setSafetyOperateLogDetailDoList(safetyOperateLogDetailDos);
        cardInfoDo.setSafetyOperateLog(safetyOperateLogDo);

        //3.校验当前介质与人的关系是否匹配
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardInfoDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);
        cardAbility.fillCardInfo(cardInfoDo, true);
        SafetyPersonMediumDo safetyPersonMediumDO = safetyPersonMediumAbility.getPersonMedium(cardInfoDo.getUid(),
                cardInfoDo.getMediumCode());
        cardInfoDo.setSafetyPersonMediumDo(safetyPersonMediumDO);
        //4.删除介质与人的关系（剩下交给安防引擎处理）若是过期状态则不用处理(安防已删除))
        if (!CardStatusEnum.EXPIRED.getCode().equals(cardInfoDo.getCardStatus()) && Objects.nonNull(safetyPersonMediumDO)) {
            cardAbility.fillPersonMediumLogDetail(cardInfoDo);
            cardAbility.deletePersonMedium(cardInfoDo.getUid(), cardInfoDo.getMediumCode(), safetyPersonMediumDO.getId());
        }

        // 更新还没有同步的权限状态为已同步
        SafetyRightDo safetyRightDO = new SafetyRightDo();
        safetyRightDO.setMediumCode(cardInfoDo.getMediumCode());
        safetyRightAbility.cancelSafetyRightSync(safetyRightDO);
        //删除介质
        cardAbility.deleteMedium(cardInfoDo.getMediumCode());
        //删除卡对应的所有有效时间
        cardAbility.deleteValidateTime(cardInfoDo.getId());

        recyclableCardAbility.releaseRecyclableCard(cardInfoDo.getRecyclableCardDo());
        //更新工卡状态为已归还
        cardInfoDo.setCardStatus(CardStatusEnum.CANCELED.getCode());
        cardAbility.updateStatus(cardInfoDo);
        //记录日志
        cardInfoDo.getSafetyOperateLog().setOperateTypeEnum(LogOperateType.DESTROY_CARD);
        cardInfoDo.putExtField("isAddGroups", false);
        cardAbility.fillSafetyCardOperateLog(cardInfoDo);
        safetyOperateLogRepository.saveOrUpdate(cardInfoDo.getSafetyOperateLog());
    }

    @Override
    public void checkHasSpecialAuthAndUpdate(CardInfoDo cardInfoDo) {
        //获取已有权限
        List<SafetyRightDo> safetyRightDoList = safetyRightAbility.findSafetyRightByMediumCode(cardInfoDo.getMediumCode());
        if (CollectionUtils.isEmpty(safetyRightDoList)) {
            if (OAUCFCommonConstants.INT_ONE.equals(cardInfoDo.getHasSpecialAuth())) {
                cardInfoDo.setHasSpecialAuth(OAUCFCommonConstants.INT_ZERO);
                cardAbility.updateStatus(cardInfoDo);
            }
            return;
        }
        List<String> carrierGroupCodes = safetyRightDoList.stream().map(SafetyRightDo::getCarrierGroupCode).collect(Collectors.toList());
        List<SafetyCarrierGroupClassDo> safetyCarrierGroupClassDos =
                safetyCarrierGroupClassAbility.getCarrierGroupClassByGroupCodes(carrierGroupCodes);
        Set<String> distinctClassCodes =
                safetyCarrierGroupClassDos.stream().map(SafetyCarrierGroupClassDo::getClassCode).collect(Collectors.toSet());
        //若原先有特殊权限 移除完后无 则更新
        if (OAUCFCommonConstants.INT_ONE.equals(cardInfoDo.getHasSpecialAuth())
                && !distinctClassCodes.contains(SafetyCarrierGroupClassEnum.GROUP_VIP.getCode())
                && !distinctClassCodes.contains(SafetyCarrierGroupClassEnum.GROUP_SPECIAL.getCode())) {
            cardInfoDo.setHasSpecialAuth(OAUCFCommonConstants.INT_ZERO);
            cardAbility.updateStatus(cardInfoDo);
        }
    }

    @Override
    public PageModel<CardInfoDo> pageEmpCardList(CardPageConditionDto pageConditionDto) {
        List<String> parkCodes = cardApplyAbility.getParkCodesByUc(false);
        //1.条件查询工卡信息 修改联表条件 不查权限组的时候不联表
        return cardAbility.pageConditionListV2(pageConditionDto, parkCodes);
    }

    @Override
    public void checkCardCodeIsOccupied(CardInfoDo card) {
        //检查物理卡号是否被占用
        cardAbility.checkPhysicsCodeIsOccupied(card);

        //检查加密卡号是否被占用
        cardAbility.checkEncryptCodeIsOccupied(card);

    }

    @Override
    public void checkTempCardIsReturn(CardInfoDo cardInfoDo) {
        cardAbility.checkTempCardIsReturn(cardInfoDo);
    }

    @Override
    public void checkEmpCardIsExist(CardInfoDo cardParam) {
        cardAbility.checkEmpCardIsExist(cardParam);
    }

    @Override
    public void checkCardIsExpire(CardInfoDo cardInfoDo) {
        List<CardTimeValidityDo> validityDos = cardAbility.findValidateTimeByCardId(cardInfoDo.getId());
        cardInfoDo.setValidatePeriod(validityDos);
        cardAbility.checkCardIsExpireForReceive(cardInfoDo);
    }

    @Override
    public void checkCardIsActive(CardInfoDo cardInfoDo) {
        cardAbility.checkCardIsActive(cardInfoDo);
    }

    @Override
    public void checkIsExistRight(CardInfoDo cardInfoDo) {
        List<SafetyRightDo> safetyRightDoList = safetyRightAbility.findSafetyRightByMediumCode(cardInfoDo.getMediumCode());
        cardInfoDo.setSafetyRightList(safetyRightDoList);
        cardAbility.checkIsExistRight(cardInfoDo);
    }

    @Override
    public void checkPermissionIsExceedCardValidateTime(CardInfoDo cardInfoDo) {
        List<CardTimeValidityDo> cardTimeValidityDos = cardAbility.findValidateTimeByCardId(cardInfoDo.getId());
        cardInfoDo.setValidatePeriod(cardTimeValidityDos);
        cardAbility.checkPermissionIsExceedCardValidateTime(cardInfoDo);
    }

    @Override
    public void checkIsExistAllRight(CardInfoDo cardInfoDo) {
        List<SafetyRightDo> safetyRightDoList = safetyRightAbility.findSafetyRightByMediumCode(cardInfoDo.getMediumCode());
        cardInfoDo.setSafetyRightList(safetyRightDoList);
        cardAbility.checkIsExistAllRight(cardInfoDo);
    }

    @Override
    public void checkCardIsDestroy(CardInfoDo cardInfoDo) {
        cardAbility.checkCardIsDestroy(cardInfoDo);
    }

    @Override
    public void checkCardIsUsing(CardInfoDo cardInfoDo) {
        cardAbility.checkCardIsUsing(cardInfoDo);
    }

    @Override
    public void fillCardLeaveRecord(CardInfoDo cardInfoDo) {
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUserName(cardInfoDo.getCardLeaveRecord().getUserName());
        safetyPersonAbility.fillPersonInfoAndOrgInfoByAccount(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);

        //填装离职记录基础信息
        cardAbility.fillCardLeaveRecordBaseInfo(cardInfoDo);
    }

    @Override
    public void checkBeforeCreateCardLeaveRecord(CardInfoDo cardInfoDo) {
        //检查人员是否已关闭或禁用
        safetyPersonAbility.checkPersonIsActive(cardInfoDo.getCardApply().getPersonInfo());
        //检查记录是否重复创建
        cardAbility.checkLeaveRecordRepeated(cardInfoDo);
        //检查是否有使用中的正式、临时工卡
        cardAbility.checkTempAndEmpCardIsUsing(cardInfoDo);
    }

    @Override
    public List<CardLeaveRecordDo> findLeaveRecordByUidList(List<String> uidList) {
        return cardAbility.findLeaveRecordByUidList(uidList);
    }

    @Override
    public void reportLeave(CardInfoDo cardInfoDo) {
        CardLeaveRecordDo cardLeaveRecordDo = cardAbility.findPreLeaveRecordByUid(cardInfoDo.getUid());
        //存在预离职申请单 通知人事 更改状态为已离职
        if (!ObjectUtils.isEmpty(cardLeaveRecordDo)) {
            //通知人事 并更新状态
            cardInfoDo.setCardLeaveRecord(cardLeaveRecordDo);
            cardAbility.reportLeave(cardInfoDo);
        }
    }

    @Override
    public void updateValidateTimeByUidAndTime(CardInfoDo cardInfoDo) {
        cardAbility.updateValidateTimeByUidAndTime(cardInfoDo);
    }

    @Override
    public void fillInfoBeforeCheckIsNewCard(CardInfoDo cardInfoDo) {
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUserName(cardInfoDo.getPartnerAccount());
        safetyPersonAbility.fillPersonInfoAndOrgInfoByAccount(safetyPersonDo);
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardInfoDo.setCardApply(cardApplyDo);
        cardAbility.fillLocationParkCode(cardInfoDo);
    }

    @Override
    public Boolean checkIsNewCard(CardInfoDo cardInfoDo) {
        if (cardAbility.checkIsM9ParkCode(cardInfoDo)) {
            return true;
        }
        if (cardAbility.checkIsM9Depart(cardInfoDo)) {
            return true;
        }
        return false;
    }


    @Override
    public List<CardInfoDo> findListByMediumCodeList(List<String> mediumCodes) {
        return cardAbility.findListByMediumCodeList(mediumCodes);
    }


    @Override
    public void checkIsRepeatOpen(CardInfoDo cardInfoDo) {
        cardAbility.checkIsRepeatOpen(cardInfoDo);
    }

    @Override
    public void checkTempCardIsExist(CardInfoDo cardParam) {
        cardAbility.checkTempCardIsExist(cardParam);
    }

    @Override
    public PageModel<SafetyCarrierGroupDo> pageGroupConditionListV3(PermissionQuery permissionQuery) {
        PageModel<SafetyCarrierGroupDo> res = safetyCarrierGroupAbility.pageGroupConditionListV3(permissionQuery);
        safetyCarrierGroupAbility.fillCarrierGroup(res.getList());
        return res;
    }

    @Override
    public CardInfoDo findUsingCardInfoByUidAndCardType(String uid, CardTypeEnum employeeCard) {
        return cardAbility.findUsingCardInfoByUidAndCardType(uid, employeeCard);
    }

    @Override
    public String createEncryptCode(String code) {
        Asserts.assertNotNull(CardSeqEnum.getCardSeqEnum(code), CardInfoDomainErrorCodeEnum.CARD_SEQ_CODE_NOT_EMPTY);
        return seqGenerator.next(code);
    }

    @Override
    public void notifyCardNumberChange(CardInfoDo cardInfoDo) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(cardInfoDo.getCardApplyId());
        cardApplyAbility.fillCardApplyDoById(cardApplyDo);
        cardApplyDo.setCardInfo(cardInfoDo);
        cardApplyDo.putExtField("operateType", cardInfoDo.getExtField("operateType"));
        cardApplyAbility.notifyIdmMessage(cardApplyDo);
    }

    private void buildParams(SafetyUmsNotifyDo safetyUmsNotifyDo, RightCompareResult compareResult) {
        Map<String, String> param = new HashMap<>();
        param.put("userName", compareResult.getPartnerAccount());
        param.put("disPlayName", compareResult.getDisPlayName());
        param.put("absentAccessCodeList", buildAbsentRightContent(compareResult.getSupplierAccessCodeList(), true));
        param.put("absentAccessNameList", buildAbsentRightContent(compareResult.getGroupNameList(), false));
        param.put("newRightNum", String.valueOf(compareResult.getNewNum()));
        param.put("oldRightNum", String.valueOf(compareResult.getOldNum()));
        safetyUmsNotifyDo.setParams(JacksonUtils.bean2Json(param));
    }

    private String buildAbsentRightContent(List<String> params, boolean isCode) {
        String absentRightList = "";
        if (CollectionUtils.isEmpty(params)) {
            return absentRightList;
        }
        if (isCode) {
            absentRightList += "**访问码集合:** <br/>\n";
        } else {
            absentRightList += "**访问码名称集合:** <br/>\n";
        }
        for (int i = 0; i < params.size(); i++) {
            absentRightList += " " + (i + 1) + ":" + params.get(i) + "<br/>\n";
        }
        return absentRightList;
    }

    private RightCompareResult compareRight(List<CardUserRight> oldUserRightList, List<CardUserRight> newUserRightList) {
        if (CollectionUtils.isNotEmpty(oldUserRightList)) {
            oldUserRightList.removeIf(item -> StringUtils.isEmpty(item.getSupplierAccessCode())
                    || warningWhiteGroupsConfig.getWarningWhiteGroups().contains(item.getSupplierAccessCode()));
            RightCompareResult result = new RightCompareResult();
            result.setOldNum(oldUserRightList.size());
            result.setDisPlayName(oldUserRightList.get(0).getDisPlayName());
            result.setPartnerAccount(oldUserRightList.get(0).getPartnerAccount());
            if (CollectionUtils.isNotEmpty(newUserRightList)) {
                result.setNewNum(newUserRightList.size());
                List<String> newAccessCodeList =
                        newUserRightList.stream().map(CardUserRight::getSupplierAccessCode).collect(Collectors.toList());
                oldUserRightList.removeIf(userRight -> newAccessCodeList.contains(userRight.getSupplierAccessCode()));
            } else {
                result.setNewNum(0);
            }
            if (CollectionUtils.isNotEmpty(oldUserRightList)) {
                List<String> supplierAccessCodeList = oldUserRightList.stream().map(CardUserRight::getSupplierAccessCode).collect(Collectors.toList());
                List<String> groupNameList = oldUserRightList.stream().map(CardUserRight::getGroupName).collect(Collectors.toList());
                result.setSupplierAccessCodeList(supplierAccessCodeList);
                result.setGroupNameList(groupNameList);
            }
            return result;
        }
        return null;
    }

    @Data
    public class RightCompareResult {

        private String partnerAccount;

        private String disPlayName;

        private List<String> supplierAccessCodeList;

        private List<String> groupNameList;

        private Integer newNum;

        private Integer oldNum;
    }
}
