package com.mi.oa.ee.safety.domain.service.impl;

import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.LogOperateType;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.domain.ability.CardElectronRecordAbility;
import com.mi.oa.ee.safety.domain.ability.CardInfoAbility;
import com.mi.oa.ee.safety.domain.ability.SafetyConfigUserAbility;
import com.mi.oa.ee.safety.domain.ability.SafetyPersonAbility;
import com.mi.oa.ee.safety.domain.ability.SafetyPersonMediumAbility;
import com.mi.oa.ee.safety.domain.ability.SafetyRightAbility;
import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyConfigUserDo;
import com.mi.oa.ee.safety.domain.model.SafetyOperateLogDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonMediumDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.service.CardElectronRecordDomainService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

/**
 * 电子工卡领域服务
 *
 * <AUTHOR>
 * @date 2023/9/26 15:28
 */
@Service
@Slf4j
public class CardElectronRecordDomainServiceImpl implements CardElectronRecordDomainService {

    @Resource
    private SafetyConfigUserAbility safetyConfigUserAbility;

    @Resource
    private CardInfoAbility cardInfoAbility;

    @Resource
    private CardElectronRecordAbility cardElectronRecordAbility;

    @Resource
    private SafetyPersonMediumAbility safetyPersonMediumAbility;

    @Resource
    private SafetyRightAbility safetyRightAbility;

    @Resource
    private SafetyPersonAbility safetyPersonAbility;

    @Override
    public void checkCanOpen(CardElectronRecordDo cardElectronRecordDo) {

        //根据电子工卡信息填装安防人员介质关系
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setUid(cardElectronRecordDo.getUid());
        cardInfoDo.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        cardInfoAbility.fillCardInfoByUidAndCardType(cardInfoDo);

        //检查当前卡是否是正式卡
        cardInfoAbility.checkCardIsEmployee(cardInfoDo);

        //校验工卡是否有效
        cardInfoAbility.checkCardIsActive(cardInfoDo);

        //检查当前是否全部可以开通电子工卡，是则认为有权限，不再校验后续
        Boolean isAllOpen = cardElectronRecordAbility.judgeIsAllCanOpen(cardElectronRecordDo);
        if (Boolean.TRUE.equals(isAllOpen)) {
            return;
        }

        //优先检查是否有已开通电子工卡 存在则认为有权限 不再校验后续
        Boolean isHave = cardElectronRecordAbility.judgeHasOpenedByUid(cardElectronRecordDo);
        if (Boolean.TRUE.equals(isHave)) {
            return;
        }

        //校验当前用户是否电子工卡开放部门
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardElectronRecordDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        cardElectronRecordDo.setSafetyPersonDo(safetyPersonDo);
        Boolean isCan = cardElectronRecordAbility.judgePersonDeptIsCanOpen(cardElectronRecordDo);

        //如果部门不符合，校验白名单是否符合
        if (Boolean.FALSE.equals(isCan)) {
            //检查是否可开通电子工卡
            SafetyConfigUserDo safetyConfigUserDo = new SafetyConfigUserDo();
            safetyConfigUserDo.setUid(cardElectronRecordDo.getUid());
            safetyConfigUserAbility.checkPersonIsCanOpenElectron(safetyConfigUserDo);
        }

    }

    @Override
    public void checkBeforeCreate(CardElectronRecordDo cardElectronRecordDo) {
        //检查卡是否有效
        cardInfoAbility.checkCardIsActive(cardElectronRecordDo.getCardInfoDo());

        //检查当前电子工卡记录必要属性是否完毕
        cardElectronRecordAbility.checkCompleted(cardElectronRecordDo);

        //检查当前电子工卡是否已存在
        cardElectronRecordAbility.checkHasEffectiveByElectronCardNum(cardElectronRecordDo);
    }

    @Override
    public Boolean judgeIsCanMigrate(CardElectronRecordDo cardElectronRecordDo) {
        //检查卡是否有效
        cardInfoAbility.checkCardIsActive(cardElectronRecordDo.getCardInfoDo());

        //检查当前电子工卡记录必要属性是否完毕
        cardElectronRecordAbility.checkCompleted(cardElectronRecordDo);

        try {
            //检查当前人员是否已迁移过
            cardElectronRecordAbility.checkMigratedByUid(cardElectronRecordDo);
            //根据小米钱包侧电子卡号检查当前电子工卡是否已存在
            cardElectronRecordAbility.checkHasEffectiveByElectronCardNum(cardElectronRecordDo);
        } catch (BizException e) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean judgeHasOtherOpenedElectronRecord(CardElectronRecordDo cardElectronRecordDo) {
        //uid为空的时候补全uid信息
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            cardElectronRecordAbility.fillInfoByElectronCardNum(cardElectronRecordDo);
        }
        //检查当前人下是否有其他已经开通过电子工卡
        return cardElectronRecordAbility.judgeHasOpenedByElectronUserIdAndUid(cardElectronRecordDo);
    }

    @Override
    public void fillInfoBeforeDeliver(CardElectronRecordDo cardElectronRecordDo) {
        //根据钱包侧电子卡-卡号填装电子卡信息
        cardElectronRecordAbility.fillInfoByElectronCardNum(cardElectronRecordDo);

        //根据实体卡ID填装实体卡基础信息
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setId(cardElectronRecordDo.getCardId());
        cardInfoAbility.fillCardInfoById(cardInfoDo);
        cardElectronRecordDo.setCardInfoDo(cardInfoDo);

        //根据电子工卡信息填装安防介质
        cardElectronRecordAbility.fillWithSafetyMediumByMediumCode(cardElectronRecordDo);

        //根据电子工卡信息填装安防人员介质关系
        cardElectronRecordAbility.fillWithSafetyPersonMediumByUidAndMediumCode(cardElectronRecordDo);

        //根据介质编码和人员ID获取实体卡安防权限列表
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setMediumCode(cardInfoDo.getMediumCode());
        safetyRightDo.setUid(cardElectronRecordDo.getUid());
        List<SafetyRightDo> safetyRightDoList = safetyRightAbility.getSafetyRightByMediumCodeAndUid(safetyRightDo);

        //根据电子工卡信息填装安防权限列表
        cardElectronRecordDo.setSafetyRightList(safetyRightDoList);
        cardElectronRecordAbility.fillWithSafetyRightListByCardInfo(cardElectronRecordDo);

    }

    @Override
    public void checkBeforeDeliver(CardElectronRecordDo cardElectronRecordDo) {
        //检查状态是否开通中
        cardElectronRecordAbility.checkStatusIsOpening(cardElectronRecordDo);
    }

    @Override
    public void fillSafetyOperateLogBeforeOpened(CardElectronRecordDo cardElectronRecordDo) {
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_ELECTRON_OPEN);
        cardElectronRecordDo.putExtField("isAddGroups", true);
        cardElectronRecordDo.putExtField("isOpenCard", true);
        cardElectronRecordDo.setSafetyOperateLog(safetyOperateLogDo);
        //根据电子工卡记录填装对应的安防日志及详情
        cardElectronRecordAbility.fillWithSafetyOperateLog(cardElectronRecordDo);
    }

    @Override
    public void fillSafetyOperateLogBeforeMigrate(CardElectronRecordDo cardElectronRecordDo) {
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_ELECTRON_OPEN);
        cardElectronRecordDo.putExtField("isAddGroups", true);
        cardElectronRecordDo.putExtField("isOpenCard", true);
        cardElectronRecordDo.setSafetyOperateLog(safetyOperateLogDo);
        //根据电子工卡记录填装对应的安防日志及详情
        cardElectronRecordAbility.fillWithSafetyOperateLog(cardElectronRecordDo);
        //迁移过来认为已经同步成功
        cardElectronRecordDo.getSafetyOperateLog().setOperateStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
    }

    @Override
    public void fillInfoBeforeDeletingByElectronCardNum(CardElectronRecordDo cardElectronRecordDo) {
        //根据钱包侧电子卡-卡号填装电子卡信息
        cardElectronRecordAbility.fillInfoByElectronCardNum(cardElectronRecordDo);

        //填充PersonMediumDoAndSafetyRightDoList
        fillInfoWithPersonMediumDoAndSafetyRightDoList(cardElectronRecordDo);
    }

    @Override
    public void fillInfoBeforeDeletingById(CardElectronRecordDo cardElectronRecordDo) {
        //根据id填充
        cardElectronRecordAbility.fillInfoById(cardElectronRecordDo);

        //填充PersonMediumDoAndSafetyRightDoList
        fillInfoWithPersonMediumDoAndSafetyRightDoList(cardElectronRecordDo);
    }

    @Override
    public void fillInfoBeforeDeleted(CardElectronRecordDo cardElectronRecordDo) {
        //根据钱包侧电子卡-卡号填装电子卡信息
        cardElectronRecordAbility.fillInfoByElectronCardNum(cardElectronRecordDo);

        //填充PersonMediumDoAndSafetyRightDoList
        fillInfoWithPersonMediumDoAndSafetyRightDoList(cardElectronRecordDo);

        //记录日志
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_ELECTRON_DESTROY);
        cardElectronRecordDo.putExtField("isAddGroups", false);
        cardElectronRecordDo.putExtField("isOpenCard", false);
        cardElectronRecordDo.setSafetyOperateLog(safetyOperateLogDo);
        cardElectronRecordAbility.fillWithSafetyOperateLog(cardElectronRecordDo);
    }

    private void fillInfoWithPersonMediumDoAndSafetyRightDoList(CardElectronRecordDo cardElectronRecordDo) {

        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setId(cardElectronRecordDo.getCardId());
        cardInfoAbility.fillCardInfoById(cardInfoDo);
        cardElectronRecordDo.setCardInfoDo(cardInfoDo);

        //判断是否老工卡迁移过来的电子工卡数据
        Boolean isMigrate = cardElectronRecordAbility.judgeIsMigrate(cardElectronRecordDo);
        if (isMigrate) {
            //如果是迁移过来的，不做任何处理
            return;
        }

        //根据安防介质或uid填装安防人员介质关系
        SafetyPersonMediumDo personMediumDo = new SafetyPersonMediumDo();
        personMediumDo.setMediumCode(cardElectronRecordDo.getMediumCode());
        safetyPersonMediumAbility.fillPersonMediumByMediumCodeOrUid(personMediumDo);
        cardElectronRecordDo.setSafetyPersonMediumDo(personMediumDo);

        //根据安防介质获取安防权限列表
        List<SafetyRightDo> safetyRightDoList = safetyRightAbility.findSafetyRightByMediumCode(cardElectronRecordDo.getMediumCode());

        //填装安防权限的权限组信息
        safetyRightAbility.fillSafetyRightWithSafetyCarrierGroupForList(safetyRightDoList);
        cardElectronRecordDo.setSafetyRightList(safetyRightDoList);
    }

    @Override
    public void checkBeforeDeleted(CardElectronRecordDo cardElectronRecordDo) {
        //检查状态是否删除中
        cardElectronRecordAbility.checkStatusIsDeleting(cardElectronRecordDo);
    }

    @Override
    public void checkBeforeForceDelete(CardElectronRecordDo cardElectronRecordDo) {
        //检查状态是否可删除
        cardElectronRecordAbility.checkStatusIsCanForceDelete(cardElectronRecordDo);
    }

    @Override
    public void fillInfoBeforeForceDelete(CardElectronRecordDo cardElectronRecordDo) {

        //根据实体卡的ID填装对应实体卡信息
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setId(cardElectronRecordDo.getCardId());
        cardInfoAbility.fillCardInfoById(cardInfoDo);
        cardElectronRecordDo.setCardInfoDo(cardInfoDo);

        //判断是否老工卡迁移过来的电子工卡数据
        Boolean isMigrate = cardElectronRecordAbility.judgeIsMigrate(cardElectronRecordDo);
        if (isMigrate) {
            //如果是迁移过来的，不做任何处理
            return;
        }

        //填装安防人员介质关系
        SafetyPersonMediumDo safetyPersonMediumDo = new SafetyPersonMediumDo();
        safetyPersonMediumDo.setUid(cardElectronRecordDo.getUid());
        safetyPersonMediumDo.setMediumCode(cardElectronRecordDo.getMediumCode());
        safetyPersonMediumAbility.fillPersonMediumByMediumCodeOrUid(safetyPersonMediumDo);
        cardElectronRecordDo.setSafetyPersonMediumDo(safetyPersonMediumDo);

        //查询安防人员权限列表
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setUid(cardElectronRecordDo.getUid());
        safetyRightDo.setMediumCode(cardElectronRecordDo.getMediumCode());
        List<SafetyRightDo> safetyRightDoList = safetyRightAbility.getSafetyRightByMediumCodeAndUid(safetyRightDo);

        //填装安防权限的权限组信息
        safetyRightAbility.fillSafetyRightWithSafetyCarrierGroupForList(safetyRightDoList);
        cardElectronRecordDo.setSafetyRightList(safetyRightDoList);

        if (Objects.nonNull(safetyPersonMediumDo.getId())) {
            //记录操作日志
            SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_ELECTRON_DESTROY);
            cardElectronRecordDo.putExtField("isAddGroups", false);
            cardElectronRecordDo.putExtField("isOpenCard", false);
            cardElectronRecordDo.setSafetyOperateLog(safetyOperateLogDo);
            cardElectronRecordAbility.fillWithSafetyOperateLog(cardElectronRecordDo);
        }
    }

    @Override
    public void checkBeforeDeleting(CardElectronRecordDo electronRecordDo) {
        cardElectronRecordAbility.checkStatusIsOpened(electronRecordDo);
    }

    @Override
    public List<CardElectronRecordDo> getElectronCardListByUid(CardElectronRecordDo electronRecordDo) {
        List<CardElectronRecordDo> electronRecordDoList = cardElectronRecordAbility.findListByUid(electronRecordDo);
        return cardElectronRecordAbility.selectAndGroupByType(electronRecordDoList);
    }

    @Override
    public List<CardElectronRecordDo> getCanForceDeleteElectronCardListByUid(CardElectronRecordDo cardElectronRecordDo) {
        return cardElectronRecordAbility.getCanForceDeleteListByUid(cardElectronRecordDo);
    }

    @Override
    public void fillBeforeCreate(CardElectronRecordDo electronRecordDo) {
        //填装卡信息
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setUid(electronRecordDo.getUid());
        cardInfoDo.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        cardInfoAbility.fillCardInfoByUidAndCardType(cardInfoDo);
        electronRecordDo.setCardInfoDo(cardInfoDo);

        //构建新电子卡基础信息
        cardElectronRecordAbility.buildNewBaseInfo(electronRecordDo);
    }

    @Override
    public void checkIsUsingNew(CardElectronRecordDo cardElectronRecordDo) {
        //uid不存在的时候，根据钱包侧电子卡-卡号填装电子卡信息
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            cardElectronRecordAbility.fillInfoByElectronCardNum(cardElectronRecordDo);
        }

        //检查当前是否全部可以开通电子工卡，是则认为有权限，补校验后续
        Boolean isAllOpen = cardElectronRecordAbility.judgeIsAllCanOpen(cardElectronRecordDo);
        if (Boolean.TRUE.equals(isAllOpen)) {
            return;
        }

        //优先检查是否有已开通电子工卡 存在则认为有权限 不再校验后续
        Boolean isHave = cardElectronRecordAbility.judgeHasOpenedByUid(cardElectronRecordDo);
        if (Boolean.TRUE.equals(isHave)) {
            return;
        }

        //检查是否有工卡
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setUid(cardElectronRecordDo.getUid());
        cardInfoAbility.checkIsHaveCardByUid(cardInfoDo);
    }

    @Override
    public Integer buildCardElectronStatusForAppByCardNum(CardElectronRecordDo cardElectronRecordDo) {
        //根据钱包侧电子卡-卡号填装电子卡信息
        cardElectronRecordAbility.fillInfoByElectronCardNum(cardElectronRecordDo);

        //根据当前电子卡状态，构建应用中电子卡状态
        return cardElectronRecordAbility.buildCardElectronStatusForAppByStatus(cardElectronRecordDo);
    }

    @Override
    public List<CardElectronRecordDo> findOpenedElectronCardByCardIdAndUid(CardElectronRecordDo cardElectronRecordDo) {
        return cardElectronRecordAbility.findOpenedListByCardIdAndUid(cardElectronRecordDo);
    }

    @Override
    public List<CardElectronRecordDo> findOpenedElectronCardByUid(CardElectronRecordDo cardElectronRecordDo) {
        return cardElectronRecordAbility.findOpenedListByUid(cardElectronRecordDo);
    }

    @Override
    public void groupSafetyRightForAddPermission(CardElectronRecordDo cardElectronRecordDo, List<SafetyRightDo> electronSafetyRightList) {

        //过滤当前用户已有权限的数据，获得需要添加的权限
        List<SafetyRightDo> existSafetyCarrierGroupList = safetyRightAbility.findSafetyRightByMediumCode(cardElectronRecordDo.getMediumCode());
        //过滤已经存在的权限
        cardElectronRecordAbility.groupSafetyRightForAddPermission(cardElectronRecordDo, existSafetyCarrierGroupList,
                electronSafetyRightList);
    }

    @Override
    public void generateOperateLogByAddPermission(CardElectronRecordDo cardElectronRecordDo, List<SafetyRightDo> electronSafetyRightList) {
        //记录日志
        cardElectronRecordDo.setSafetyRightList(electronSafetyRightList);
        if (CollectionUtils.isNotEmpty(electronSafetyRightList)) {
            SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_ELECTRON_AUTHORITY_CHANGE);
            cardElectronRecordDo.putExtField("isAddGroups", true);
            cardElectronRecordDo.putExtField("isOpenCard", false);
            cardElectronRecordDo.setSafetyOperateLog(safetyOperateLogDo);
            cardElectronRecordAbility.fillWithSafetyOperateLog(cardElectronRecordDo);
        }
    }

    @Override
    public void fillBeforeRemovePermission(CardElectronRecordDo cardElectronRecordDo) {
        if (CollectionUtils.isEmpty(cardElectronRecordDo.getCardInfoDo().getDelGroupCodes())) {
            return;
        }
        //处理对应的安防权限数据，生成操作详情日志
        List<String> carrierGroupCodeList = cardElectronRecordDo.getCardInfoDo().getDelGroupCodes();
        List<SafetyRightDo> rightDoList = safetyRightAbility.findListByMediumCodeAndGroupCodeList(cardElectronRecordDo.getMediumCode(), carrierGroupCodeList);

        //填装安防权限中安防载体集信息列表
        safetyRightAbility.fillSafetyRightWithSafetyCarrierGroupForList(rightDoList);

        //记录权限变更日志
        cardElectronRecordDo.setSafetyRightList(rightDoList);
        if (CollectionUtils.isNotEmpty(rightDoList)) {
            SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
            safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_ELECTRON_AUTHORITY_CHANGE);
            cardElectronRecordDo.putExtField("isAddGroups", false);
            cardElectronRecordDo.putExtField("isOpenCard", false);
            cardElectronRecordDo.setSafetyOperateLog(safetyOperateLogDo);
            cardElectronRecordAbility.fillWithSafetyOperateLog(cardElectronRecordDo);
        }
    }

    @Override
    public void fillBeforeUpdateElectronName(CardElectronRecordDo electronRecordDo) {
        cardElectronRecordAbility.fillWithElectronNameFromPay(electronRecordDo);
    }



    @Override
    public Boolean judgeHasOtherOpenedElectronRecordV2(CardElectronRecordDo cardElectronRecordDo) {
        //uid为空的时候补全uid信息
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            cardElectronRecordAbility.fillInfoByElectronCardNum(cardElectronRecordDo);
        }
        //检查当前人下是否有其他已经开通过电子工卡
        return cardElectronRecordAbility.judgeHasOpenedByElectronUserIdAndUidV2(cardElectronRecordDo);
    }
}
