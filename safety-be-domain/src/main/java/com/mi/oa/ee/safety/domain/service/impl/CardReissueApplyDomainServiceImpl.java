package com.mi.oa.ee.safety.domain.service.impl;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.LogOperateType;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.ability.*;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.service.CardReissueApplyDomainService;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/3/26 15:36
 */
@Slf4j
@Service
public class CardReissueApplyDomainServiceImpl implements CardReissueApplyDomainService {

    @Resource
    private SafetyPersonAbility safetyPersonAbility;

    @Resource
    private CardApplyAbility cardApplyAbility;

    @Resource
    private CardInfoAbility cardInfoAbility;

    @Resource
    private CardReissueApplyAbility cardReissueApplyAbility;

    @Resource
    private SafetyRightAbility safetyRightAbility;

    @Resource
    private RecyclableCardAbility recyclableCardAbility;

    @Resource
    private CommonDoConverter commonDoConverter;

    @Override
    public void fillCardReissueApplyBeforeCreate(CardReissueApplyDo cardReissueApplyDo) {
        //填充人员信息
        fillSafetyPersonByUid(cardReissueApplyDo);

        //填充卡申请单信息
        CardApplyDo nowApply = cardReissueApplyDo.getCardApplyDo();
        CardTypeEnum cardTypeEnum = CardTypeEnum.ofNumber(cardReissueApplyDo.getCardType());
        CardApplyDo originApply = cardApplyAbility.findCardByUidAndCardType(cardReissueApplyDo.getUid(), cardTypeEnum);
        Asserts.assertNotNull(originApply, CardApplyDomainErrorCodeEnum.CARD_APPLY_NOT_EXIST);
        cardReissueApplyDo.setCardApplyId(originApply.getId());
        updateOriginApplyParkAndPhoto(nowApply, originApply, cardReissueApplyDo);
        cardReissueApplyDo.setCardApplyDo(originApply);

        //填充工卡信息
        CardInfoDo cardInfoDo = cardInfoAbility.findCardInfoByApplyId(originApply.getId());
        Asserts.assertNotNull(cardInfoDo, CardInfoDomainErrorCodeEnum.CARD_REISSUE_IS_NOT_EXIST);
        cardReissueApplyDo.setCardId(cardInfoDo.getId());
        cardReissueApplyDo.setCardInfo(cardInfoDo);

        //填充补卡申请单基本信息
        cardReissueApplyAbility.buildBaseInfo(cardReissueApplyDo);
    }

    private void updateOriginApplyParkAndPhoto(CardApplyDo nowApply, CardApplyDo originApply,
                                               CardReissueApplyDo cardReissueApplyDo) {
        if (StringUtils.isEmpty(cardReissueApplyDo.getMachineCode())) {
            originApply.setParkCode(nowApply.getParkCode());
        }
        originApply.setPhotoUrl(nowApply.getPhotoUrl());
        originApply.setReceiptParkCode(nowApply.getReceiptParkCode());
    }

    @Override
    public void checkCardReissueApplyBeforeCreate(CardReissueApplyDo cardReissueApplyDo) {
        //检查人员状态
        safetyPersonAbility.checkPersonIsActive(cardReissueApplyDo.getSafetyPersonDo());

        //检查属性是否完备
        cardReissueApplyAbility.checkBaseInfoIsComplete(cardReissueApplyDo);

        //检查是否存在其他未完成补卡单
        cardReissueApplyAbility.checkOtherUncompletedApply(cardReissueApplyDo);

        //检查卡状态是否允许创建补卡单
        cardInfoAbility.checkCardStatusIsCanCreateReissueApply(cardReissueApplyDo.getCardInfo());
    }

    @Override
    public void fillCardReissueApplyBeforeAppPageQuery(CardReissueApplyDo cardReissueApplyDo) {
        fillSafetyPersonByUid(cardReissueApplyDo);
    }

    @Override
    public void fillCardReissueApplyForDetail(CardReissueApplyDo cardReissueApplyDo) {
        Boolean isNeedLoadCardInfo = (Boolean) cardReissueApplyDo.getExtField("isNeedLoadCardInfo");
        //填充补卡申请单
        cardReissueApplyAbility.fillCardReissueApplyById(cardReissueApplyDo);

        //填充人员信息
        fillSafetyPersonByUid(cardReissueApplyDo);

        //填充制卡申请单信息
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(cardReissueApplyDo.getCardApplyId());
        cardApplyAbility.fillCardApplyDoById(cardApplyDo);

        //加载工卡信息
        if (isNeedLoadCardInfo) {
            CardInfoDo cardInfoDo = new CardInfoDo();
            cardInfoDo.setCardApplyId(cardReissueApplyDo.getCardApplyId());
            cardInfoAbility.fillCardInfoByApplyId(cardInfoDo);
            cardReissueApplyDo.setCardInfo(cardInfoDo);
        }

        //填充园区信息
        cardApplyAbility.fillParkInfo(cardApplyDo);
        cardReissueApplyDo.setCardApplyDo(cardApplyDo);
    }

    @Override
    public void fillCardReissueApplyBeforeCancel(CardReissueApplyDo cardReissueApplyDo) {
        //填充补卡申请单
        cardReissueApplyAbility.fillCardReissueApplyById(cardReissueApplyDo);

        //加载原实体卡信息
        loadOriginEntityCard(cardReissueApplyDo);

        //加载支付订单
        cardReissueApplyAbility.loadCardReissuePayByReissueApplyId(cardReissueApplyDo);
    }

    private void loadOriginEntityCard(CardReissueApplyDo cardReissueApplyDo) {
        //加载原实体卡信息
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setId(cardReissueApplyDo.getCardId());
        cardInfoAbility.fillCardInfoById(cardInfoDo);
        cardReissueApplyDo.setCardInfo(cardInfoDo);
    }

    @Override
    public void checkCardReissueApplyCanCancel(CardReissueApplyDo cardReissueApplyDo) {
        //检查补卡单状态是否可取消
        cardReissueApplyAbility.checkReissueCardApplyStatusCanCancel(cardReissueApplyDo);
    }

    @Override
    public void fillReissueCardApplyBeforePageQueryForAdmin(CardReissueApplyDo cardReissueApplyDo) {
        //填充人员信息
        fillSafetyPersonByUid(cardReissueApplyDo);

        //填充制卡申请单信息
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(cardReissueApplyDo.getCardApplyId());
        cardApplyAbility.fillCardApplyDoById(cardApplyDo);

        //加载原实体卡信息
        loadOriginEntityCard(cardReissueApplyDo);

        //填充园区信息
        cardApplyAbility.fillParkInfo(cardApplyDo);
        cardReissueApplyDo.setCardApplyDo(cardApplyDo);
    }

    @Override
    public void fillCardReissueApplyBeforeApproval(CardReissueApplyDo cardReissueApplyDo) {
        //填充补卡申请单
        cardReissueApplyAbility.fillCardReissueApplyById(cardReissueApplyDo);
    }

    @Override
    public void fillCardReissueApplyBeforeRefuse(CardReissueApplyDo cardReissueApplyDo) {
        //填充补卡申请单
        cardReissueApplyAbility.fillCardReissueApplyById(cardReissueApplyDo);

        //加载原实体卡信息
        loadOriginEntityCard(cardReissueApplyDo);

        //加载支付订单
        cardReissueApplyAbility.loadCardReissuePayByReissueApplyId(cardReissueApplyDo);

    }

    private void fillSafetyPersonByUid(CardReissueApplyDo cardReissueApplyDo) {
        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardReissueApplyDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        cardReissueApplyDo.setSafetyPersonDo(safetyPersonDo);
    }

    @Override
    public void createPayOrder(CardReissueApplyDo cardReissueApplyDo) {
        //校验补卡申请单状态
        cardReissueApplyAbility.checkBeforePay(cardReissueApplyDo);
        //创建支付订单
        cardReissueApplyAbility.createPayOrder(cardReissueApplyDo);
    }

    @Override
    public void payCompleted(CardReissueApplyDo cardReissueApplyDo) {
        cardReissueApplyAbility.payCompleted(cardReissueApplyDo);
    }

    @Override
    public void payFailed(CardReissueApplyDo cardReissueApplyDo) {
        cardReissueApplyAbility.payFailed(cardReissueApplyDo);
    }

    @Override
    public void payRefund(CardReissueApplyDo cardReissueApplyDo) {
        cardReissueApplyAbility.payRefund(cardReissueApplyDo);
    }

    @Override
    public void refundCompleted(CardReissueApplyDo cardReissueApplyDo) {
        cardReissueApplyAbility.refundCompleted(cardReissueApplyDo);
    }

    @Override
    public void submitRefund(CardReissueApplyDo cardReissueApplyDo) {
        cardReissueApplyAbility.submitRefund(cardReissueApplyDo);
    }

    @Override
    public void checkCardReissueApplyBeforeRefuse(CardReissueApplyDo cardReissueApplyDo) {
        //检查当前状态是否可拒绝
        cardReissueApplyAbility.checkReissueCardApplyStatusCanRefuse(cardReissueApplyDo);
    }

    @Override
    public void checkCardReissueApplyBeforeApproval(CardReissueApplyDo cardReissueApplyDo) {
        //检查当前状态是否通过
        cardReissueApplyAbility.checkReissueCardApplyStatusCanApproval(cardReissueApplyDo);
    }

    @Override
    public void fillCardReissueApplyBeforeOpen(CardReissueApplyDo cardReissueApplyDo) {
        //填充补卡申请单
        CardInfoDo param = cardReissueApplyDo.getCardInfo();
        cardReissueApplyAbility.fillCardReissueApplyById(cardReissueApplyDo);
        cardReissueApplyDo.setCardInfo(param);

        //填充人员信息
        fillSafetyPersonByUid(cardReissueApplyDo);

        //加载原实体卡信息
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardApplyId(cardReissueApplyDo.getCardApplyId());
        cardInfoAbility.fillCardInfoByApplyId(cardInfoDo);

        //复制原卡信息
        CardInfoDo oldCard = new CardInfoDo();
        commonDoConverter.copyCardInfoDo(cardInfoDo, oldCard);
        cardInfoAbility.fillRecyclableCard(oldCard);
        cardReissueApplyDo.setOldCard(oldCard);

        //加载原实体卡权限
        SafetyRightDo safetyRightDo = new SafetyRightDo();
        safetyRightDo.setMediumCode(cardInfoDo.getMediumCode());
        safetyRightDo.setUid(cardInfoDo.getUid());
        List<SafetyRightDo> safetyRightList = safetyRightAbility.getSafetyRightByMediumCodeAndUid(safetyRightDo);

        //初始化原实体卡信息
        initOriginCardInfo(cardInfoDo, cardReissueApplyDo.getCardInfo());

        if (CollectionUtils.isNotEmpty(safetyRightList)) {
            //填装安防载体集信息
            safetyRightAbility.fillSafetyRightWithSafetyCarrierGroupForList(safetyRightList);

            //填装要删除权限组
            cardInfoDo.setRemoveRight(safetyRightList);

            //初始化要新增权限组
            initAddRightList(safetyRightList, cardInfoDo);
        }

        //加载循环卡
        cardInfoAbility.fillRecyclableCard(cardInfoDo);

        //构建新的介质编码
        cardInfoAbility.buildNewSafetyMedium(cardInfoDo);

        //构建新的人介关系
        cardInfoAbility.buildNewSafetyPersonMedium(cardInfoDo);

        //填充卡有效期时间
        cardInfoAbility.buildValidateTime(cardInfoDo);

        cardReissueApplyDo.setCardInfo(cardInfoDo);
    }

    @Override
    public void checkCardReissueApplyBeforeEdit(CardReissueApplyDo cardReissueApplyDo) {

        //校验人员状态
        safetyPersonAbility.checkPersonIsActive(cardReissueApplyDo.getSafetyPersonDo());

        //校验新工卡正式卡是否已存在
        CardInfoDo cardInfo = cardReissueApplyDo.getCardInfo();
        if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfo.getCardType())) {
            cardInfo.putExtField("statusList", CardStatusEnum.usingList());
            cardInfoAbility.checkEmpCardIsExist(cardInfo);
        }

        //检查新卡循环卡是否被用
        recyclableCardAbility.checkRecyclableCard(cardInfo.getRecyclableCardDo());

        //校验使用者人卡是否一致
        CardInfoDo oldCard = cardReissueApplyDo.getOldCard();
        recyclableCardAbility.checkRecyclableCard(oldCard.getRecyclableCardDo());

        //校验卡号是否被占用
        cardInfoAbility.checkCardIsOccupation(cardInfo);
    }

    @Override
    public void openCard(CardReissueApplyDo cardReissueApplyDo) {
        //删除老卡安防权限并更新权限状态
        CardInfoDo oldCard = cardReissueApplyDo.getOldCard();
        if (CollectionUtils.isNotEmpty(cardReissueApplyDo.getCardInfo().getRemoveRight())) {
            SafetyRightDo safetyRightDo = new SafetyRightDo();
            safetyRightDo.setMediumCode(oldCard.getMediumCode());
            safetyRightAbility.removeSafetyRight(safetyRightDo);
        }

        //释放旧卡
        recyclableCardAbility.releaseRecyclableCard(oldCard.getRecyclableCardDo());

        //新卡占用工卡
        CardInfoDo cardInfo = cardReissueApplyDo.getCardInfo();
        recyclableCardAbility.occupyRecyclableCard(cardInfo.getRecyclableCardDo());
    }

    @Override
    public void fillSafetyOpenCardLog(CardReissueApplyDo cardReissueApplyDo) {
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setOperateTypeEnum(LogOperateType.CARD_REPLACEMENT);
        cardReissueApplyDo.putExtField("isAddGroups", true);
        cardReissueApplyDo.setSafetyOperateLog(safetyOperateLogDo);
        //根据电子工卡记录填装对应的安防日志及详情
        cardReissueApplyAbility.fillWithSafetyOperateLog(cardReissueApplyDo);
    }

    private void initAddRightList(List<SafetyRightDo> safetyRightList, CardInfoDo cardInfoDo) {
        List<SafetyRightDo> newSafetyRightList = Lists.newArrayList(safetyRightList);
        newSafetyRightList.forEach(item -> {
            item.setMediumCode(cardInfoDo.getMediumCode());
            item.setSyncStatus(OAUCFCommonConstants.INT_ZERO);
            item.setId(null);
        });
        cardInfoDo.setAddRights(newSafetyRightList);
    }

    private void initOriginCardInfo(CardInfoDo cardInfoDo, CardInfoDo newParam) {
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        if (ObjectUtils.isNotEmpty(newParam.getStartTime()) && !OAUCFCommonConstants.LONG_ZERO.equals(newParam.getStartTime().toEpochSecond())) {
            cardInfoDo.setStartTime(newParam.getStartTime());
        } else {
            cardInfoDo.setStartTime(SafetyConstants.Card.DEFAULT_START_TIME);
        }
        if (ObjectUtils.isNotEmpty(newParam.getEndTime()) && !OAUCFCommonConstants.LONG_ZERO.equals(newParam.getEndTime().toEpochSecond())) {
            cardInfoDo.setEndTime(newParam.getEndTime());
        } else {
            cardInfoDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
        }
        cardInfoDo.setMediumPhysicsCode(newParam.getMediumPhysicsCode());
        cardInfoDo.setMediumEncryptCode(newParam.getMediumEncryptCode());
        cardInfoDo.setNewMediumPhysicsCode(newParam.getMediumPhysicsCode());
        cardInfoDo.setNewMediumEncryptCode(newParam.getMediumEncryptCode());
        cardInfoDo.setCardNum(newParam.getCardNum());
        cardInfoDo.setPrefixEncryptCode(newParam.getPrefixEncryptCode());
        cardInfoDo.setSuffixEncryptCode(newParam.getSuffixEncryptCode());
        cardInfoDo.setBackTime(ZonedDateTimeUtils.getZeroTime());
    }

    @Override
    public void forceDetermine(CardReissueApplyDo cardReissueApplyDo) {
        cardReissueApplyAbility.loadCardReissuePayByReissueApplyId(cardReissueApplyDo);
        cardReissueApplyAbility.forceDetermine(cardReissueApplyDo);
    }
}
