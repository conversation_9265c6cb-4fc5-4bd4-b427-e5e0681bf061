package com.mi.oa.ee.safety.domain.ability.impl;

import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.concurrent.Callable;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;


class CardApplyAbilityImplTest {

    @InjectMocks
    private CardApplyAbilityImpl cardApplyAbility;

    @Mock
    private CardApplyRepository cardApplyRepository;

    @Mock
    private IdmSdk idmSdk;

    @Mock(name = "avatarUrlCache")
    private Cache<String, Object> avatarUrlCache;

    @Mock
    private RedisTemplate redisTemplate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void findAvatarUrlByUidList_WhenCacheHit_ShouldReturnFromCache() throws Exception {
        // 准备测试数据
        List<String> uidList = Lists.newArrayList("uid1", "uid2");
        when(avatarUrlCache.get(eq("uid1"), any(Callable.class))).thenReturn("photo1");
        when(avatarUrlCache.get(eq("uid2"), any(Callable.class))).thenReturn("photo2");

        // 执行测试
        List<CardApplyDo> result = cardApplyAbility.findAvatarUrlByUidList(uidList);

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("uid1", result.get(0).getUid());
        assertEquals("photo1", result.get(0).getPhotoUrl());
        assertEquals("uid2", result.get(1).getUid());
        assertEquals("photo2", result.get(1).getPhotoUrl());
        Mockito.verify(avatarUrlCache, Mockito.times(2)).get(anyString(), any(Callable.class));
    }

    @Test
    void findAvatarUrlByUidList_WhenCacheMiss_ShouldLoadFromRepository() throws Exception {
        // 准备静态mock
        MockedStatic<RedisUtils> mockedStatic = mockStatic(RedisUtils.class);
        // 准备测试数据
        List<String> uidList = Lists.newArrayList("uid1");
        CardApplyDo cardApplyDo = createCardApplyDo("uid1", "photo1");
        when(avatarUrlCache.get(eq("uid1"), any(Callable.class))).thenAnswer(invocation -> {
            Callable<String> loader = invocation.getArgument(1);
            return loader.call();
        });
        when(cardApplyRepository.findAvatarUrlByUid("uid1")).thenReturn(cardApplyDo);
        mockedStatic.when(() -> RedisUtils.get(anyString())).thenReturn(null);
        // 执行测试
        List<CardApplyDo> result = cardApplyAbility.findAvatarUrlByUidList(uidList);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("uid1", result.get(0).getUid());
        assertEquals("photo1", result.get(0).getPhotoUrl());
        Mockito.verify(avatarUrlCache).get(anyString(), any(Callable.class));
        Mockito.verify(cardApplyRepository).findAvatarUrlByUid("uid1");
    }

    @Test
    void findAvatarUrlByUidList_WhenCacheError_ShouldHandleException() throws Exception {
        // 准备测试数据
        List<String> uidList = Lists.newArrayList("uid1");
        when(avatarUrlCache.get(eq("uid1"), any(Callable.class))).thenThrow(new RuntimeException("Cache error"));

        // 执行测试
        List<CardApplyDo> result = cardApplyAbility.findAvatarUrlByUidList(uidList);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("uid1", result.get(0).getUid());
        assertEquals("", result.get(0).getPhotoUrl());
        Mockito.verify(avatarUrlCache).get(anyString(), any(Callable.class));
    }

    @Test
    void findAvatarUrlByUidList_WhenEmptyUid_ShouldSkip() throws Exception {
        // 准备测试数据
        List<String> uidList = Lists.newArrayList("", null, "uid1");
        when(avatarUrlCache.get(eq("uid1"), any(Callable.class))).thenReturn("photo1");

        // 执行测试
        List<CardApplyDo> result = cardApplyAbility.findAvatarUrlByUidList(uidList);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("uid1", result.get(0).getUid());
        assertEquals("photo1", result.get(0).getPhotoUrl());
        Mockito.verify(avatarUrlCache, Mockito.times(1)).get(anyString(), any(Callable.class));
    }

    private CardApplyDo createCardApplyDo(String uid, String photoUrl) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setUid(uid);
        cardApplyDo.setPhotoUrl(photoUrl);
        return cardApplyDo;
    }
} 