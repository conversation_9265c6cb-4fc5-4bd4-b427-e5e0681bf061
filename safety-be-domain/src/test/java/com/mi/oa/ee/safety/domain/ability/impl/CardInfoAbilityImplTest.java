package com.mi.oa.ee.safety.domain.ability.impl;

import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

class CardInfoAbilityImplTest {

    @InjectMocks
    private CardInfoAbilityImpl cardInfoAbility;

    @Mock
    private CardInfoRepository cardInfoRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void checkIsOccupation_WhenPhysicsCodeEmpty_ShouldNotThrowException() {
        // 准备测试数据
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setMediumPhysicsCode("");

        // 执行测试并验证
        assertDoesNotThrow(() -> cardInfoAbility.checkIsOccupation(cardInfoDo));
    }

    @Test
    void checkIsOccupation_WhenPhysicsCodeExistsAndSameUid_ShouldNotThrowException() {
        // 准备测试数据
        String uid = "testUid";
        String physicsCode = "testPhysicsCode";
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setUid(uid);
        cardInfoDo.setMediumPhysicsCode(physicsCode);

        CardInfoDo existingCard = new CardInfoDo();
        existingCard.setUid(uid);
        existingCard.setMediumPhysicsCode(physicsCode);
        when(cardInfoRepository.findCardInfoByPhysicsCode(physicsCode)).thenReturn(existingCard);

        // 执行测试并验证
        assertDoesNotThrow(() -> cardInfoAbility.checkIsOccupation(cardInfoDo));
    }

    @Test
    void checkIsOccupation_WhenPhysicsCodeExistsAndDifferentUid_ShouldThrowException() {
        // 准备测试数据
        String uid = "testUid";
        String differentUid = "differentUid";
        String physicsCode = "testPhysicsCode";
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setUid(uid);
        cardInfoDo.setMediumPhysicsCode(physicsCode);

        CardInfoDo existingCard = new CardInfoDo();
        existingCard.setUid(differentUid);
        existingCard.setMediumPhysicsCode(physicsCode);
        when(cardInfoRepository.findCardInfoByPhysicsCode(physicsCode)).thenReturn(existingCard);

        // 执行测试
        BizException exception = assertThrows(BizException.class, 
            () -> cardInfoAbility.checkIsOccupation(cardInfoDo));

        // 验证异常
        assertEquals(CardInfoDomainErrorCodeEnum.CURRENT_CARD_OCCUPIED, 
            exception.getErrCode());
    }

    @Test
    void checkIsOccupation_WhenPhysicsCodeNotExists_ShouldNotThrowException() {
        // 准备测试数据
        String physicsCode = "testPhysicsCode";
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setMediumPhysicsCode(physicsCode);

        when(cardInfoRepository.findCardInfoByPhysicsCode(physicsCode)).thenReturn(null);

        // 执行测试并验证
        assertDoesNotThrow(() -> cardInfoAbility.checkIsOccupation(cardInfoDo));
    }
} 