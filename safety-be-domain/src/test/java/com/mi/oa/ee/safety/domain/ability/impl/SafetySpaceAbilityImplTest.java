package com.mi.oa.ee.safety.domain.ability.impl;

import com.mi.oa.ee.safety.common.dto.AddressInfoDto;
import com.mi.oa.ee.safety.domain.ability.SafetySpaceAbility;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.SafetySpaceDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.SafetySpaceDo;
import com.mi.oa.ee.safety.infra.remote.converter.SpaceConverter;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SafetySpaceAbilityImplTest {

    @InjectMocks
    private SafetySpaceAbilityImpl safetySpaceAbility;

    @Mock
    private AddressSdk addressSdk;

    @Mock
    private CommonDoConverter commonDoConverter;

    @Mock
    private SpaceConverter spaceConverter;

    private SafetySpaceDo safetySpaceDo;

    @BeforeEach
    void setUp() {
        safetySpaceDo = new SafetySpaceDo();
        safetySpaceDo.setAddrId("123");
        safetySpaceDo.setAddrName("北京市");
        safetySpaceDo.setCountryId(AddressSdk.CHINA);
    }

    @Test
    void fillAddressInfoByOldAddressName_WithEmptyAddressName_ShouldThrowException() {
        // 设置空的地址名称
        safetySpaceDo.setAddrName("");

        // 执行测试并验证异常
        BizException exception = assertThrows(BizException.class, () -> 
            safetySpaceAbility.fillAddressInfoByOldAddressName(safetySpaceDo)
        );

        assertEquals(SafetySpaceDomainErrorCodeEnum.ADDRESS_NAME_IS_EMPTY, exception.getErrCode());
        verify(addressSdk, never()).getAddressByName(any(), any());
    }

    @Test
    void fillAddressInfoByOldAddressName_WithHongKongAddress_ShouldProcessCorrectly() {
        // 设置香港地址
        safetySpaceDo.setAddrName("香港中环");
        safetySpaceDo.setCountryId(AddressSdk.CHINA);

        // 模拟地址查询返回
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        addressInfoDto.setAddrId("123");
        addressInfoDto.setAddrName("北京市");
        addressInfoDto.setParentId("456");
        addressInfoDto.setCountryId("3385");
        when(addressSdk.getAddressByName("香港", "3385")).thenReturn(addressInfoDto);

        // 执行测试
        safetySpaceAbility.fillAddressInfoByOldAddressName(safetySpaceDo);

        // 验证结果
        assertEquals("123", safetySpaceDo.getAddrId());
        verify(commonDoConverter).copyAddressInfoDto(addressInfoDto, safetySpaceDo);
    }

    @Test
    void fillAddressInfoByOldAddressName_WithTaipeiAddress_ShouldProcessCorrectly() {
        // 设置台北地址
        safetySpaceDo.setAddrName("台北市信义区");
        safetySpaceDo.setCountryId(AddressSdk.CHINA);

        // 模拟地址查询返回
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        addressInfoDto.setAddrId("123");
        addressInfoDto.setAddrName("北京市");
        addressInfoDto.setParentId("456");
        addressInfoDto.setCountryId("3386");
        when(addressSdk.getAddressByName("台北市", "3386")).thenReturn(addressInfoDto);

        // 执行测试
        safetySpaceAbility.fillAddressInfoByOldAddressName(safetySpaceDo);

        // 验证结果
        assertEquals("123", safetySpaceDo.getAddrId());
        verify(commonDoConverter).copyAddressInfoDto(addressInfoDto, safetySpaceDo);
    }


    @Test
    void fillAddressInfoByOldAddressName_WithNullAddressInfo_ShouldReturnWithoutUpdate() {
        // 设置地址
        safetySpaceDo.setAddrName("北京");
        safetySpaceDo.setCountryId(AddressSdk.CHINA);

        // 模拟地址查询返回 null
        when(addressSdk.getAddressByName("北京市", AddressSdk.CHINA)).thenReturn(null);

        // 执行测试
        safetySpaceAbility.fillAddressInfoByOldAddressName(safetySpaceDo);

        // 验证结果
        assertEquals("123", safetySpaceDo.getAddrId());
        assertEquals("北京", safetySpaceDo.getAddrName());
        verify(commonDoConverter, never()).copyAddressInfoDto(any(), any());
    }

} 