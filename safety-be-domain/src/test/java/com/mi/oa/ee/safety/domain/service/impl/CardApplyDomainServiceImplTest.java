package com.mi.oa.ee.safety.domain.service.impl;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.domain.ability.CardApplyAbility;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

class CardApplyDomainServiceImplTest {

    @InjectMocks
    private CardApplyDomainServiceImpl cardApplyDomainService;

    @Mock
    private CardApplyAbility cardApplyAbility;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void findAvatarUrlByUserNameOrUidList_WhenUidListNotEmpty_ShouldCallFindAvatarUrlByUidList() {
        // 准备测试数据
        CardApplyDo cardApplyDo = new CardApplyDo();
        List<String> uidList = Lists.newArrayList("uid1", "uid2");
        cardApplyDo.putExtField("uidList", uidList);
        cardApplyDo.putExtField("userNameList", null);

        List<CardApplyDo> expectedResult = Lists.newArrayList(
            createCardApplyDo("uid1", "photo1"),
            createCardApplyDo("uid2", "photo2")
        );
        when(cardApplyAbility.findAvatarUrlByUidList(uidList)).thenReturn(expectedResult);

        // 执行测试
        List<CardApplyDo> result = cardApplyDomainService.findAvatarUrlByUserNameOrUidList(cardApplyDo);

        // 验证结果
        assertEquals(expectedResult, result);
        verify(cardApplyAbility).findAvatarUrlByUidList(uidList);
        verify(cardApplyAbility, never()).findAvatarUrlByUserNameList(anyList());
    }

    @Test
    void findAvatarUrlByUserNameOrUidList_WhenUidListEmptyAndUserNameListNotEmpty_ShouldCallFindAvatarUrlByUserNameList() {
        // 准备测试数据
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.putExtField("uidList", null);
        List<String> userNameList = Lists.newArrayList("user1", "user2");
        cardApplyDo.putExtField("userNameList", userNameList);

        List<CardApplyDo> expectedResult = Lists.newArrayList(
            createCardApplyDo("user1", "photo1"),
            createCardApplyDo("user2", "photo2")
        );
        when(cardApplyAbility.findAvatarUrlByUserNameList(userNameList)).thenReturn(expectedResult);

        // 执行测试
        List<CardApplyDo> result = cardApplyDomainService.findAvatarUrlByUserNameOrUidList(cardApplyDo);

        // 验证结果
        assertEquals(expectedResult, result);
        verify(cardApplyAbility, never()).findAvatarUrlByUidList(anyList());
        verify(cardApplyAbility).findAvatarUrlByUserNameList(userNameList);
    }

    @Test
    void findAvatarUrlByUserNameOrUidList_WhenBothListsEmpty_ShouldReturnEmptyList() {
        // 准备测试数据
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.putExtField("uidList", null);
        cardApplyDo.putExtField("userNameList", null);

        // 执行测试
        List<CardApplyDo> result = cardApplyDomainService.findAvatarUrlByUserNameOrUidList(cardApplyDo);

        // 验证结果
        assertTrue(result.isEmpty());
        verify(cardApplyAbility, never()).findAvatarUrlByUidList(anyList());
        verify(cardApplyAbility, never()).findAvatarUrlByUserNameList(anyList());
    }

    private CardApplyDo createCardApplyDo(String uid, String photoUrl) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setUid(uid);
        cardApplyDo.setPhotoUrl(photoUrl);
        return cardApplyDo;
    }
} 