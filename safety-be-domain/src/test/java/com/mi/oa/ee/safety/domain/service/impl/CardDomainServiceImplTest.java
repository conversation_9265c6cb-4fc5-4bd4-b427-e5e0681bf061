package com.mi.oa.ee.safety.domain.service.impl;

import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;

import com.mi.oa.ee.safety.domain.ability.*;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.RecyclableCardDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogRepository;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class CardDomainServiceImplTest {

    @InjectMocks
    private CardDomainServiceImpl cardDomainService;

    @Mock
    private CardInfoAbility cardInfoAbility;

    @Mock
    private RecyclableCardAbility recyclableCardAbility;

    @Mock
    private SafetyMediumAbility safetyMediumAbility;

    @Mock
    private SafetyOperateLogRepository safetyOperateLogRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void openCard_WhenEmployeeCard_ShouldSaveWithSuccessSyncStatus() {
        // 准备测试数据
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        cardInfoDo.setUid("testUid");
        cardInfoDo.setMediumPhysicsCode("testPhysicsCode");

        RecyclableCardDo recyclableCardDo = new RecyclableCardDo();
        cardInfoDo.setRecyclableCardDo(recyclableCardDo);

        SafetyMediumDo safetyMediumDo = new SafetyMediumDo();
        safetyMediumDo.setId(1L);
        when(cardInfoAbility.fillMedium(any())).thenReturn(safetyMediumDo);
        when(cardInfoAbility.saveMedium(any())).thenReturn(safetyMediumDo);

        SafetyPersonMediumDo safetyPersonMediumDo = new SafetyPersonMediumDo();
        safetyPersonMediumDo.setId(2L);
        when(safetyMediumAbility.fillPersonMediumByCard(any(), eq(false), eq(true))).thenReturn(safetyPersonMediumDo);
        when(safetyMediumAbility.savePersonMedium(any(SafetyPersonMediumDo.class))).thenReturn(safetyPersonMediumDo);

        // 执行测试
        assertDoesNotThrow(() -> cardDomainService.openCard(cardInfoDo));

        // 验证调用
        verify(cardInfoAbility).fillRecyclableCard(cardInfoDo);
        verify(recyclableCardAbility).checkRecyclableCard(recyclableCardDo);
        verify(cardInfoAbility).checkCardIsOccupation(cardInfoDo);
        verify(cardInfoAbility).save(cardInfoDo);
        verify(safetyMediumAbility).savePersonMedium(any(SafetyPersonMediumDo.class));
        verify(safetyOperateLogRepository).saveOrUpdate(any());
        verify(recyclableCardAbility).occupyRecyclableCard(recyclableCardDo);
    }

    @Test
    void openCard_WhenNonEmployeeCard_ShouldSaveWithoutSyncStatus() {
        // 准备测试数据
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardType(CardTypeEnum.TEMP_CARD.getNumber());
        cardInfoDo.setUid("testUid");
        cardInfoDo.setMediumPhysicsCode("testPhysicsCode");

        RecyclableCardDo recyclableCardDo = new RecyclableCardDo();
        cardInfoDo.setRecyclableCardDo(recyclableCardDo);

        SafetyMediumDo safetyMediumDo = new SafetyMediumDo();
        safetyMediumDo.setId(1L);
        when(cardInfoAbility.fillMedium(any())).thenReturn(safetyMediumDo);
        when(cardInfoAbility.saveMedium(any())).thenReturn(safetyMediumDo);

        SafetyPersonMediumDo safetyPersonMediumDo = new SafetyPersonMediumDo();
        safetyPersonMediumDo.setId(2L);
        when(safetyMediumAbility.fillPersonMediumByCard(any(), eq(false), eq(true))).thenReturn(safetyPersonMediumDo);
        when(safetyMediumAbility.savePersonMedium(any(SafetyPersonMediumDo.class))).thenReturn(safetyPersonMediumDo);

        // 执行测试
        assertDoesNotThrow(() -> cardDomainService.openCard(cardInfoDo));

        // 验证调用
        verify(safetyMediumAbility).savePersonMedium(any(SafetyPersonMediumDo.class));
        verify(safetyOperateLogRepository).saveOrUpdate(any());
        verify(recyclableCardAbility).occupyRecyclableCard(recyclableCardDo);
        verify(cardInfoAbility).fillRecyclableCard(cardInfoDo);
        verify(recyclableCardAbility).checkRecyclableCard(recyclableCardDo);
        verify(cardInfoAbility).checkCardIsOccupation(cardInfoDo);
        verify(cardInfoAbility).save(cardInfoDo);
    }

    @Test
    void openCard_WhenRecyclableCardCheckFails_ShouldThrowException() {
        // 准备测试数据
        CardInfoDo cardInfoDo = new CardInfoDo();
        RecyclableCardDo recyclableCardDo = new RecyclableCardDo();
        cardInfoDo.setRecyclableCardDo(recyclableCardDo);

        doThrow(new BizException(RecyclableCardDomainErrorCodeEnum.CARD_STATUS_ERROR))
            .when(recyclableCardAbility).checkRecyclableCard(any());

        // 执行测试并验证异常
        assertThrows(BizException.class, () -> cardDomainService.openCard(cardInfoDo));

        // 验证其他方法没有被调用
        verify(cardInfoAbility, never()).checkCardIsOccupation(any());
        verify(cardInfoAbility, never()).save(any());
        verify(safetyMediumAbility, never()).savePersonMedium(any(SafetyPersonMediumDo.class));
        verify(safetyOperateLogRepository, never()).saveOrUpdate(any());
        verify(recyclableCardAbility, never()).occupyRecyclableCard(any());
    }

    @Test
    void openCard_WhenCardIsOccupied_ShouldThrowException() {
        // 准备测试数据
        CardInfoDo cardInfoDo = new CardInfoDo();
        RecyclableCardDo recyclableCardDo = new RecyclableCardDo();
        cardInfoDo.setRecyclableCardDo(recyclableCardDo);

        doThrow(new BizException(CardInfoDomainErrorCodeEnum.CARD_IS_EXIST_IN_OLD_CARD))
            .when(cardInfoAbility).checkCardIsOccupation(any());

        // 执行测试并验证异常
        assertThrows(BizException.class, () -> cardDomainService.openCard(cardInfoDo));

        // 验证其他方法没有被调用
        verify(cardInfoAbility, never()).save(any());
        verify(safetyMediumAbility, never()).savePersonMedium(any(SafetyPersonMediumDo.class));
        verify(safetyOperateLogRepository, never()).saveOrUpdate(any());
        verify(recyclableCardAbility, never()).occupyRecyclableCard(any());
    }
} 