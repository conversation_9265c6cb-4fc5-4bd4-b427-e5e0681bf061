package com.mi.oa.ee.safety.infra.remote.converter;

import cn.hutool.core.util.ObjectUtil;
import com.mi.oa.ee.safety.common.dto.AccountModel;
import com.mi.oa.ee.safety.common.dto.DeptDto;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel.PersonExpandInfo;
import com.mi.oa.ee.safety.common.dto.SafetyZoneDto;
import com.mi.oa.ee.safety.common.enums.AccountTypeEnum;
import com.mi.oa.ee.safety.common.enums.SafetyAccountStatusEnum;
import com.mi.oa.ee.safety.common.enums.SafetyPersonStatusEnum;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.model.VisitorApplyVisitUserInfoDo;
import com.mi.oa.ee.safety.infra.remote.model.IdmUserModel;
import com.mi.oa.ee.safety.infra.remote.model.IdmUserV1Model;
import com.mi.oa.infra.oaucf.idm.api.rep.AccountDo;
import com.mi.oa.infra.oaucf.idm.api.rep.AccountInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.EsAccountDto;
import com.mi.oa.infra.oaucf.idm.api.rep.UserInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.UserUniqueDto;
import com.mi.oa.infra.oaucf.idm.api.rep.ZoneDto;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.organization.enums.AccountStatusEnum;
import com.mi.oa.infra.organization.enums.PersonStatusEnum;
import com.mi.oa.infra.organization.enums.PersonTypeEnum;
import com.mi.oa.infra.organization.enums.PersonUniqueEnum;
import com.mi.oa.infra.organization.rep.OrgPathVO;
import com.mi.oa.infra.organization.rep.OrgVO;
import com.mi.oa.infra.organization.rep.PersonAccountDTO;
import com.mi.oa.infra.organization.rep.PersonResp;
import com.mi.oa.infra.organization.rep.RelationTypePathVO;
import com.mi.oa.infra.organization.req.PersonEntryReq;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 账号模型转换
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/8/21 11:40
 */

@Mapper(componentModel = "spring")
public interface PersonModelConverter {

    List<SafetyZoneDto> dtoListToSafetyZoneDtoList(List<ZoneDto> zoneDtoList);

    /**
     * @param accountInfoDto
     * @return com.mi.oa.ee.safety.common.dto.AccountModel
     * @desc dto转模型
     * <AUTHOR> denghui
     * @date 2022/8/21 11:47
     */
    @Mapping(source = "accountStatus", target = "statusEnum", qualifiedByName = "convertStatusEnum")
    AccountModel dtoToModel(AccountInfoDto accountInfoDto);

    @Named("convertStatusEnum")
    default SafetyAccountStatusEnum convertStatusEnum(com.mi.oa.infra.oaucf.idm.api.enums.AccountStatusEnum accountStatusEnum) {
        return SafetyAccountStatusEnum.getStatusEnum(accountStatusEnum.getValue());
    }

    List<AccountModel> dtoToModelList(List<AccountInfoDto> accountInfoDtos);

    default AccountModel doToModel(AccountDo accountDo) {
        AccountModel accountModel = new AccountModel();
        accountModel.setAccountName(accountDo.getUserName());
        accountModel.setMobile(accountDo.getMobile());
        accountModel.setDisplayName(accountDo.getDisplayName());
        accountModel.setUid(accountDo.getUid());
        accountModel.setPersonalEmail(accountDo.getEmail());
        return accountModel;
    }

    IdmUserModel userToModel(UserUniqueDto userUniqueDto);

    IdmUserV1Model userToModel(UserInfoDto userInfoDto);

    @Named("mapToExpandInfo")
    default PersonExpandInfo mapToExpandInfo(Map<String, Object> expandInfo) {
        if (MapUtils.isEmpty(expandInfo)) {
            return new PersonExpandInfo();
        }
        PersonExpandInfo personExpandInfo = GsonUtils.convert(expandInfo, PersonExpandInfo.class);
        if (Objects.nonNull(expandInfo.get("onboardingAddressId"))) {
            personExpandInfo.setReportAddressId((String) expandInfo.get("onboardingAddressId"));
        }
        return personExpandInfo;
    }

    @Mapping(target = "companyName", source = "personBasicInfo.companyDescr")
    @Mapping(target = "country", source = "personBasicInfo.nationality")
    @Mapping(target = "expandInfo", source = "expandInfo", qualifiedByName = "mapToExpandInfo")
    @Mapping(target = "lastName", source = "personBasicInfo.lastName")
    @Mapping(target = "firstName", source = "personBasicInfo.firstName")
    @Mapping(target = "lastNameEn", source = "personBasicInfo.lastNameEn")
    @Mapping(target = "firstNameEn", source = "personBasicInfo.firstNameEn")
    @Mapping(target = "startTime", source = "personTime.expectedActivateTime")
    @Mapping(target = "managerUid", source = "personReportLineDto.reportPersonUid")
    @Mapping(target = "fullName", source = "personBasicInfo.name")
    @Mapping(target = "sex", source = "personBasicInfo.sex")
    @Mapping(target = "pinyinName", source = "personBasicInfo.namePinyin")
    @Mapping(target = "displayName", source = "personBasicInfo.displayName")
    @Mapping(target = "name", source = "personBasicInfo.name")
    @Mapping(target = "zoneCode", source = "personOrgUnique.zoneCode")
    @Mapping(target = "mobile", source = "personOrgUnique.mobile")
    @Mapping(target = "personalEmail", source = "personOrgUnique.personEmail")
    @Mapping(target = "email", source = "personOrgUnique.email")
    @Mapping(target = "employeeId", source = "personOrgUnique.externalId")
    @Mapping(target = "idCardNumber", source = "personOrgUnique.idCardNumber")
    @Mapping(target = "accountType", source = "personTypeEnum", qualifiedByName = "converterPersonTypeEnum")
    @Mapping(target = "accountStatus", source = "personAccount", qualifiedByName = "converterSafetyAccountStatusEnum")
    @Mapping(target = "expireTime", source = "personTime.expireTime")
    @Mapping(target = "accountOwner", source = "personReportLineDto.reportAccountName")
    @Mapping(target = "personStatus", source = "personStatusEnum", qualifiedByName = "converterPersonStatusEnum")
    @Mapping(target = "uid", source = "personId")
    @Mapping(target = "hireDate", source = "personTime.entryTime")
    @Mapping(target = "depts", source = "orgInfoList", qualifiedByName = "deptOrgListConverter")
    @Mapping(target = "locationCode", source = "personBasicInfo.city")
    @Mapping(target = "accountName", source = "personAccount", qualifiedByName = "getAccountName")
    @Mapping(target = "deptId", source = "personBasicInfo.deptId")
    PersonInfoModel toPersonModel(PersonResp person);

    default SafetyAccountStatusEnum converterSafetyAccountStatusEnum(PersonAccountDTO personAccount) {
        SafetyAccountStatusEnum safetyAccountStatusEnum = null;
        if (ObjectUtil.isNotEmpty(personAccount.getAccountStatusEnum())) {
            safetyAccountStatusEnum = SafetyAccountStatusEnum.getStatusEnum(Integer.valueOf(personAccount.getAccountStatusEnum().getValue()));
        }
        return safetyAccountStatusEnum;
    }

    default String getAccountName(PersonAccountDTO personAccount) {
        if (ObjectUtil.isNotEmpty(personAccount.getAccountStatusEnum()) &&
                (AccountStatusEnum.PRE.getValue().equals(personAccount.getAccountStatusEnum().getValue()) ||
                        AccountStatusEnum.ENABLE.getValue().equals(personAccount.getAccountStatusEnum().getValue()) ||
                        AccountStatusEnum.DISABLE.getValue().equals(personAccount.getAccountStatusEnum().getValue()) ||
                        AccountStatusEnum.CLOSE.getValue().equals(personAccount.getAccountStatusEnum().getValue())
                )) {
            return personAccount.getAccountName();
        }
        return null;
    }

    @Named("deptOrgListConverter")
    default List<DeptDto> deptOrgListConverter(List<OrgVO> orgInfoList) {
        if (CollectionUtils.isNotEmpty(orgInfoList)) {
            OrgVO orgVO = orgInfoList.get(0);
            List<RelationTypePathVO> pathVOS = orgVO.getPathVOS();
            if (CollectionUtils.isNotEmpty(pathVOS)) {
                List<OrgPathVO> pathList = pathVOS.get(0).getPathList();
                return this.converterListDept(pathList);
            }
        }
        return null;
    }

    @Mapping(target = "orgTreeCode", constant = "VISITOR")
    @Mapping(target = "entryTypeEnum", constant = "DIRECT")
    @Mapping(target = "personTypeEnum", constant = "EXTERNAL")
    @Mapping(target = "zoneCode", source = "infoDo", qualifiedByName = "fillZoneCode")
    @Mapping(target = "mobile", source = "phone", qualifiedByName = "decryptPhone")
    @Mapping(target = "personEmail", source = "email", qualifiedByName = "emptyIgnore")
    @Mapping(target = "email", ignore = true)
    @Mapping(target = "personUniqueEnum", source = "infoDo", qualifiedByName = "personUniqueEnum")
    @Mapping(target = "personBasicInfoDto.name", source = "name")
    PersonEntryReq toPersonReq(VisitorApplyVisitUserInfoDo infoDo);

    @Named("fillZoneCode")
    default String fillZoneCode(VisitorApplyVisitUserInfoDo infoDo) {
        if (ObjectUtil.isNotEmpty(infoDo.getPhone())) {
            return "+86";
        }

        return null;
    }

    @Named("emptyIgnore")
    default String emptyIgnore(String str) {
        return ObjectUtil.isEmpty(str) ? null : str;
    }

    @Named("decryptPhone")
    default String decryptPhone(String encryptedPhone) {
        return emptyIgnore(CodeUtils.encryptOrDecryptPhone(encryptedPhone, false));
    }

    @Named("personUniqueEnum")
    default PersonUniqueEnum personUniqueEnum(VisitorApplyVisitUserInfoDo infoDo) {
        if (ObjectUtil.isNotEmpty(infoDo.getEmail())) {
            return PersonUniqueEnum.PERSONAL_EMAIL;
        }

        return PersonUniqueEnum.MOBILE;
    }

    List<DeptDto> converterListDept(List<OrgPathVO> pathList);

    @Mapping(target = "deptEnName", source = "orgNameEn")
    @Mapping(target = "deptName", source = "orgName")
    @Mapping(target = "deptId", source = "orgCode")
    DeptDto convertPersonDept(OrgPathVO orgPathVO);

    @Named("converterPersonTypeEnum")
    default AccountTypeEnum converterPersonTypeEnum(PersonTypeEnum personTypeEnum) {
        return AccountTypeEnum.getEnumByCode(Integer.valueOf(personTypeEnum.getCode()));
    }

    @Named("converterPersonStatusEnum")
    default SafetyPersonStatusEnum converterPersonStatusEnum(PersonStatusEnum personStatusEnum) {
        return SafetyPersonStatusEnum.getByCode(personStatusEnum.getCode());
    }

    @Named("convertAccountTypeEnum")
    default AccountTypeEnum convertAccountTypeEnum(String type) {
        return AccountTypeEnum.ofDesc(type);
    }

    @Mapping(source = "name", target = "fullName")
    @Mapping(source = "userName", target = "accountName")
    @Mapping(source = "type", target = "accountType", qualifiedByName = "convertAccountTypeEnum")
    AccountModel esAccountDtoToModel(EsAccountDto esAccountDto);

    List<AccountModel> esAccountDtoToModelList(List<EsAccountDto> data);

}
