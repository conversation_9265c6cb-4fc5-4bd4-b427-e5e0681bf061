package com.mi.oa.ee.safety.infra.remote.sdk;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.dto.DeptDto;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.enums.visitor.OACacheKeyEnum;
import com.mi.oa.ee.safety.infra.errorcode.HrodErrorCodeEnum;
import com.mi.oa.ee.safety.infra.errorcode.InfraErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.converter.PsSdkConverter;
import com.mi.oa.ee.safety.infra.remote.model.PsHireAddressDto;
import com.mi.oa.ee.safety.infra.remote.model.PsLocationCityDto;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import com.mi.oa.infra.ps.rep.HrodHireLocation;
import com.mi.oa.infra.ps.req.HrodHireLocationReq;
import com.mi.oa.infra.ps.req.ReportLeaveReq;
import com.mi.oa.infra.ps.service.HrodHireService;
import com.mi.oa.infra.ps.service.PsService;
import com.xiaomi.oa.hr.hrod.boot.request.HpcConditionByOprid;
import com.xiaomi.oa.hr.hrod.boot.request.HpcConditionByOprids;
import com.xiaomi.oa.hr.hrod.boot.request.HpcConditionDepartment;
import com.xiaomi.oa.hr.hrod.boot.request.HpcDepartmentFuzzyQueryCondition;
import com.xiaomi.oa.hr.hrod.boot.request.HpcLocationCityCondition;
import com.xiaomi.oa.hr.hrod.boot.request.HpcLocationHireCondition;
import com.xiaomi.oa.hr.hrod.boot.response.HpcDepartmentBasis;
import com.xiaomi.oa.hr.hrod.boot.response.HpcDepartmentFuzzyQuery;
import com.xiaomi.oa.hr.hrod.boot.response.HpcEmployeeBasis;
import com.xiaomi.oa.hr.hrod.boot.response.HpcLocationCity;
import com.xiaomi.oa.hr.hrod.boot.response.HpcLocationHire;
import com.xiaomi.oa.hr.hrod.boot.response.HrodResp;
import com.xiaomi.oa.hr.hrod.boot.service.HpcDepartmentService;
import com.xiaomi.oa.hr.hrod.boot.service.HpcEmployeeService;
import com.xiaomi.oa.hr.hrod.boot.service.HpcLocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/22 14:44
 */
@Component
@Slf4j
public class HrodSdk {

    @Resource
    private HpcDepartmentService hpcDepartmentService;

    @Resource
    private HpcEmployeeService hpcEmployeeService;

    @Resource
    private HpcLocationService hpcLocationService;

    @Resource
    private HrodHireService hrodHireService;

    @Resource
    private PsService psService;

    @Resource
    private IdmRemote idmRemote;

    @Resource
    private PsSdkConverter psSdkConverter;

    public List<DeptDto> getDepartsByParentCode(String parentNode) {
        if (StringUtils.isEmpty(parentNode)) {
            parentNode = "MI";
        }
        HpcConditionDepartment department = new HpcConditionDepartment();
        department.setPageNum(OAUCFCommonConstants.INT_ONE);
        department.setPageSize(OAUCFCommonConstants.INT_TWENTY);
        department.setParentNodeName(parentNode);
        List<DeptDto> departs = Lists.newArrayList();
        try {
            HrodResp<List<HpcDepartmentBasis>> departments = hpcDepartmentService.getDepartments(department);
            List<HpcDepartmentBasis> departmentBasisList = departments.getBody().getData();
            for (HpcDepartmentBasis departmentBasis : departmentBasisList) {
                DeptDto dept = new DeptDto();
                dept.setDeptId(departmentBasis.getDeptid());
                dept.setDeptName(departmentBasis.getDescr());
                dept.setLevel(departmentBasis.getTreeLevelNum());
                departs.add(dept);
            }
            return departs;
        } catch (Exception e) {
            throw new BizException(HrodErrorCodeEnum.GET_DEPARTS_ERROR);
        }
    }

    /**
     * @param deptName
     * @return java.util.List<com.xiaomi.oa.hr.hrod.boot.response.HpcDepartmentFuzzyQuery>
     * @desc 模糊搜索部门
     * <AUTHOR> denghui
     * @date 2022/12/6 21:16
     */
    public List<DeptDto> getDeptPathByDeptName(String deptName) {
        HpcDepartmentFuzzyQueryCondition condition = new HpcDepartmentFuzzyQueryCondition();
        condition.setDept_name(deptName);
        condition.setPageNum(OAUCFCommonConstants.INT_ONE);
        condition.setPageSize(OAUCFCommonConstants.INT_TEN);
        try {
            HrodResp<List<HpcDepartmentFuzzyQuery>> resp = hpcDepartmentService.getDeptPathByDeptName(condition);
            List<DeptDto> deptDtos = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(resp.getBody().getData())) {
                resp.getBody().getData().forEach(item -> {
                    DeptDto deptDto = new DeptDto();
                    deptDto.setDeptId(item.getDeptid());
                    deptDto.setDeptName(item.getDeptName());
                    deptDto.setDeptIdPath(item.getDeptIdPath());
                    deptDto.setDeptNamePath(item.getDeptNamePath());
                    deptDtos.add(deptDto);
                });
            }
            return deptDtos;
        } catch (Exception e) {
            throw new BizException(HrodErrorCodeEnum.GET_DEPARTS_ERROR);
        }
    }

    /**
     * @param accountName
     * @return com.mi.oa.ee.safety.common.dto.PersonInfoModel
     * @desc 人事获取工作地(暂没用)
     * <AUTHOR> denghui
     * @date 2023/8/10 15:32
     */
    public PersonInfoModel getWorkAddress(String accountName) {
        HpcConditionByOprid hpcConditionByOprid = new HpcConditionByOprid();
        hpcConditionByOprid.setOprid(accountName);
        try {
            HrodResp<HpcEmployeeBasis> resp = hpcEmployeeService.getEmployeeByOprid(hpcConditionByOprid);
            if ("200".equals(resp.getHeader().getCode())) {
                PersonInfoModel personInfoModel = new PersonInfoModel();
                personInfoModel.setWorkAddress(resp.getBody().getData().getLocationDescr());
                personInfoModel.setLocationCode(resp.getBody().getData().getLocation());
                return personInfoModel;
            }
        } catch (Exception e) {
            log.error("getWorkAddress by account:{} not find", accountName);
        }
        return null;
    }

    /**
     * @param accountNames
     * @return java.util.List<com.mi.oa.ee.safety.common.dto.PersonInfoModel>
     * @desc 获取离职日期
     * <AUTHOR> denghui
     * @date 2023/8/10 15:33
     */
    public List<PersonInfoModel> getLeaveDate(List<String> accountNames) {
        if (!CollectionUtils.isEmpty(accountNames)) {
            HpcConditionByOprids hpcConditionByOprids = new HpcConditionByOprids();
            String accounts = accountNames.stream().collect(Collectors.joining(","));
            hpcConditionByOprids.setOprids(accounts);
            try {
                HrodResp<List<HpcEmployeeBasis>> resp = hpcEmployeeService.getEmployeeListByOprid(hpcConditionByOprids);
                if ("200".equals(resp.getHeader().getCode())) {
                    List<PersonInfoModel> personInfoModelList = Lists.newArrayList();
                    resp.getBody().getData().forEach(item -> {
                        PersonInfoModel personInfoModel = new PersonInfoModel();
                        if (!StringUtils.isEmpty(item.getTerminationDt())) {
                            personInfoModel.setLeaveDate(ZonedDateTimeUtils.toZonedDateTime(item.getTerminationDt()));
                        }
                        if (!ObjectUtils.isEmpty(item.getActionDtSs())) {
                            personInfoModel.setPreLeaveDate(ZonedDateTimeUtils.dateToZonedDateTime(item.getActionDtSs()));
                        }
                        personInfoModel.setAccountName(item.getOprid());
                        personInfoModelList.add(personInfoModel);
                    });
                    return personInfoModelList;
                }
            } catch (Exception e) {
                log.error("getLeaveDateList by account:{} not find", accountNames);
            }
        }
        return Lists.newArrayList();
    }

    /**
     * @param employeeNo
     * @param leaveSerial
     * @return java.lang.Boolean
     * @desc 完成人事 离职工卡归还节点
     * <AUTHOR> denghui
     * @date 2023/8/11 20:10
     */
    public Boolean reportLeave(String employeeNo, String leaveSerial) {
        if (StringUtils.isEmpty(employeeNo) || StringUtils.isEmpty(leaveSerial)) {
            throw new BizException(InfraErrorCodeEnum.INFRA_PARAM_ERROR);
        }
        String userName;
        try {
            userName = idmRemote.getLoginAccount().getValue();
        } catch (Exception e) {
            userName = "admin";
        }
        if (StringUtils.isEmpty(userName)) {
            userName = "admin";
        }
        ReportLeaveReq req = new ReportLeaveReq();
        req.setLeaveSerial(leaveSerial);
        req.setEmployeeNo(employeeNo);
        req.setOperateName(userName);
        try {
            BaseResp<Boolean> resp = psService.reportLeave(req);
            if (resp.getCode() == 0) {
                return resp.getData();
            }
            return false;
        } catch (Exception e) {
            log.error("request ps error leaveSerial:{}", leaveSerial);
            return false;
        }
    }

    /**
     * @param
     * @return java.util.List<com.mi.oa.ee.safety.infra.remote.model.PsHireAddressDto>
     * @desc 获取人事所有入职地址
     * <AUTHOR> denghui
     * @date 2023/8/15 10:19
     */
    public List<PsHireAddressDto> getAllLocationHire() {
        int pageNum = 1;
        boolean flag = true;
        try {
            List<PsHireAddressDto> res = Lists.newArrayList();
            while (flag) {
                HpcLocationHireCondition condition = new HpcLocationHireCondition();
                condition.setPageNum(pageNum);
                condition.setPageSize(200);
                HrodResp<List<HpcLocationHire>> resp = hpcLocationService.getLocationHire(condition);
                if ("200".equals(resp.getHeader().getCode()) && !CollectionUtils.isEmpty(resp.getBody().getData())) {
                    pageNum++;
                    List<PsHireAddressDto> hireAddressDtoList = psSdkConverter.toHireAddressDtoList(resp.getBody().getData());
                    res.addAll(hireAddressDtoList);
                } else {
                    flag = false;
                }
            }
            return res;
        } catch (Exception e) {
            log.error("getLocationHire failed reason:{}", e.getMessage());
            throw new BizException(InfraErrorCodeEnum.SYNC_HIRE_LOCATION_FAILED);
        }
    }

    /**
     * @param
     * @return java.util.List<com.mi.oa.ee.safety.infra.remote.model.PsLocationCityDto>
     * @desc 获取ps所有城市信息
     * <AUTHOR> denghui
     * @date 2023/8/18 17:11
     */
    public List<PsLocationCityDto> getAllLocationCity() {
        int pageNum = 1;
        boolean flag = true;
        try {
            List<PsLocationCityDto> res = Lists.newArrayList();
            while (flag) {
                HpcLocationCityCondition condition = new HpcLocationCityCondition();
                condition.setPageNum(pageNum);
                condition.setPageSize(200);
                HrodResp<List<HpcLocationCity>> resp = hpcLocationService.getLocationCity(condition);
                if ("200".equals(resp.getHeader().getCode()) && !CollectionUtils.isEmpty(resp.getBody().getData())) {
                    pageNum++;
                    List<PsLocationCityDto> hireAddressDtoList =
                            psSdkConverter.toCityLocationDtoList(resp.getBody().getData());
                    res.addAll(hireAddressDtoList);
                } else {
                    flag = false;
                }
            }
            return res;
        } catch (Exception e) {
            log.error("getLocationCity failed reason:{}", e.getMessage());
            throw new BizException(InfraErrorCodeEnum.SYNC_CITY_LOCATION_FAILED);
        }
    }

    /**
     * @param userName
     * @return com.mi.oa.ee.safety.common.dto.PersonInfoModel
     * @desc 获取报道地址
     * <AUTHOR> denghui
     * @date 2023/8/23 11:39
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "PORTAL_REPORT_ADDRESS", param =
            "userName", refreshCacheTime = 5 * 60)
    public PersonInfoModel getLocationReportAddressByAccount(String userName) {
        HrodHireLocationReq req = new HrodHireLocationReq();
        req.setOprId(userName);
        try {
            BaseResp<HrodHireLocation> resp = hrodHireService.getHireLocation(req);
            if (resp.getCode() == 200) {
                return psSdkConverter.hireToPerson(resp.getData());
            } else {
                log.error(InfraErrorCodeEnum.GET_REPORT_ADDRESS_FAIL.getErrDesc(), resp.getMessage());
            }
        } catch (Exception e) {
            log.error(InfraErrorCodeEnum.GET_REPORT_ADDRESS_NET_WORK_ERROR.getErrDesc(), e.getMessage());
        }
        return new PersonInfoModel();
    }

    /**
     * @param accountName
     * @return com.mi.oa.ee.safety.common.dto.PersonInfoModel
     * @desc 通过账号获取部门信息
     * <AUTHOR> denghui
     * @date 2023/9/6 11:05
     */
    public PersonInfoModel getDepartments(String accountName) {
        PersonInfoModel personInfoModel = getDepartmentsByReTry(accountName);
        try {
            if (StringUtils.isEmpty(personInfoModel.getFirstDept())) {
                Thread.sleep(5000L);
                personInfoModel = getDepartmentsByReTry(accountName);
            }
        } catch (Exception e) {
            log.error("getDepartments by account:{} not find", accountName, e);
        }
        return personInfoModel;
    }

    private PersonInfoModel getDepartmentsByReTry(String accountName) {
        HpcConditionByOprid hpcConditionByOprid = new HpcConditionByOprid();
        hpcConditionByOprid.setOprid(accountName);
        try {
            HrodResp<HpcEmployeeBasis> resp = hpcEmployeeService.getEmployeeByOprid(hpcConditionByOprid);
            if ("200".equals(resp.getHeader().getCode()) && !ObjectUtils.isEmpty(resp.getBody().getData())) {
                return psSdkConverter.employeeBaseToPersonModel(resp.getBody().getData());
            }
        } catch (Exception e) {
            log.error("getDepartments by account:{} not find", accountName);
        }
        return new PersonInfoModel();
    }

    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "LOCATION_HIRE_ADDRESS", param =
            "reportAddressId", refreshCacheTime = 5 * 60)
    public String getLocationHireByReportAddress(String reportAddressId) {
        if (StringUtils.isEmpty(reportAddressId)) {
            return "";
        }
        HpcLocationHireCondition req = new HpcLocationHireCondition();
        req.setKey(reportAddressId);
        req.setPageNum(1);
        req.setPageSize(200);
        try {
            HrodResp<List<HpcLocationHire>> resp = hpcLocationService.getLocationHire(req);
            if ("200".equals(resp.getHeader().getCode())) {
                List<HpcLocationHire> data = resp.getBody().getData();
                if (!CollectionUtils.isEmpty(data)) {
                    HpcLocationHire hpcLocationHire = data.get(0);
                    return hpcLocationHire.getCity();
                }
            } else {
                log.error(InfraErrorCodeEnum.GET_REPORT_ADDRESS_FAIL.getErrDesc(), resp.getHeader().getMessage());
            }
        } catch (Exception e) {
            log.error(InfraErrorCodeEnum.GET_REPORT_ADDRESS_NET_WORK_ERROR.getErrDesc(), e.getMessage());
        }
        return "";
    }

    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "LOCATION_CITY_ADDRESS", param =
            "cityCode", refreshCacheTime = 5 * 60)
    public PersonInfoModel getLocationCityByCityCode(String cityCode) {
        HpcLocationCityCondition req = new HpcLocationCityCondition();
        req.setKey(cityCode);
        req.setPageNum(1);
        req.setPageSize(200);
        try {
            HrodResp<List<HpcLocationCity>> resp = hpcLocationService.getLocationCity(req);
            if ("200".equals(resp.getHeader().getCode())) {
                List<HpcLocationCity> data = resp.getBody().getData();
                if (!CollectionUtils.isEmpty(data)) {
                    PersonInfoModel personInfoModel = new PersonInfoModel();
                    HpcLocationCity hpcLocationCity = data.get(0);
                    PersonInfoModel.PersonExpandInfo expandInfo = new PersonInfoModel.PersonExpandInfo();
                    expandInfo.setOfficeCityCode(cityCode);
                    expandInfo.setOfficeNationalityCode(hpcLocationCity.getCountry());
                    personInfoModel.setExpandInfo(expandInfo);
                    return personInfoModel;
                }
            } else {
                log.error(InfraErrorCodeEnum.GET_REPORT_ADDRESS_FAIL.getErrDesc(), resp.getHeader().getMessage());
            }
        } catch (Exception e) {
            log.error(InfraErrorCodeEnum.GET_REPORT_ADDRESS_NET_WORK_ERROR.getErrDesc(), e.getMessage());
        }
        return new PersonInfoModel();
    }
}
