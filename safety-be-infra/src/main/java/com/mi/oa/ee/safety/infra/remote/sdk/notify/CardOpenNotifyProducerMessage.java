package com.mi.oa.ee.safety.infra.remote.sdk.notify;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/1/4 18:15
 */
@Slf4j
@Service
public class CardOpenNotifyProducerMessage extends AbstractNotifySdk {

    private static final String TOPIC_ID = "partner_card_open_status";

    @Override
    public String getTopicId() {
        return TOPIC_ID;
    }

}
