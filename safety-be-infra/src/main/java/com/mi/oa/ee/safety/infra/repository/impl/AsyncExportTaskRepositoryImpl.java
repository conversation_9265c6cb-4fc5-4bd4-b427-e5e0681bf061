package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.ee.safety.domain.enums.ExportStatusEnum;
import com.mi.oa.ee.safety.domain.enums.ExportTypeEnum;
import com.mi.oa.ee.safety.domain.model.AsyncExportTaskDo;
import com.mi.oa.ee.safety.infra.repository.AsyncExportTaskRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.AsyncExportTaskConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.AsyncExportTaskPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.AsyncExportTaskMapper;
import com.mi.oa.ee.safety.infra.repository.query.ExportHistoryQuery;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AsyncExportTaskRepositoryImpl implements AsyncExportTaskRepository {

    private static final Long HALF_HOUR = 3600L;

    @Resource
    private AsyncExportTaskMapper asyncExportRecordMapper;

    @Resource
    private AsyncExportTaskConverter asyncExportRecordConverter;

    @Override
    public AsyncExportTaskDo findByMd5(String md5) {
        //近半小时有导出记录的提示不允许导出
        LambdaQueryWrapper<AsyncExportTaskPo> queryWrapper = Wrappers.<AsyncExportTaskPo>lambdaQuery()
                .eq(AsyncExportTaskPo::getMd5, md5)
                .ne(AsyncExportTaskPo::getStatus, ExportStatusEnum.FAIL.getStatus())
                .gt(AsyncExportTaskPo::getOpTime, Instant.now().minusSeconds(HALF_HOUR).getEpochSecond());
        AsyncExportTaskPo asyncExportRecordPo = asyncExportRecordMapper.selectOne(queryWrapper);
        return asyncExportRecordConverter.toDo(asyncExportRecordPo);
    }

    @Override
    public void updateStatusById(AsyncExportTaskDo asyncExportRecordDo) {
        AsyncExportTaskPo asyncExportRecordPo = new AsyncExportTaskPo();
        asyncExportRecordPo.setStatus(asyncExportRecordDo.getStatus());
        asyncExportRecordPo.setMessage(asyncExportRecordDo.getMessage());
        asyncExportRecordPo.setUrl(asyncExportRecordDo.getUrl());
        asyncExportRecordPo.setId(asyncExportRecordDo.getId());
        asyncExportRecordMapper.updateById(asyncExportRecordPo);
    }

    @Override
    public AsyncExportTaskDo findById(Long id) {
        AsyncExportTaskPo asyncExportRecordPo = asyncExportRecordMapper.selectById(id);
        return asyncExportRecordConverter.toDo(asyncExportRecordPo);
    }

    @Override
    public void save(AsyncExportTaskDo asyncExportRecordDo) {
        AsyncExportTaskPo asyncExportRecordPo = asyncExportRecordConverter.toPo(asyncExportRecordDo);
        asyncExportRecordMapper.insert(asyncExportRecordPo);
        asyncExportRecordDo.setId(asyncExportRecordPo.getId());
    }

    @Override
    public void updateProgress(Long id, Integer progress) {
        AsyncExportTaskPo asyncExportRecordPo = new AsyncExportTaskPo();
        asyncExportRecordPo.setProgress(progress);
        asyncExportRecordPo.setId(id);
        asyncExportRecordMapper.updateById(asyncExportRecordPo);
    }

    @Override
    public PageVO<AsyncExportTaskDo> page(ExportHistoryQuery exportHistoryQueryDo) {
        LambdaQueryWrapper<AsyncExportTaskPo> queryWrapper = Wrappers.<AsyncExportTaskPo>lambdaQuery()
                .eq(StringUtils.isNotBlank(exportHistoryQueryDo.getUid()),
                        AsyncExportTaskPo::getUid, exportHistoryQueryDo.getUid());
        queryWrapper.orderByDesc(AsyncExportTaskPo::getId);
        if (Objects.nonNull(exportHistoryQueryDo.getOpType())) {
            queryWrapper.eq(AsyncExportTaskPo::getOpType, exportHistoryQueryDo.getOpType());
        } else {
            //只选择工卡这个部分的导出记录
            queryWrapper.in(AsyncExportTaskPo::getOpType, ExportTypeEnum.getCardTypeList());
        }
        return getAsyncExportTaskDoPageVO(exportHistoryQueryDo, queryWrapper);
    }

    @Override
    public PageVO<AsyncExportTaskDo> pageSafetyGroup(ExportHistoryQuery exportHistoryQueryDo) {
        LambdaQueryWrapper<AsyncExportTaskPo> queryWrapper = Wrappers.<AsyncExportTaskPo>lambdaQuery()
                .eq(StringUtils.isNotBlank(exportHistoryQueryDo.getUid()),
                        AsyncExportTaskPo::getUid, exportHistoryQueryDo.getUid());
        queryWrapper.orderByDesc(AsyncExportTaskPo::getId);
        if (Objects.nonNull(exportHistoryQueryDo.getOpType())) {
            queryWrapper.eq(AsyncExportTaskPo::getOpType, exportHistoryQueryDo.getOpType());
        } else {
            //只选择权限组这个部分的导出记录
            queryWrapper.in(AsyncExportTaskPo::getOpType, ExportTypeEnum.getCarrierGroupTypeList());
        }
        return getAsyncExportTaskDoPageVO(exportHistoryQueryDo, queryWrapper);
    }

    @Override
    public void setExportImageFolder(Long id, String urlFolder) {
        if (Objects.isNull(id)) {
            log.error("导出任务id为空");
            return;
        }
        if (StringUtils.isBlank(urlFolder)) {
            log.error("导出文件夹为空");
            return;
        }
        AsyncExportTaskPo asyncExportRecordPo = new AsyncExportTaskPo();
        asyncExportRecordPo.setId(id);
        asyncExportRecordPo.setImgFolder(urlFolder);
        asyncExportRecordMapper.updateById(asyncExportRecordPo);
    }

    private @NotNull PageVO<AsyncExportTaskDo> getAsyncExportTaskDoPageVO(ExportHistoryQuery exportHistoryQueryDo,
                                                                          LambdaQueryWrapper<AsyncExportTaskPo> queryWrapper) {
        if (Objects.nonNull(exportHistoryQueryDo.getStatus())) {
            queryWrapper.eq(AsyncExportTaskPo::getStatus, exportHistoryQueryDo.getStatus());
        }
        if (Objects.nonNull(exportHistoryQueryDo.getOpTimeFrom()) &&
                Objects.nonNull(exportHistoryQueryDo.getOpTimeTo())) {
            queryWrapper.between(AsyncExportTaskPo::getOpTime,
                    exportHistoryQueryDo.getOpTimeFrom(), exportHistoryQueryDo.getOpTimeTo());
        }
        Page<AsyncExportTaskPo> recordPage = asyncExportRecordMapper.selectPage(
                new Page<>(exportHistoryQueryDo.getPageNum(), exportHistoryQueryDo.getPageSize()), queryWrapper);
        if (CollectionUtils.isEmpty(recordPage.getRecords())) {
            return PageVO.build(Lists.newArrayList(), exportHistoryQueryDo.getPageSize(),
                    exportHistoryQueryDo.getPageNum(), 0L);
        }
        List<AsyncExportTaskDo> exportRecordDoList = asyncExportRecordConverter.toDoList(recordPage.getRecords());
        return PageVO.build(exportRecordDoList, recordPage.getSize(),
                recordPage.getCurrent(), recordPage.getTotal());
    }


}
