package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.ee.safety.common.dto.AccountModel;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.YesNoEnum;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.query.card.CardApplyQuery;
import com.mi.oa.ee.safety.infra.errorcode.CardApplyErrorCodeEnum;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.CardApplyPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.CardInfoPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardApplyEnhancePo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardApplyPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardTimeValidityPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardApplyMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardInfoMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardReceiptAddressMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardTimeValidityMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.service.CardApplyDaoService;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/22 10:18
 */
@Slf4j
@Service
public class CardApplyRepositoryImpl implements CardApplyRepository {

    @Autowired
    CardApplyMapper cardApplyMapper;

    @Autowired
    CardReceiptAddressMapper cardReceiptAddressMapper;

    @Autowired
    CardApplyPoConverter cardApplyPoConverter;

    @Autowired
    CardInfoPoConverter cardInfoPoConverter;

    @Autowired
    CardApplyDaoService cardApplyService;

    @Autowired
    CardInfoMapper cardInfoMapper;

    @Autowired
    CardTimeValidityMapper cardTimeValidityMapper;

    @Override
    public CardApplyDo getCardApplyByUidAndApplyType(CardApplyDo cardApplyDo) {
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(StringUtils.isNotEmpty(cardApplyDo.getUid()), CardApplyPO::getUid, cardApplyDo.getUid())
                .eq(ObjectUtils.isNotEmpty(cardApplyDo.getApplyType()), CardApplyPO::getApplyType, cardApplyDo.getApplyType())
                .last(" limit 1");
        CardApplyPO cardApplyPO = cardApplyMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(cardApplyPO)) {
            cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), false));
            cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), false));
        }
        return cardApplyPoConverter.toDo(cardApplyPO);
    }

    @Override
    public CardApplyDo getCardApplyById(Long id) {
        CardApplyPO cardApplyPO = cardApplyMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isNotEmpty(cardApplyPO)) {
            cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), false));
            cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), false));
        }
        return cardApplyPoConverter.toDo(cardApplyPO);
    }

    @Override
    public Long save(CardApplyDo cardApplyDo) {
        CardApplyPO cardApplyPO = null;
        if (ObjectUtils.isNotEmpty(cardApplyDo.getPersonInfo())) {
            cardApplyPO = cardApplyPoConverter.toPoWithPersonInfo(cardApplyDo);
        } else {
            cardApplyPO = cardApplyPoConverter.toPo(cardApplyDo);
        }
        if (StringUtils.isNotEmpty(cardApplyPO.getIdNumber())) {
            cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), true));
        }
        if (StringUtils.isNotBlank(cardApplyPO.getPhone())) {
            // 设置手机号后四位
            cardApplyPO.setPhoneLastFour(CodeUtils.getPhoneLastFour(cardApplyPO.getPhone()));
            cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), true));
        }
        cardApplyMapper.insert(cardApplyPO);
        return cardApplyPO.getId();
    }

    @Override
    public void updateByUid(CardApplyDo cardApplyDo) {
        LambdaUpdateWrapper<CardApplyPO> wrapper = Wrappers.<CardApplyPO>lambdaUpdate()
                .eq(CardApplyPO::getUid, cardApplyDo.getUid())
                .eq(CardApplyPO::getIsDeleted, OAUCFCommonConstants.INT_ZERO);
        if (StringUtils.isNotBlank(cardApplyDo.getPhone())) {
            cardApplyDo.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyDo.getPhone(), true));
        }
        cardApplyMapper.update(cardApplyPoConverter.toPo(cardApplyDo), wrapper);
    }

    @Override
    public PageModel<CardApplyDo> pageList(CardApplyDo cardApplyDo, Long pageNum, Long pageSize, List<String> parkCodes) {
        Page<CardApplyPO> page = new Page<>(pageNum, pageSize);
        log.info("开始分页查询");
        if (StringUtils.isNotBlank(cardApplyDo.getPhone())) {
            String encryptPhone = CodeUtils.encryptOrDecryptPhone(cardApplyDo.getPhone(), true);
            cardApplyDo.setPhone(encryptPhone);
        }
        List<Integer> statusList = (List<Integer>) cardApplyDo.getExtField("statusList");
        Boolean isShowBpmApproval = (Boolean) cardApplyDo.getExtField("isShowBpmApproval");
        IPage<CardApplyPO> iPage = cardApplyMapper.pageList(page, cardApplyDo, parkCodes, statusList, isShowBpmApproval);
        if (CollectionUtils.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(cardApplyPO -> {
                cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), false));
                cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), false));
            });
        }
        return PageModel.build(cardApplyPoConverter.toDoList(iPage.getRecords()), iPage.getSize(), iPage.getPages(),
                iPage.getTotal());
    }

    @Override
    public PageModel<CardApplyDo> pageList(CardApplyQuery query) {
        IPage<CardApplyPO> iPage = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.lambdaQuery();
        //权限 空情况只出现在superAdmin
        if (CollectionUtils.isNotEmpty(query.getParkCodeList())) {
            queryWrapper.and(wrapper -> wrapper.in(CardApplyPO::getParkCode, query.getParkCodeList())
                    .or().eq(CardApplyPO::getParkCode, ""));
        }
        // 工卡领取地
        queryWrapper.eq(StringUtils.isNotBlank(query.getReceiptParkCode()), CardApplyPO::getReceiptParkCode, query.getReceiptParkCode())
                // 工作地
                .eq(query.getCityId() != null, CardApplyPO::getCityId, query.getCityId())
                // 一级部门
                .eq(StringUtils.isNotBlank(query.getFirstDeptId()), CardApplyPO::getFirstDeptId, query.getFirstDeptId())
                //二级部门
                .eq(StringUtils.isNotBlank(query.getSecondDeptId()), CardApplyPO::getSecondDeptId, query.getSecondDeptId())
                //三级部门
                .eq(StringUtils.isNotBlank(query.getThirdDeptId()), CardApplyPO::getThirdDeptId, query.getThirdDeptId())
                //四级部门
                .eq(StringUtils.isNotBlank(query.getFourthDeptId()), CardApplyPO::getFourthDeptId, query.getFourthDeptId())
                //公司名称
                .like(StringUtils.isNotBlank(query.getCompanyName()), CardApplyPO::getCompanyName, query.getCompanyName())
                //人员
                .eq(StringUtils.isNotBlank(query.getUid()), CardApplyPO::getUid, query.getUid())
                //idList
                .in(CollectionUtils.isNotEmpty(query.getIdList()), CardApplyPO::getId, query.getIdList())
                //id
                .eq(Objects.nonNull(query.getId()), CardApplyPO::getId, query.getId())
                //card type
                .eq(Objects.nonNull(query.getCardType()), CardApplyPO::getApplyType, query.getCardType())
                //责任人
                .eq(StringUtils.isNotBlank(query.getResponsible()), CardApplyPO::getResponsible, query.getResponsible())
                //申请单状态
                .eq(Objects.nonNull(query.getCardStatus()), CardApplyPO::getApplyStatus, query.getCardStatus())
                //工号
                .eq(StringUtils.isNotBlank(query.getEmployeeId()), CardApplyPO::getEmpNo, query.getEmployeeId())
                .eq(StringUtils.isNotBlank(query.getParkCode()), CardApplyPO::getParkCode, query.getParkCode())
                .in(CollectionUtils.isNotEmpty(query.getStatusList()), CardApplyPO::getApplyStatus, query.getStatusList());
        if (YesNoEnum.yes(query.getIsExistPhoto())) {
            queryWrapper.ne(CardApplyPO::getPhotoUrl, StringUtils.EMPTY);
        } else if (YesNoEnum.no(query.getIsExistPhoto())) {
            queryWrapper.eq(CardApplyPO::getPhotoUrl, StringUtils.EMPTY);
        }
        queryWrapper.orderByDesc(CardApplyPO::getCreateTime);
        IPage<CardApplyPO> page = cardApplyMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), queryWrapper);
        if (CollectionUtils.isNotEmpty(iPage.getRecords())) {
            //处理手机号
            iPage.getRecords().forEach(cardApplyPO -> {
                cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), false));
                cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), false));
            });
        }
        return PageModel.build(cardApplyPoConverter.toDoList(page.getRecords()), page.getSize(), page.getPages(),
                page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWithCardTimeById(CardApplyDo cardApplyDo) {
        try {
            CardApplyPO cardApplyPO = cardApplyPoConverter.toPo(cardApplyDo);
            if (StringUtils.isNotEmpty(cardApplyPO.getIdNumber())) {
                cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), true));
            }
            if (StringUtils.isNotBlank(cardApplyPO.getPhone())) {
                cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), true));
            }
            cardApplyMapper.updateById(cardApplyPO);
            //保存对应的工卡有效期
            CardInfoDo cardInfoDo = cardApplyDo.getCardInfo();
            if (!ObjectUtils.isEmpty(cardInfoDo)) {
                //记录新的卡有效期时间
                cardInfoDo.setStartTime(cardApplyDo.getStartTime());
                cardInfoDo.setEndTime(cardApplyDo.getEndTime());
                if (CollectionUtils.isNotEmpty(cardInfoDo.getValidatePeriod())) {
                    cardInfoDo.getValidatePeriod().forEach(cardValidateDo -> {
                                CardTimeValidityPo cardTimeValidityPo = cardInfoPoConverter.toPo(cardValidateDo);
                                cardTimeValidityMapper.insert(cardTimeValidityPo);
                                cardValidateDo.setId(cardTimeValidityPo.getId());
                            }
                    );
                }
            }
        } catch (Exception e) {
            log.error("card apply update error:{}", e.toString());
            throw new BizException(CardApplyErrorCodeEnum.UPDATE_ERROR);
        }
    }

    @Override
    public void updateStatus(List<Long> ids, Integer status) {
        try {
            cardApplyMapper.batchUpdateStatus(ids, status);
        } catch (Exception e) {
            throw new BizException(CardApplyErrorCodeEnum.BATCH_UPDATE_APPROVAL_ERROR);
        }
    }

    @Override
    public List<CardApplyDo> getBatchApply(List<Long> ids) {
        List<CardApplyPO> cardApplyPOS = cardApplyMapper.selectBatchIds(ids);
        cardApplyPOS.forEach(cardApplyPO -> {
            cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), false));
            cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), false));
        });
        return cardApplyPoConverter.toDoList(cardApplyPOS);
    }

    @Override
    public String getReceiptAddress(String parkCode) {
        return cardReceiptAddressMapper.getReceiptAddress(parkCode);
    }

    @Override
    public List<CardApplyDo> batchSave(List<CardApplyDo> cardApplyDos) {
        List<CardApplyPO> cardApplyPOS = cardApplyPoConverter.toPoList(cardApplyDos);
        if (CollectionUtils.isNotEmpty(cardApplyPOS)) {
            //合作卡除驻场外 申请单开始结束时间默认0
            cardApplyPOS.forEach(cardApplyPO -> {
                cardApplyPO.setStartTime(null);
                cardApplyPO.setEndTime(null);
            });
            cardApplyService.saveBatch(cardApplyPOS);
            Map<String, CardApplyPO> accountMap =
                    cardApplyPOS.stream().collect(Collectors.toMap(CardApplyPO::getPartnerAccount,
                            item -> item, (a, b) -> a));
            for (CardApplyDo cardApplyDo : cardApplyDos) {
                CardApplyPO cardApplyPO = accountMap.get(cardApplyDo.getPartnerAccount());
                cardApplyDo.setId(cardApplyPO.getId());
            }
        }
        return cardApplyDos;
    }

    @Override
    public List<CardApplyDo> getApplyByFuzzyName(String name) {
        if (StringUtils.isNotBlank(name)) {
            LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                    .like(CardApplyPO::getName, name)
                    .last(" limit 10");
            List<CardApplyPO> cardApplyPOS = cardApplyMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(cardApplyPOS)) {
                queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                        .like(CardApplyPO::getPartnerAccount, name)
                        .last(" limit 10");
                cardApplyPOS = cardApplyMapper.selectList(queryWrapper);
            }
            return cardApplyPoConverter.toDoList(cardApplyPOS);
        } else {
            throw new BizException(CardApplyErrorCodeEnum.PARAM_ERROR);
        }
    }

    @Override
    public PageModel<CardApplyDo> pageDistinctPark(List<String> parkCodes, Integer pageNum, Integer pageSize) {
        Page<CardApplyPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .select(CardApplyPO::getParkCode)
                .in(CollectionUtils.isNotEmpty(parkCodes), CardApplyPO::getParkCode, parkCodes);
        Page<CardApplyPO> pageResp = cardApplyMapper.selectPage(page, queryWrapper);
        return PageModel.build(cardApplyPoConverter.toDoList(pageResp.getRecords()), pageSize, pageNum,
                pageResp.getTotal());
    }

    @Override
    public List<CardApplyDo> getListByParkCodes(List<String> resourceCodes) {
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .select(CardApplyPO::getParkCode)
                .in(CollectionUtils.isNotEmpty(resourceCodes), CardApplyPO::getParkCode, resourceCodes);
        List<CardApplyPO> cardApplyPOS = cardApplyMapper.selectList(queryWrapper);
        return cardApplyPoConverter.toDoList(cardApplyPOS);
    }

    @Override
    public CardApplyDo findById(Long applyId) {
        CardApplyPO cardApply = cardApplyMapper.selectByPrimaryKey(applyId);
        if (ObjectUtils.isNotEmpty(cardApply)) {
            cardApply.setPhone(CodeUtils.encryptOrDecryptPhone(cardApply.getPhone(), false));
            cardApply.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApply.getIdNumber(), false));
        }
        return cardApplyPoConverter.toDo(cardApply);
    }

    @Override
    public List<CardApplyDo> findByIdList(List<Long> applyIdList) {
        if (CollectionUtils.isEmpty(applyIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .in(CardApplyPO::getId, applyIdList);
        List<CardApplyPO> cardApply = cardApplyMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(cardApply)) {
            cardApply.forEach(item -> {
                item.setPhone(CodeUtils.encryptOrDecryptPhone(item.getPhone(), false));
                item.setIdNumber(CodeUtils.encryptOrDecryptPhone(item.getIdNumber(), false));
            });
        }
        return cardApplyPoConverter.toDoList(cardApply);
    }

    @Override
    public CardApplyDo findCardByUidAndCardType(String uid, CardTypeEnum cardTypeEnum) {
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(CardApplyPO::getUid, uid)
                .eq(CardApplyPO::getApplyType, cardTypeEnum.getNumber())
                .orderByDesc(CardApplyPO::getId)
                .last("limit 1");
        CardApplyPO cardApplyPO = cardApplyMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(cardApplyPO)) {
            cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), false));
            cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), false));
        }
        return cardApplyPoConverter.toDo(cardApplyPO);
    }

    @Override
    public void batchUpdateByUsername(List<CardApplyDo> cardApplyDoList) {
        if (CollectionUtils.isNotEmpty(cardApplyDoList)) {
            List<CardApplyPO> cardApplyPOS = cardApplyPoConverter.toPoList(cardApplyDoList);
            cardApplyPOS.forEach(cardApplyPO -> {
                LambdaUpdateWrapper<CardApplyPO> wrapper = Wrappers.<CardApplyPO>lambdaUpdate()
                        .eq(CardApplyPO::getPartnerAccount, cardApplyPO.getPartnerAccount());
                cardApplyMapper.update(cardApplyPO, wrapper);
            });
        }
    }

    @Override
    public CardApplyDo findAvatarUrlByUid(String uid) {
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(StringUtils.isNotBlank(uid), CardApplyPO::getUid, uid)
                .orderByDesc(CardApplyPO::getId)
                .last(" limit 1");
        return cardApplyPoConverter.toDo(cardApplyMapper.selectOne(queryWrapper));
    }

    @Override
    public CardApplyDo findAvatarUrlByUserName(String userName) {
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(StringUtils.isNotBlank(userName), CardApplyPO::getPartnerAccount, userName)
                .orderByDesc(CardApplyPO::getId)
                .last(" limit 1");
        return cardApplyPoConverter.toDo(cardApplyMapper.selectOne(queryWrapper));
    }

    @Override
    public List<CardApplyDo> findAllEffectiveAvatarUrl() {
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(CardApplyPO::getApplyType, CardTypeEnum.EMPLOYEE_CARD.getNumber())
                .in(CardApplyPO::getApplyStatus, CardApplyStatusEnum.effectiveAvatarStatus())
                .ne(CardApplyPO::getPhotoUrl, "")
                .ne(CardApplyPO::getUid, "");
        List<CardApplyPO> cardApplyPOList = cardApplyMapper.selectList(queryWrapper);
        return cardApplyPoConverter.toDoList(cardApplyPOList);
    }

    @Override
    public void updateById(CardApplyDo cardApplyDo) {
        CardApplyPO cardApplyPO = cardApplyPoConverter.toPo(cardApplyDo);
        if (StringUtils.isNotEmpty(cardApplyPO.getIdNumber())) {
            cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), true));
        }
        if (StringUtils.isNotBlank(cardApplyPO.getPhone())) {
            cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), true));
        }
        cardApplyMapper.updateById(cardApplyPO);
    }

    @Override
    public CardApplyDo getCardApplyByUid(String uid) {
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(StringUtils.isNotBlank(uid), CardApplyPO::getUid, uid)
                .last(" limit 1");
        CardApplyPO cardApplyPO = cardApplyMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(cardApplyPO)) {
            cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), false));
            cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), false));
        }
        return cardApplyPoConverter.toDo(cardApplyPO);
    }

    @Override
    public CardApplyDo findCardByUidAndCardTypes(String uid, List<Integer> applyTypes) {
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(CardApplyPO::getUid, uid)
                .in(CardApplyPO::getApplyType, applyTypes)
                .orderByDesc(CardApplyPO::getId)
                .last("limit 1");
        CardApplyPO cardApplyPO = cardApplyMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(cardApplyPO)) {
            cardApplyPO.setPhone(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getPhone(), false));
            cardApplyPO.setIdNumber(CodeUtils.encryptOrDecryptPhone(cardApplyPO.getIdNumber(), false));
        }
        return cardApplyPoConverter.toDo(cardApplyPO);
    }

    @Override
    public List<CardApplyDo> findListByUidAndCardTypes(String uid, List<Integer> applyTypes) {
        if (StringUtils.isEmpty(uid) || CollectionUtils.isEmpty(applyTypes)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(CardApplyPO::getUid, uid)
                .in(CardApplyPO::getApplyType, applyTypes);
        List<CardApplyPO> cardApplyPOList = cardApplyMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(cardApplyPOList)) {
            cardApplyPOList.forEach(item -> {
                item.setPhone(CodeUtils.encryptOrDecryptPhone(item.getPhone(), false));
                item.setIdNumber(CodeUtils.encryptOrDecryptPhone(item.getIdNumber(), false));
            });
        }
        return cardApplyPoConverter.toDoList(cardApplyPOList);
    }

    @Override
    public List<CardApplyDo> findByBpmCode(String businessKey) {
        if (StringUtils.isEmpty(businessKey)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(CardApplyPO::getBpmCode, businessKey);
        List<CardApplyPO> cardApplyPos = cardApplyMapper.selectList(queryWrapper);
        return cardApplyPoConverter.toDoList(cardApplyPos);
    }

    @Override
    public void updatePhotoUrl(String uid, String photoUrl) {
        cardApplyMapper.updatePhotoUrlByUid(uid, photoUrl);
    }

    @Override
    public List<AccountModel> getAccountByName(String name, Integer limit, List<String> uidList) {
        List<CardApplyEnhancePo> cardApplyEnhancePo = cardApplyMapper.getAccountByFuzzyName(name, limit, uidList);
        List<AccountModel> accountModelList = cardApplyPoConverter.toAccountModelList(cardApplyEnhancePo);
        accountModelList.forEach(accountModel -> {
            if (accountModel.getDisplayName() == null || accountModel.getDisplayName().isEmpty()) {
                accountModel.setDisplayName(accountModel.getFullName());
            }
        });
        return accountModelList;
    }

    @Override
    public List<CardApplyDo> getPhotoByPhoneLastFour(String phoneLastFour) {
        // 不获取临时卡的记录
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .eq(StringUtils.isNotBlank(phoneLastFour), CardApplyPO::getPhoneLastFour, phoneLastFour)
                .ne(CardApplyPO::getApplyType, CardTypeEnum.TEMP_CARD.getNumber());
        List<CardApplyPO> cardApplyPOS = cardApplyMapper.selectList(queryWrapper);
        return cardApplyPoConverter.toDoList(cardApplyPOS);
    }

    @Override
    public void batchUpdateById(List<CardApplyDo> cardApplyDos) {
        if (CollectionUtils.isNotEmpty(cardApplyDos)) {
            cardApplyService.updateBatchById(cardApplyPoConverter.toPoList(cardApplyDos));
        }
    }

    @Override
    public List<CardApplyDo> findPhotoUrlByUidList(List<String> uidList) {
        if (CollectionUtils.isEmpty(uidList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardApplyPO> queryWrapper = Wrappers.<CardApplyPO>lambdaQuery()
                .in(CardApplyPO::getUid, uidList)
                .ne(CardApplyPO::getPhotoUrl, StringUtils.EMPTY)
                .last("limit 1000");
        List<CardApplyPO> cardApplyPOS = cardApplyMapper.selectList(queryWrapper);
        return cardApplyPoConverter.toDoList(cardApplyPOS);
    }
}
