package com.mi.oa.ee.safety.infra.repository.impl;

import com.aliyuncs.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.ee.safety.common.dto.PayDto;
import com.mi.oa.ee.safety.common.enums.card.CardElectronStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.infra.errorcode.CardElectronRecordInfraErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.converter.PayDtoConverter;
import com.mi.oa.ee.safety.infra.remote.sdk.PaySdk;
import com.mi.oa.ee.safety.infra.repository.CardElectronRecordRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.CardElectronRecordPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardElectronRecordPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyMediumPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyPersonMediumPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardElectronRecordMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyMediumMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyPersonMediumMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyRightMapper;
import com.mi.oa.ee.safety.infra.repository.query.CardElectronRecordQuery;
import com.mi.oa.ee.safety.infra.repository.query.ElectronicCardRecordQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/9/19 9:51
 */
@Service
@Slf4j
public class CardElectronicRecordRepositoryImpl implements CardElectronRecordRepository {

    @Resource
    private CardElectronRecordMapper mapper;

    @Resource
    private SafetyPersonMediumMapper safetyPersonMediumMapper;

    @Resource
    private SafetyRightMapper safetyRightMapper;

    @Resource
    private CardElectronRecordPoConverter converter;

    @Resource
    private PayDtoConverter payDtoConverter;

    @Resource
    private PaySdk paySdk;

    @Resource
    private IdmRemote idmRemote;

    @Resource
    private SafetyMediumMapper safetyMediumMapper;

    @Override
    public PageModel<CardElectronRecordDo> page(ElectronicCardRecordQuery query) {
        IPage<CardElectronRecordPo> iPage = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(CardElectronRecordPo::getCardId, query.getCardId())
                .eq(CardElectronRecordPo::getUid, query.getUid())
                .orderByDesc(CardElectronRecordPo::getUpdateTime);
        IPage<CardElectronRecordPo> page = mapper.selectPage(iPage, queryWrapper);
        return PageModel.build(converter.toDoList(page.getRecords()), page.getSize(), page.getCurrent(),
                page.getTotal());
    }

    @Override
    public CardElectronRecordDo findInfoByElectronCardNum(String electronCardNum) {
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(CardElectronRecordPo::getElectronCardNum, electronCardNum);
        CardElectronRecordPo cardElectronRecordPo = mapper.selectOne(queryWrapper);
        return converter.toDo(cardElectronRecordPo);
    }

    @Override
    public Boolean updateById(CardElectronRecordDo electronRecordDo) {
        CardElectronRecordPo cardElectronRecordPo = converter.toPo(electronRecordDo);
        try {
            int num = mapper.updateById(cardElectronRecordPo);
            return num > 0;
        } catch (Exception e) {
            log.error("update electron card failed : {} by id : {}", e, electronRecordDo.getCardId());
            throw new BizException(CardElectronRecordInfraErrorCodeEnum.ELECTRON_CARD_UPDATE_FAILED);
        }
    }

    @Override
    public List<CardElectronRecordDo> findListByUid(String uid) {
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(CardElectronRecordPo::getUid, uid);
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public List<CardElectronRecordDo> findListWithDeletedByUid(String uid) {
        return converter.toDoList(mapper.findListWithDeletedByUid(uid));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void forceDelete(CardElectronRecordDo cardElectronRecordDo) {
        if (cardElectronRecordDo == null) {
            throw new BizException(CardElectronRecordInfraErrorCodeEnum.ELECTRON_CARD_NOT_EXIST);
        }
        //删除电子工卡记录
        CardElectronRecordPo cardElectronRecordPo = new CardElectronRecordPo();
        cardElectronRecordPo.setId(cardElectronRecordDo.getId());
        cardElectronRecordPo.setStatus(CardElectronStatusEnum.DELETED_FORCE.getCode());
        mapper.updateById(cardElectronRecordPo);

        if (cardElectronRecordDo.getSafetyPersonMediumDo() != null && cardElectronRecordDo.getSafetyPersonMediumDo().getId() != null) {
            //删除安防介质人员
            SafetyPersonMediumPO safetyPersonMediumPO = new SafetyPersonMediumPO();
            safetyPersonMediumPO.setId(cardElectronRecordDo.getSafetyPersonMediumDo().getId());
            safetyPersonMediumPO.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
            safetyPersonMediumMapper.updateById(safetyPersonMediumPO);
            safetyPersonMediumMapper.deleteById(cardElectronRecordDo.getSafetyPersonMediumDo().getId());

            //删除介质
            LambdaQueryWrapper<SafetyMediumPO> queryWrapper = Wrappers.<SafetyMediumPO>lambdaQuery()
                    .eq(SafetyMediumPO::getMediumCode, cardElectronRecordDo.getMediumCode());
            safetyMediumMapper.delete(queryWrapper);
        }

        //删除安防权限
        if (CollectionUtils.isNotEmpty(cardElectronRecordDo.getSafetyRightList())) {
            List<Long> idList = cardElectronRecordDo.getSafetyRightList().stream().map(SafetyRightDo::getId).collect(
                    Collectors.toList());
            safetyRightMapper.updateSyncStatusByIdList(idList, SafetySyncStatusEnum.WAIT_SYNC.getCode(), "admin", ZonedDateTime.now().toEpochSecond());
            //此处的删除会更新update_user
            safetyRightMapper.deleteBatchIds(idList);
        }
    }

    @Override
    @Async("asyncServiceExecutor")
    public void asyncNotifyPayCardIsDeleting(CardElectronRecordDo cardElectronRecordDo, SecurityContext securityContext) {
        SecurityContextHolder.setContext(securityContext);
        //异步通知小米钱包删除电子卡
        if (cardElectronRecordDo != null) {
            notifyPayCardIsDeleting(cardElectronRecordDo);
        }
    }

    @Override
    public void notifyPayCardIsDeleting(CardElectronRecordDo cardElectronRecordDo) {
        PayDto payDto = payDtoConverter.toPayDto(cardElectronRecordDo);
        paySdk.notifyPayDelete(payDto);
    }

    @Override
    public List<CardElectronRecordDo> findListByConditions(ElectronicCardRecordQuery query) {
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(StringUtils.isNotEmpty(query.getUid()), CardElectronRecordPo::getUid, query.getUid())
                .eq(query.getCardId() != null, CardElectronRecordPo::getCardId, query.getCardId())
                .in(CollectionUtils.isNotEmpty(query.getInStatusList()), CardElectronRecordPo::getStatus, query.getInStatusList());
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public List<CardElectronRecordDo> getCanForceDeleteListByUid(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(CardElectronRecordPo::getUid, uid)
                .in(CardElectronRecordPo::getStatus, CardElectronStatusEnum.canForceDeleteStatusList);
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public List<CardElectronRecordDo> findHasOtherOpenedElectronCardByElectronUserIdV2(CardElectronRecordDo cardElectronRecordDo) {
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(CardElectronRecordPo::getUid, cardElectronRecordDo.getUid())
                .eq(CardElectronRecordPo::getElectronUserId, cardElectronRecordDo.getElectronUserId())
                .ne(CardElectronRecordPo::getElectronCardNum, cardElectronRecordDo.getElectronCardNum())
                .eq(CardElectronRecordPo::getMediumCode, cardElectronRecordDo.getMediumCode())
                .eq(CardElectronRecordPo::getStatus, CardElectronStatusEnum.OPENED.getCode());
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public List<CardElectronRecordDo> findListByMediumCodeList(List<String> mediumCodeList) {
        if (CollectionUtils.isEmpty(mediumCodeList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .in(CardElectronRecordPo::getMediumCode, mediumCodeList);
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public void save(CardElectronRecordDo electronRecordDo) {
        CardElectronRecordPo recordPo = converter.toPo(electronRecordDo);
        try {
            mapper.insert(recordPo);
        } catch (Exception e) {
            log.error("electron insert fail:{}", e.toString());
        }
    }

    @Override
    public CardElectronRecordDo findById(Long id) {
        return converter.toDo(mapper.selectById(id));
    }

    @Override
    public List<CardElectronRecordDo> findOpenedElectronCardByCardIdAndUid(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())
                || Objects.isNull(cardElectronRecordDo.getCardId())) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(CardElectronRecordPo::getCardId, cardElectronRecordDo.getCardId())
                .eq(CardElectronRecordPo::getUid, cardElectronRecordDo.getUid())
                .eq(CardElectronRecordPo::getStatus, CardElectronStatusEnum.OPENED.getCode());
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public List<CardElectronRecordDo> findOpenedElectronCardByUid(CardElectronRecordDo cardElectronRecordDo) {
        if (StringUtils.isEmpty(cardElectronRecordDo.getUid())) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(CardElectronRecordPo::getUid, cardElectronRecordDo.getUid())
                .eq(CardElectronRecordPo::getStatus, CardElectronStatusEnum.OPENED.getCode());
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public List<CardElectronRecordDo> getOpenedListByUid(CardElectronRecordDo cardElectronRecordDo) {
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(CardElectronRecordPo::getUid, cardElectronRecordDo.getUid())
                .eq(CardElectronRecordPo::getStatus, CardElectronStatusEnum.OPENED.getCode());
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public List<CardElectronRecordDo> queryElectronRecordList(CardElectronRecordQuery recordQuery) {
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(recordQuery.getMediumCodeList()), CardElectronRecordPo::getMediumCode,
                        recordQuery.getMediumCodeList())
                .eq(CardElectronRecordPo::getStatus, CardElectronStatusEnum.OPENED.getCode());
        return converter.toDoList(mapper.selectList(queryWrapper));
    }

    @Override
    public List<CardElectronRecordDo> findHasOtherOpenedElectronCardByElectronUserId(CardElectronRecordDo cardElectronRecordDo) {
        LambdaQueryWrapper<CardElectronRecordPo> queryWrapper = Wrappers.<CardElectronRecordPo>lambdaQuery()
                .eq(StringUtils.isNotEmpty(cardElectronRecordDo.getUid()), CardElectronRecordPo::getUid,
                        cardElectronRecordDo.getUid())
                .eq(CardElectronRecordPo::getElectronUserId, cardElectronRecordDo.getElectronUserId())
                .ne(CardElectronRecordPo::getElectronCardNum, cardElectronRecordDo.getElectronCardNum())
                .eq(CardElectronRecordPo::getStatus, CardElectronStatusEnum.OPENED.getCode());
        return converter.toDoList(mapper.selectList(queryWrapper));
    }
}
