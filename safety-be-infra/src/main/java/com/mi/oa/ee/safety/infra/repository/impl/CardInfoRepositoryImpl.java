package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.dto.CardPageConditionDto;
import com.mi.oa.ee.safety.common.dto.CardUserRight;
import com.mi.oa.ee.safety.common.dto.SafetyCardToolDto;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.CardTimeValidityDo;
import com.mi.oa.ee.safety.domain.model.SafetyMediumDo;
import com.mi.oa.ee.safety.domain.query.card.TempCardInfoQuery;
import com.mi.oa.ee.safety.infra.errorcode.CardApplyErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.model.MqMessageBody;
import com.mi.oa.ee.safety.infra.remote.sdk.RocketMqSdk;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.CardTimeValidateRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.CardInfoPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.SafetyCarrierGroupPOConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.SafetyMediumPOConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoTempPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardTimeValidityPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyMediumPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardInfoMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardTimeValidityMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyCarrierGroupMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyMediumMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.service.CardDaoService;
import com.mi.oa.ee.safety.infra.repository.query.CardInfoQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.exception.MQClientException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/2 15:02
 */
@Slf4j
@Service
public class CardInfoRepositoryImpl implements CardInfoRepository {

    @Autowired
    CardInfoMapper cardInfoMapper;

    @Autowired
    SafetyCarrierGroupMapper safetyCarrierGroupMapper;

    @Autowired
    CardTimeValidityMapper cardTimeValidityMapper;

    @Autowired
    CardInfoPoConverter cardInfoPoConverter;

    @Autowired
    SafetyCarrierGroupPOConverter carrierGroupPOConverter;

    @Autowired
    SafetyMediumMapper safetyMediumMapper;

    @Autowired
    SafetyMediumPOConverter safetyMediumPOConverter;

    @Autowired
    CardDaoService cardDaoService;

    @Autowired
    IdmRemote idmRemote;

    @Autowired
    RocketMqSdk rocketMqSdk;

    @Resource
    CardTimeValidateRepository cardTimeValidateRepository;

    private static final String LIMIT_ONE_STR = " limit 1";

    @Override
    public List<CardInfoDo> getListByConditions(CardInfoQuery cardInfoQuery) {
        LambdaQueryWrapper<CardInfoPO> wrapper = Wrappers.<CardInfoPO>lambdaQuery();
        wrapper.eq(!StringUtils.isEmpty(cardInfoQuery.getMediumCode()), CardInfoPO::getMediumCode, cardInfoQuery.getMediumCode())
                .eq(!StringUtils.isEmpty(cardInfoQuery.getMediumEncryptCode()), CardInfoPO::getMediumEncryptCode, cardInfoQuery.getMediumEncryptCode())
                .eq(!StringUtils.isEmpty(cardInfoQuery.getMediumPhysicsCode()), CardInfoPO::getMediumPhysicsCode, cardInfoQuery.getMediumPhysicsCode())
                .eq(!StringUtils.isEmpty(cardInfoQuery.getUid()), CardInfoPO::getUid, cardInfoQuery.getUid())
                .eq(cardInfoQuery.getCardType() != null, CardInfoPO::getCardType, cardInfoQuery.getCardType())
                .eq(cardInfoQuery.getCardStatus() != null, CardInfoPO::getCardStatus, cardInfoQuery.getCardStatus())
                .eq(StringUtils.isNotEmpty(cardInfoQuery.getTenantCode()), CardInfoPO::getTenantCode, cardInfoQuery.getTenantCode())
                .in(CollectionUtils.isNotEmpty(cardInfoQuery.getMediumCodeList()), CardInfoPO::getMediumCode,
                        cardInfoQuery.getMediumCodeList());

        List<CardInfoPO> cardInfoPOList = cardInfoMapper.selectList(wrapper);
        return cardInfoPoConverter.toDoList(cardInfoPOList);
    }

    @Override
    public List<CardInfoDo> getNeedUpdateTimeList() {
        List<CardInfoPO> list = cardInfoMapper.getNeedUpdateTimeList();
        return cardInfoPoConverter.toDoList(list);
    }

    @Override
    public PageModel<CardInfoDo> pageConditionList(CardPageConditionDto pageConditionDto, List<String> parkCodes) {
        IPage<CardInfoPO> iPage = new Page<>(pageConditionDto.getPageNum(), pageConditionDto.getPageSize());
        if (pageConditionDto.getCardStatus() != null && CardStatusEnum.USING_FORBIDDEN.getCode().equals(pageConditionDto.getCardStatus())) {
            pageConditionDto.setCardStatus(null);
            pageConditionDto.setCardStatusList(Lists.newArrayList(CardStatusEnum.USING_FORBIDDEN.getCode(), CardStatusEnum.NOT_ACTIVE_FOR_FORBIDDEN.getCode()));
        }
        if (StringUtils.isNotBlank(pageConditionDto.getPhone())) {
            String encryptPhone = CodeUtils.encryptOrDecryptPhone(pageConditionDto.getPhone(), true);
            pageConditionDto.setPhone(encryptPhone);
        }
        IPage<CardInfoPO> pageModel = cardInfoMapper.pageConditionList(iPage, pageConditionDto, parkCodes);
        return PageModel.build(cardInfoPoConverter.toDoList(pageModel.getRecords()), pageModel.getPages(),
                pageModel.getSize(), pageModel.getTotal());
    }

    @Override
    public PageModel<CardInfoDo> pageConditionListV3(CardPageConditionDto pageConditionDto, List<String> parkCodes) {
        IPage<CardInfoPO> iPage = new Page<>(pageConditionDto.getPageNum(), pageConditionDto.getPageSize());
        if (pageConditionDto.getCardStatus() != null && CardStatusEnum.USING_FORBIDDEN.getCode().equals(pageConditionDto.getCardStatus())) {
            pageConditionDto.setCardStatus(null);
            pageConditionDto.setCardStatusList(Lists.newArrayList(CardStatusEnum.USING_FORBIDDEN.getCode(), CardStatusEnum.NOT_ACTIVE_FOR_FORBIDDEN.getCode()));
        }
        if (StringUtils.isNotBlank(pageConditionDto.getPhone())) {
            String encryptPhone = CodeUtils.encryptOrDecryptPhone(pageConditionDto.getPhone(), true);
            pageConditionDto.setPhone(encryptPhone);
        }
        IPage<CardInfoPO> pageModel = cardInfoMapper.pageConditionListV3(iPage, pageConditionDto, parkCodes);
        return PageModel.build(cardInfoPoConverter.toDoList(pageModel.getRecords()), pageModel.getPages(),
                pageModel.getSize(), pageModel.getTotal());
    }

    @Override
    public PageModel<CardInfoDo> pageConditionList(TempCardInfoQuery query) {
        IPage<CardInfoPO> iPage = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<CardInfoPO> pageModel = cardInfoMapper.pageConditionListV2(iPage, query);
        return PageModel.build(cardInfoPoConverter.toDoList(pageModel.getRecords()), pageModel.getPages(),
                pageModel.getSize(), pageModel.getTotal());
    }

    @Override
    public List<CardTimeValidityDo> getValidatePeriod(Long cardId) {
        LambdaQueryWrapper<CardTimeValidityPo> queryWrapper = Wrappers.<CardTimeValidityPo>lambdaQuery()
                .eq(!ObjectUtils.isEmpty(cardId), CardTimeValidityPo::getCardId, cardId)
                .orderByAsc(CardTimeValidityPo::getEndTime);
        return cardInfoPoConverter.toCardTimeDoList(cardTimeValidityMapper.selectList(queryWrapper));
    }

    @Override
    public CardInfoDo getOneById(Long id) {
        return cardInfoPoConverter.toDo(cardInfoMapper.selectById(id));
    }

    @Override
    public void deleteById(Long id) {
        try {
            cardInfoMapper.deleteById(id);
        } catch (Exception e) {
            throw new BizException(CardApplyErrorCodeEnum.CARD_DELETE_FAIL);
        }
    }

    @Override
    public void deleteValidateTime(String uid, ZonedDateTime startTime, ZonedDateTime endTime) {
        LambdaQueryWrapper<CardTimeValidityPo> queryWrapper = Wrappers.<CardTimeValidityPo>lambdaQuery()
                .eq(CardTimeValidityPo::getUid, uid)
                .eq(!ObjectUtils.isEmpty(startTime), CardTimeValidityPo::getStartTime, startTime)
                .eq(!ObjectUtils.isEmpty(endTime), CardTimeValidityPo::getEndTime, endTime);
        cardTimeValidityMapper.delete(queryWrapper);
    }

    @Override
    public void deleteValidateTimeByCardId(Long cardId) {
        LambdaQueryWrapper<CardTimeValidityPo> queryWrapper = Wrappers.<CardTimeValidityPo>lambdaQuery()
                .eq(CardTimeValidityPo::getCardId, cardId);
        cardTimeValidityMapper.delete(queryWrapper);
    }

    @Override
    public void updateStatusById(CardInfoDo cardInfoDo) {
        //发送mq消息
        String oldCard = getOldPhysicsCard(cardInfoDo);
        log.info("update card info:{}", cardInfoDo);
        cardInfoMapper.updateById(cardInfoPoConverter.toPo(cardInfoDo));
        sendMq(cardInfoDo, oldCard);
    }

    @Override
    public void restoreValidateTime(String uid) {
        String nowUser = idmRemote.getLoginUid();
        Long nowTime = ZonedDateTime.now().toEpochSecond();
        cardTimeValidityMapper.restoreValidateTime(uid, nowTime, nowUser);
    }

    @Override
    public void restoreValidateTimeByCardId(Long cardId) {
        String nowUser = idmRemote.getLoginUid();
        Long nowTime = ZonedDateTime.now().toEpochSecond();
        cardTimeValidityMapper.restoreValidateTimeByCardId(cardId, nowTime, nowUser);
    }

    @Override
    public void insertTime(CardTimeValidityDo cardValidateDo) {
        try {
            CardTimeValidityPo cardTimeValidityPo = cardInfoPoConverter.toPo(cardValidateDo);
            cardTimeValidityMapper.insert(cardTimeValidityPo);
            cardValidateDo.setId(cardTimeValidityPo.getId());
        } catch (Exception e) {
            log.info("current uid:{} has same time {} {}", cardValidateDo.getUid(), cardValidateDo.getStartTime(),
                    cardValidateDo.getEndTime());
        }
    }

    @Override
    public void updateCardTimeValidity(CardInfoDo cardInfoDo) {
        if (CollectionUtils.isNotEmpty(cardInfoDo.getValidatePeriod())) {
            cardInfoDo.getValidatePeriod().forEach(cardTimeValidityDo -> {
                cardTimeValidityMapper.updateById(cardInfoPoConverter.toPo(cardTimeValidityDo));
            });
        }

    }

    @Override
    public Long save(CardInfoDo cardInfoDo) {
        try {
            CardInfoPO cardInfoPO = cardInfoPoConverter.toPo(cardInfoDo);
            //防止空对象无法插入
            if (StringUtils.isEmpty(cardInfoPO.getCardNum())) {
                cardInfoPO.setCardNum("");
            }
            String oldCard = getOldPhysicsCard(cardInfoDo);
            cardInfoMapper.insert(cardInfoPO);
            cardInfoDo.setId(cardInfoPO.getId());
            //新开卡发送消息
            sendMq(cardInfoDo, oldCard);
            return cardInfoPO.getId();
        } catch (Exception e) {
            log.info("current uid:{} and card:{} open card error", cardInfoDo.getUid(), cardInfoDo.getMediumCode());
            return null;
        }
    }

    @Override
    public CardInfoDo getCardByStatus(String uid, List<Integer> status) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getUid, uid)
                .in(!CollectionUtils.isEmpty(status), CardInfoPO::getCardStatus, status)
                .last(LIMIT_ONE_STR);
        return cardInfoPoConverter.toDo(cardInfoMapper.selectOne(queryWrapper));
    }

    @Override
    public void deleteMedium(String mediumCode) {
        LambdaQueryWrapper<SafetyMediumPO> queryWrapper = Wrappers.<SafetyMediumPO>lambdaQuery()
                .eq(SafetyMediumPO::getMediumCode, mediumCode);
        try {
            safetyMediumMapper.delete(queryWrapper);
        } catch (Exception e) {
            throw new BizException(CardApplyErrorCodeEnum.MEDIUM_DELETE_ERROR);
        }
    }

    @Override
    public void restoreMedium(String mediumCode) {
        String nowUser = idmRemote.getLoginUid();
        Long nowTime = ZonedDateTime.now().toEpochSecond();
        safetyMediumMapper.restoreMedium(mediumCode, nowUser, nowTime);
    }

    @Override
    public SafetyMediumDo saveMedium(SafetyMediumDo safetyMediumDO) {
        try {
            SafetyMediumPO safetyMediumPO = safetyMediumPOConverter.doToPO(safetyMediumDO);
            safetyMediumMapper.insert(safetyMediumPO);
            safetyMediumDO.setId(safetyMediumPO.getId());
            return safetyMediumPOConverter.poToDO(safetyMediumPO);
        } catch (Exception e) {
            log.info("current medium code:{} is exist", safetyMediumDO.getMediumCode());
        }
        return null;
    }

    @Override
    public void updateTime(CardTimeValidityDo cardTimeValidityDo) {
        if (StringUtils.isEmpty(cardTimeValidityDo.getUid()) || ObjectUtils.isEmpty(cardTimeValidityDo.getCardId())) {
            throw new BizException(CardApplyErrorCodeEnum.PARAM_ERROR);
        }
        LambdaUpdateWrapper<CardTimeValidityPo> updateWrapper = Wrappers.<CardTimeValidityPo>lambdaUpdate()
                .eq(CardTimeValidityPo::getUid, cardTimeValidityDo.getUid());
        cardTimeValidityMapper.update(cardInfoPoConverter.toPo(cardTimeValidityDo), updateWrapper);
    }

    @Override
    public List<CardInfoDo> getListByStatus(List<Integer> code) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .in(CardInfoPO::getCardStatus, code);
        return cardInfoPoConverter.toDoList(cardInfoMapper.selectList(queryWrapper));
    }

    @Override
    public List<CardTimeValidityDo> getTimeByCardIds(List<Long> usingIds) {
        if (!CollectionUtils.isEmpty(usingIds)) {
            LambdaQueryWrapper<CardTimeValidityPo> wrapper = Wrappers.<CardTimeValidityPo>lambdaQuery()
                    .in(CardTimeValidityPo::getCardId, usingIds);
            return cardInfoPoConverter.toCardTimeDoList(cardTimeValidityMapper.selectList(wrapper));
        }
        return null;
    }

    @Override
    public CardInfoDo getByMediumCodeAndStatus(String mediumCode) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getMediumCode, mediumCode)
                .in(CardInfoPO::getCardStatus, Lists.newArrayList(CardStatusEnum.USING.getCode(),
                        CardStatusEnum.NOT_ACTIVE.getCode(), CardStatusEnum.EXPIRED.getCode()))
                .last(LIMIT_ONE_STR);
        CardInfoPO cardInfoPO = cardInfoMapper.selectOne(queryWrapper);
        return cardInfoPoConverter.toDo(cardInfoPO);
    }

    @Override
    public List<CardInfoDo> batchSaveWithIds(List<CardInfoDo> cardInfoDos) {
        List<CardInfoPO> cardInfoPOS = cardInfoPoConverter.toPoList(cardInfoDos);
        cardDaoService.saveBatch(cardInfoPOS);
        return cardInfoPoConverter.toDoList(cardInfoPOS);
    }

    @Override
    public void batchSaveValidateTime(List<CardTimeValidityDo> cardTimeValidityDos) {
        String nowUser = idmRemote.getLoginUid();
        if (StringUtils.isBlank(nowUser)) {
            nowUser = "importUser";
        }
        Long nowTime = ZonedDateTime.now().toEpochSecond();
        cardTimeValidityMapper.batchInsert(cardTimeValidityDos, nowTime, nowUser);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByPhysicCodes(List<String> mediumPhysicsCodes) {
        List<CardInfoTempPo> cardInfoTempPos = cardInfoMapper.getListCardInfoByPhysicCodes(mediumPhysicsCodes);
        for (CardInfoTempPo cardInfoTempPo : cardInfoTempPos) {
            if (!StringUtils.isEmpty(cardInfoTempPo.getPhone())) {
                cardInfoTempPo.setPhone(CodeUtils.encryptOrDecryptPhone(cardInfoTempPo.getPhone(), false));
            }
        }
        return cardInfoPoConverter.toDoListByTempPo(cardInfoTempPos);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByEncryptCodes(List<String> mediumEncryptCodes) {
        List<CardInfoTempPo> cardInfoTempPos = cardInfoMapper.getListCardInfoByEncryptCodes(mediumEncryptCodes);
        for (CardInfoTempPo cardInfoTempPo : cardInfoTempPos) {
            if (!StringUtils.isEmpty(cardInfoTempPo.getPhone())) {
                cardInfoTempPo.setPhone(CodeUtils.encryptOrDecryptPhone(cardInfoTempPo.getPhone(), false));
            }
        }
        return cardInfoPoConverter.toDoListByTempPo(cardInfoTempPos);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByUid(List<String> uidList) {
        List<CardInfoTempPo> cardInfoTempPos = cardInfoMapper.getListCardInfoByUid(uidList);
        for (CardInfoTempPo cardInfoTempPo : cardInfoTempPos) {
            if (!StringUtils.isEmpty(cardInfoTempPo.getPhone())) {
                cardInfoTempPo.setPhone(CodeUtils.encryptOrDecryptPhone(cardInfoTempPo.getPhone(), false));
            }
        }
        return cardInfoPoConverter.toDoListByTempPo(cardInfoTempPos);
    }

    @Override
    public List<CardInfoDo> getListCardInfoByAccounts(List<String> accounts) {
        List<CardInfoTempPo> cardInfoTempPos = cardInfoMapper.getListCardInfoByAccounts(accounts);
        for (CardInfoTempPo cardInfoTempPo : cardInfoTempPos) {
            if (!StringUtils.isEmpty(cardInfoTempPo.getPhone())) {
                cardInfoTempPo.setPhone(CodeUtils.encryptOrDecryptPhone(cardInfoTempPo.getPhone(), false));
            }
        }
        return cardInfoPoConverter.toDoListByTempPo(cardInfoTempPos);
    }

    @Override
    public CardInfoDo getCardByCardNum(String cardNum) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getCardNum, cardNum)
                .in(CardInfoPO::getCardStatus, CardStatusEnum.usingList())
                .orderByDesc(CardInfoPO::getId)
                .last(LIMIT_ONE_STR);
        CardInfoPO cardInfoPO = cardInfoMapper.selectOne(queryWrapper);
        return cardInfoPoConverter.toDo(cardInfoPO);
    }

    @Override
    public CardInfoDo getCardByNums(SafetyCardToolDto safetyCardToolDto, List<CardStatusEnum> status) {
        List<Integer> statusCodes = status.stream().map(CardStatusEnum::getCode).collect(Collectors.toList());
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(!StringUtils.isEmpty(safetyCardToolDto.getCardNum()), CardInfoPO::getCardNum, safetyCardToolDto.getCardNum())
                .eq(!StringUtils.isEmpty(safetyCardToolDto.getMediumPhysicsCode()), CardInfoPO::getMediumPhysicsCode,
                        safetyCardToolDto.getMediumPhysicsCode())
                .eq(!StringUtils.isEmpty(safetyCardToolDto.getMediumEncryptCode()), CardInfoPO::getMediumEncryptCode,
                        safetyCardToolDto.getMediumEncryptCode())
                .in(!CollectionUtils.isEmpty(statusCodes), CardInfoPO::getCardStatus, statusCodes)
                .eq(CardInfoPO::getIsDeleted, safetyCardToolDto.getIsDelete())
                .last(LIMIT_ONE_STR);
        CardInfoPO cardInfoPO = cardInfoMapper.selectOne(queryWrapper);
        return cardInfoPoConverter.toDo(cardInfoPO);
    }

    @Override
    public CardInfoDo findCardInfoByPhysicsCode(String mediumPhysicsCode) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getMediumPhysicsCode, mediumPhysicsCode)
                .in(CardInfoPO::getCardStatus, CardStatusEnum.usingList())
                .orderByDesc(CardInfoPO::getId)
                .last(LIMIT_ONE_STR);
        CardInfoPO cardInfoPO = cardInfoMapper.selectOne(queryWrapper);
        return cardInfoPoConverter.toDo(cardInfoPO);
    }

    @Override
    public CardInfoDo findCardInfoByEncryptCode(String mediumEncryptCode) {
        List<Integer> occupationStatus = CardStatusEnum.usingList();
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getMediumEncryptCode, mediumEncryptCode)
                .in(CardInfoPO::getCardStatus, occupationStatus)
                .orderByDesc(CardInfoPO::getId)
                .last(LIMIT_ONE_STR);
        CardInfoPO cardInfoPO = cardInfoMapper.selectOne(queryWrapper);
        return cardInfoPoConverter.toDo(cardInfoPO);
    }

    @Override
    public List<CardInfoDo> findCardInfoByPhysicsCodeList(List<String> mediumPhysicsCodeList) {
        if (CollectionUtils.isEmpty(mediumPhysicsCodeList)) {
            return Lists.newArrayList();
        }
        List<Integer> occupationStatus = Lists.newArrayList(CardStatusEnum.USING.getCode(),
                CardStatusEnum.NOT_ACTIVE.getCode(), CardStatusEnum.EXPIRED.getCode());
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .in(CardInfoPO::getMediumPhysicsCode, mediumPhysicsCodeList)
                .in(CardInfoPO::getCardStatus, occupationStatus)
                .orderByDesc(CardInfoPO::getId)
                .last(LIMIT_ONE_STR);
        List<CardInfoPO> cardInfoList = cardInfoMapper.selectList(queryWrapper);
        return cardInfoPoConverter.toDoList(cardInfoList);
    }

    @Override
    public CardInfoDo findCardInfoByApplyId(Long applyId) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getCardApplyId, applyId);
        CardInfoPO cardInfo = cardInfoMapper.selectOne(queryWrapper);
        return cardInfoPoConverter.toDo(cardInfo);
    }

    @Override
    public List<CardInfoDo> findCardInfoByApplyIdList(List<Long> applyIdList) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .in(CardInfoPO::getCardApplyId, applyIdList);
        List<CardInfoPO> cardInfoList = cardInfoMapper.selectList(queryWrapper);
        return cardInfoPoConverter.toDoList(cardInfoList);
    }

    @Override
    public CardInfoDo findCardByUidAndCardType(String uid, CardTypeEnum cardTypeEnum, List<Integer> status) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getUid, uid)
                .eq(CardInfoPO::getCardType, cardTypeEnum.getNumber())
                .in(CollectionUtils.isNotEmpty(status), CardInfoPO::getCardStatus, status)
                .orderByDesc(CardInfoPO::getId)
                .last(LIMIT_ONE_STR);
        return cardInfoPoConverter.toDo(cardInfoMapper.selectOne(queryWrapper));
    }

    @Override
    public List<CardTimeValidityDo> findValidatePeriodByCardIdList(List<Long> cardIdList) {
        if (CollectionUtils.isEmpty(cardIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardTimeValidityPo> queryWrapper = Wrappers.<CardTimeValidityPo>lambdaQuery()
                .in(CardTimeValidityPo::getCardId, cardIdList)
                .orderByAsc(CardTimeValidityPo::getEndTime);
        return cardInfoPoConverter.toCardTimeDoList(cardTimeValidityMapper.selectList(queryWrapper));
    }

    @Override
    public List<CardTimeValidityDo> findValidateTimeByCardId(Long cardId) {
        LambdaQueryWrapper<CardTimeValidityPo> queryWrapper = Wrappers.<CardTimeValidityPo>lambdaQuery()
                .eq(CardTimeValidityPo::getCardId, cardId)
                .orderByAsc(CardTimeValidityPo::getEndTime);
        return cardInfoPoConverter.toCardTimeDoList(cardTimeValidityMapper.selectList(queryWrapper));
    }

    @Override
    public List<Long> findCardIdByEffectTime(ZonedDateTime endTimeFrom, ZonedDateTime endTimeTo) {
        LambdaQueryWrapper<CardTimeValidityPo> queryWrapper = Wrappers.<CardTimeValidityPo>lambdaQuery()
                .between(CardTimeValidityPo::getEndTime, endTimeFrom, endTimeTo)
                .orderByAsc(CardTimeValidityPo::getEndTime);
        queryWrapper.select(CardTimeValidityPo::getCardId);
        List<CardTimeValidityPo> cardTimeValidityList = cardTimeValidityMapper.selectList(queryWrapper);
        return cardTimeValidityList.stream().map(CardTimeValidityPo::getCardId).collect(Collectors.toList());
    }

    @Override
    public void saveValidateTime(CardInfoDo cardInfoDo) {
        if (CollectionUtils.isNotEmpty(cardInfoDo.getValidatePeriod())) {
            cardInfoDo.getValidatePeriod().forEach(this::insertTime);
        }
    }

    @Override
    public CardInfoDo findTempAndEmpCard(CardInfoDo cardInfoDo) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getUid, cardInfoDo.getUid())
                .in(CardInfoPO::getCardType, CardTypeEnum.tempAndEmpList())
                .orderByDesc(CardInfoPO::getId)
                .last(" limit 1");
        return cardInfoPoConverter.toDo(cardInfoMapper.selectOne(queryWrapper));
    }

    @Override
    public CardInfoDo findUsingCardByEncryptCode(String mediumEncryptCode) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getMediumEncryptCode, mediumEncryptCode)
                .eq(CardInfoPO::getCardType, CardTypeEnum.EMPLOYEE_CARD.getNumber())
                .eq(CardInfoPO::getCardStatus, CardStatusEnum.USING.getCode())
                .orderByDesc(CardInfoPO::getId)
                .last(" limit 1");
        return cardInfoPoConverter.toDo(cardInfoMapper.selectOne(queryWrapper));
    }

    @Override
    public List<CardInfoDo> findListByMediumCodeList(List<String> mediumCodes) {
        if (CollectionUtils.isEmpty(mediumCodes)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .in(CardInfoPO::getMediumCode, mediumCodes);
        return cardInfoPoConverter.toDoList(cardInfoMapper.selectList(queryWrapper));
    }

    @Override
    public void updateTenantCodeById(CardInfoDo card) {
        if (Objects.isNull(card.getId())) {
            throw new BizException(CardInfoDomainErrorCodeEnum.CARD_ID_IS_EMPTY);
        }
        cardInfoMapper.updateById(cardInfoPoConverter.toPo(card));
    }

    @Override
    public List<CardUserRight> findOpenedRightList(String uid) {
        return cardInfoMapper.findOpenedRightList(uid);
    }

    private String getOldPhysicsCard(CardInfoDo cardInfoDo) {
        String oldPhysicsCard = "";
        if (cardInfoDo.getId() != null) {
            CardInfoPO cardInfoPO = cardInfoMapper.selectByPrimaryKey(cardInfoDo.getId());
            if (cardInfoPO != null) {
                oldPhysicsCard = cardInfoPO.getMediumPhysicsCode();
            }
        }
        return oldPhysicsCard;
    }

    /**
     * 发送MQ消息
     *
     * @param cardInfoDo
     */
    private void sendMq(CardInfoDo cardInfoDo, String oldPhysicsCard) {
        try {
            MqMessageBody body = new MqMessageBody();
            body.setPhysicsCard(cardInfoDo.getMediumPhysicsCode());
            body.setOldPhysicsCard(oldPhysicsCard);
            body.setUid(cardInfoDo.getUid());
            rocketMqSdk.sendMessage(body);
        } catch (MQClientException e) {
            if (!ObjectUtils.isEmpty(cardInfoDo)) {
                log.error("mq send message exception,current card:{}, oldCard: {}, uid:{}",
                        cardInfoDo.getMediumPhysicsCode(), oldPhysicsCard, cardInfoDo.getUid());
            }
        } finally {
            log.info("----- mq send message,current card:{}, oldCard: {}, uid:{}", cardInfoDo.getMediumPhysicsCode(), oldPhysicsCard, cardInfoDo.getUid());
        }
    }

    @Override
    public CardInfoDo getCardInfoByUid(String uid, Integer number) {
        LambdaQueryWrapper<CardInfoPO> queryWrapper = Wrappers.<CardInfoPO>lambdaQuery()
                .eq(CardInfoPO::getUid, uid)
                .eq(CardInfoPO::getCardType, number);
        return cardInfoPoConverter.toDo(cardInfoMapper.selectOne(queryWrapper));
    }
}
