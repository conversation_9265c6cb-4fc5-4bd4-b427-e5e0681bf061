package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyGroupStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyStatusEnum;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.domain.query.card.CardGroupConfigGroupApplyUserQuery;
import com.mi.oa.ee.safety.domain.query.card.CardPermissionApplyQuery;
import com.mi.oa.ee.safety.infra.repository.CardPermissionApplyRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.CardPermissionApplyPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardPermissionApplyGroupCountPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardPermissionApplyGroupPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardPermissionApplyPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardPermissionApplyGroupMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardPermissionApplyMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.service.CardPermissionApplyGroupService;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/3 20:22
 */
@Slf4j
@Service
public class CardPermissionApplyRepositoryImpl implements CardPermissionApplyRepository {

    @Resource
    private CardPermissionApplyMapper cardPermissionApplyMapper;

    @Resource
    private CardPermissionApplyGroupService cardPermissionApplyGroupService;

    @Resource
    private CardPermissionApplyGroupMapper cardPermissionApplyGroupMapper;

    @Resource
    private CardPermissionApplyPoConverter converter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CardPermissionApplyDo cardPermissionApplyDo) {
        log.info("card permission apply create param:{}", cardPermissionApplyDo);
        //保存申请单和权限包关系
        List<CardPermissionApplyGroupDo> permissionApplyGroupList = cardPermissionApplyDo.getPermissionApplyGroupList();
        if (CollectionUtils.isNotEmpty(permissionApplyGroupList)) {
            List<CardPermissionApplyGroupPo> applyGroupPoList = converter.toApplyGroupPoList(permissionApplyGroupList);
            cardPermissionApplyGroupService.saveBatch(applyGroupPoList);
        }
        //保存权限申请单
        CardPermissionApplyPo cardPermissionApplyPo = converter.toPo(cardPermissionApplyDo);
        cardPermissionApplyMapper.insert(cardPermissionApplyPo);
        cardPermissionApplyDo.setId(cardPermissionApplyPo.getId());
    }

    @Override
    public CardPermissionApplyDo findById(Long id) {
        log.info("card permission apply findById param:{}", id);
        LambdaQueryWrapper<CardPermissionApplyPo> queryWrapper = Wrappers.<CardPermissionApplyPo>lambdaQuery()
                .eq(CardPermissionApplyPo::getId, id);
        CardPermissionApplyPo cardPermissionApplyPo = cardPermissionApplyMapper.selectOne(queryWrapper);
        return converter.toDo(cardPermissionApplyPo);
    }

    @Override
    public List<CardPermissionApplyGroupDo> findPermissionApplyGroupListByCode(String permissionApplyCode) {
        log.info("card permission apply findPermissionApplyGroupListByCode param:{}", permissionApplyCode);
        // 当把申请的权限组删除的时候，mybatisPlus查不到is_deleted的数据
        LambdaQueryWrapper<CardPermissionApplyGroupPo> queryWrapper = Wrappers.<CardPermissionApplyGroupPo>lambdaQuery()
                .eq(CardPermissionApplyGroupPo::getPermissionApplyCode, permissionApplyCode);
        List<CardPermissionApplyGroupPo> poList = cardPermissionApplyGroupService.list(queryWrapper);
        return converter.toApplyGroupDoList(poList);
    }

    @Override
    public List<CardPermissionApplyGroupDo> findPermissionApplyGroupListByCodeWithIsDeleted(String permissionApplyCode) {
        log.info("card permission apply findPermissionApplyGroupListByCode param:{}", permissionApplyCode);
        // 当把申请的权限组删除的时候，mybatisPlus查不到is_deleted的数据
        // 小程序申请的时候 即使删除了权限组，也要查出来
        // 获取全部的数据 包括删除和未删除的
        List<CardPermissionApplyGroupPo> poList = cardPermissionApplyGroupService.findPermissionApplyGroupListByCodeWithIsDeleted(permissionApplyCode);
        // 保留source为 2且未删除的数据和source为 1 的数据
        List<CardPermissionApplyGroupPo> list1 = poList.stream().filter(po -> po.getSource() == 2 && po.getIsDeleted() == 0)
                .collect(Collectors.toList());
        // 保留source为 1的数据
        List<CardPermissionApplyGroupPo> list2 = poList.stream().filter(po -> po.getSource() == 1)
                .collect(Collectors.toList());
        list1.addAll(list2);

        return converter.toApplyGroupDoList(list1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByCancel(CardPermissionApplyDo cardPermissionApplyDo) {
        log.info("card permission apply updateByCancel param:{}", cardPermissionApplyDo);
        //更新权限申请单
        CardPermissionApplyPo cardPermissionApplyPo = converter.toPo(cardPermissionApplyDo);
        cardPermissionApplyMapper.updateById(cardPermissionApplyPo);

        //更新关系
        List<CardPermissionApplyGroupDo> permissionApplyGroupList = cardPermissionApplyDo.getPermissionApplyGroupList();
        if (CollectionUtils.isNotEmpty(permissionApplyGroupList)) {
            List<CardPermissionApplyGroupPo> applyGroupPoList = converter.toApplyGroupPoList(permissionApplyGroupList);
            cardPermissionApplyGroupService.updateBatchById(applyGroupPoList);
        }
    }

    @Override
    public List<CardPermissionApplyGroupDo> findAllPermissionGroupForOnceOpened(CardPermissionApplyDo cardPermissionApplyDo) {
        List<CardPermissionApplyGroupPo> allPermissionGroupForOnceOpened = cardPermissionApplyGroupMapper
                .findAllPermissionGroupForOnceOpened(cardPermissionApplyDo);
        return converter.toApplyGroupDoList(allPermissionGroupForOnceOpened);
    }

    @Override
    public List<CardPermissionApplyDo> findPermissionApplyListByCode(List<String> permissionApplyCodeList) {
        if (CollectionUtils.isEmpty(permissionApplyCodeList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CardPermissionApplyPo> queryWrapper = Wrappers.<CardPermissionApplyPo>lambdaQuery()
                .in(CardPermissionApplyPo::getPermissionApplyCode, permissionApplyCodeList);
        List<CardPermissionApplyPo> poList = cardPermissionApplyMapper.selectList(queryWrapper);
        return converter.toPoList(poList);
    }

    @Override
    public PageModel<CardPermissionApplyDo> pageApply(CardPermissionApplyQuery query) {
        log.info("card permission apply pageApply param:{}", query);
        Page<CardPermissionApplyPo> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<CardPermissionApplyPo> iPage = cardPermissionApplyMapper.pageApply(page, query);
        List<CardPermissionApplyDo> doList = converter.toPoList(iPage.getRecords());
        return PageModel.build(doList, iPage.getSize(), iPage.getCurrent(), iPage.getTotal());
    }

    @Override
    public CardPermissionApplyDo findByBpmCode(String businessKey) {
        LambdaQueryWrapper<CardPermissionApplyPo> queryWrapper = Wrappers.<CardPermissionApplyPo>lambdaQuery()
                .eq(CardPermissionApplyPo::getBpmCode, businessKey);
        CardPermissionApplyPo cardPermissionApplyPo = cardPermissionApplyMapper.selectOne(queryWrapper);
        return converter.toDo(cardPermissionApplyPo);
    }

    @Override
    public void updateByReject(CardPermissionApplyDo cardPermissionApplyDo) {
        //更新权限申请单
        CardPermissionApplyPo cardPermissionApplyPo = converter.toPo(cardPermissionApplyDo);
        cardPermissionApplyMapper.updateById(cardPermissionApplyPo);

        //更新关系
        List<CardPermissionApplyGroupDo> permissionApplyGroupList = cardPermissionApplyDo.getPermissionApplyGroupList();
        if (CollectionUtils.isNotEmpty(permissionApplyGroupList)) {
            List<CardPermissionApplyGroupPo> applyGroupPoList = converter.toApplyGroupPoList(permissionApplyGroupList);
            cardPermissionApplyGroupService.updateBatchById(applyGroupPoList);
        }
    }

    @Override
    public void updateByPass(CardPermissionApplyDo cardPermissionApplyDo) {
        //更新权限申请单
        CardPermissionApplyPo cardPermissionApplyPo = converter.toPo(cardPermissionApplyDo);
        cardPermissionApplyMapper.updateById(cardPermissionApplyPo);
    }

    @Override
    public void updateRelation(List<CardPermissionApplyGroupDo> relationList) {
        List<CardPermissionApplyGroupPo> applyGroupPoList = converter.toApplyGroupPoList(relationList);
        cardPermissionApplyGroupService.updateBatchById(applyGroupPoList);
    }

    @Override
    public List<CardPermissionApplyGroupDo> findRelationByBpmCode(String businessKey) {
        LambdaQueryWrapper<CardPermissionApplyGroupPo> queryWrapper = Wrappers.<CardPermissionApplyGroupPo>lambdaQuery()
                .eq(CardPermissionApplyGroupPo::getBpmCode, businessKey);
        List<CardPermissionApplyGroupPo> poList = cardPermissionApplyGroupService.list(queryWrapper);
        return converter.toApplyGroupDoList(poList);
    }

    @Override
    public CardPermissionApplyDo findByApplyCode(String applyCode) {
        LambdaQueryWrapper<CardPermissionApplyPo> queryWrapper = Wrappers.<CardPermissionApplyPo>lambdaQuery()
                .eq(CardPermissionApplyPo::getPermissionApplyCode, applyCode);
        CardPermissionApplyPo cardPermissionApplyPo = cardPermissionApplyMapper.selectOne(queryWrapper);
        return converter.toDo(cardPermissionApplyPo);
    }

    @Override
    public void updateByFinal(CardPermissionApplyDo applicantApplyDo) {
        //更新权限申请单
        CardPermissionApplyPo cardPermissionApplyPo = converter.toPo(applicantApplyDo);
        cardPermissionApplyMapper.updateById(cardPermissionApplyPo);
    }

    @Override
    public Map<String, Long> findPermissionApplyUserCount(List<String> groupCodeList) {
        if (CollectionUtils.isEmpty(groupCodeList)) {
            return Maps.newHashMap();
        }
        List<CardPermissionApplyGroupCountPo> list = cardPermissionApplyGroupMapper.findPermissionApplyUserCount(groupCodeList);
        return list.stream().collect(Collectors.toMap(CardPermissionApplyGroupCountPo::getCardGroupCode,
                CardPermissionApplyGroupCountPo::getCount));
    }

    @Override
    public PageVO<CardPermissionApplyGroupDo> pageApplyUser(CardGroupConfigGroupApplyUserQuery query) {
        IPage<CardPermissionApplyGroupPo> iPage = new Page<>(query.getPageNum(), query.getPageSize());
        Page<CardPermissionApplyGroupPo> page = cardPermissionApplyGroupMapper.pageApplyUser(iPage, query);
        List<CardPermissionApplyGroupDo> doList = converter.toApplyGroupDoList(page.getRecords());
        return PageVO.build(doList, iPage.getSize(), iPage.getCurrent(), iPage.getTotal());
    }

    @Override
    public List<CardPermissionApplyDo> findExpiredPermissionList() {
        long nowSecond = ZonedDateTimeUtils.getSecondEnd(ZonedDateTime.now());
        LambdaQueryWrapper<CardPermissionApplyPo> queryWrapper = Wrappers.<CardPermissionApplyPo>lambdaQuery()
                .in(CardPermissionApplyPo::getPermissionApplyStatus, CardPermissionApplyStatusEnum.getOpenedList())
                .lt(CardPermissionApplyPo::getEndTime, nowSecond);
        List<CardPermissionApplyPo> cardPermissionApplyPos = cardPermissionApplyMapper.selectList(queryWrapper);
        return converter.toDoList(cardPermissionApplyPos);
    }

    @Override
    public List<CardPermissionApplyDo> findWillExpiredPermissionList() {
        long nowSecond = ZonedDateTimeUtils.getSecondEnd(ZonedDateTime.now());
        long willExpiredSecond = ZonedDateTimeUtils.getSecondEnd(ZonedDateTime.now().plusDays(7));
        LambdaQueryWrapper<CardPermissionApplyPo> queryWrapper = Wrappers.<CardPermissionApplyPo>lambdaQuery()
                .in(CardPermissionApplyPo::getPermissionApplyStatus, CardPermissionApplyStatusEnum.getOpenedList())
//                .eq(CardPermissionApplyPo::getIsNotify, YesNoEnum.YES.getCode())
                .between(CardPermissionApplyPo::getEndTime, nowSecond, willExpiredSecond);
        List<CardPermissionApplyPo> cardPermissionApplyPos = cardPermissionApplyMapper.selectList(queryWrapper);
        return converter.toDoList(cardPermissionApplyPos);
    }

    @Override
    public void editNotifyStatus(CardPermissionApplyDo cardPermissionApplyDo) {
        LambdaQueryWrapper<CardPermissionApplyPo> queryWrapper = Wrappers.<CardPermissionApplyPo>lambdaQuery()
                .eq(CardPermissionApplyPo::getPermissionApplyCode, cardPermissionApplyDo.getPermissionApplyCode());

        CardPermissionApplyPo cardPermissionApplyPo = cardPermissionApplyMapper.selectOne(queryWrapper);
//        if (cardPermissionApplyPo != null) {
//            //状态值取反
//            cardPermissionApplyPo.setIsNotify(cardPermissionApplyPo.getIsNotify() == YesNoEnum.YES.getCode() ?
//                    YesNoEnum.NO.getCode() : YesNoEnum.YES.getCode());
//            cardPermissionApplyMapper.updateById(cardPermissionApplyPo);
//        } else {
//            log.error("editNotifyStatus error, cardPermissionApplyDo:{}", cardPermissionApplyDo);
//        }

    }

    @Override
    public List<CardPermissionApplyDo> findPreExpireApplyList() {
        long nowSecond = ZonedDateTimeUtils.getSecondEnd(ZonedDateTime.now().plusDays(7L));
        LambdaQueryWrapper<CardPermissionApplyPo> queryWrapper = Wrappers.<CardPermissionApplyPo>lambdaQuery()
                .in(CardPermissionApplyPo::getPermissionApplyStatus, CardPermissionApplyStatusEnum.getOpenedList())
                .lt(CardPermissionApplyPo::getEndTime, nowSecond);
        List<CardPermissionApplyPo> cardPermissionApplyPos = cardPermissionApplyMapper.selectList(queryWrapper);
        return converter.toDoList(cardPermissionApplyPos);
    }

    @Override
    public void updateById(CardPermissionApplyDo cardPermissionApplyDo) {
        log.info("updateById:{}", cardPermissionApplyDo);
        //更新权限申请单
        CardPermissionApplyPo cardPermissionApplyPo = converter.toPo(cardPermissionApplyDo);
        cardPermissionApplyMapper.updateById(cardPermissionApplyPo);
    }

    @Override
    public void batchSaveRelation(List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList) {
        log.info("batch save relation param:{}", cardPermissionApplyGroupDoList);
        cardPermissionApplyGroupService.saveBatch(converter.toApplyGroupPoList(cardPermissionApplyGroupDoList));
    }

    @Override
    public void batchDeleteRelation(List<CardPermissionApplyGroupDo> cardPermissionApplyDoList) {
        log.info("batch delete relation param:{}", cardPermissionApplyDoList);
        LambdaQueryWrapper<CardPermissionApplyGroupPo> queryWrapper = Wrappers.<CardPermissionApplyGroupPo>lambdaQuery()
                .eq(CardPermissionApplyGroupPo::getUid, cardPermissionApplyDoList.get(0).getUid())
                .in(CardPermissionApplyGroupPo::getCardGroupCode, cardPermissionApplyDoList.stream()
                        .map(CardPermissionApplyGroupDo::getCardGroupCode).collect(Collectors.toList()));
        List<CardPermissionApplyGroupPo> applyGroups = cardPermissionApplyGroupMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(applyGroups)) {
            List<Long> ids = applyGroups.stream().map(CardPermissionApplyGroupPo::getId).collect(Collectors.toList());
            log.info("batch delete relation ids:{}", ids);
            cardPermissionApplyGroupService.removeByIds(ids);
        }
    }

    @Override
    public List<CardPermissionApplyGroupDo> findPermissionApplyGroupListByUidAndSourceAndCode(String uid, Integer code,
                                                                                              List<String> groupCodeList) {
        log.info("findPermissionApplyGroupListByUidAndSource uid:{},code:{}", uid, code);
        uid = uid == null ? "" : uid;
        LambdaQueryWrapper<CardPermissionApplyGroupPo> queryWrapper = Wrappers.<CardPermissionApplyGroupPo>lambdaQuery()
                .eq(CardPermissionApplyGroupPo::getUid, uid)
                .eq(Objects.nonNull(code), CardPermissionApplyGroupPo::getSource, code)
                .eq(CardPermissionApplyGroupPo::getCardPermissionStatus,
                        CardPermissionApplyGroupStatusEnum.AUDIT_COMPLETED.getCode())
                .in(CollectionUtils.isNotEmpty(groupCodeList), CardPermissionApplyGroupPo::getCardGroupCode, groupCodeList);
        return converter.toApplyGroupDoList(cardPermissionApplyGroupMapper.selectList(queryWrapper));
    }
}
