package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyCardDoorEventLogDo;
import com.mi.oa.ee.safety.infra.errorcode.InfraErrorCodeEnum;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyCardDoorEventLogRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.SafetyCardDoorEventLogPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardDoorEventLogPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardDoorEventLogMapper;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCardDoorEventLogQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.regex.Pattern;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/7/10 14:53
 */
@Slf4j
@Service
public class SafetyCardDoorEventLogRepositoryImpl implements SafetyCardDoorEventLogRepository {

    @Resource
    CardDoorEventLogMapper cardDoorEventLogMapper;

    @Resource
    CardInfoRepository cardInfoRepository;

    @Resource
    SafetyCardDoorEventLogPoConverter safetyCardDoorEventLogPoConverter;

    @Override
    public PageModel<SafetyCardDoorEventLogDo> pageSwipeCardRecordList(SafetyCardDoorEventLogQuery cardDoorEventLogQuery) {
        if (ObjectUtils.isEmpty(cardDoorEventLogQuery) || StringUtils.isEmpty(cardDoorEventLogQuery.getUserName())) {
            throw new BizException(InfraErrorCodeEnum.INFRA_PARAM_ERROR);
        }
        String regex = "^[A-C]\\d+$";
        Pattern pattern = Pattern.compile(regex);
        Boolean isCardNum = pattern.matcher(cardDoorEventLogQuery.getUserName()).matches();
        if (isCardNum) {
            //卡编号转真正加密卡号 先查本地不存在查老工卡
            CardInfoDo cardInfo = cardInfoRepository.getCardByCardNum(cardDoorEventLogQuery.getUserName());
            if (ObjectUtils.isEmpty(cardInfo)) {
                throw new BizException(InfraErrorCodeEnum.NOT_FOUND_ENCRYPT_CARD);
            } else {
                cardDoorEventLogQuery.setUserName(cardInfo.getMediumEncryptCode());
            }
        }

        LocalDate startDateTime = null;
        LocalDate endDateTime = null;
        Long pageNum = cardDoorEventLogQuery.getPageNum();
        Long pageSize = cardDoorEventLogQuery.getPageSize();
        if (ObjectUtils.isEmpty(cardDoorEventLogQuery.getPageNum()) || ObjectUtils.isEmpty(cardDoorEventLogQuery.getPageSize())) {
            pageNum = 1L;
            pageSize = 10L;
        }
        if (pageSize > 100L) {
            pageSize = 100L;
        }
        if (ObjectUtils.isEmpty(cardDoorEventLogQuery.getStartTime())) {
            startDateTime = ZonedDateTime.now().minusDays(30L).toLocalDate();
        } else {
            startDateTime = cardDoorEventLogQuery.getStartTime().toLocalDate();
        }
        if (ObjectUtils.isEmpty(cardDoorEventLogQuery.getEndTime())) {
            endDateTime = ZonedDateTime.now().toLocalDate();
        } else {
            endDateTime = cardDoorEventLogQuery.getEndTime().toLocalDate();
        }
        IPage<CardDoorEventLogPo> iPage = new Page<>(pageNum, pageSize);
        IPage<CardDoorEventLogPo> res = cardDoorEventLogMapper.pageSwipeCardRecordList(iPage, startDateTime,
                endDateTime, cardDoorEventLogQuery.getUserName(), isCardNum);
        return PageModel.build(safetyCardDoorEventLogPoConverter.toDoList(res.getRecords()), res.getSize(),
                res.getCurrent(), res.getTotal());
    }

}
