package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.card.LogOperateType;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyOperateLogDo;
import com.mi.oa.ee.safety.infra.errorcode.CardApplyErrorCodeEnum;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogDetailRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyOperateLogRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.SafetyOperateLogPOConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyOperateLogPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyOperateLogMapper;
import com.mi.oa.ee.safety.infra.repository.query.SafetyOperateLogQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 安防操作日志仓储层
 *
 * <AUTHOR>
 * @date 2022/9/20 10:01
 */
@Service
public class SafetyOperateLogRepositoryImpl implements SafetyOperateLogRepository {

    @Autowired
    SafetyOperateLogMapper safetyOperateLogMapper;

    @Autowired
    SafetyOperateLogDetailRepository safetyOperateLogDetailRepository;

    @Autowired
    SafetyOperateLogPOConverter safetyOperateLogPOConverter;

    @Override
    @Async("asyncLogExecutor")
    public void asyncBatchSaveOrUpdate(List<SafetyOperateLogDo> list, SecurityContext securityContext) {
        SecurityContextHolder.setContext(securityContext);
        //日志的保存是异步的
        if (CollectionUtils.isNotEmpty(list)) {
            for (SafetyOperateLogDo safetyOperateLogDo : list) {
                saveOrUpdate(safetyOperateLogDo);
            }
        }
    }

    @Override
    public void saveOrUpdate(SafetyOperateLogDo safetyOperateLogDo) {
        if (safetyOperateLogDo == null) {
            return;
        }
        SafetyOperateLogPO logPO = safetyOperateLogPOConverter.toPO(safetyOperateLogDo);
        safetyOperateLogMapper.insert(logPO);
        safetyOperateLogDo.setId(logPO.getId());
        if (logPO.getId() != null && CollectionUtils.isNotEmpty(safetyOperateLogDo.getSafetyOperateLogDetailDoList())) {
            safetyOperateLogDo.getSafetyOperateLogDetailDoList().stream().forEach(item -> item.setLogId(logPO.getId()));
            safetyOperateLogDetailRepository.batchSave(safetyOperateLogDo.getSafetyOperateLogDetailDoList());
        }
    }

    @Override
    public PageModel<SafetyOperateLogDo> getListByConditionForPage(SafetyOperateLogDo safetyOperateLogDo) {
        Long pageNum = 1L;
        Long pageSize = 10L;
        if (safetyOperateLogDo.getExtField(SafetyConstants.PAGE_NUM_KEY) != null) {
            pageNum = (Long) safetyOperateLogDo.getExtField(SafetyConstants.PAGE_NUM_KEY);
        }
        if (safetyOperateLogDo.getExtField(SafetyConstants.PAGE_SIZE_KEY) != null) {
            pageSize = (Long) safetyOperateLogDo.getExtField(SafetyConstants.PAGE_SIZE_KEY);
        }
        IPage<SafetyOperateLogPO> iPage = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SafetyOperateLogPO> wrapper = Wrappers.<SafetyOperateLogPO>lambdaQuery();
        wrapper.eq(StringUtils.isNotEmpty(safetyOperateLogDo.getAppCode()), SafetyOperateLogPO::getAppCode, safetyOperateLogDo.getAppCode())
                .eq(StringUtils.isNotEmpty(safetyOperateLogDo.getBizId()), SafetyOperateLogPO::getBizId, safetyOperateLogDo.getBizId())
                .eq(safetyOperateLogDo.getOperateStatus() != null, SafetyOperateLogPO::getOperateStatus, safetyOperateLogDo.getOperateStatus())
                .eq(StringUtils.isNotEmpty(safetyOperateLogDo.getRequestUrl()), SafetyOperateLogPO::getRequestUrl, safetyOperateLogDo.getRequestUrl())
                .orderByDesc(SafetyOperateLogPO::getUpdateTime);
        IPage<SafetyOperateLogPO> res = safetyOperateLogMapper.selectPage(iPage, wrapper);
        return PageModel.build(safetyOperateLogPOConverter.toDOList(res.getRecords()),
                pageSize, pageNum, res.getTotal());
    }

    @Override
    public List<SafetyOperateLogDo> getListByCondition(SafetyOperateLogQuery safetyOperateLogQuery) {
        LambdaQueryWrapper<SafetyOperateLogPO> wrapper = Wrappers.<SafetyOperateLogPO>lambdaQuery();
        Long updateTimeSecond = ObjectUtils.isNotEmpty(safetyOperateLogQuery.getUpdateTime()) ? safetyOperateLogQuery.getUpdateTime().toEpochSecond() : null;
        wrapper.eq(StringUtils.isNotEmpty(safetyOperateLogQuery.getAppCode()), SafetyOperateLogPO::getAppCode, safetyOperateLogQuery.getAppCode())
                .eq(StringUtils.isNotEmpty(safetyOperateLogQuery.getBizId()), SafetyOperateLogPO::getBizId, safetyOperateLogQuery.getBizId())
                .eq(safetyOperateLogQuery.getOperateStatus() != null, SafetyOperateLogPO::getOperateStatus, safetyOperateLogQuery.getOperateStatus())
                .eq(StringUtils.isNotEmpty(safetyOperateLogQuery.getRequestUrl()), SafetyOperateLogPO::getRequestUrl, safetyOperateLogQuery.getRequestUrl())
                .gt(updateTimeSecond != null, SafetyOperateLogPO::getCreateTime, updateTimeSecond)
                .gt(safetyOperateLogQuery.getId() != null, SafetyOperateLogPO::getId, safetyOperateLogQuery.getId())
                .orderBy("id".equals(safetyOperateLogQuery.getSortBy()), "asc".equals(safetyOperateLogQuery.getOrderBy()), SafetyOperateLogPO::getId);
        if (safetyOperateLogQuery.getLimitSize() != null) {
            wrapper.last("limit " + safetyOperateLogQuery.getLimitSize());
        } else {
            wrapper.last("limit 1000");
        }
        List<SafetyOperateLogPO> result = safetyOperateLogMapper.selectList(wrapper);
        return safetyOperateLogPOConverter.toDOList(result);
    }

    @Override
    public SafetyOperateLogDo findById(Long id) {
        return safetyOperateLogPOConverter.toDO(safetyOperateLogMapper.selectByPrimaryKey(id));
    }

    @Override
    public PageModel<SafetyOperateLogDo> pageList(Integer operateStatus, Integer operateType, Long pageNum,
                                                  Long pageSize, Long cardId) {
        if (ObjectUtils.isEmpty(pageNum) || ObjectUtils.isEmpty(pageSize) || ObjectUtils.isEmpty(cardId)) {
            throw new BizException(CardApplyErrorCodeEnum.PARAM_ERROR);
        }
        IPage<SafetyOperateLogPO> iPage = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SafetyOperateLogPO> queryWrapper = Wrappers.<SafetyOperateLogPO>lambdaQuery()
                .eq(!ObjectUtils.isEmpty(operateStatus), SafetyOperateLogPO::getOperateStatus, operateStatus)
                .eq(!ObjectUtils.isEmpty(operateType), SafetyOperateLogPO::getRequestUrl,
                        LogOperateType.getCodeByType(operateType))
                .eq(SafetyOperateLogPO::getBizId, String.valueOf(cardId))
                .eq(SafetyOperateLogPO::getAppCode, AppCodeEnum.CARD.getAppCode())
                .eq(SafetyOperateLogPO::getIsDeleted, OAUCFCommonConstants.INT_ZERO)
                .orderByDesc(SafetyOperateLogPO::getId);
        IPage<SafetyOperateLogPO> res = safetyOperateLogMapper.selectPage(iPage, queryWrapper);
        return PageModel.build(safetyOperateLogPOConverter.toDOList(res.getRecords()), res.getSize(), res.getPages(),
                res.getTotal());
    }

    @Override
    public SafetyOperateLogDo getOneByBizId(CardInfoDo cardInfoDo) {
        LambdaQueryWrapper<SafetyOperateLogPO> queryWrapper = Wrappers.<SafetyOperateLogPO>lambdaQuery()
                .eq(StringUtils.isNotEmpty(cardInfoDo.getId().toString()), SafetyOperateLogPO::getBizId, cardInfoDo.getId().toString())
//                .eq(SafetyOperateLogPO::getOperateStatus, SafetySyncStatusEnum.SUCCESS_SYNC.getCode())
                .eq(SafetyOperateLogPO::getRequestUrl, cardInfoDo.getSafetyOperateLog().getOperateTypeEnum().getCode())
                .orderByDesc(SafetyOperateLogPO::getUpdateTime)
                .last(" limit 1");
        return safetyOperateLogPOConverter.toDO(safetyOperateLogMapper.selectOne(queryWrapper));
    }

    @Override
    public void updateOperateStatus(SafetyOperateLogDo safetyOperateLogDo) {
        LambdaUpdateWrapper<SafetyOperateLogPO> updateWrapper = Wrappers.<SafetyOperateLogPO>lambdaUpdate()
                .eq(SafetyOperateLogPO::getId, safetyOperateLogDo.getId())
                .set(SafetyOperateLogPO::getOperateStatus, safetyOperateLogDo.getOperateStatus());
        safetyOperateLogMapper.update(new SafetyOperateLogPO(), updateWrapper);
    }
}
