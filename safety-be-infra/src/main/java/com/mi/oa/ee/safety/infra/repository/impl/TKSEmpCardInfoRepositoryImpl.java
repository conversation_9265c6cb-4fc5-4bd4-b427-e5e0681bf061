package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.ee.safety.domain.model.TKSEmpCardInfoDo;
import com.mi.oa.ee.safety.infra.repository.TKSEmpCardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.TKSEmpCardInfoPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.TKSEmpCardInfoPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.TKSEmpCardInfoMapper;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.List;
import java.util.Objects;

@Service
public class TKSEmpCardInfoRepositoryImpl implements TKSEmpCardInfoRepository {
    @Resource
    private TKSEmpCardInfoMapper tksEmpCardInfoMapper;
    @Resource
    private TKSEmpCardInfoPoConverter tksEmpCardInfoPoConverter;

    @Override
    public TKSEmpCardInfoDo getTksEmpCardInfoByUid(String uid) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        LambdaQueryWrapper<TKSEmpCardInfoPo> queryWrapper = Wrappers.<TKSEmpCardInfoPo>lambdaQuery()
                .eq(TKSEmpCardInfoPo::getUid, uid)
                .eq(TKSEmpCardInfoPo::getStatus, OAUCFCommonConstants.INT_ZERO);
        List<TKSEmpCardInfoPo> tksEmpCardInfoPos = tksEmpCardInfoMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(tksEmpCardInfoPos)) {
            queryWrapper = Wrappers.<TKSEmpCardInfoPo>lambdaQuery()
                    .eq(TKSEmpCardInfoPo::getUid, uid)
                    .ne(TKSEmpCardInfoPo::getStatus, OAUCFCommonConstants.INT_ZERO)
                    .orderByDesc(TKSEmpCardInfoPo::getId);
            tksEmpCardInfoPos = tksEmpCardInfoMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tksEmpCardInfoPos)) {
                return null;
            }
        }

        return tksEmpCardInfoPoConverter.toDo(tksEmpCardInfoPos.get(0));
    }

    @Override
    public void insertSelective(TKSEmpCardInfoDo tksEmpCardInfoDo) {
        TKSEmpCardInfoPo po = tksEmpCardInfoPoConverter.toPo(tksEmpCardInfoDo);

        if (Objects.nonNull(po)) {
            tksEmpCardInfoMapper.insertSelective(po);
        }
    }

    @Override
    public TKSEmpCardInfoDo selectOneByUidWithUserId(TKSEmpCardInfoDo tksEmpCardInfoDo) {
        if (StringUtils.isBlank(tksEmpCardInfoDo.getUid()) && StringUtils.isBlank(tksEmpCardInfoDo.getThirdUserId())) {
            return null;
        }

        LambdaQueryWrapper<TKSEmpCardInfoPo> queryWrapper = Wrappers.<TKSEmpCardInfoPo>lambdaQuery()
                .eq(StringUtils.isNotBlank(tksEmpCardInfoDo.getUid()), TKSEmpCardInfoPo::getUid, tksEmpCardInfoDo.getUid())
                .eq(StringUtils.isNotBlank(tksEmpCardInfoDo.getThirdUserId()), TKSEmpCardInfoPo::getThirdUserId, tksEmpCardInfoDo.getThirdUserId())
                .eq(TKSEmpCardInfoPo::getStatus, 0);
        List<TKSEmpCardInfoPo> tksEmpCardInfoPos = tksEmpCardInfoMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(tksEmpCardInfoPos)) {
            return null;
        }

        return tksEmpCardInfoPoConverter.toDo(tksEmpCardInfoPos.get(0));
    }

    @Override
    public void updateByPrimaryKeySelective(TKSEmpCardInfoDo tksEmpCardInfoDo) {
        if (Objects.isNull(tksEmpCardInfoDo) || Objects.isNull(tksEmpCardInfoDo.getId())) {
            return;
        }

        TKSEmpCardInfoPo po = tksEmpCardInfoPoConverter.toPo(tksEmpCardInfoDo);

        tksEmpCardInfoMapper.updateByPrimaryKeySelective(po);
    }

    @Override
    public TKSEmpCardInfoDo findByEncryptCode(String mediumEncryptCode) {
        if (StringUtils.isBlank(mediumEncryptCode)) {
            return null;
        }

        LambdaQueryWrapper<TKSEmpCardInfoPo> queryWrapper = Wrappers.<TKSEmpCardInfoPo>lambdaQuery()
                .eq(TKSEmpCardInfoPo::getMediumEncryptCode, mediumEncryptCode)
                .eq(TKSEmpCardInfoPo::getStatus, 0);

        return tksEmpCardInfoPoConverter.toDo(tksEmpCardInfoMapper.selectOne(queryWrapper));
    }

    @Override
    public TKSEmpCardInfoDo findByPhysicsCode(String mediumPhysicsCode) {
        if (StringUtils.isBlank(mediumPhysicsCode)) {
            return null;
        }

        LambdaQueryWrapper<TKSEmpCardInfoPo> queryWrapper = Wrappers.<TKSEmpCardInfoPo>lambdaQuery()
                .eq(TKSEmpCardInfoPo::getMediumPhysicsCode, mediumPhysicsCode)
                .eq(TKSEmpCardInfoPo::getStatus, 0);

        return tksEmpCardInfoPoConverter.toDo(tksEmpCardInfoMapper.selectOne(queryWrapper));
    }
}
