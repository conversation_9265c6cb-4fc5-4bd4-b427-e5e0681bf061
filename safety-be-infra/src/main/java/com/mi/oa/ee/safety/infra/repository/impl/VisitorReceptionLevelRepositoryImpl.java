package com.mi.oa.ee.safety.infra.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.ee.safety.domain.model.VisitorReceptionLevelDo;
import com.mi.oa.ee.safety.infra.repository.VisitorReceptionLevelRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.VisitorReceptionLevelPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorReceptionLevelPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.VisitorReceptionLevelMapper;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/5/10 17:01
 */
@Slf4j
@Service
public class VisitorReceptionLevelRepositoryImpl
        extends ServiceImpl<VisitorReceptionLevelMapper, VisitorReceptionLevelPO>
        implements VisitorReceptionLevelRepository {

    @Autowired
    private VisitorReceptionLevelPoConverter converter;

    @Override
    public VisitorReceptionLevelDo find(Long id) {
        VisitorReceptionLevelPO levelPO = getById(id);

        return converter.toDo(levelPO);
    }

    @Override
    public VisitorReceptionLevelDo find(VisitorReceptionLevelDo levelDo) {
        LambdaQueryWrapper<VisitorReceptionLevelPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtil.isNotEmpty(levelDo.getName()), VisitorReceptionLevelPO::getName, levelDo.getName());
        VisitorReceptionLevelPO levelPO = getOne(wrapper);

        return converter.toDo(levelPO);
    }

    @Override
    public List<VisitorReceptionLevelDo> list(VisitorReceptionLevelDo levelDo) {
        LambdaQueryWrapper<VisitorReceptionLevelPO> wrapper = Wrappers.lambdaQuery();
        wrapper
                .like(ObjectUtil.isNotEmpty(levelDo.getName()), VisitorReceptionLevelPO::getName, levelDo.getName())
                .eq(ObjectUtil.isNotEmpty(levelDo.getLevelStatus()), VisitorReceptionLevelPO::getLevelStatus, levelDo.getLevelStatus())
                .orderByDesc(VisitorReceptionLevelPO::getCreateTime);

        return converter.toDoList(list(wrapper));
    }

    @Override
    public List<VisitorReceptionLevelDo> listByIds(List<Long> ids) {
        LambdaQueryWrapper<VisitorReceptionLevelPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(VisitorReceptionLevelPO::getId, ids);

        return converter.toDoList(list(wrapper));
    }

    @Override
    public PageModel<VisitorReceptionLevelDo> page(VisitorReceptionLevelDo levelDo, Long pageNum, Long pageSize) {
        IPage<VisitorReceptionLevelPO> iPage = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<VisitorReceptionLevelPO> wrapper = Wrappers.lambdaQuery();
        wrapper
                .like(ObjectUtil.isNotEmpty(levelDo.getName()), VisitorReceptionLevelPO::getName, levelDo.getName())
                .eq(ObjectUtil.isNotEmpty(levelDo.getLevelStatus()), VisitorReceptionLevelPO::getLevelStatus, levelDo.getLevelStatus())
                .orderByDesc(VisitorReceptionLevelPO::getCreateTime);

        IPage<VisitorReceptionLevelPO> page = page(iPage, wrapper);

        return PageModel.build(converter.toDoList(page.getRecords()), page.getSize(), page.getPages(), page.getTotal());
    }

    @Override
    public VisitorReceptionLevelDo create(VisitorReceptionLevelDo levelDo) {
        VisitorReceptionLevelPO levelPO = converter.toPo(levelDo);
        save(levelPO);

        return converter.toDo(levelPO);
    }

    @Override
    public void update(VisitorReceptionLevelDo levelDo) {
        updateById(converter.toPo(levelDo));
    }
}
