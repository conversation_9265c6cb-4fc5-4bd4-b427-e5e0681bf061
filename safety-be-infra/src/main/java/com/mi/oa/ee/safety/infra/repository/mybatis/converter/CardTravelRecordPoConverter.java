package com.mi.oa.ee.safety.infra.repository.mybatis.converter;

import com.mi.oa.ee.safety.domain.model.CardTravelRecordDo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardTravelRecordPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/8/9 21:55
 */
@Mapper(componentModel = "spring")
public interface CardTravelRecordPoConverter {

    CardTravelRecordPo toPo(CardTravelRecordDo cardTravelRecordDo);

    CardTravelRecordDo toDo(CardTravelRecordPo cardTravelRecordPo);

    List<CardTravelRecordDo> toDoList(List<CardTravelRecordPo> cardTravelRecordPoList);
}
