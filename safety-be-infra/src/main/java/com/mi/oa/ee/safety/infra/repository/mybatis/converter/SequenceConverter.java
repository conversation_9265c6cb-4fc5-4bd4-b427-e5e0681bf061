package com.mi.oa.ee.safety.infra.repository.mybatis.converter;

import com.mi.oa.ee.safety.domain.model.SysSequenceDo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SysSequencePo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface SequenceConverter {

    @Mapping(source = "code", target = "code")
    @Mapping(source = "prefix", target = "prefix")
    @Mapping(source = "concat", target = "concat")
    @Mapping(source = "dateFormat", target = "dateFormat")
    @Mapping(source = "current", target = "current")
    @Mapping(source = "step", target = "step")
    SysSequenceDo toSequenceDo(SysSequencePo merchantDo);
}
