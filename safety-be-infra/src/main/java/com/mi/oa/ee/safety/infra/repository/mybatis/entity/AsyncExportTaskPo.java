package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.ZonedDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("async_export_task")
public class AsyncExportTaskPo extends BasePO<AsyncExportTaskPo> {
    /**
     * 操作人uid
     */
    private String uid;

    /**
     * 操作类型
     */
    private Integer opType;

    /**
     * 操作时间
     */
    @TableField(typeHandler = ZonedDateTimeBigIntTypeHandler.class)
    private ZonedDateTime opTime;

    /**
     * 处理状态
     */
    private Integer status;

    /**
     * 导出条数
     */
    private Integer progress;

    /**
     * 参数md5
     */
    private String md5;

    /**
     * 请求参数，json
     */
    private String param;

    /**
     * 文件下载地址
     */
    private String url;

    /**
     * 失败消息
     */
    private String message;

    /**
     * 国际化
     */
    private String locale;

    /**
     * 导出图片的文件夹
     */
    private String imgFolder;
}
