package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/08/18/03:43
 */
@Data
@TableName(value = "safety_carrier_group_class", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SafetyCarrierGroupClassPO extends BasePO<SafetyCarrierGroupClassPO> {
    /**
     * 分类编码 class_code
     */
    private String classCode;

    /**
     * 载体集编码 carrier_group_code
     */
    private String carrierGroupCode;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;
}