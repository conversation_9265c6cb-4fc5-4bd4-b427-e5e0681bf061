package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/10/18/02:21
 */
@Data
@TableName(value = "safety_person_medium", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SafetyPersonMediumPO extends BasePO<SafetyPersonMediumPO> {
    /**
     * 用户UID uid
     */
    private String uid;

    /**
     * 介质编码 medium_code
     */
    private String mediumCode;

    /**
     * 同步状态
     */
    private Integer syncStatus;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 开始时间 start_time
     */
    private ZonedDateTime startTime;

    /**
     * 结束时间 end_time
     */
    private ZonedDateTime endTime;

    /**
     * 是否主介质 is_main
     */
    private Integer isMain;
}