package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/09/23/11:08
 */
@Data
@TableName(value = "safety_right", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SafetyRightPO extends BasePO<SafetyRightPO> {

    /**
     * 人员id uid
     */
    private String uid;

    /**
     * 介质编码 medium_code
     */
    private String mediumCode;

    /**
     * 载体集编码 carrier_group_code
     */
    private String carrierGroupCode;

    /**
     * 供应商编码 supplier_code
     */
    private String supplierCode;

    /**
     * 供应商侧校验编码 supplier_check_code
     */
    private String supplierCheckCode;

    /**
     * 供应商侧访问码，可以是json等 supplier_access_code
     */
    private String supplierAccessCode;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 最近一次操作时间
     */
    private ZonedDateTime operateTime;

    /**
     * 开始时间 start_time
     */
    private ZonedDateTime startTime;

    /**
     * 同步状态：0：待同步 1：同步一次  2：同步2次  200：已同步  sync_status
     */
    private Integer syncStatus;

    /**
     * 结束时间 end_time
     */
    private ZonedDateTime endTime;
}