package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZonedDateTime;
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "t_ks_emp_card_info", autoResultMap = true)
public class TKSEmpCardInfoPo extends BasePO<TKSEmpCardInfoPo> {
    /**
     * 员工在金山userId
     */
    private String thirdUserId;

    /**
     * 员工在金山账号
     */
    private String thirdUserName;

    /**
     * 员工姓名
     */
    private String thirdName;

    /**
     * 员工在金山的工号
     */
    private String thirdEmpNo;

    /**
     * 员工在小米注册的uid
     */
    private String uid;

    /**
     * 公司code，kingsoft等
     */
    private String company;

    /**
     * 加密卡号
     */
    private String mediumEncryptCode;

    /**
     * 物理卡号
     */
    private String mediumPhysicsCode;

    /**
     * 工卡状态 0启用 -1禁用
     */
    private Integer status;

    /**
     * 更新时间戳
     */
    private ZonedDateTime expireDate;

}
