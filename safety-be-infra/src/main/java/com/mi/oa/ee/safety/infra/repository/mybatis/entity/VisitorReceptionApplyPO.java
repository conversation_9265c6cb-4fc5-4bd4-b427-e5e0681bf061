package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import lombok.*;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/06/08/11:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "visitor_reception_apply", autoResultMap = true)
public class VisitorReceptionApplyPO extends BasePO<VisitorReceptionApplyPO> {
    /**
     * 接待申请ID apply_id
     */
    private Long applyId;

    /**
     * 接待配置id
     */
    private Long receptionConfigId;

    /**
     * 来宾类型ID reception_guest_id
     */
    private Long receptionGuestId;

    /**
     * 来宾人数 guest_num
     */
    private Integer guestNum;

    /**
     * 来宾名单列表 guest_list_url
     */
    private String guestListUrl;

    /**
     * 来宾名单列表名称 guest_list_url_name
     */
    private String guestListUrlName;

    /**
     * 接待等级ID reception_level_id
     */
    private Long receptionLevelId;

    /**
     * 来宾紧急对接人 emergency_contact_guest
     */
    private String emergencyContactGuest;

    /**
     * 我方陪同人员uids entourages
     */
    private String entourages;

    /**
     * 签入时间 check_in_time
     */
    @TableField(typeHandler = ZonedDateTimeBigIntTypeHandler.class)
    private ZonedDateTime checkInTime;

    /**
     * 签出时间 check_out_time
     */
    @TableField(typeHandler = ZonedDateTimeBigIntTypeHandler.class)
    private ZonedDateTime checkOutTime;

    /**
     * 行程安排 scheduling
     */
    private String scheduling;

    /**
     * 行程附件url scheduling_attach_url
     */
    private String schedulingAttachUrl;

    /**
     * 行程附件名称 scheduling_attach_url_name
     */
    private String schedulingAttachUrlName;

    /**
     * 复核状态；1：待复核，2：复核通过，3：复合驳回 check_status
     */
    private Integer checkStatus;

    /**
     * 复合驳回原因 check_reject_reason
     */
    private String checkRejectReason;

    /**
     * 复核人uid checker
     */
    private String checker;

    /**
     * 复核时间 check_time
     */
    @TableField(typeHandler = ZonedDateTimeBigIntTypeHandler.class)
    private ZonedDateTime checkTime;

    /**
     * wifi密码 wifi_pwd
     */
    private String wifiPwd;

    /**
     * 0：未评价 1：已评价 evaluate_status
     */
    private Integer evaluateStatus;

    /**
     * 1:职场签到 2:业务签到 sign_type
     */
    private Integer signType;

    /**
     * 是否可上传来访名单：0 - 否，1 - 是 uploadable
     */
    private Boolean uploadable;

    /**
     * 关键人员摘要
     */
    private String guestSummary;

    /**
     * 保密协议备注信息
     */
    private String caComment;
}