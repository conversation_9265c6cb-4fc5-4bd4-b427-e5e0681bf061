package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/05/16/04:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "visitor_reception_apply_service", autoResultMap = true)
public class VisitorReceptionApplyServicePO extends BasePO<VisitorReceptionApplyServicePO> {
    /**
     * 接待申请ID reception_apply_id
     */
    private Long receptionApplyId;

    /**
     * 接待服务ID service_id
     */
    private Long serviceId;

    /**
     * 服务是否需要；0：不需要，1：需要 is_need
     */
    private Integer isNeed;

    /**
     * 申请服务状态；1：待完成，2：已完成 apply_service_status
     */
    private Integer applyServiceStatus;

    /**
     * 申请服务项内容json apply_service_item_json
     */
    private String applyServiceItemJson;
}