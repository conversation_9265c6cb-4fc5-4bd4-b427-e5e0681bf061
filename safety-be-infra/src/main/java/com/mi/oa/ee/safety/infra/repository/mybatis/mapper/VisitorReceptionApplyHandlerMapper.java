package com.mi.oa.ee.safety.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorReceptionApplyHandlerPO;

/**
 * table_name : visitor_reception_apply_handler
 * <AUTHOR>
 * @date 2023/06/01/09:22
 */
public interface VisitorReceptionApplyHandlerMapper extends BaseMapper<VisitorReceptionApplyHandlerPO> {
    /**
     *
     * @mbg.generated
     */
    VisitorReceptionApplyHandlerPO selectByPrimaryKey(Long id);
}