package com.mi.oa.ee.safety.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorReceptionApplyServicePO;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.VisitorReceptionApplyServiceMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.service.ReceptionServiceDaoService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/5/11 19:57
 */
@Service
public class ReceptionServiceDaoServiceImpl
        extends ServiceImpl<VisitorReceptionApplyServiceMapper, VisitorReceptionApplyServicePO>
        implements ReceptionServiceDaoService {
}
