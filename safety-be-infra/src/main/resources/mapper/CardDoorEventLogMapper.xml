<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardDoorEventLogMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardDoorEventLogPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="card_number" jdbcType="VARCHAR" property="cardNumber" />
    <result column="event_date" jdbcType="DATE" property="eventDate" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="device_description" jdbcType="VARCHAR" property="deviceDescription" />
    <result column="access_point_id" jdbcType="VARCHAR" property="accessPointId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="data_source" jdbcType="INTEGER" property="dataSource" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, username, card_number, event_date, event_time, device_description, access_point_id,
    create_time, data_source, status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from card_door_event_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="pageSwipeCardRecordList"
          resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardDoorEventLogPo">
    select id, name, username, card_number, event_date, event_time, device_description, access_point_id
    from card_door_event_log
    <where>
      <if test="!isCardNum">
        and username = #{userName,jdbcType=VARCHAR}
      </if>
      <if test="isCardNum">
        and card_number = #{userName,jdbcType=VARCHAR}
      </if>
    </where>
    and event_date between #{startDateTime,jdbcType=DATE} and #{endDateTime,jdbcType=DATE}
    order by event_time desc
  </select>
  <select id="getDoorEventLog"
          resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardDoorEventLogPo">
    select id, name, username, card_number, event_date, event_time, device_description, access_point_id
    from card_door_event_log
    <where>
      <if test="userName != null">
        and username = #{userName,jdbcType=VARCHAR}
      </if>
    </where>
    order by event_time desc
    limit 1
  </select>
</mapper>