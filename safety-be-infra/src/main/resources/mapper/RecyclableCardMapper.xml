<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.RecyclableCardMapper">
    <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.RecyclableCardPo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="card_num" jdbcType="VARCHAR" property="cardNum" />
        <result column="seq" jdbcType="VARCHAR" property="seq" />
        <result column="medium_physics_code" jdbcType="VARCHAR" property="mediumPhysicsCode" />
        <result column="medium_encrypt_code" jdbcType="VARCHAR" property="mediumEncryptCode" />
        <result column="card_status" jdbcType="INTEGER" property="cardStatus" />
        <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
        <result column="operate_time" jdbcType="BIGINT" property="operateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
        <result column="uid" jdbcType="VARCHAR" property="uid" />
        <result column="operator_uid" jdbcType="VARCHAR" property="operatorUid" />
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    </resultMap>

    <sql id="Base_Column_List">
        id, card_num, seq, medium_physics_code, medium_encrypt_code, card_status, country_code, operate_time, uid,
    operator_uid, is_deleted, create_user, create_time, update_user, update_time
    </sql>

    <select id="findMaxSeqRecyclableCard" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from card_recyclable_card
        where country_code = #{code,jdbcType=VARCHAR} order by seq desc limit 1;
    </select>
</mapper>