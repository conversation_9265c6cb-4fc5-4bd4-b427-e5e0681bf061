<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyRightMapper">
    <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyRightPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="uid" jdbcType="VARCHAR" property="uid"/>
        <result column="medium_code" jdbcType="VARCHAR" property="mediumCode"/>
        <result column="carrier_group_code" jdbcType="VARCHAR" property="carrierGroupCode"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_check_code" jdbcType="VARCHAR" property="supplierCheckCode"/>
        <result column="supplier_access_code" jdbcType="VARCHAR" property="supplierAccessCode"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="operate_time" jdbcType="BIGINT" property="operateTime"
                typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler"/>
        <result column="start_time" jdbcType="BIGINT" property="startTime"
                typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler"/>
        <result column="sync_status" jdbcType="INTEGER" property="syncStatus"/>
        <result column="end_time" jdbcType="BIGINT" property="endTime"
                typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler"/>
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"
                typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"
                typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, uid, medium_code, carrier_group_code, supplier_code, supplier_check_code, supplier_access_code, tenant_code,
    start_time, sync_status, end_time, is_deleted, create_user, create_time, update_user, 
    update_time,operate_time
    </sql>
    <sql id="Base_Column_List_t">
        t.id, t.uid, t.medium_code, t.carrier_group_code, t.supplier_check_code, t.supplier_access_code, t.tenant_code,
    t.start_time, t.sync_status, t.end_time, t.is_deleted, t.create_user, t.create_time, t.update_user,
    t.update_time,t.operate_time
    </sql>
    <update id="batchUpdateSyncStatusToWaitSync">
        update safety_right
        set sync_status = 0, update_user = #{updateUser,jdbcType=VARCHAR}, operate_time = unix_timestamp(), update_time = #{updateTime,jdbcType=BIGINT}
        <where>
            <if test="carrierGroupCodes.size > 0">
                and carrier_group_code in
                <foreach collection="carrierGroupCodes" open="(" close=")" item="item" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="mediumCode != null and mediumCode != ''">
                and medium_code = #{mediumCode,jdbcType=VARCHAR}
            </if>
            <if test="updateDelete == 0">
                and is_deleted = 0
            </if>
            <if test="updateDelete == 1">
                and is_deleted != 0
            </if>
        </where>
    </update>
    <update id="syncRightByMediumCode">
        update safety_right set sync_status = 0, operate_time = unix_timestamp(), update_time = #{updateTime,jdbcType=BIGINT},
        update_user = #{loginUid,jdbcType=VARCHAR} where medium_code = #{mediumCode,jdbcType=VARCHAR}
        <if test="isDelete != null and isDelete == 0">
            and is_deleted = 0
        </if>
        <if test="isDelete != null and isDelete != 0">
            and is_deleted != 0
        </if>
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from safety_right
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="getCarrierGroupsByMediums" resultType="com.mi.oa.ee.safety.domain.model.SafetyRightDo">
        select
        distinct
        <include refid="Base_Column_List_t"/>
        from safety_right t
        <where>
            <if test="mediumCodes.size == 0">
                and t.medium_code = null
            </if>
            <if test="mediumCodes.size > 0">
                and t.medium_code in
                <foreach collection="mediumCodes" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            and t.is_deleted = 0
        </where>
    </select>

    <insert id="batchSaveOrUpdate">
        insert into safety_right(
        uid,
        medium_code,
        carrier_group_code,
        supplier_code,
        supplier_check_code,
        supplier_access_code,
        operate_time,
        start_time,
        end_time,
        sync_status,
        create_time,
        update_time,
        create_user,
        update_user,
        is_deleted
        )values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uid},
            #{item.mediumCode},
            #{item.carrierGroupCode},
            #{item.supplierCode},
            #{item.supplierCheckCode},
            #{item.supplierAccessCode},
            #{item.operateTime},
            #{item.startTime},
            #{item.endTime},
            #{item.syncStatus},
            #{item.createTime,typeHandler=com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler},
            #{item.updateTime,typeHandler=com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler},
            #{item.createUser},
            #{item.updateUser},
            #{item.isDeleted}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        uid =VALUES(uid),
        medium_code =VALUES(medium_code),
        carrier_group_code =VALUES(carrier_group_code),
        supplier_code =VALUES(supplier_code),
        supplier_check_code =VALUES(supplier_check_code),
        supplier_access_code =VALUES(supplier_access_code),
        operate_time=values(operate_time),
        sync_status =VALUES(sync_status),
        update_time=VALUES(update_time),
        is_deleted=VALUES(is_deleted)
    </insert>

    <insert id="batchSaveOrUpdateWithIsDeleted">
        insert into safety_right(
        uid,
        medium_code,
        carrier_group_code,
        supplier_code,
        supplier_check_code,
        supplier_access_code,
        operate_time,
        start_time,
        end_time,
        sync_status,
        create_time,
        update_time,
        create_user,
        update_user,
        is_deleted
        )values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uid},
            #{item.mediumCode},
            #{item.carrierGroupCode},
            #{item.supplierCode},
            #{item.supplierCheckCode},
            #{item.supplierAccessCode},
            #{item.operateTime},
            #{item.startTime},
            #{item.endTime},
            #{item.syncStatus},
            #{item.createTime,typeHandler=com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler},
            #{item.updateTime,typeHandler=com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler},
            #{item.createUser},
            #{item.updateUser},
            #{item.isDeleted}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        uid =VALUES(uid),
        medium_code =VALUES(medium_code),
        carrier_group_code =VALUES(carrier_group_code),
        supplier_code =VALUES(supplier_code),
        supplier_check_code =VALUES(supplier_check_code),
        supplier_access_code =VALUES(supplier_access_code),
        operate_time=values(operate_time),
        start_time=VALUES(start_time),
        end_time=VALUES(end_time),
        sync_status =VALUES(sync_status),
        update_time=VALUES(update_time),
        is_deleted=VALUES(is_deleted)
    </insert>

    <update id="updateSyncStatusByIdList">
        update safety_right set sync_status = #{syncStatus,jdbcType=INTEGER},update_user =
        #{updateUser,jdbcType=VARCHAR},update_time = #{updateTime} where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectListByCondition"
            resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyRightPO"
            parameterType="com.mi.oa.ee.safety.infra.repository.query.SafetyRightQuery">
        select
        <include refid="Base_Column_List"/>
        from safety_right
        <where>
            <if test="supplierCode != null and supplierCode != ''">
                and supplier_code = #{supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="mediumCode != null and mediumCode != ''">
                and medium_code = #{mediumCode,jdbcType=VARCHAR}
            </if>
            <if test="uid != null and uid != ''">
                and uid = #{uid,jdbcType=VARCHAR}
            </if>
            <if test="supplierCodeList != null and supplierCodeList.size >0">
                and supplier_code in
                <foreach collection="supplierCodeList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="startTime != null">
                and start_time &gt; #{startTime,jdbcType=INTEGER}
                and start_time != 0
            </if>
            <if test="expireTime != null">
                and end_time &lt; #{expireTime,jdbcType=INTEGER}
                and end_time != 0
                and end_time &gt; 1688356800
            </if>
            <if test="syncStatus != null">
                and sync_status = #{syncStatus,jdbcType=INTEGER}
            </if>
            <if test="needSyncStatusNum != null">
                and sync_status &lt; #{needSyncStatusNum,jdbcType=INTEGER}
            </if>
            <if test="queryStartTime != null">
                and update_time &gt; #{queryStartTime,jdbcType=INTEGER}
            </if>
            <if test="queryEndTime != null">
                and update_time &lt; #{queryEndTime,jdbcType=INTEGER}
            </if>
            <if test="groupCodes != null and groupCodes.size > 0">
                and carrier_group_code in
                <foreach collection="groupCodes" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="mediumCodes != null and mediumCodes.size > 0">
                and medium_code in
                <foreach collection="mediumCodes" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="uidList != null and uidList.size > 0">
                and uid in
                <foreach collection="uidList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="isDeleted != null and isDeleted == 0">
                and is_deleted = 0
            </if>
            <if test="isDeleted != null and isDeleted == 1">
                and is_deleted != 0
            </if>
        </where>
    </select>
    <select id="pageMediumCodeByCarrierGroupCode" resultType="java.lang.String">
        select distinct medium_code
        from safety_right
        where carrier_group_code = #{carrierGroupCode,jdbcType=VARCHAR}
        and is_deleted = 0
    </select>
</mapper>