<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyUmsConfigMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyUmsConfigPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_code" jdbcType="VARCHAR" property="appCode" />
    <result column="message_type" jdbcType="INTEGER" property="messageType" />
    <result column="message_name" jdbcType="VARCHAR" property="messageName" />
    <result column="bot_biz_id" jdbcType="VARCHAR" property="botBizId" />
    <result column="template_biz_id" jdbcType="VARCHAR" property="templateBizId" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyUmsConfigPO">
    <result column="template_content" jdbcType="LONGVARCHAR" property="templateContent" />
    <result column="params" jdbcType="LONGVARCHAR" property="params" />
  </resultMap>
  <sql id="Base_Column_List">
    id, app_code, message_type, message_name, bot_biz_id, template_biz_id, tenant_code, 
    is_deleted, create_user, create_time, update_user, update_time
  </sql>
  <sql id="Blob_Column_List">
    template_content, params
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from safety_ums_config
    where id = #{id,jdbcType=BIGINT}
  </select>
</mapper>