<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.VisitorReceptionApplyMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorReceptionApplyPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_id" jdbcType="BIGINT" property="applyId" />
    <result column="reception_guest_id" jdbcType="BIGINT" property="receptionGuestId" />
    <result column="guest_num" jdbcType="INTEGER" property="guestNum" />
    <result column="guest_list_url" jdbcType="VARCHAR" property="guestListUrl" />
    <result column="guest_list_url_name" jdbcType="VARCHAR" property="guestListUrlName" />
    <result column="reception_level_id" jdbcType="BIGINT" property="receptionLevelId" />
    <result column="emergency_contact_guest" jdbcType="VARCHAR" property="emergencyContactGuest" />
    <result column="entourages" jdbcType="VARCHAR" property="entourages" />
    <result column="check_in_time" jdbcType="BIGINT" property="checkInTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="check_out_time" jdbcType="BIGINT" property="checkOutTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="scheduling" jdbcType="VARCHAR" property="scheduling" />
    <result column="scheduling_attach_url" jdbcType="VARCHAR" property="schedulingAttachUrl" />
    <result column="scheduling_attach_url_name" jdbcType="VARCHAR" property="schedulingAttachUrlName" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="check_reject_reason" jdbcType="VARCHAR" property="checkRejectReason" />
    <result column="checker" jdbcType="VARCHAR" property="checker" />
    <result column="check_time" jdbcType="BIGINT" property="checkTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="wifi_pwd" jdbcType="VARCHAR" property="wifiPwd" />
    <result column="evaluate_status" jdbcType="TINYINT" property="evaluateStatus" />
    <result column="sign_type" jdbcType="TINYINT" property="signType" />
    <result column="uploadable" jdbcType="BIT" property="uploadable" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <sql id="Base_Column_List">
    id, apply_id, reception_guest_id, guest_num, guest_list_url, guest_list_url_name, 
    reception_level_id, emergency_contact_guest, entourages, check_in_time, check_out_time, 
    scheduling, scheduling_attach_url, scheduling_attach_url_name, check_status, check_reject_reason, 
    checker, check_time, wifi_pwd, evaluate_status, sign_type, uploadable, is_deleted, 
    create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from visitor_reception_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
</mapper>