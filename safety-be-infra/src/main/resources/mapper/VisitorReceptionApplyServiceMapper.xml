<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.VisitorReceptionApplyServiceMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorReceptionApplyServicePO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="reception_apply_id" jdbcType="BIGINT" property="receptionApplyId" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="is_need" jdbcType="BIT" property="isNeed" />
    <result column="apply_service_status" jdbcType="INTEGER" property="applyServiceStatus" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorReceptionApplyServicePO">
    <result column="apply_service_item_json" jdbcType="LONGVARCHAR" property="applyServiceItemJson" />
  </resultMap>
  <sql id="Base_Column_List">
    id, reception_apply_id, service_id, is_need, apply_service_status, is_deleted, create_user, 
    create_time, update_user, update_time
  </sql>
  <sql id="Blob_Column_List">
    apply_service_item_json
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from visitor_reception_apply_service
    where id = #{id,jdbcType=BIGINT}
  </select>
</mapper>