package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyCardDoorEventLogDo;
import com.mi.oa.ee.safety.infra.repository.CardInfoRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.SafetyCardDoorEventLogPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardDoorEventLogPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardDoorEventLogMapper;
import com.mi.oa.ee.safety.infra.repository.query.SafetyCardDoorEventLogQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class SafetyCardDoorEventLogRepositoryImplTest {

    @InjectMocks
    private SafetyCardDoorEventLogRepositoryImpl repository;

    @Mock
    private CardDoorEventLogMapper cardDoorEventLogMapper;

    @Mock
    private CardInfoRepository cardInfoRepository;

    @Mock
    private SafetyCardDoorEventLogPoConverter safetyCardDoorEventLogPoConverter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void pageSwipeCardRecordList_WhenQueryIsNull_ShouldThrowException() {
        assertThrows(BizException.class, () -> repository.pageSwipeCardRecordList(null));
    }

    @Test
    void pageSwipeCardRecordList_WhenUserNameIsEmpty_ShouldThrowException() {
        SafetyCardDoorEventLogQuery query = new SafetyCardDoorEventLogQuery();
        query.setUserName("");
        assertThrows(BizException.class, () -> repository.pageSwipeCardRecordList(query));
    }

    @Test
    void pageSwipeCardRecordList_WhenCardNumNotFound_ShouldThrowException() {
        SafetyCardDoorEventLogQuery query = new SafetyCardDoorEventLogQuery();
        query.setUserName("A123");
        when(cardInfoRepository.getCardByCardNum("A123")).thenReturn(null);
        
        assertThrows(BizException.class, () -> repository.pageSwipeCardRecordList(query));
    }

    @Test
    void pageSwipeCardRecordList_WhenCardNumNotMatchPattern_ShouldQueryDirectly() {
        // 准备测试数据
        SafetyCardDoorEventLogQuery query = new SafetyCardDoorEventLogQuery();
        query.setUserName("testUser"); // 不符合A-C开头的数字格式
        
        Page<CardDoorEventLogPo> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0);
        page.setCurrent(1);
        page.setSize(10);
        
        // 设置mock行为
        when(cardDoorEventLogMapper.pageSwipeCardRecordList(any(), any(), any(), any(), anyBoolean()))
            .thenReturn(page);
        when(safetyCardDoorEventLogPoConverter.toDoList(anyList()))
            .thenReturn(Collections.emptyList());
        
        // 执行测试
        PageModel<SafetyCardDoorEventLogDo> result = repository.pageSwipeCardRecordList(query);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.getList().isEmpty());
        
        // 验证方法调用 - 直接使用原始用户名查询，isCardNum为false
        verify(cardDoorEventLogMapper).pageSwipeCardRecordList(
            any(),
            any(),
            any(),
            eq("testUser"),
            eq(false)
        );
    }

    @Test
    void pageSwipeCardRecordList_WithValidCardNum_ShouldConvertAndQuery() {
        // 准备测试数据
        SafetyCardDoorEventLogQuery query = new SafetyCardDoorEventLogQuery();
        query.setUserName("A123");
        
        CardInfoDo cardInfo = new CardInfoDo();
        cardInfo.setMediumEncryptCode("ENCRYPTED123");
        
        CardDoorEventLogPo logPo = new CardDoorEventLogPo();
        Page<CardDoorEventLogPo> page = new Page<>();
        page.setRecords(Collections.singletonList(logPo));
        page.setTotal(1);
        page.setCurrent(1);
        page.setSize(10);
        
        SafetyCardDoorEventLogDo logDo = new SafetyCardDoorEventLogDo();
        
        // 设置mock行为
        when(cardInfoRepository.getCardByCardNum("A123")).thenReturn(cardInfo);
        when(cardDoorEventLogMapper.pageSwipeCardRecordList(any(), any(), any(), any(), anyBoolean()))
            .thenReturn(page);
        when(safetyCardDoorEventLogPoConverter.toDoList(anyList()))
            .thenReturn(Collections.singletonList(logDo));
        
        // 执行测试
        PageModel<SafetyCardDoorEventLogDo> result = repository.pageSwipeCardRecordList(query);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        
        // 验证方法调用
        verify(cardInfoRepository).getCardByCardNum("A123");
        verify(cardDoorEventLogMapper).pageSwipeCardRecordList(any(), any(), any(), eq("ENCRYPTED123"), eq(true));
    }

    @Test
    void pageSwipeCardRecordList_WithDefaultPagination_ShouldUseDefaultValues() {
        // 准备测试数据
        SafetyCardDoorEventLogQuery query = new SafetyCardDoorEventLogQuery();
        query.setUserName("testUser");
        
        Page<CardDoorEventLogPo> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0);
        page.setCurrent(1);
        page.setSize(10);
        
        // 设置mock行为
        when(cardDoorEventLogMapper.pageSwipeCardRecordList(any(), any(), any(), any(), anyBoolean()))
            .thenReturn(page);
        when(safetyCardDoorEventLogPoConverter.toDoList(anyList()))
            .thenReturn(Collections.emptyList());
        
        // 执行测试
        PageModel<SafetyCardDoorEventLogDo> result = repository.pageSwipeCardRecordList(query);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.getList().isEmpty());
    }

    @Test
    void pageSwipeCardRecordList_WithLargePageSize_ShouldLimitTo100() {
        // 准备测试数据
        SafetyCardDoorEventLogQuery query = new SafetyCardDoorEventLogQuery();
        query.setUserName("testUser");
        query.setPageSize(200L);
        
        Page<CardDoorEventLogPo> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0);
        page.setCurrent(1);
        page.setSize(100);
        
        // 设置mock行为
        when(cardDoorEventLogMapper.pageSwipeCardRecordList(any(), any(), any(), any(), anyBoolean()))
            .thenReturn(page);
        when(safetyCardDoorEventLogPoConverter.toDoList(anyList()))
            .thenReturn(Collections.emptyList());
        
        // 执行测试
        PageModel<SafetyCardDoorEventLogDo> result = repository.pageSwipeCardRecordList(query);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.getList().isEmpty());
        
        // 验证分页大小被限制为100
        verify(cardDoorEventLogMapper).pageSwipeCardRecordList(
            any(),
            any(),
            any(),
            any(),
            anyBoolean()
        );
    }

    @Test
    void pageSwipeCardRecordList_WhenCardNumExists_ShouldConvertAndQuery() {
        // 准备测试数据
        SafetyCardDoorEventLogQuery query = new SafetyCardDoorEventLogQuery();
        query.setUserName("A123");
        
        CardInfoDo cardInfo = new CardInfoDo();
        cardInfo.setMediumEncryptCode("ENCRYPTED123");
        
        Page<CardDoorEventLogPo> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0);
        page.setCurrent(1);
        page.setSize(10);
        
        // 设置mock行为
        when(cardInfoRepository.getCardByCardNum("A123")).thenReturn(cardInfo);
        when(cardDoorEventLogMapper.pageSwipeCardRecordList(any(), any(), any(), any(), anyBoolean()))
            .thenReturn(page);
        when(safetyCardDoorEventLogPoConverter.toDoList(anyList()))
            .thenReturn(Collections.emptyList());
        
        // 执行测试
        PageModel<SafetyCardDoorEventLogDo> result = repository.pageSwipeCardRecordList(query);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.getList().isEmpty());
        
        // 验证方法调用 - 使用转换后的加密卡号查询，isCardNum为true
        verify(cardInfoRepository).getCardByCardNum("A123");
        verify(cardDoorEventLogMapper).pageSwipeCardRecordList(
            any(),
            any(),
            any(),
            eq("ENCRYPTED123"),
            eq(true)
        );
    }

    @Test
    void pageSwipeCardRecordList_WhenCardInfoIsNull_ShouldThrowException() {
        // 准备测试数据
        SafetyCardDoorEventLogQuery query = new SafetyCardDoorEventLogQuery();
        query.setUserName("A123");
        
        // 设置mock行为 - 返回null
        when(cardInfoRepository.getCardByCardNum("A123")).thenReturn(null);
        
        assertThrows(BizException.class, 
            () -> repository.pageSwipeCardRecordList(query));
        
        // 验证方法调用
        verify(cardInfoRepository).getCardByCardNum("A123");
        verify(cardDoorEventLogMapper, never()).pageSwipeCardRecordList(any(), any(), any(), any(), anyBoolean());
    }
} 