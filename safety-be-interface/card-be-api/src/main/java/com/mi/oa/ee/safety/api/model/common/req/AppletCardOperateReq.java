package com.mi.oa.ee.safety.api.model.common.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/3/28 14:41
 */
@ApiModel(description = "小小程序-卡操作请求参数")
@Data
public class AppletCardOperateReq {

    @NotNull(message = "卡编号不能为空")
    @ApiModelProperty(value = "卡片id", required = true, example = "1")
    private Long cardId;

    private String uid;
}
