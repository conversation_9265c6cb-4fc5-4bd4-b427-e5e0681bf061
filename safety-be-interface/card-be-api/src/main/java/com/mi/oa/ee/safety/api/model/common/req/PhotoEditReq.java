package com.mi.oa.ee.safety.api.model.common.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "PhotoEditReq", description = "工卡编辑参数")
public class PhotoEditReq {

    @ApiModelProperty(value = "工卡主键", required = true, dataType = "java.lang.Long")
    @NotNull(message = "{{{工卡id不能为空}}}")
    private Long cardId;

    @ApiModelProperty(value = "uid", required = true, dataType = "java.lang.String")
    @NotNull(message = "uid不能为空")
    private String uid;

    /**
     * 照片地址 photo_url
     */
    @ApiModelProperty(value = "照片地址", dataType = "java.lang.String", example = "")
    private String photoUrl;
}

