package com.mi.oa.ee.safety.api.model.common.vo;

import com.mi.oa.ee.safety.api.model.permission.vo.CardGroupVo;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/25 16:38
 */
@ApiModel(value = "人员信息")
@Data
public class AppletEmployeeInfoVo {

    @ApiModelProperty(value = "人员uid", example = "")
    private String uid;

    @ApiModelProperty(value = "性别 1-男性 2-女性 据说可能有其它性别", example = "1")
    private String sex;

    @ApiModelProperty(value = "官方照url", example = "https://xxx")
    private String photoUrl;

    @ApiModelProperty(value = "工号", example = "M000001")
    private String employeeId;

    @ApiModelProperty(value = "{{{姓名}}}", dataType = "java.lang.String")
    private String displayName;

    @ApiModelProperty(value = "{{{账号}}}", dataType = "java.lang.String")
    private String userName;

    @ApiModelProperty(value = "{{{名字拼音}}}", dataType = "java.lang.String")
    private String pinyinName;

    /**
     * 名字拼音
     */
    @ApiModelProperty(value = "{{{名字拼音}}}", dataType = "java.lang.String")
    private String firstNameEn;

    /**
     * 姓氏拼音
     */
    @ApiModelProperty(value = "{{{姓氏拼音}}}", dataType = "java.lang.String")
    private String lastNameEn;


    @ApiModelProperty(value = "{{{入职时间}}}", dataType = "java.lang.String")
    private ZonedDateTime lastHireDate;

    @ApiModelProperty(value = "正式卡信息")
    private AppletCardInfoVo empCardInfo;

    @ApiModelProperty(value = "临时卡信息")
    private AppletCardInfoVo tempCardInfo;
}
