package com.mi.oa.ee.safety.api.model.coop.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/16 21:44
 */
@Data
@ApiModel(value = "CardPageConditionReq", description = "工卡条件分页查询请求参数")
public class CardPageConditionReq {

    @ApiModelProperty(value = "省份编码", dataType = "java.lang.String")
    private Long provinceId;

    @ApiModelProperty(value = "城市id", dataType = "java.lang.String")
    private Integer cityId;

    @ApiModelProperty(value = "园区编码", dataType = "java.lang.String")
    private String parkCode;

    @ApiModelProperty(value = "载体集编码", dataType = "java.lang.String")
    private String carrierGroupCode;

    @ApiModelProperty(value = "一级部门id", dataType = "java.lang.String")
    private String firstDeptId;

    @ApiModelProperty(value = "二级部门id", dataType = "java.lang.String")
    private String secondDeptId;

    @ApiModelProperty(value = "三级部门id", dataType = "java.lang.String")
    private String thirdDeptId;

    @ApiModelProperty(value = "四级部门id", dataType = "java.lang.String")
    private String fourthDeptId;

    @ApiModelProperty(value = "工卡状态", dataType = "java.lang.Integer")
    private Integer cardStatus;

    @ApiModelProperty(value = "卡类型 合作卡：1 物业卡：4", dataType = "java.lang.Integer")
    private Integer cardType;

    @ApiModelProperty(value = "责任人uid", dataType = "java.lang.String")
    private String responsible;

    @ApiModelProperty(value = "合作伙伴uid", dataType = "java.lang.String")
    private String uid;

    @ApiModelProperty(value = "{{{公司名称}}}", dataType = "java.lang.String")
    private String companyName;

    @ApiModelProperty(value = "{{{介质编码}}}", dataType = "java.lang.String")
    private String mediumCode;

    @ApiModelProperty(value = "{{{物理卡号}}}", dataType = "java.lang.String")
    private String mediumPhysicsCode;

    @ApiModelProperty(value = "{{{加密卡号}}}", dataType = "java.lang.String")
    private String mediumEncryptCode;

    @ApiModelProperty(value = "电话号码", dataType = "java.lang.String")
    private String phone;

    @ApiModelProperty(value = "当前页", dataType = "java.lang.Long")
    private Long pageNum = 1L;

    @ApiModelProperty(value = "页面大小", dataType = "java.lang.Long")
    private Long pageSize = 10L;

    @ApiModelProperty(value = "离职日期", dataType = "java.time.ZonedDateTime")
    private ZonedDateTime preLeaveDate;
}
