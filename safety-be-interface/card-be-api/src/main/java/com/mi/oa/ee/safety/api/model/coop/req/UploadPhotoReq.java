package com.mi.oa.ee.safety.api.model.coop.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/17 16:32
 */
@Data
@ApiModel(value = "UploadPhotoReq", description = "照片上传更新请求参数")
public class UploadPhotoReq {

    @ApiModelProperty(value = "申请单id", dataType = "java.lang.Long")
    @NotNull(message = "申请单id必填")
    private Long id;

    @ApiModelProperty(value = "照片地址", dataType = "java.lang.String")
    @NotEmpty(message = "照片地址必填")
    private String photoUrl;
}
