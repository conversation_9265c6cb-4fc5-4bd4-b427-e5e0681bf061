package com.mi.oa.ee.safety.api.model.emp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023年6月7日 19:33:26
 */
@Data
@ApiModel(value = "EmpCardApplyDetailVo", description = "正式卡工卡申请单详情vo")
public class EmpCardApplyDetailVo {

    @ApiModelProperty(value = "申请单id", dataType = "java.lang.Long")
    private Long id;

    @ApiModelProperty(value = "临时卡id", dataType = "java.lang.Long")
    private Long tempCardId;

    @ApiModelProperty(value = "{{{申请单状态}}}", dataType = "java.lang.Integer")
    private Integer applyStatus;

    @ApiModelProperty(value = "{{{申请单状态}}}", dataType = "java.lang.String")
    private String applyStatusName;

    @ApiModelProperty(value = "用户uid", dataType = "java.lang.String")
    private String uid;

    @ApiModelProperty(value = "{{{公司名称}}}", dataType = "java.lang.String")
    private String companyName;

    @ApiModelProperty(value = "{{{姓名}}}", dataType = "java.lang.String")
    private String displayName;

    @ApiModelProperty(value = "{{{账号}}}", dataType = "java.lang.String")
    private String userName;

    @ApiModelProperty(value = "{{{姓氏拼音}}}", dataType = "java.lang.String")
    private String surname;

    @ApiModelProperty(value = "{{{名字拼音}}}", dataType = "java.lang.String")
    private String pinyinName;

    @ApiModelProperty(value = "员工照片", dataType = "java.lang.String")
    private String photoUrl;

    @ApiModelProperty(value = "{{{工号}}}", dataType = "java.lang.String")
    private String employeeNo;

    @ApiModelProperty(value = "员工类型code", dataType = "java.lang.String")
    private String employeeType;

    @ApiModelProperty(value = "员工类型名称", dataType = "java.lang.String")
    private String employeeTypeDesc;

    @ApiModelProperty(value = "{{{工作地}}}", dataType = "java.lang.String")
    private String workCity;

    @ApiModelProperty(value = "{{{邮箱}}}", dataType = "java.lang.String")
    private String email;

    @ApiModelProperty(value = "{{{办公园区}}}", dataType = "java.lang.String")
    private String parkName;

    @ApiModelProperty(value = "{{{工卡领取地}}}", dataType = "java.lang.String")
    private String receiptParkName;

    @ApiModelProperty(value = "一级部门名称", dataType = "java.lang.String")
    private String firstDeptName;

    @ApiModelProperty(value = "二级部门名称", dataType = "java.lang.String")
    private String secondDeptName;

    @ApiModelProperty(value = "三级部门名称", dataType = "java.lang.String")
    private String thirdDeptName;

    @ApiModelProperty(value = "四级部门名称", dataType = "java.lang.String")
    private String fourthDeptName;

    @ApiModelProperty(value = "{{{入职时间}}}", dataType = "java.lang.String")
    private ZonedDateTime lastHireDate;

    @ApiModelProperty(value = "工卡有效期从", dataType = "java.lang.String")
    private ZonedDateTime startTime;

    @ApiModelProperty(value = "工卡有效期到", dataType = "java.lang.String")
    private ZonedDateTime endTime;

    @ApiModelProperty(value = "卡编号", dataType = "java.lang.String")
    private String cardNum;

    /**
     * 介质物理卡号
     */
    @ApiModelProperty(value = "介质物理卡号", dataType = "java.lang.String")
    private String mediumPhysicsCode;

    /**
     * 介质加密码
     */
    @ApiModelProperty(value = "介质加密码", dataType = "java.lang.String")
    private String mediumEncryptCode;
}
