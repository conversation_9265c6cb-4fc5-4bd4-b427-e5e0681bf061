package com.mi.oa.ee.safety.api.model.park.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/8/15 19:46
 */
@Data
@ApiModel(value = "ParkAdminQueryReq", description = "园区管理分页请求参数")
public class ParkAdminQueryReq {

    @ApiModelProperty(value = "省份id", dataType = "java.lang.Integer")
    private Integer provinceId;

    @ApiModelProperty(value = "国家id", dataType = "java.lang.Integer")
    private Integer countryId;

    @ApiModelProperty(value = "城市id", dataType = "java.lang.Integer")
    private Integer cityId;

    @ApiModelProperty(value = "ps城市id", dataType = "java.lang.Integer")
    private Integer psCityId;

    @ApiModelProperty(value = "人事工作城市名称", dataType = "java.lang.String")
    private String workCityName;

    @ApiModelProperty(value = "报道地址", dataType = "java.lang.String")
    private String reportAddress;

    @ApiModelProperty(value = "园区编码", dataType = "java.lang.String")
    private String parkCode;

    @ApiModelProperty(value = "生效状态", dataType = "java.lang.String")
    private String effectiveStatus;

    @ApiModelProperty(value = "更新开始时间", dataType = "java.lang.String")
    private ZonedDateTime updateStartTime;

    @ApiModelProperty(value = "更新结束时间", dataType = "java.lang.String")
    private ZonedDateTime updateEndTime;

    private Long pageNum;

    private Long pageSize;

}
