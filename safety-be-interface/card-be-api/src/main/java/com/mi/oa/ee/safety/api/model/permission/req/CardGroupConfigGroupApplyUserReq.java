package com.mi.oa.ee.safety.api.model.permission.req;

import com.mi.oa.infra.oaucf.core.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2024/4/1 22:25
 */
@Data
@ApiModel("权限配置 - 权限查询参数")
public class CardGroupConfigGroupApplyUserReq extends BaseReq {

    @ApiModelProperty(value = "{{{权限组类型}}}", required = true, example = "GROUP_NORMAL")
    @NotEmpty(message = "{{{权限组类型不能为空}}}")
    private String cardGroupCode;

    @ApiModelProperty(value = "{{{权限组名称}}}", example = "武汉总部")
    private String uid;
}
