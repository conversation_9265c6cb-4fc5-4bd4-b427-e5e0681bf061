package com.mi.oa.ee.safety.api.model.shared.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/7/30 14:14
 */
@Data
@ApiModel("删除权限包")
public class PermissionGroupDeleteReq {

    @ApiModelProperty(value = "申请单id，和工卡记录id有一个必填", dataType = "java.lang.Long", required = true,
            example = "105")
    private Long applyId;

    @ApiModelProperty(value = "工卡记录Id，和申请单id有一个必填", dataType = "java.lang.Long", required = true,
            example = "105")
    private Long cardId;

    @ApiModelProperty(value = "用户uid", dataType = "java.lang.String", required = true, example = "16726bdf")
    private String uid;

    @ApiModelProperty(value = "权限包集合", required = true, example = "xxx")
    private List<String> cardGroupCodeList;

}
