package com.mi.oa.ee.safety.api.model.shared.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/7/30 16:58
 */
@Data
public class CardGroupAdminPageVo {

    @ApiModelProperty(value = "权限包编码", dataType = "java.lang.String")
    private String cardGroupCode;

    @ApiModelProperty(value = "权限包名称", dataType = "java.lang.String")
    private String cardGroupName;

    @ApiModelProperty(value = "门禁类型", dataType = "java.lang.String")
    private String controlType;

    @ApiModelProperty(value = "门禁类型描述", dataType = "java.lang.String")
    private String controlTypeDesc;

    @ApiModelProperty(value = "园区名称", dataType = "java.lang.String")
    private String parkName;

    @ApiModelProperty(value = "城市名称", dataType = "java.lang.String")
    private String cityName;

    @ApiModelProperty(value = "省份名称", dataType = "java.lang.String")
    private String provinceName;

    @ApiModelProperty(value = "权限组数量", dataType = "java.lang.String")
    private Integer carrierGroupNum;

    @ApiModelProperty(value = "备注", dataType = "java.lang.String")
    private String remark;

    @ApiModelProperty(value = "开始时间", dataType = "java.lang.String")
    private ZonedDateTime startTime;

    @ApiModelProperty(value = "结束时间", dataType = "java.lang.String")
    private ZonedDateTime endTime;

    @ApiModelProperty(value = "权限包对应权限组code集合", dataType = "java.lang.String")
    private List<String> carrierGroupCodeList;

    @ApiModelProperty(value = "权限包是否已添加过", dataType = "java.lang.Boolean")
    private Boolean hasExist;
}
