package com.mi.oa.ee.safety.api.web.card;

import com.mi.oa.ee.safety.api.converter.card.CardInfoVoConverter;
import com.mi.oa.ee.safety.api.model.coop.vo.CardDetailVo;
import com.mi.oa.ee.safety.api.model.remote.req.AvatarReq;
import com.mi.oa.ee.safety.api.model.remote.vo.AvatarVo;
import com.mi.oa.ee.safety.application.dto.card.CardAvatarRemoteDto;
import com.mi.oa.ee.safety.application.dto.card.CardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto;
import com.mi.oa.ee.safety.application.service.card.coop.CardApplyService;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.ee.safety.application.service.card.electronic.CardElectronRecordService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/22 10:26
 */
@Api(tags = "远端接口")
@WebLog
@RestController
@RequestMapping("/api/v1/card/remote")
@Slf4j
public class CardRemoteController {

    @Resource
    private CardService cardService;

    @Resource
    private CardInfoVoConverter voConverter;

    @Resource
    private CardApplyService cardApplyService;

    @Resource
    private CardElectronRecordService cardElectronRecordService;

    @PostMapping("/batch/info/physicCode")
    @ApiOperation(value = "根据物理卡号批量查询工卡信息(远程调用)", httpMethod = "POST")
    public BaseResp<List<CardDetailVo>> getCardInfoByPhysicCodes(@RequestBody List<String> mediumPhysicsCodes) {
        List<CardInfoDto> cardInfoDtos = cardService.getListCardInfoByPhysicCodes(mediumPhysicsCodes);
        return BaseResp.success(voConverter.toVoList(cardInfoDtos));
    }

    @PostMapping("/batch/info/encryptCode")
    @ApiOperation(value = "根据物理卡号批量查询工卡信息(远程调用)", httpMethod = "POST")
    public BaseResp<List<CardDetailVo>> getCardInfoByEncryptCodes(@RequestBody List<String> mediumEncryptCodes) {
        List<CardInfoDto> cardInfoDtos = cardService.getListCardInfoByEncryptCodes(mediumEncryptCodes);
        return BaseResp.success(voConverter.toVoList(cardInfoDtos));
    }

    @PostMapping("/batch/info/uid")
    @ApiOperation(value = "根据uid批量查询工卡信息(远程调用)", httpMethod = "POST")
    public BaseResp<List<CardDetailVo>> getCardInfoByUid(@RequestBody List<String> uid) {
        List<CardInfoDto> cardInfoDtos = cardService.getListCardInfoByUid(uid);
        return BaseResp.success(voConverter.toVoList(cardInfoDtos));
    }

    @PostMapping("/batch/info/account")
    @ApiOperation(value = "根据账号批量查询工卡信息(远程调用)", httpMethod = "POST")
    public BaseResp<List<CardDetailVo>> getCardInfoByAccounts(@RequestBody List<String> accounts) {
        List<CardInfoDto> cardInfoDtos = cardService.getListCardInfoByAccounts(accounts);
        return BaseResp.success(voConverter.toVoList(cardInfoDtos));
    }

    @GetMapping("/location/{userName}/parkCode")
    @ApiOperation(value = "根据账号判断入职园区和部门是否在新工卡系统", httpMethod = "GET")
    public BaseResp<Boolean> checkIsNewCard(@PathVariable("userName") String userName) {
        Boolean isNewSystem = cardService.checkIsNewCard(userName);
        return BaseResp.success(isNewSystem);
    }

    @GetMapping("/info/{uid}")
    @ApiOperation(value = "根据uid查询是否存在工卡或制卡单", httpMethod = "GET")
    public BaseResp<CardDetailVo> getCardInfoByUid(@PathVariable("uid") String uid) {
        CardPartnerApplyDto cardPartnerApplyDto = cardApplyService.findCoopApplyByUid(uid);
        return BaseResp.success(voConverter.toVo(cardPartnerApplyDto));
    }

    @PostMapping("/batch/avatar")
    @ApiOperation(value = "批量获取头像地址", httpMethod = "POST")
    public BaseResp<List<AvatarVo>> findAvatarUrlList(@RequestBody AvatarReq req) {
        CardAvatarRemoteDto dto = voConverter.toRemoteDto(req);
        List<CardPartnerApplyDto> cardApplyDtoList = cardApplyService.findAvatarUrlList(dto);
        return BaseResp.success(voConverter.toRemoteVoList(cardApplyDtoList));
    }

    @GetMapping("/electron/new/check/{username}")
    @ApiOperation(value = "检查是否启用新流程", httpMethod = "GET")
    public BaseResp<Boolean> checkElectronNew(@PathVariable("username") String username) {
        Boolean isNew = cardElectronRecordService.checkIsUseElectronNew(username, null);
        log.info("checkElectronNew username : {} isNew : {}", username, isNew);
        return BaseResp.success(isNew);
    }


}
