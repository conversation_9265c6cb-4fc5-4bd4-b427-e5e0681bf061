package com.mi.oa.ee.safety.api.web.card;

import com.mi.oa.ee.safety.api.converter.card.CardApplyVoConverter;
import com.mi.oa.ee.safety.api.converter.card.CommonCardVoConverter;
import com.mi.oa.ee.safety.api.errorcode.ApplyInterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.model.common.req.AppletCardOperateReq;
import com.mi.oa.ee.safety.api.model.common.req.BatchModifyParkReq;
import com.mi.oa.ee.safety.api.model.common.req.CardPayReq;
import com.mi.oa.ee.safety.api.model.common.req.PinCardReq;
import com.mi.oa.ee.safety.api.model.common.vo.CardPersonVo;
import com.mi.oa.ee.safety.api.model.coop.req.AuthLogRedoReq;
import com.mi.oa.ee.safety.api.model.coop.req.UpdateCardApplyReq;
import com.mi.oa.ee.safety.api.model.coop.req.UploadPhotoReq;
import com.mi.oa.ee.safety.api.model.coop.vo.CardApplyDetailVo;
import com.mi.oa.ee.safety.api.model.coop.vo.OperateLogVo;
import com.mi.oa.ee.safety.api.model.shared.req.BatchPermissionDeleteReq;
import com.mi.oa.ee.safety.api.model.shared.req.BatchPermissionGroupDeleteReq;
import com.mi.oa.ee.safety.api.model.shared.req.BatchPermissionGroupSaveReq;
import com.mi.oa.ee.safety.api.model.shared.req.BatchPermissionSaveReq;
import com.mi.oa.ee.safety.api.model.shared.req.CardBatchReceiveNoticeReq;
import com.mi.oa.ee.safety.api.model.shared.req.CardBatchReceiveReq;
import com.mi.oa.ee.safety.api.model.shared.req.CardGroupModifyTimeReq;
import com.mi.oa.ee.safety.api.model.shared.req.CardReceiveNoticeReq;
import com.mi.oa.ee.safety.api.model.shared.req.CardReceiveReq;
import com.mi.oa.ee.safety.api.model.shared.req.OperatePageConditionReq;
import com.mi.oa.ee.safety.api.model.shared.req.PagePermissionReq;
import com.mi.oa.ee.safety.api.model.shared.req.PermissionAddReq;
import com.mi.oa.ee.safety.api.model.shared.req.PermissionDeleteReq;
import com.mi.oa.ee.safety.api.model.shared.req.PermissionGroupAddReq;
import com.mi.oa.ee.safety.api.model.shared.req.PermissionGroupCarrierGroupPageReq;
import com.mi.oa.ee.safety.api.model.shared.req.PermissionGroupDeleteReq;
import com.mi.oa.ee.safety.api.model.shared.req.PermissionGroupPageReq;
import com.mi.oa.ee.safety.api.model.shared.req.PermissionQueryReq;
import com.mi.oa.ee.safety.api.model.shared.vo.CardGroupAdminPageVo;
import com.mi.oa.ee.safety.api.model.shared.vo.SafetyRightVo;
import com.mi.oa.ee.safety.api.model.temp.req.CardApplyImportReq;
import com.mi.oa.ee.safety.application.dto.card.BatchModifyParkDto;
import com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyImportDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardBatchPermissionGroupImportDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardBatchPermissionImportDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardBatchReceiveDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardBatchReceiveNoticeDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardGroupAdminPageDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardGroupModifyTimeDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardReceiveDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardReceiveNoticeDto;
import com.mi.oa.ee.safety.application.dto.card.shared.OperatePageConditionDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionAddDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionDeleteDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionGroupAddDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionGroupCarrierGroupQueryDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionGroupDeleteDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionGroupQueryDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionQueryDto;
import com.mi.oa.ee.safety.application.dto.card.shared.SafetyRightCityTreeDto;
import com.mi.oa.ee.safety.application.dto.card.temp.CardReturnDto;
import com.mi.oa.ee.safety.application.dto.common.AppletCardOperateDto;
import com.mi.oa.ee.safety.application.dto.common.CardPayDto;
import com.mi.oa.ee.safety.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.errorcode.CardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.coop.CardApplyService;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.ee.safety.application.service.card.shared.CommonCardService;
import com.mi.oa.ee.safety.application.service.card.temp.TempCardService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.AuthLogRedoDto;
import com.mi.oa.ee.safety.common.dto.BpmLinkDto;
import com.mi.oa.ee.safety.common.dto.OperateLogDto;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.dto.SafetyRightDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.card.CardOperateTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.infra.remote.sdk.BpmSdk;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2023/6/8 14:34
 */
@Api(tags = "工卡通用操作接口")
@WebLog
@RestController
@RequestMapping("/api/v1/card/apply/common")
@Slf4j
public class CommonCardApplyController {

    @Resource
    private CommonCardService commonCardService;

    @Autowired
    CardApplyService cardApplyService;

    @Resource
    private CommonCardVoConverter commonCardVoConverter;

    @Resource
    private IdmRemote idmRemote;

    @Resource
    private BpmSdk bpmSdk;

    @Resource
    private CardService cardService;

    @Resource
    private TempCardService tempCardService;

    @Resource
    private CardApplyVoConverter cardApplyVoConverter;

    @GetMapping("/recommended/park")
    @ApiOperation(value = "园区推荐列表", httpMethod = "GET")
    public BaseResp<List<SafetySpaceParkDto>> recommendedParkList() {
        //获取推荐园区列表
        return BaseResp.success(commonCardService.findRecommendedParkList());
    }

    @PostMapping("/confirm")
    @ApiOperation(value = "申请单确认(确认页面)", httpMethod = "POST")
    public BaseResp<String> confirm(@RequestBody @Valid UpdateCardApplyReq req) {
        String uid = idmRemote.getLoginUid();
        if (StringUtils.isEmpty(uid)) {
            throw new BizException(ApplyInterfaceErrorCodeEnum.APPLY_NOT_LOGIN);
        }
        //转换对象DTO
        CardPartnerApplyDto cardPartnerApplyDto = commonCardVoConverter.updateReqToDto(req);
        //确认提交
        cardApplyService.confirmApply(cardPartnerApplyDto, uid);
        //返回成功
        return BaseResp.success();
    }

    @GetMapping("/info/confirm/{id}")
    @ApiOperation(value = "根据申请单id获取详情(确认页面)", httpMethod = "GET")
    public BaseResp<CardApplyDetailVo> getDetailApplyInfoForConfirm(@PathVariable("id") Long id) {
        String uid = idmRemote.getLoginUid();
        if (StringUtils.isEmpty(uid)) {
            throw new BizException(ApplyInterfaceErrorCodeEnum.APPLY_NOT_LOGIN);
        }
        return BaseResp.success(commonCardVoConverter.toVo(cardApplyService.getDetailCardApply(id, uid)));
    }

    @PostMapping("/upload")
    @ApiOperation(value = "照片上传更新", httpMethod = "POST")
    public BaseResp<String> photoUpload(@RequestBody UploadPhotoReq req) {
        cardApplyService.uploadPhoto(req.getId(), req.getPhotoUrl());
        return BaseResp.success();
    }

    @PostMapping("/batchNotice")
    @ApiOperation(value = "批量通知", httpMethod = "POST")
    public BaseResp<Void> batchNotice(@RequestBody @Valid CardBatchReceiveNoticeReq req) {
        CardBatchReceiveNoticeDto batchReceiveNoticeDto = commonCardVoConverter.toCardBatchReceiveNoticeDto(req);
        commonCardService.batchReceiveNotice(batchReceiveNoticeDto);
        return BaseResp.success();
    }

    @PostMapping("/notice")
    @ApiOperation(value = "通知", httpMethod = "POST")
    public BaseResp<Void> notice(@RequestBody @Valid CardReceiveNoticeReq req) {
        CardReceiveNoticeDto receiveNoticeDto = commonCardVoConverter.toCardReceiveNoticeDto(req);
        commonCardService.receiveNotice(receiveNoticeDto);
        return BaseResp.success();
    }

    @PostMapping("/batchReceive")
    @ApiOperation(value = "批量领取", httpMethod = "POST")
    public BaseResp<Void> batchReceive(@RequestBody @Valid CardBatchReceiveReq req) {
        CardBatchReceiveDto batchReceiveDto = commonCardVoConverter.toCardBatchReceiveDto(req);
        commonCardService.batchReceive(batchReceiveDto);
        return BaseResp.success();
    }

    @PostMapping("/receive")
    @ApiOperation(value = "领取", httpMethod = "POST")
    public BaseResp<Void> receive(@RequestBody @Valid CardReceiveReq req) {
        CardReceiveDto cardReceiveDto = commonCardVoConverter.toCardReceiveDto(req);
        commonCardService.receive(cardReceiveDto);
        return BaseResp.success();
    }

    @GetMapping("/permission/grant/list")
    @ApiOperation(value = "员工已经授予的权限列表", httpMethod = "GET")
    public BaseResp<PageVO<SafetyRightVo>> grantPermissionList(@Valid PermissionQueryReq req) {
        if (Objects.isNull(req.getApplyId()) && Objects.isNull(req.getCardId())) {
            return BaseResp.error(CardApplicationErrorCodeEnum.CARD_ID_APPLY_ID_ALL_EMPTY);
        }
        PermissionQueryDto queryDto = commonCardVoConverter.toPermissionQueryDto(req);
        PageModel<SafetyRightDto> page = commonCardService.grantPermissionList(queryDto);
        List<SafetyRightVo> groupVoList = commonCardVoConverter.toSafetyRightVoWithTime(page.getList());
        return BaseResp.success(PageVO.build(groupVoList, page.getPageSize(), page.getPageNum(), page.getTotal()));
    }

    @GetMapping("/permission/rest/list")
    @ApiOperation(value = "所有权限列表", httpMethod = "GET")
    public BaseResp<PageVO<SafetyRightVo>> restPermissionList(@Valid PermissionQueryReq req) {
        if (Objects.isNull(req.getApplyId()) && Objects.isNull(req.getCardId())) {
            return BaseResp.error(CardApplicationErrorCodeEnum.CARD_ID_APPLY_ID_ALL_EMPTY);
        }
        PermissionQueryDto queryDto = commonCardVoConverter.toPermissionQueryDto(req);
        PageModel<SafetyCarrierGroupDto> page = commonCardService.restPermissionList(queryDto);
        List<SafetyRightVo> groupVoList = commonCardVoConverter.toSafetyRightVo(page.getList());
        return BaseResp.success(PageVO.build(groupVoList, page.getPageSize(), page.getPageNum(), page.getTotal()));
    }

    @PostMapping("/permission/save")
    @ApiOperation(value = "授予权限", httpMethod = "POST")
    public BaseResp<Void> savePermission(@RequestBody @Valid PermissionAddReq req) {
        PermissionAddDto permissionAddDto = commonCardVoConverter.toPermissionAddDto(req);
        commonCardService.addPermission(permissionAddDto);
        return BaseResp.success();
    }

    @PostMapping("/permission/remove")
    @ApiOperation(value = "移除权限", httpMethod = "POST")
    public BaseResp<Void> removePermission(@RequestBody @Valid PermissionDeleteReq req) {
        PermissionDeleteDto permissionAddDto = commonCardVoConverter.toPermissionDeleteDto(req);
        commonCardService.removePermission(permissionAddDto);
        return BaseResp.success();
    }

    @PostMapping("/batch/permission/save")
    @ApiOperation(value = "多人员批量添加权限(权限组)", httpMethod = "POST")
    public BaseResp<Void> batchSavePermission(@RequestBody @Valid BatchPermissionSaveReq req) {
        CardBatchPermissionImportDto importDto = commonCardVoConverter.toBatchAddPermissionImportDto(req);
        commonCardService.batchImportAddPermission(importDto);
        log.info("batchSavePermission");
        return BaseResp.success();
    }

    @PostMapping("/batch/permission/remove")
    @ApiOperation(value = "多人员批量移除权限(权限组)", httpMethod = "POST")
    public BaseResp<Void> batchRemovePermission(@RequestBody @Valid BatchPermissionDeleteReq req) {
        CardBatchPermissionImportDto importDto = commonCardVoConverter.toBatchDeletePermissionImportDto(req);
        commonCardService.batchImportDeletePermission(importDto);
        log.info("batchRemovePermission");
        return BaseResp.success();
    }

    @PostMapping("/batch/permission/group/save")
    @ApiOperation(value = "多人员批量添加权限(权限包)", httpMethod = "POST")
    public BaseResp<Void> batchSavePermissionGroup(@RequestBody @Valid BatchPermissionGroupSaveReq req) {
        CardBatchPermissionGroupImportDto importDto = commonCardVoConverter.toBatchAddPermissionGroupImportDto(req);
        commonCardService.batchImportAddPermissionGroup(importDto);
        log.info("batchSavePermissionGroup");
        return BaseResp.success();
    }

    @PostMapping("/batch/permission/group/remove")
    @ApiOperation(value = "多人员批量移除权限(权限包)", httpMethod = "POST")
    public BaseResp<Void> batchRemovePermissionGroup(@RequestBody @Valid BatchPermissionGroupDeleteReq req) {
        CardBatchPermissionGroupImportDto importDto = commonCardVoConverter.toBatchDeletePermissionGroupImportDto(req);
        commonCardService.batchImportDeletePermissionGroup(importDto);
        log.info("batchRemovePermissionGroup");
        return BaseResp.success();
    }

    @PostMapping("/permission/group/save")
    @ApiOperation(value = "权限包授予权限", httpMethod = "POST")
    public BaseResp<Void> savePermissionGroup(@RequestBody PermissionGroupAddReq req) {
        PermissionGroupAddDto permissionAddDto = commonCardVoConverter.toPermissionGroupAddDto(req);
        commonCardService.addGroupPermission(permissionAddDto);
        return BaseResp.success();
    }

    @PostMapping("/permission/group/remove")
    @ApiOperation(value = "权限包移除权限", httpMethod = "POST")
    public BaseResp<Void> removePermissionGroup(@RequestBody PermissionGroupDeleteReq req) {
        PermissionGroupDeleteDto permissionAddDto = commonCardVoConverter.toPermissionGroupDeleteDto(req);
        commonCardService.removeGroupPermission(permissionAddDto);
        return BaseResp.success();
    }

    @PostMapping("/permission/group/page")
    @ApiOperation(value = "权限包分页查询", httpMethod = "POST")
    public BaseResp<PageVO<CardGroupAdminPageVo>> page(@RequestBody PermissionGroupPageReq req) {
        PermissionGroupQueryDto queryDto = commonCardVoConverter.toPermissionGroupQueryDto(req);
        PageModel<CardGroupAdminPageDto> page = commonCardService.pageCardGroupList(queryDto);
        return BaseResp.success(PageVO.build(page, commonCardVoConverter.toCardGroupAdminPageVo(page.getList())));
    }

    @PostMapping("/permission/carrier/group/page")
    @ApiOperation(value = "权限包下权限组分页查询", httpMethod = "POST")
    public BaseResp<PageVO<SafetyRightVo>> pageCarrierGroup(@RequestBody PermissionGroupCarrierGroupPageReq req) {
        PermissionGroupCarrierGroupQueryDto queryDto = commonCardVoConverter.toPermissionGroupCarrierGroupQueryDto(req);
        PageModel<SafetyCarrierGroupDto> page = commonCardService.pageCardGroupCarrierGroup(queryDto);
        List<SafetyRightVo> groupVoList = commonCardVoConverter.toSafetyRightVo(page.getList());
        return BaseResp.success(PageVO.build(page, groupVoList));
    }

    @PostMapping("/permission/group/update")
    @ApiOperation(value = "批量修改权限包/组有效期", httpMethod = "POST")
    public BaseResp<Void> batchModifyGroupTime(@RequestBody @Valid CardGroupModifyTimeReq req) {
        CardGroupModifyTimeDto queryDto = commonCardVoConverter.toModifyTimeDto(req);
        commonCardService.batchModifyGroupTime(queryDto);
        return BaseResp.success();
    }

    @PostMapping("/condition/log/list")
    @ApiOperation(value = "条件分页获取门禁权限变更列表", httpMethod = "POST")
    public BaseResp<PageVO<OperateLogVo>> pageConditionOperateLogList(@RequestBody OperatePageConditionReq req) {
        OperatePageConditionDto conditionDto = commonCardVoConverter.toOperatePageConditionDto(req);
        PageModel<OperateLogDto> pageModel = commonCardService.pageListOperateLogs(conditionDto);
        return BaseResp.success(PageVO.build(pageModel, commonCardVoConverter.toVoListLogs(pageModel.getList())));
    }

    @PostMapping("/log/redo")
    @ApiOperation(value = "重执行工卡操作日志变更(权限)", httpMethod = "POST")
    public BaseResp<String> operateLogRedo(@RequestBody AuthLogRedoReq req) {
        AuthLogRedoDto authLogRedoDto = commonCardVoConverter.authReqToDto(req);
        commonCardService.authorityRedo(authLogRedoDto);
        return BaseResp.success();
    }

    @GetMapping("/tree/group/list")
    @ApiOperation(value = "根据卡编码查对应权限的权限组集合(树状结构)", httpMethod = "GET")
    public BaseResp<List<SafetyRightCityTreeDto>> accessTreeList(@RequestParam("mediumCode") String mediumCode) {
        List<SafetyRightCityTreeDto> treeSafetyCarrierGroup = commonCardService.accessTreeCityByMedium(mediumCode);
        return BaseResp.success(treeSafetyCarrierGroup);
    }

    @GetMapping("/permission/group/fuzzy")
    @ApiOperation(value = "模糊搜索权限组", httpMethod = "GET")
    public BaseResp<List<SafetyCarrierGroupDto>> fuzzyGroupList(@RequestParam("name") String name) {
        List<SafetyCarrierGroupDto> treeSafetyCarrierGroup = commonCardService.fuzzyGroupList(name);
        return BaseResp.success(treeSafetyCarrierGroup);
    }

    @PostMapping("/sync/batchImport")
    @ApiOperation(value = "老工卡数据迁移到新工卡", httpMethod = "POST")
    public BaseResp<Void> batchSyncImport(@RequestBody @Valid CardApplyImportReq req) {
        req.setCardType("migrate");
        CardApplyImportDto importDto = commonCardVoConverter.toCardApplyImportDto(req);
        return BaseResp.success();
    }

    @GetMapping("/permission/page/list")
    @ApiOperation(value = "所有权限列表分页查询", httpMethod = "GET")
    public BaseResp<PageVO<SafetyRightVo>> pagePermissionList(@Valid PagePermissionReq req) {
        PermissionQueryDto queryDto = commonCardVoConverter.toPagePermissionQueryDto(req);
        PageModel<SafetyRightDto> page = commonCardService.pagePermissionList(queryDto);
        List<SafetyRightVo> groupVoList = commonCardVoConverter.toSafetyRightVoWithTime(page.getList());
        return BaseResp.success(PageVO.build(groupVoList, page.getPageSize(), page.getPageNum(), page.getTotal()));
    }

    @GetMapping("/self/upload")
    @ApiOperation(value = "照片自行上传", httpMethod = "GET")
    public BaseResp<String> selfUpload(@RequestParam("id") Long id) {
        cardApplyService.selfUpload(id);
        //返回成功
        return BaseResp.success();
    }

    @GetMapping("/self/upload/reSend")
    @ApiOperation(value = "重新发送上传照片提醒", httpMethod = "GET")
    public BaseResp<String> selfUploadReSend(@RequestParam("id") Long id) {
        cardApplyService.reSendSelfUploadUmsNotify(id);
        //返回成功
        return BaseResp.success();
    }

    @GetMapping("/upload/reSend")
    @ApiOperation(value = "重新发送确认提醒", httpMethod = "GET")
    public BaseResp<String> photoUploadReSend(@RequestParam("id") Long id) {
        cardApplyService.uploadPhotoBySupplierReSend(id);
        return BaseResp.success();
    }

    @PostMapping("/supplier/upload")
    @ApiOperation(value = "供应商上传照片", httpMethod = "POST")
    public BaseResp<String> photoUploadForSupplier(@RequestBody UploadPhotoReq req) {
        cardApplyService.uploadPhotoBySupplier(req.getId(), req.getPhotoUrl());
        return BaseResp.success();
    }

    @ApiOperation(value = "跳转到BPM详情页", httpMethod = "GET")
    @GetMapping("/link/bpm/detail")
    public BaseResp<BpmLinkDto> goToDetailBpm(@RequestParam("bpmCode") String bpmCode,
                                              @RequestParam(value = "isNeedAssign") Boolean isNeedAssign) {
        String userName = idmRemote.getLoginAccount().getValue();

        return BaseResp.success(bpmSdk.allTypeLink(bpmCode, userName));
    }

    @GetMapping("/fuzzy/name")
    @ApiOperation(value = "工卡侧根据姓名模糊搜索", httpMethod = "GET")
    public BaseResp<CardPersonVo> cardFuzzyName(@RequestParam("name") String name) {

        return BaseResp.success();
    }

    @PostMapping("/remove-loss")
    @ApiOperation(value = "解除挂失", httpMethod = "POST")
    public BaseResp<Void> removeLoss(@RequestBody @Valid AppletCardOperateReq appletCardOperateReq) {
        AppletCardOperateDto appletCardOperateDto = commonCardVoConverter.toAppletCardOperateDto(appletCardOperateReq);
        commonCardService.removeLossCard(appletCardOperateDto);
        return BaseResp.success();
    }

    @PostMapping("/pinCard")
    @ApiOperation(value = "销卡", httpMethod = "POST")
    public BaseResp<Void> pinCard(@RequestBody PinCardReq req) {
        commonCardService.checkCardIsConsistent(req.getCardId(), req.getMediumPhysicsCode(), req.getMediumEncryptCode());
        if (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(req.getCardType())) {
            cardService.pinCard(req.getCardId());
        } else if (CardTypeEnum.TEMP_CARD.getNumber().equals(req.getCardType())) {
            CardReturnDto cardReturnDto = new CardReturnDto();
            cardReturnDto.setCardId(req.getCardId());
            tempCardService.returnCard(cardReturnDto);
        } else {
            cardService.cardOperate(CardOperateTypeEnum.CARD_DESTROY.getCode(), req.getCardId(), req.getUid());
        }
        return BaseResp.success();
    }

    @PostMapping("/pay")
    @ApiOperation(value = "工卡相关支付", httpMethod = "POST")
    public BaseResp<String> pay(@RequestBody @Valid CardPayReq req) {
        CardPayDto cardPayDto = commonCardVoConverter.payReqToDto(req);
        String url = commonCardService.pay(cardPayDto);
        return BaseResp.success(url);
    }

    @PostMapping("/batchImport/photo")
    @ApiOperation(value = "导入照片", httpMethod = "POST")
    public BaseResp<Void> batchImport(@RequestBody @Valid CardApplyImportReq req) {
        CardApplyImportDto importDto = cardApplyVoConverter.toCardApplyImportDto(req);
        cardApplyService.batchImportPhoto(importDto);
        log.info("batchImport");
        return BaseResp.success();
    }

    @PostMapping("/modify/park")
    @ApiOperation(value = "批量修改园区", httpMethod = "POST")
    public BaseResp<Void> batchModifyPark(@RequestBody BatchModifyParkReq req) {
        BatchModifyParkDto modifyParkDto = cardApplyVoConverter.toModifyParkDto(req);
        cardApplyService.batchModifyParks(modifyParkDto);
        return BaseResp.success();
    }

    @GetMapping("/batchExport/photo")
    @ApiOperation(value = "照片导出", httpMethod = "GET")
    public BaseResp<Void> batchExportPhoto(@RequestParam String queryData) {
        String loginUid = idmRemote.getLoginUid();
        String token = RedisUtils.tryLock(loginUid, SafetyConstants.Common.FIVE_MINUTES);
        if (StringUtils.isEmpty(token)) {
            throw new BizException(ApplicationErrorCodeEnum.APPLICATION_SUPPLIER_SYNC_ERROR);
        }
        cardApplyService.asyncExportPhoto(queryData);
        return BaseResp.success();
    }
}
