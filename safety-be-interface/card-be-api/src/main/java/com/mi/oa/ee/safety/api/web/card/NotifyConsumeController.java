package com.mi.oa.ee.safety.api.web.card;

import com.mi.oa.ee.safety.api.converter.card.CardInfoVoConverter;
import com.mi.oa.ee.safety.api.facade.NotifyConsumeFacade;
import com.mi.oa.ee.safety.api.model.common.req.PayNotifyReq;
import com.mi.oa.ee.safety.api.model.common.req.RefundResultReq;
import com.mi.oa.ee.safety.api.model.coop.req.AccountActiveReq;
import com.mi.oa.ee.safety.api.model.coop.req.NotifyCallBackReq;
import com.mi.oa.ee.safety.api.model.notify.PersonNotifyDto;
import com.mi.oa.ee.safety.api.model.notify.req.CancelLeaveReq;
import com.mi.oa.ee.safety.api.model.notify.req.PersonUpdateDto;
import com.mi.oa.ee.safety.api.model.notify.req.PreLeaveReq;
import com.mi.oa.ee.safety.application.dto.card.CardLeaveRecordDto;
import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardPayCallbackDto;
import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardRefundCallbackDto;
import com.mi.oa.ee.safety.application.service.card.coop.CardApplyService;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.ee.safety.application.service.card.reissue.ReissueCardApplyService;
import com.mi.oa.ee.safety.common.config.PayProperties;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.enums.AccountTypeEnum;
import com.mi.oa.ee.safety.common.enums.ConsumeNotifyNameEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.utils.JsonUtils;
import com.mi.oa.ee.safety.domain.ability.CardPersonInfoAbility;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPersonInfoDo;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.infra.oaucf.pay.enums.RefundStatusEnum;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.xiaomi.info.infra.notify.req.ConsumerResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/18 15:08
 */
@Api(tags = "notify消息消费接口")
@WebLog
@RestController
@RequestMapping("/api/v1/notify")
@Slf4j
public class NotifyConsumeController {

    @Autowired
    private CardService cardService;

    @Autowired
    private CardApplyService cardApplyService;

    @Resource
    private CardInfoVoConverter cardInfoVoConverter;

    @Autowired
    private NotifyConsumeFacade notifyConsumeFacade;

    @Resource
    private ReissueCardApplyService reactionCardApplyService;

    @Autowired
    private CardPersonInfoAbility cardPersonInfoAbility;

    @Resource
    private PayProperties payProperties;

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Autowired
    private IdmSdk idmSdk;

    private static final Integer CLOSE = 3;

    /**
     * 人员更新
     *
     * <AUTHOR>
     * @date 2024/6/13 9:34
     */
    @PostMapping("/idaas_person_updated")
    public ConsumerResponse personUpdated(@RequestBody PersonUpdateDto personUpdateDto) {
        try {
            log.info("idaas_person_updated data:{}", JsonUtils.toJson(personUpdateDto));
            CardPersonInfoDo cardPersonInfoDo = new CardPersonInfoDo();
            cardPersonInfoDo.setUid(personUpdateDto.getPersonId());
            if (StringUtils.isNotEmpty(personUpdateDto.getManagerAccountName())) {
                PersonInfoModel person = idmSdk.findPersonInfoByAccountAndOrgFromLocalCache(personUpdateDto.getManagerAccountName(),
                        personUpdateDto.getOrgTreeCode());
                Optional.ofNullable(person).ifPresent(it -> cardPersonInfoDo.setResponsible(it.getUid()));
            }
            cardPersonInfoDo.setEmployeeNo(personUpdateDto.getExternalId());
            if (personUpdateDto.getPersonBasicInfoDto() != null) {
                cardPersonInfoDo.setCompanyName(personUpdateDto.getPersonBasicInfoDto().getCompanyDescr());
                cardPersonInfoDo.setDisplayName(personUpdateDto.getPersonBasicInfoDto().getDisplayName());
            }
            if (StringUtils.isNotEmpty(personUpdateDto.getExternalId())) {
                cardPersonInfoDo.setEmployeeNo(personUpdateDto.getExternalId());
                cardPersonInfoAbility.updatePerson(cardPersonInfoDo);
                CardApplyDo cardApplyDo = cardApplyRepository.findCardByUidAndCardType(personUpdateDto.getPersonId(), CardTypeEnum.EMPLOYEE_CARD);
                if (ObjectUtils.isEmpty(cardApplyDo)) {
                    //创建正式卡制卡单
                    cardApplyService.createEmpApply(null, personUpdateDto.getExternalId(), null, true);
                } else {
                    //更新制卡单
                    cardApplyDo.setEmpNo(personUpdateDto.getExternalId());
                    cardApplyDo.setEmpType(AccountTypeEnum.EMPLOYEE.getValue());
                    cardApplyRepository.updateById(cardApplyDo);
                }
            } else {
                cardPersonInfoAbility.updatePerson(cardPersonInfoDo);
            }
        } catch (Exception e) {
            log.error("personUpdated notify consume failed: uid:+" + personUpdateDto.getPersonId(), e);
            return new ConsumerResponse(555);
        }
        return ConsumerResponse.ok();
    }

    /**
     * 人员离职
     *
     * @param personNotifyDto
     * @return com.xiaomi.info.infra.notify.req.ConsumerResponse
     * <AUTHOR>
     * @date 2023/7/25 15:14
     */
    @PostMapping("/idaas_person_close")
    public ConsumerResponse personClose(@RequestBody PersonNotifyDto personNotifyDto) {
        notifyConsumeFacade.consumeNotify(personNotifyDto, ConsumeNotifyNameEnum.IDAAS_PERSON_CLOSE);
        return ConsumerResponse.ok();
    }

    /**
     * 人员取消离职
     *
     * @param personNotifyDto
     * @return com.xiaomi.info.infra.notify.req.ConsumerResponse
     * <AUTHOR>
     * @date 2023/7/25 15:14
     */
    @PostMapping("/idaas_person_resume")
    public ConsumerResponse personResume(@RequestBody PersonNotifyDto personNotifyDto) {
        notifyConsumeFacade.consumeNotify(personNotifyDto, ConsumeNotifyNameEnum.IDAAS_PERSON_RESUME);
        return ConsumerResponse.ok();
    }

    /**
     * 人员启用
     *
     * @param personNotifyDto
     * @return com.xiaomi.info.infra.notify.req.ConsumerResponse
     * <AUTHOR>
     * @date 2023/7/25 15:14
     */
    @PostMapping("/idaas_person_enabled")
    public ConsumerResponse personEnabled(@RequestBody PersonNotifyDto personNotifyDto) {
        notifyConsumeFacade.consumeNotify(personNotifyDto, ConsumeNotifyNameEnum.IDAAS_PERSON_ENABLED);
        return ConsumerResponse.ok();
    }

    /**
     * 人员禁用
     *
     * @param personNotifyDto
     * @return com.xiaomi.info.infra.notify.req.ConsumerResponse
     * <AUTHOR>
     * @date 2023/7/25 15:14
     */
    @PostMapping("/idaas_person_disabled")
    public ConsumerResponse personDisabled(@RequestBody PersonNotifyDto personNotifyDto) {
        notifyConsumeFacade.consumeNotify(personNotifyDto, ConsumeNotifyNameEnum.IDAAS_PERSON_DISABLED);
        return ConsumerResponse.ok();
    }

    /**
     * 合作伙伴驻场申请状态变更
     *
     * @param req
     * @return com.xiaomi.info.infra.notify.req.ConsumerResponse
     * <AUTHOR>
     * @date 2023/7/25 14:25
     */
    @PostMapping("/onsite/close")
    public ConsumerResponse onSiteClose(@RequestBody NotifyCallBackReq req) {
        if (!ObjectUtils.isEmpty(req) && !StringUtils.isEmpty(req.getPartnerUser())
                && CLOSE.equals(req.getApplyPartnerStatus())) {
            cardService.deleteTimeAndRight(req.getPartnerUser(), req.getOnsiteStartTime(), req.getOnsiteEndTime());
        } else {
            log.error("notify onsite close failed {}", req);
        }
        return ConsumerResponse.ok();
    }

    /**
     * IDM账号激活
     *
     * @param accountActiveReq
     * @return com.xiaomi.info.infra.notify.req.ConsumerResponse
     * <AUTHOR>
     * @date 2023/7/25 14:26
     */
    @PostMapping("/card/account/active")
    public ConsumerResponse accountActive(@RequestBody AccountActiveReq accountActiveReq) {
        try {
            cardApplyService.createTempApply(accountActiveReq.getAccountName());
            cardApplyService.createEmpApply(accountActiveReq.getAccountName(), null, null, true);
        } catch (Exception e) {
            log.error("notify consume failed : {}", e.getMessage(), e);
            return new ConsumerResponse(555);
        }
        return ConsumerResponse.ok();
    }

    @PostMapping("/card/pre/leave")
    public ConsumerResponse preLeave(@RequestBody @Valid PreLeaveReq preLeaveReq) {
        try {
            CardLeaveRecordDto cardLeaveRecordDto = cardInfoVoConverter.toLeaveDto(preLeaveReq);
            cardService.createPreLeave(cardLeaveRecordDto);
        } catch (Exception e) {
            log.error("notify consume failed : {}", e.getMessage(), e);
            return new ConsumerResponse(555);
        }
        return ConsumerResponse.ok();
    }

    @PostMapping("/card/cancel/leave")
    public ConsumerResponse cancelLeave(@RequestBody CancelLeaveReq cancelLeaveReq) {
        try {
            CardLeaveRecordDto cardLeaveRecordDto = cardInfoVoConverter.cancelReqToLeaveDto(cancelLeaveReq);
            cardService.cancelLeave(cardLeaveRecordDto);
        } catch (Exception e) {
            log.error("notify consume failed : {}", e.getMessage(), e);
            return new ConsumerResponse(555);
        }
        return ConsumerResponse.ok();
    }

    @PostMapping("/reissue/pay/callback")
    public ConsumerResponse payCallback(@RequestBody PayNotifyReq payNotifyReq) {
        log.info("pay callback,param:{} ", payNotifyReq);
        if (StringUtils.equals(payProperties.getBizId(), payNotifyReq.getBizId()) ||
                StringUtils.equals(payProperties.getQrBizId(), payNotifyReq.getBizId())) {
            ReissueCardPayCallbackDto chargeCallbackDto = new ReissueCardPayCallbackDto();
            chargeCallbackDto.setOuterTradeId(payNotifyReq.getPlatformOrderNo());
            chargeCallbackDto.setOutOrderId(payNotifyReq.getPayOrderNo());
            chargeCallbackDto.setTotalFee(payNotifyReq.getTotalAmount());
            chargeCallbackDto.setPayResult(payNotifyReq.getPayResult());
            chargeCallbackDto.setOrderId(payNotifyReq.getBizOrderNo());
            reactionCardApplyService.payCallback(chargeCallbackDto);
        }
        return ConsumerResponse.ok();
    }

    @PostMapping("/reissue/refund/callback")
    public ConsumerResponse refundCallback(@RequestBody RefundResultReq refundResultReq) {
        log.info("refund callback,param:{} ", refundResultReq);
        if (StringUtils.equals(payProperties.getBizId(), refundResultReq.getBizId()) ||
                StringUtils.equals(payProperties.getQrBizId(), refundResultReq.getBizId())) {
            ReissueCardRefundCallbackDto refundCallback = new ReissueCardRefundCallbackDto();
            int status = RefundStatusEnum.SUCCESS.getCode() == refundResultReq.getRefundStatus() ?
                    1 : 0;
            refundCallback.setStatus(status);
            refundCallback.setPayOrderNo(refundResultReq.getPayOrderNo());
            refundCallback.setRefundId(refundResultReq.getBizRefundNo());
            refundCallback.setPartnerRefundId(refundResultReq.getRefundNo());
            refundCallback.setRefundAmount(refundResultReq.getRefundAmount());
            reactionCardApplyService.refundCallback(refundCallback);
        }
        return ConsumerResponse.ok();
    }
}
