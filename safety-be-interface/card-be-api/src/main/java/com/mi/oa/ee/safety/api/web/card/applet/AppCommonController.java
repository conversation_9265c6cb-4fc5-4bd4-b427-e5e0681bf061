package com.mi.oa.ee.safety.api.web.card.applet;

import com.mi.oa.ee.safety.api.converter.common.AppCommonVoConverter;
import com.mi.oa.ee.safety.api.converter.common.UserBehaviorRecordVoConverter;
import com.mi.oa.ee.safety.api.errorcode.ApplyInterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.model.common.req.UserBehaviorQueryReq;
import com.mi.oa.ee.safety.api.model.common.req.UserBehaviorReportReq;
import com.mi.oa.ee.safety.api.model.common.vo.AppletUserInfoRes;
import com.mi.oa.ee.safety.api.model.common.vo.CardBackVo;
import com.mi.oa.ee.safety.api.model.common.vo.UserBehaviorRecordVo;
import com.mi.oa.ee.safety.api.model.park.vo.ParkCityVo;
import com.mi.oa.ee.safety.application.dto.common.UserBehaviorQueryDto;
import com.mi.oa.ee.safety.application.dto.common.UserBehaviorRecordDto;
import com.mi.oa.ee.safety.application.service.common.CommonService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.AccountModel;
import com.mi.oa.ee.safety.common.dto.ParkCityDto;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.idm.api.enums.UserIdTypeEnum;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.newauth.core.dto.UserInfoDto;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2024/3/25 10:55
 */
@Api(tags = "小程序通用接口")
@WebLog
@RestController
@RequestMapping("/api/v1/applet/common")
@Slf4j
public class AppCommonController {

    @Resource
    private UserBehaviorRecordVoConverter userBehaviorRecordVoConverter;

    @Resource
    private CommonService commonService;

    @Resource
    private AppCommonVoConverter commonVoConverter;

    @Resource
    private IdmRemote idmRemote;

    @Resource
    private IdmSdk idmSdk;

    @ApiOperation(value = "查询当前员工是否在白名单中")
    @GetMapping(value = "/white-list/check")
    public BaseResp<Integer> checkWhiteList() {
        String uid = idmRemote.getLoginUid();
        log.info("checkWhiteList,uid:{}, param:{} ", uid);
        Asserts.assertNotNull(uid, ApplyInterfaceErrorCodeEnum.APPLY_NOT_LOGIN);
        return BaseResp.success(Integer.parseInt(SafetyConstants.Common.LARK_WHITE_LIST_FLAG));
    }

    @ApiOperation(value = "查询当前员工是否在白名单中")
    @GetMapping(value = "/white-list/checkByAccountName")
    public BaseResp<Integer> checkWhiteList(
            @RequestParam(value = "accountName", defaultValue = StringUtils.EMPTY) String accountName) {
        if (StringUtils.isBlank(accountName)) {
            return BaseResp.success(OAUCFCommonConstants.INT_ZERO);
        }
        AccountModel accountModel = idmSdk.getAccountInfo(accountName, UserIdTypeEnum.ACCOUNT_NAME);
        if (Objects.isNull(accountModel)) {
            return BaseResp.success(OAUCFCommonConstants.INT_ZERO);
        }
        return BaseResp.success(Integer.parseInt(SafetyConstants.Common.LARK_WHITE_LIST_FLAG));
    }

    @ApiOperation(value = "园区办公地址接口")
    @GetMapping(value = "/park/space")
    public BaseResp<List<ParkCityVo>> parkList() {
        List<ParkCityDto> parkCity = commonService.getAllSpaceParkTree();
        List<ParkCityVo> parkList = commonVoConverter.toParkCityVoList(parkCity);
        return BaseResp.success(parkList);
    }

    @ApiOperation("行为上报")
    @PostMapping("/behavior/report")
    public BaseResp<Void> reportUserBehavior(@RequestBody @Valid UserBehaviorReportReq userBehaviorReportReq) {
        String uid = idmRemote.getLoginUid();
        log.info("reportUserBehavior,uid:{}, param:{} ", uid, userBehaviorReportReq);
        Asserts.assertNotNull(uid, ApplyInterfaceErrorCodeEnum.APPLY_NOT_LOGIN);
        UserBehaviorRecordDto userBehaviorRecordDto = userBehaviorRecordVoConverter.toUserBehaviorRecordDto(userBehaviorReportReq);
        userBehaviorRecordDto.setUid(uid);
        commonService.reportUserBehavior(userBehaviorRecordDto);
        return BaseResp.success();
    }

    @ApiOperation("获取行为数据")
    @PostMapping("/behavior/query")
    public BaseResp<UserBehaviorRecordVo> queryUserBehavior(@RequestBody @Valid UserBehaviorQueryReq userBehaviorQueryReq) {
        String uid = idmRemote.getLoginUid();
        log.info("queryUserBehavior,uid:{}, param:{} ", uid, userBehaviorQueryReq);
        Asserts.assertNotNull(uid, ApplyInterfaceErrorCodeEnum.APPLY_NOT_LOGIN);
        UserBehaviorQueryDto userBehaviorQueryDto = userBehaviorRecordVoConverter.toUserBehaviorQueryDto(userBehaviorQueryReq);
        userBehaviorQueryDto.setUid(uid);
        UserBehaviorRecordDto userBehaviorRecordDto = commonService.queryUserBehavior(userBehaviorQueryDto);
        UserBehaviorRecordVo userBehaviorRecordRes = userBehaviorRecordVoConverter.toUserBehaviorRecordRes(userBehaviorRecordDto);
        return BaseResp.success(userBehaviorRecordRes);
    }

    @GetMapping("/auth/user-info")
    public BaseResp<AppletUserInfoRes> user() {
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        AppletUserInfoRes userInfoRes = commonVoConverter.toAppletUserInfoRes(userInfoDto);
        if (Objects.nonNull(userInfoRes)) {
            Resp<String> avatarResp = idmSdk.getAvatarByUid(userInfoDto.getUid());
            if (Objects.nonNull(avatarResp) && StringUtils.isNotBlank(avatarResp.getData())) {
                userInfoRes.setHeadUrl(avatarResp.getData());
            }
        }
        return BaseResp.success(userInfoRes);
    }

    @ApiOperation(value = "修改推送状态")
    @GetMapping("/edit/notify/status")
    public CardBackVo editNotifyStatus(@RequestParam String uid, @RequestParam String permissionApplyCode) {
        log.info("editNotifyStatus,uid:{}, permissionApplyCode:{} ", uid, permissionApplyCode);
        Asserts.assertNotNull(uid, ApplyInterfaceErrorCodeEnum.APPLY_PARAMS_ERROR);
        Asserts.assertNotNull(permissionApplyCode, ApplyInterfaceErrorCodeEnum.APPLY_PARAMS_ERROR);
        commonService.editNotifyStatus(uid, permissionApplyCode);
        // 创建 CardBackVo 对象
        CardBackVo cardBackVo = new CardBackVo();

        // 设置 msgType
        cardBackVo.setMsgType("interactive");

        // 设置 Config 对象
        CardBackVo.Config config = new CardBackVo.Config();
        config.setWideScreenMode(true);
        cardBackVo.setConfig(config);

        // 设置 CardDetails 对象
        CardBackVo.CardDetails cardDetails = new CardBackVo.CardDetails();

        // 设置 Header 对象
        CardBackVo.Header header = new CardBackVo.Header();
        CardBackVo.Title title = new CardBackVo.Title();
        title.setTag("plain_text");

        // 设置 I18n 对象
        CardBackVo.I18n i18n = new CardBackVo.I18n();
        i18n.setZhCn("门禁权限到期预提醒");
        i18n.setEnUs("Access Control Expiration Pre-reminder");
        title.setI18n(i18n);
        header.setTitle(title);
        cardDetails.setHeader(header);

        // 设置 I18nElements 对象
        CardBackVo.I18nElements i18nElements = new CardBackVo.I18nElements();

        // 填充 zh_cn 元素
        CardBackVo.Element hrElementZhCn = new CardBackVo.Element();
        hrElementZhCn.setTag("hr");

        CardBackVo.Element divElementZhCn = new CardBackVo.Element();
        divElementZhCn.setTag("div");

        CardBackVo.Field fieldZhCn = new CardBackVo.Field();
        fieldZhCn.setShort(false);

        CardBackVo.Text textZhCn = new CardBackVo.Text();
        textZhCn.setTag("lark_md");
        textZhCn.setContent("${name!}，您好：\r您申请的**${parkName!}-${controlTypeDesc!}**将于**${endTime!}**到期，" +
                "权限清单请点击查看详情。\n如需继续使用请重新发起申请。\n\n如有疑问，请联系行政助手。");
        fieldZhCn.setText(textZhCn);

        divElementZhCn.setFields(Arrays.asList(fieldZhCn));

        // 添加到 zh_cn 元素列表
        i18nElements.setZhCnElements(Arrays.asList(hrElementZhCn, divElementZhCn, hrElementZhCn));

        // 填充 en_us 元素
        CardBackVo.Element hrElementEnUs = new CardBackVo.Element();
        hrElementEnUs.setTag("hr");

        CardBackVo.Element divElementEnUs = new CardBackVo.Element();
        divElementEnUs.setTag("div");

        CardBackVo.Field fieldEnUs = new CardBackVo.Field();
        fieldEnUs.setShort(false);

        CardBackVo.Text textEnUs = new CardBackVo.Text();
        textEnUs.setTag("lark_md");
        textEnUs.setContent("${name!}, Hello!\rYour requested **${parkName!}-${controlTypeDesc!}** will expire on **${endTimeEn!}**. " +
                "Please click the permission list for details.\nIf you need to continue to use, please re-apply.\n\nIf you have any " +
                "questions, please contact the administrative assistant.");
        fieldEnUs.setText(textEnUs);

        divElementEnUs.setFields(Arrays.asList(fieldEnUs));

        // 添加到 en_us 元素列表
        i18nElements.setEnUsElements(Arrays.asList(hrElementEnUs, divElementEnUs, hrElementEnUs));

        // 设置 cardDetails 的 i18n_elements
        cardDetails.setI18nElements(i18nElements);

        // 将 cardDetails 设置到 cardBackVo
        cardBackVo.setCardDetails(cardDetails);
        return cardBackVo;
    }

}
