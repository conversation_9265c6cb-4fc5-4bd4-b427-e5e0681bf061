package com.mi.oa.ee.safety.api.web.card.coop;

import com.google.common.collect.Lists;
import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.api.converter.card.CardApplyVoConverter;
import com.mi.oa.ee.safety.api.errorcode.InterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.model.common.vo.DeptVo;
import com.mi.oa.ee.safety.api.model.coop.req.ApplyPageConditionReq;
import com.mi.oa.ee.safety.api.model.coop.req.BatchOperateReq;
import com.mi.oa.ee.safety.api.model.coop.req.CoopCardApplyEditReq;
import com.mi.oa.ee.safety.api.model.coop.req.PartnerCardApplyReq;
import com.mi.oa.ee.safety.api.model.coop.req.PartnerCardCreateReq;
import com.mi.oa.ee.safety.api.model.coop.vo.CardApplyDetailVo;
import com.mi.oa.ee.safety.api.model.coop.vo.PartnerInfoVo;
import com.mi.oa.ee.safety.api.model.permission.req.BpmCallBackReq;
import com.mi.oa.ee.safety.api.web.common.ExportBaseController;
import com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto;
import com.mi.oa.ee.safety.application.dto.card.coop.CoopCardApplyEditDto;
import com.mi.oa.ee.safety.application.dto.card.event.CardApplyCreateEvent;
import com.mi.oa.ee.safety.application.dto.visitor.BpmCallBackDto;
import com.mi.oa.ee.safety.application.service.card.coop.CardApplyService;
import com.mi.oa.ee.safety.common.dto.CardRemoteDto;
import com.mi.oa.ee.safety.common.enums.RocketMqTopicEnum;
import com.mi.oa.ee.safety.common.enums.card.CardSeqEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.GsonUtils;
import com.mi.oa.ee.safety.infra.remote.mq.CommonProducer;
import com.mi.oa.ee.safety.infra.remote.sdk.HrodSdk;
import com.mi.oa.ee.safety.infra.seq.generator.SeqGenerator;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> denghui
 * @desc 合作卡申请单接口
 * @date 2022/11/15 16:19
 */
@Api(tags = "合作卡申请单相关")
@WebLog
@RestController
@RequestMapping(value = {"/api/v1/card/apply", "/api/v1/card/coop/apply"})
@Slf4j
public class CoopCardApplyController extends ExportBaseController {

    @Resource
    CardApplyVoConverter cardApplyVoConverter;

    @Autowired
    CardApplyService cardApplyService;

    @Resource
    HrodSdk hrodSdk;

    @Resource
    private IdmRemote idmRemote;

    @Resource
    private SeqGenerator seqGenerator;

    @Resource
    private CommonProducer commonProducer;


    @PostMapping("/create")
    @ApiOperation(value = "申请单创建", httpMethod = "POST")
    public BaseResp<CardRemoteDto> save(@RequestBody @Valid PartnerCardCreateReq req) {
        CardPartnerApplyDto cardPartnerApplyDto = cardApplyVoConverter.reqToDto(req);
        cardPartnerApplyDto.setBpmCode(seqGenerator.next(CardSeqEnum.COOP_CARD_BPM_CODE_CREATE.getCode()));
        CardRemoteDto cardRemoteDto = cardApplyService.saveCoopCard(cardPartnerApplyDto);
        //制卡单创建成功消息发送
        CardApplyCreateEvent event = CardApplyCreateEvent.builder()
                .applyIdList(Lists.newArrayList(cardRemoteDto.getCardApplyId())).build();
        commonProducer.send(RocketMqTopicEnum.APPLY_CREATE.getTopicName(), GsonUtils.toJsonFilterNullField(event));
        return BaseResp.success(cardRemoteDto);
    }

    @PostMapping("/onsite/create")
    @ApiOperation(value = "申请单创建(驻场用)", httpMethod = "POST")
    public BaseResp<CardRemoteDto> saveForOnSite(@RequestBody @Valid PartnerCardApplyReq req) {
        PartnerCardCreateReq partnerCardCreateReq = cardApplyVoConverter.reqConverter(req);
        CardRemoteDto cardRemoteDto = cardApplyService.saveCoopCardApplyForOnSite(cardApplyVoConverter.reqToDto(partnerCardCreateReq));
        return BaseResp.success(cardRemoteDto);
    }

    @GetMapping("/feedback/{phone}")
    @ApiOperation(value = "（可测）通过电话号码回显用户信息", httpMethod = "GET")
    public BaseResp<PartnerInfoVo> feedback(@PathVariable("phone") String phone) {
        CardPartnerApplyDto partnerApplyDto = cardApplyService.getPersonInfoByPhone(phone);
        return BaseResp.success(cardApplyVoConverter.toPartnerVo(partnerApplyDto));
    }

    @GetMapping("/info/{id}")
    @ApiOperation(value = "根据申请单id获取详情", httpMethod = "GET")
    public BaseResp<CardApplyDetailVo> getDetailApplyInfo(@PathVariable("id") Long id) {
        return BaseResp.success(cardApplyVoConverter.toVo(cardApplyService.getDetailCardApply(id, null)));
    }

    @GetMapping("/list/dept")
    @ApiOperation(value = "模糊搜索部门", httpMethod = "GET")
    public BaseResp<List<DeptVo>> getDeptList(@RequestParam(value = "deptName", required = false) String deptName) {
        return BaseResp.success(cardApplyVoConverter.toVoList(hrodSdk.getDeptPathByDeptName(deptName)));
    }

    @PostMapping("/page/list")
    @ApiOperation(value = "申请单列表分页查询", httpMethod = "POST")
    public BaseResp<PageVO<CardApplyDetailVo>> pageConditionList(@RequestBody ApplyPageConditionReq req) {
        PageModel<CardPartnerApplyDto> pageModel = cardApplyService.pageConditionApply(cardApplyVoConverter.reqToDto(req), true);
        return BaseResp.success(PageVO.build(cardApplyVoConverter.toVoApplyList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal()));
    }

    @GetMapping("/export/list")
    @ApiOperation(value = "异步申请单导出", httpMethod = "GET")
    public BaseResp<Void> exportListV2(@RequestParam String queryData) {
        // 防止频繁点击
        String uid = idmRemote.getLoginUid();
        int shardIndex = uid.hashCode() % 4;
        String shardKey = "COOP_CARD_APPLY_LIST:" + shardIndex; // 生成分片锁的键
        String token = RedisUtils.tryLock(shardKey, 30); // 使用分片锁
        if (StringUtils.isEmpty(token)) {
            String errDesc = NeptuneClient.getInstance()
                    .parseEntryTemplate(InterfaceErrorCodeEnum.INTERFACE_EXPORT_ERROR.getErrDesc()).replace("{", "").replace("}", "");
            throw new RuntimeException(errDesc);
        }
        cardApplyService.asyncExportCoopCardApply(queryData);
        return BaseResp.success();
    }

    @PostMapping("/import/list")
    @ApiOperation(value = "异步申请单批量导入(创建)", httpMethod = "POST")
    public BaseResp<Void> importListV2(@RequestParam MultipartFile file, @RequestParam String cardType) {
        String applyType = String.valueOf(Objects.requireNonNull(CardTypeEnum.ofCode(cardType)).getNumber());
        cardApplyService.batchSaveApplyV2(file, applyType);
        return BaseResp.success();
    }

    @PostMapping("/batch/operate")
    @ApiOperation(value = "批量操作(打印，通过，拒绝，通知领取，领取，UMS消息)", httpMethod = "POST")
    public BaseResp<String> batchOperate(@RequestBody BatchOperateReq req) {
        cardApplyService.batchOperate(cardApplyVoConverter.toDto(req));
        return BaseResp.success();
    }

    @GetMapping("/receipt/address")
    @ApiOperation(value = "根据园区获取具体领取地", httpMethod = "GET")
    public BaseResp<String> getReceiptAddress(@RequestParam("parkCode") String parkCode) {
        return BaseResp.success(cardApplyService.getReceiptAddress(parkCode));
    }

    @GetMapping("/check/parkCode")
    @ApiOperation(value = "校验所选园区是否在权限范围内", httpMethod = "GET")
    public BaseResp<Integer> checkParkCodeExist(@RequestParam("parkCode") String parkCode) {
        return BaseResp.success(cardApplyService.checkParkCodeExist(parkCode));
    }

    @PostMapping("/edit")
    @ApiOperation(value = "合作卡申请单保存或编辑", httpMethod = "POST")
    public BaseResp<Void> editCardApply(@RequestBody CoopCardApplyEditReq req) {
        CoopCardApplyEditDto editDto = cardApplyVoConverter.toCoopCardApplyEditDto(req);
        cardApplyService.openCardByCardApply(editDto);
        return BaseResp.success();
    }

    @ApiOperation(value = "bpm回调接口", httpMethod = "POST")
    @PostMapping("/apply/callBack")
    public BaseResp<Void> applyCallBack(@RequestBody BpmCallBackReq params) {
        if (StringUtils.isBlank(params.getBusinessKey()) || ObjectUtils.isEmpty(params.getStatus())) {
            throw new BizException(InterfaceErrorCodeEnum.INTERFACE_PARAM_EMPTY_ERROR,
                    params.getBusinessKey(), params.getStatus());
        }
        BpmCallBackDto bpmCallBackDto = cardApplyVoConverter.toBpmCallBackDto(params);
        cardApplyService.applyBpmCallBack(bpmCallBackDto);
        return BaseResp.success();
    }
}
