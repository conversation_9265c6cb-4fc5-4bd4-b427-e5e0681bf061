package com.mi.oa.ee.safety.api.web.card.electron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.api.converter.card.ElectronicCardVoConverter;
import com.mi.oa.ee.safety.api.errorcode.CardElectronRecordInterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.errorcode.InterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.model.electronic.req.ElectronCardForceDeleteReq;
import com.mi.oa.ee.safety.api.model.electronic.req.ElectronicCardDeleteReq;
import com.mi.oa.ee.safety.api.model.electronic.req.ElectronicCardFindReq;
import com.mi.oa.ee.safety.api.model.electronic.vo.ElectronicCardForAppVo;
import com.mi.oa.ee.safety.application.annotation.ControllerLog;
import com.mi.oa.ee.safety.application.dto.card.electronic.CardElectronPayDto;
import com.mi.oa.ee.safety.application.dto.card.electronic.CardElectronRemoteDto;
import com.mi.oa.ee.safety.application.service.card.electronic.CardElectronRecordService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.card.PayOperationAndResultEnum;
import com.mi.oa.ee.safety.infra.common.x5.SystemCode;
import com.mi.oa.ee.safety.infra.common.x5.rsaserver.annotation.Rsa;
import com.mi.oa.ee.safety.infra.common.x5.rsaserver.response.RsaResponse;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/9/19 15:02
 */
@Api(tags = "电子工卡APP接口")
@WebLog
@RestController
@RequestMapping("/api")
@Slf4j
public class CardElectronAppController {
    @Resource
    private CardElectronRecordService electronicCardService;

    @Resource
    private ElectronicCardVoConverter converter;

    @PostMapping("/electron/find/list")
    @ApiOperation(value = "查询可开通的电子工卡", httpMethod = "POST")
    public BaseResp<ElectronicCardForAppVo> findCardElectronList(@RequestBody ElectronicCardFindReq req) {
        ElectronicCardForAppVo electronicCardForAppVo = new ElectronicCardForAppVo();
        if (StringUtils.isEmpty(req.getElectronDeviceId())) {
            throw new BizException(InterfaceErrorCodeEnum.ELECTRONIC_IMEI_EMPTY);
        }
        if (StringUtils.isEmpty(req.getUid())) {
            throw new BizException(CardElectronRecordInterfaceErrorCodeEnum.ELECTRONIC_UID_EMPTY);
        }
        Boolean isUsingNew = electronicCardService.isUsingNew(req.getUid());
        electronicCardForAppVo.setNewOrOld(isUsingNew);
        try {
            electronicCardService.checkCanOpenCardElectronByUid(req.getUid());
            electronicCardForAppVo.setIsCanOpenCard(true);
            List<CardElectronRemoteDto> statusList = electronicCardService.findCardElectronList(converter.toRemoteDto(req));
            electronicCardForAppVo.setElectronicCardStatusVoList(converter.toRemoteVoList(statusList));
            log.info("statusList:_=============={},param{}", JacksonUtils.bean2Json(electronicCardForAppVo), req);
            return BaseResp.success(electronicCardForAppVo);
        } catch (Exception e) {
            electronicCardForAppVo.setIsCanOpenCard(false);
            electronicCardForAppVo.setMessage(e.getMessage());
            return BaseResp.success(electronicCardForAppVo);
        }

    }

    @PostMapping("/electron/destroyElectronCard")
    @ApiOperation(value = "电子卡删除,根据主键id删除电子工卡", httpMethod = "POST")
    public BaseResp<String> destroyElectronCardById(@RequestBody ElectronicCardDeleteReq req) {

        try {
            electronicCardService.deleteByMiEr(req.getId());
            return BaseResp.success(SafetyConstants.Card.DELETE_ELECTRON_CARD_SUCCESS);
        } catch (Exception e) {
            return BaseResp.error(String.format(SafetyConstants.Card.DELETE_ELECTRON_CARD_EXCEPTION, e.getMessage()));
        }
    }

    @Rsa
    @ControllerLog
    @GetMapping("/electron/deliverCard")
    @ResponseBody
    public String deliverCard(String param) {
        RsaResponse response = new RsaResponse();
        JSONObject json = JSONObject.parseObject(param);
        String sessionId = json.getString("sessionId");
        CardElectronPayDto payDto = null;
        try {
            payDto = parseParamToDto(json, true);
            checkDeliverCardParams(payDto, response);
        } catch (Exception e) {
            log.error("<BUSINESS_ERROR> 金服请求开卡接口异常：sessionId=" + sessionId, e);
            response.setTypeEnum(SystemCode.MITSM_SIGN_FAILED, e.toString());
            return response.toJSONString();
        }

        Map<String, Object> result;
        try {
            log.info("deliveryparam:_=============={},param{}", JSON.toJSONString(json), param);
            result = electronicCardService.deliverCard(payDto);
        } catch (Exception e) {
            log.error("<BUSINESS_ERROR> 金服请求开卡接口异常插入数据操作异常", e);
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, e.toString());
            return response.toJSONString();
        }
        response.setData(result);
        log.info("deliveryresult:_=============={}", JSON.toJSONString(result));
        response.setTypeEnum(SystemCode.SUCCESS_CODE_200, "SUCCESS");
        return response.toJSONString();
    }

    @Rsa
    @ControllerLog
    @PostMapping("/electron/notify")
    @ResponseBody
    public String notify(String param) {
        RsaResponse response = new RsaResponse();
        JSONObject json = JSONObject.parseObject(param);
        log.info("notify json object:{}", json);
        String sessionId = json.getString("sessionId");
        try {
            CardElectronPayDto dto = parseParamToDto(json, false);
            dto.setSessionId(sessionId);
            checkNotifyParams(dto, response);
            electronicCardService.notifyOperation(dto);
            response.setTypeEnum(SystemCode.SUCCESS_CODE_200, "SUCCESS");
        } catch (Exception e) {
            log.error("<BUSINESS_ERROR> 金服异步通知接口异常：sessionId=" + sessionId, e);
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, e.toString());
        }
        return response.toJSONString();
    }

    @ApiOperation(value = "强制删除所有的电子工卡", httpMethod = "POST")
    @PostMapping("/electron/force/delete")
    public BaseResp<Void> forceDeleteAllCardElectronByUid(@RequestBody ElectronCardForceDeleteReq req) {
        if (StringUtils.isEmpty(req.getUid())) {
            throw new BizException(CardElectronRecordInterfaceErrorCodeEnum.ELECTRONIC_UID_EMPTY);
        }
        electronicCardService.forceDeleteAllCardElectronByUid(req.getUid());
        return BaseResp.success();
    }

    @Rsa
    @ApiOperation(value = "电子卡删除接口（钱包调用）", httpMethod = "GET")
    @ControllerLog
    @GetMapping("/electron/delete")
    @ResponseBody
    public String delete(String param) {
        RsaResponse response = new RsaResponse();
        JSONObject json = JSONObject.parseObject(param);
        CardElectronPayDto cardElectronPayDto = parseParamToDto(json, false);
        checkDeleteParams(cardElectronPayDto, response);
        Map<String, Object> result = Maps.newHashMap();
        try {
            result = electronicCardService.deleteForPay(cardElectronPayDto);
            response.setTypeEnum(SystemCode.SUCCESS_CODE_200, "SUCCESS");
        } catch (Exception e) {
            log.error("<BUSINESS_ERROR> 金服请求删除接口异常：sessionId={}", cardElectronPayDto.getSessionId(), e);
            response.setTypeEnum(SystemCode.DELETE_FAIL, e.toString());
        }
        response.setData(result);
        return response.toJSONString();
    }

    private void checkDeleteParams(CardElectronPayDto cardElectronPayDto, RsaResponse response) {
        if (StringUtils.isEmpty(cardElectronPayDto.getElectronUserId())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "用户的小米ID必填项");
        }
        if (StringUtils.isEmpty(cardElectronPayDto.getElectronCardNum())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "小米TSM侧卡号必填项");
        }
        log.info("param:{}", cardElectronPayDto);
    }

    private void checkDeliverCardParams(CardElectronPayDto payDto, RsaResponse response) {
        if (StringUtils.isEmpty(payDto.getElectronUserId())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "用户的小米ID必填");
        }
        if (StringUtils.isEmpty(payDto.getElectronProductId())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "发卡的产品ID必填");
        }

        if (StringUtils.isEmpty(payDto.getElectronCardNum())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "小米TSM侧卡号必填");
        }
        //判断是否有型号类型
        if (StringUtils.isEmpty(payDto.getElectronType())) {
            response.setTypeEnum(SystemCode.MIAPP_TOKEN_TYPE_FAILED, "设备类型必填");
        }

        if (StringUtils.isEmpty(payDto.getElectronModel())) {
            response.setTypeEnum(SystemCode.MIAPP_TOKEN_MODEL_FAILED, "设备模型必填");
        }

        if (StringUtils.isEmpty(payDto.getElectronDeviceId())) {
            response.setTypeEnum(SystemCode.MIAPP_TOKEN_DEVICEID_FAILED, "设备ID必填");
        }
    }

    private void checkNotifyParams(CardElectronPayDto dto, RsaResponse response) {
        if (StringUtils.isEmpty(dto.getAction())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "操作类型必填项");
        }
        String sendType = PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_ACTION_SEND.getType();
        String deleteType = PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_ACTION_DELETE.getType();
        String deletingType = PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_ACTION_DELETING.getType();

        if (!StringUtils.isEmpty(dto.getAction()) && !sendType.equals(dto.getAction())
                && !deleteType.equals(dto.getAction()) && !deletingType.equals(dto.getAction())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "操作类型不支持");
        }
        if (StringUtils.isEmpty(dto.getElectronUserId())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "用户的小米ID必填项");
        }
        if (StringUtils.isEmpty(dto.getElectronProductId())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "发卡的产品ID必填项");
        }
        if (StringUtils.isEmpty(dto.getElectronCardNum())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "小米TSM侧卡号必填项");
        }
        if (StringUtils.isEmpty(dto.getResult())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "操作结果必填项");
        }
        String resultType = PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_RESULT_SUC.getType();
        String noExistsType = PayOperationAndResultEnum.ELECTRON_CARD_NOTIFY_RESULT_NO_EXISTS.getType();
        if (!StringUtils.isEmpty(dto.getResult()) && !resultType.equals(dto.getResult()) && !noExistsType.equals(dto.getResult())) {
            response.setTypeEnum(SystemCode.MITSM_UNKNOWN_FAILED, "操作结果不支持");
        }
    }

    private CardElectronPayDto parseParamToDto(JSONObject json, Boolean isNeedToken) {
        CardElectronPayDto payDto = new CardElectronPayDto();
        payDto.setAction(json.getString("action"));
        payDto.setElectronUserId(json.getString("userId"));
        payDto.setResult(json.getString("result"));
        payDto.setElectronCardNum(json.getString("cardNo"));
        payDto.setElectronProductId(json.getString("productId"));
        String token = json.getString("token");
        if (isNeedToken) {
            if (StringUtils.isEmpty(token)) {
                throw new BizException(CardElectronRecordInterfaceErrorCodeEnum.ELECTRONIC_TOKEN_EMPTY);
            }
            parseToken(token, payDto);
        }
        return payDto;
    }

    private void parseToken(String token, CardElectronPayDto payDto) {
        //token = 用户ＩＤ+"_"+用户AUTH+"_"+deviceID + "类型M 手机　B手表"　+ "模型" + "第三方设备"

        String[] arr = token.split("_");
        String uid = arr[0];
        String deviceId = arr[arr.length - 1];
        String type = arr[arr.length - 3];
        String model = arr[arr.length - 2];
        payDto.setUid(uid);
        payDto.setElectronDeviceId(deviceId);
        payDto.setElectronModel(model);
        payDto.setElectronType(type);
    }
}
