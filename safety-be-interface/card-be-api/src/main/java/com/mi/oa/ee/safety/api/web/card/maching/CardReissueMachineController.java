package com.mi.oa.ee.safety.api.web.card.maching;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mi.oa.ee.safety.api.converter.card.CardReissueMachineVoConverter;
import com.mi.oa.ee.safety.api.errorcode.InterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.model.machine.req.CardReissueMachineReq;
import com.mi.oa.ee.safety.api.model.machine.vo.CardReissueMachineVo;
import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardApplyDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardReceiveDto;
import com.mi.oa.ee.safety.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.electronic.CardElectronRecordService;
import com.mi.oa.ee.safety.application.service.card.reissue.ReissueCardApplyService;
import com.mi.oa.ee.safety.application.service.card.shared.CommonCardService;
import com.mi.oa.ee.safety.application.service.safety.SafetyUmsNotifyService;
import com.mi.oa.ee.safety.common.dto.SafetyUmsConfigDto;
import com.mi.oa.ee.safety.common.dto.SafetyUmsNotifyDto;
import com.mi.oa.ee.safety.common.enums.card.CardReissueApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyConfigUserTypeEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.errorcode.CardReissueApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.SafetyUmsNotifyDo;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyUmsNotifyDomainService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.omg.CORBA.SystemException;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/8/19 15:01
 */
@Api(tags = "补卡机接口")
@WebLog
@RestController
@RequestMapping("/api")
@Slf4j
public class CardReissueMachineController {

    @Resource
    private ReissueCardApplyService reissueCardApplyService;

    @Resource
    private CardReissueMachineVoConverter voConverter;

    @Resource
    private CardElectronRecordService cardElectronRecordService;

    @Resource
    private SafetyPersonDomainService safetyPersonDomainService;

    @Resource
    SafetyUmsNotifyDomainService safetyUmsNotifyDomainService;

    @NacosValue(value = "${card.prefix-encrypt-code:201046}", autoRefreshed = true)
    private String prefixEncryptCode;

    @Resource
    SafetyUmsNotifyService safetyUmsNotifyService;

    @Resource
    CommonCardService commonCardService;

    private static final Integer EXPIRE_TIME = 5;

    //验证码有效时间：秒
    private static final int EXPIRE = 300;
    //验证码有效时间：秒
    private static final String PRE_KEY = "makeCard_";
    // 消息configId
    private static final Long MSG_CONFIG_ID = 113L;

    /**
     * 根据工号获取员工信息
     *
     * @param req
     * @return
     */
    @PostMapping("/makeCard/getUserMakeCardInfo")
    @ResponseBody
    public BaseResp<CardReissueMachineVo> getUserMakeCardInfo(@RequestBody CardReissueMachineReq req) {
        checkGetUserInfoParam(req.getUid(), req.getUserName());
        ReissueCardApplyDto info = this.checkIsMakingCard(req.getUid(), req.getUserName());
        return BaseResp.success(voConverter.toReissueCardMachineVo(info));
    }

    private void checkGetUserInfoParam(String uid, String userName) {
        if (StringUtils.isEmpty(uid) && StringUtils.isEmpty(userName)) {
            throw new BizException(CardReissueApplyDomainErrorCodeEnum.PARAMS_ERROR);
        }
    }

    /**
     * 激活，修改状态为已领取
     *
     * @param req
     * @return
     * @throws Exception
     */
    @PostMapping("/makeCard/setPrintedCardState")
    @ResponseBody
    public BaseResp<Void> setPrintedCardState(@RequestBody CardReissueMachineReq req) {
        String userName = req.getUserName();
        String cardNo = req.getMediumPhysicsCode();
        String encryptCard = req.getMediumEncryptCode();
        String uid = req.getUid();
        checkGetUserInfoParam(uid, userName);
        if (StringUtils.isEmpty(cardNo)) {
            throw new BizException(InterfaceErrorCodeEnum.PHYSICS_CARD_CAN_NOT_EMPTY);
        }
        if (cardNo.length() != 8) {
            throw new BizException(InterfaceErrorCodeEnum.PHYSICS_CARD_ERROR);
        }
        if (StringUtils.isEmpty(encryptCard)) {
            throw new BizException(InterfaceErrorCodeEnum.ENCRYPT_CARD_NOT_EMPTY);
        }
        openNewCard(uid, userName, cardNo, encryptCard);
        //修改状态为已领卡
        return BaseResp.success("修改状态成功", null);
    }

    public void openNewCard(String uid, String userName, String cardNo, String encryptCard) {
        ReissueCardApplyDto info = this.checkIsMakingCard(uid, userName);
        if (!CardReissueApplyStatusEnum.TO_BE_OPEN.getCode().equals(info.getReissueApplyStatus())) {
            throw new BizException(InterfaceErrorCodeEnum.NOT_EXIST_TO_OPEN, userName);
        }
        info.setMediumPhysicsCode(cardNo);
        info.setMediumEncryptCode(encryptCard);
        if (prefixEncryptCode.equals(encryptCard.substring(0, 6))) {
            String[] split = encryptCard.split(prefixEncryptCode);
            info.setPrefixEncryptCode(prefixEncryptCode);
            info.setSuffixEncryptCode(split[1]);
        }
        //清空卡编号
        info.setCardNum(StringUtils.EMPTY);
        String actualUid = reissueCardApplyService.openCardByApply(info);
        //电子卡删除
        cardElectronRecordService.forceDeleteAllCardElectronByUid(actualUid);
        // 领取卡
        CardReceiveDto cardReceiveDto = new CardReceiveDto();
        cardReceiveDto.setApplyId(info.getCardApplyId());
        cardReceiveDto.setCardType(String.valueOf(info.getCardType()));
        cardReceiveDto.setIsNeedSyncSupplier(Boolean.TRUE);
        commonCardService.receive(cardReceiveDto);
    }

    /**
     * 发送验证码
     *
     * @param req
     * @return
     */
    @PostMapping("/makeCard/sendVerificationCode")
    @ResponseBody
    public BaseResp<Void> sendVerificationCode(@RequestBody CardReissueMachineReq req) {
        checkSendVerificationCodeReq(req);
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setMobile(req.getPhone());
        safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
        safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByPhoneAndOrgCode(safetyPersonDo);
        if (StringUtils.isEmpty(safetyPersonDo.getUid())) {
            throw new BizException(InterfaceErrorCodeEnum.NOT_EXIST_USER, req.getPhone());
        }
        String code = CodeUtils.generateVisitorCode(6, false);
        log.info("send code：{}", code);
        String key = PRE_KEY + req.getPhone();
        log.info("send key：{}", key);
        //将验证码存redis
        RedisUtils.setEx(key, code, EXPIRE_TIME, TimeUnit.MINUTES);
        //发送手机验证码
        SafetyUmsNotifyDo simpleUmsNotifyDo = safetyUmsNotifyDomainService.buildUmsSmsNotify(safetyPersonDo, code);
        //创建对应的UMS消息
        try {
            Map<Long, SafetyUmsConfigDto> safetyUmsConfigDTOMap =
                    safetyUmsNotifyService.getAllSafetyUmsConfig("card");
            SafetyUmsConfigDto safetyUmsConfigDto = safetyUmsConfigDTOMap.get(MSG_CONFIG_ID);
            List<SafetyUmsNotifyDto> simpleUmsNotifyDtos = new ArrayList<>();
            // do转dto
            SafetyUmsNotifyDto safetyUmsNotifyDto = new SafetyUmsNotifyDto();
            BeanUtils.copyProperties(simpleUmsNotifyDo, safetyUmsNotifyDto);
            simpleUmsNotifyDtos.add(safetyUmsNotifyDto);
            safetyUmsNotifyService.sendNotify(simpleUmsNotifyDtos, safetyUmsConfigDto);

        } catch (Exception e) {
            log.error("----- sendErrorMessageUms : {}", e.getMessage());
        }
        return BaseResp.success("验证码发送成功", null);
    }

    private void checkSendVerificationCodeReq(CardReissueMachineReq req) {
        if (StringUtils.isEmpty(req.getPhone())) {
            throw new BizException(InterfaceErrorCodeEnum.PHONE_CAN_NOT_EMPTY);
        }
    }

    /**
     * 校验验证码，成功登录返回员工号
     *
     * @param req
     * @return
     * @throws SystemException
     */
    @PostMapping("/makeCard/checkVerificationCode")
    @ResponseBody
    public BaseResp<Map<String, String>> checkVerificationCode(@RequestBody CardReissueMachineReq req)
            throws SystemException {
        String phone = req.getPhone();
        String verificationCode = req.getVerificationCode();
        if (StringUtils.isEmpty(phone)) {
            throw new BizException(InterfaceErrorCodeEnum.PHONE_CAN_NOT_EMPTY);
        }
        if (StringUtils.isEmpty(verificationCode)) {
            throw new BizException(InterfaceErrorCodeEnum.VERIFICATION_CODE_CAN_NOT_EMPTY);
        }
        String key = PRE_KEY + phone;
        String code = (String) RedisUtils.get(key);
        if (StringUtils.isEmpty(code)) {
            throw new BizException(InterfaceErrorCodeEnum.VERIFICATION_CODE_NOT_EXIST);
        }
        log.info("新唯一key：{}", key);
        log.info("验证码：{}", code);
        if (!code.equals(verificationCode)) {
            throw new BizException(InterfaceErrorCodeEnum.VERIFICATION_CODE_ERROR);
        }
        Map<String, String> result = new HashMap<>();
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setMobile(phone);
        safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
        safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByPhoneAndOrgCode(safetyPersonDo);
        Asserts.assertNotNull(safetyPersonDo.getUid(), ApplicationErrorCodeEnum.PERSON_CAN_NOT_NULL);
        result.put("empId", safetyPersonDo.getEmployeeId());
        result.put("userName", safetyPersonDo.getUserName());
        result.put("uid", safetyPersonDo.getUid());
        RedisUtils.delete(key); //验证通过，删除验证码
        return BaseResp.success("验证成功", result);
    }

    /**
     * 打印完成通知工卡中心
     *
     * @param req
     * @return
     * @throws SystemException
     */
    @PostMapping("/makeCard/printingComplete")
    @Transactional(rollbackFor = SystemException.class)
    @ResponseBody
    public BaseResp<Void> printingComplete(@RequestBody CardReissueMachineReq req) {
        Long id = req.getId();
        if (Objects.isNull(id)) {
            throw new BizException(InterfaceErrorCodeEnum.ID_NOT_EMPTY);
        }
        ReissueCardApplyDto info = new ReissueCardApplyDto();
        info.setReissueApplyId(id);
        info.setReissueApplyStatus(CardReissueApplyStatusEnum.TO_BE_OPEN.getCode());
        try {
            reissueCardApplyService.updateReissueCardApplyStatus(info);
        } catch (Exception e) {
            throw new BizException(InterfaceErrorCodeEnum.UPDATE_REISSUE_CARD_APPLY_ERROR);
        }
        return BaseResp.success("修改状态成功", null);
    }

    /**
     * 点击打印后验证是否可以开卡
     *
     * @return
     * @throws SystemException
     */
    @PostMapping("/makeCard/checkCardInfo")
    @ResponseBody
    public BaseResp<Void> checkCardInfo(@RequestBody CardReissueMachineReq req) throws SystemException {
        String uid = req.getUid();
        String userName = req.getUserName();
        Integer id = Math.toIntExact(req.getId());
        if (StringUtils.isEmpty(id.toString())) {
            throw new BizException(InterfaceErrorCodeEnum.ID_NOT_EMPTY);
        }
        checkGetUserInfoParam(uid, userName);
        ReissueCardApplyDto info = this.checkIsMakingCard(uid, userName);
        if (!info.getReissueApplyStatus().equals(CardReissueApplyStatusEnum.TO_BE_PRINTED.getCode())) {
            throw new BizException(InterfaceErrorCodeEnum.NOT_EXIST_TO_PRINT, userName);
        } else {
            return BaseResp.success("验证成功", null);
        }
    }

    /**
     * 校验是否有在途制卡数据
     *
     * @param uid
     * @return
     * @throws SystemException
     */
    public ReissueCardApplyDto checkIsMakingCard(String uid, String userName) {
        return reissueCardApplyService.findOneByUidAndUserName(uid, userName);
    }

}
