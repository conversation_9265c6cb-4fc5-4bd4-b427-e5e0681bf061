package com.mi.oa.ee.safety.api.web.card.migration;

import com.mi.oa.ee.safety.application.dto.card.config.RecyclableCardDto;
import com.mi.oa.ee.safety.application.service.card.migration.CardMigrationService;
import com.mi.oa.ee.safety.domain.enums.card.RecyclableCardCountryEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/8/14 18:00
 */
@Api(tags = "地点管理")
@WebLog
@RestController
@RequestMapping("/api/v1/card/recyclable/migration")
@Slf4j
public class CardMigrationController {

    @Resource
    private CardMigrationService cardMigrationService;

    @Resource
    private CardMigrationVoConverter cardMigrationVoConverter;

    @PostMapping("/sync")
    @ApiOperation(value = "地址管理新增/编辑", httpMethod = "POST")
    public BaseResp<Void> sync(@RequestBody RecyclableMigrationReq req) {
        RecyclableCardDto recyclableCardDto = cardMigrationVoConverter.toRecyclableCardDto(req);
        recyclableCardDto.setCountryEnum(RecyclableCardCountryEnum.CHINA);
        cardMigrationService.sync(recyclableCardDto);
        return BaseResp.success();
    }

}
