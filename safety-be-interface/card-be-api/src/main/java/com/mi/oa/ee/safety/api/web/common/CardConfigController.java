package com.mi.oa.ee.safety.api.web.common;

import com.mi.oa.ee.safety.api.converter.card.RecyclableCardVoConverter;
import com.mi.oa.ee.safety.api.model.common.req.SearchCardReq;
import com.mi.oa.ee.safety.api.model.config.vo.RecyclableCardVo;
import com.mi.oa.ee.safety.api.model.coop.vo.CardDetailVo;
import com.mi.oa.ee.safety.application.dto.card.config.RecyclableCardDto;
import com.mi.oa.ee.safety.application.dto.card.config.RecyclableCardSearchDto;
import com.mi.oa.ee.safety.application.service.card.config.RecyclableCardService;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.ee.safety.common.dto.CoopworkCardInfoDto;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/4/10 20:21
 */
@Api(tags = "合作卡配置管理")
@WebLog
@RestController
@RequestMapping("/api/v1/card/config")
@Slf4j
public class CardConfigController {

    @Resource
    private CardService cardService;

    @Resource
    private RecyclableCardService recyclableCardService;

    @Resource
    private RecyclableCardVoConverter recyclableCardVoConverter;


    @GetMapping("/check/available")
    @ApiOperation(value = "开正式卡时加密卡号校验是否可用", httpMethod = "GET")
    public BaseResp<String> checkIsAvailableByEncryptCode(@RequestParam("mediumEncryptCard") String mediumEncryptCard) {
        return BaseResp.success();
    }

    @PostMapping("/searchByCardNum")
    @ApiOperation(value = "根据物理卡号，加密卡号，卡编号查询卡信息", httpMethod = "POST")
    public BaseResp<CardDetailVo> getDetailApplyInfo(@RequestBody SearchCardReq cardReq) {
        return BaseResp.error("{{{未查到匹配的工卡，请检查卡类型和卡编号}}}", null);
    }

    @PostMapping("/recyclable/search")
    @ApiOperation(value = "根据物理卡号，加密卡号，卡编号查询卡信息", httpMethod = "POST")
    public BaseResp<RecyclableCardVo> recyclableSearch(@RequestBody SearchCardReq cardReq) {
        //参数全部为空则返回
        if (StringUtils.isAllBlank(cardReq.getCardNum(), cardReq.getMediumPhysicsCode(), cardReq.getMediumEncryptCode())) {
            return BaseResp.error("{{{参数错误}}}", null);
        }
        RecyclableCardSearchDto searchDto = recyclableCardVoConverter.toSearchDto(cardReq);
        RecyclableCardDto recyclableCardDto = recyclableCardService.findUsableRecyclableCard(searchDto);
        if (Objects.nonNull(recyclableCardDto)) {
            RecyclableCardVo cardVo = recyclableCardVoConverter.toRecyclableCardVo(recyclableCardDto);
            return BaseResp.success(cardVo);
        }
        return BaseResp.error("{{{未查到匹配的工卡，请检查卡类型和卡编号}}}", null);
    }

    private CoopworkCardInfoDto buildRequestParam(SearchCardReq cardReq) {
        CoopworkCardInfoDto coopworkCardInfoDto = new CoopworkCardInfoDto();
        Integer cardType = StringUtils.isNotBlank(cardReq.getCardType())
                ? CardTypeEnum.ofCode(cardReq.getCardType()).getOldCardNumber() : CardTypeEnum.COOPERATION_CARD.getOldCardNumber();

        coopworkCardInfoDto.setCardType(cardType);
        if (StringUtils.isNotEmpty(cardReq.getMediumPhysicsCode())) {
            coopworkCardInfoDto.setNewCard(cardReq.getMediumPhysicsCode());
        } else if (StringUtils.isNotEmpty(cardReq.getMediumEncryptCode())) {
            coopworkCardInfoDto.setEncryptCard(cardReq.getMediumEncryptCode());
        } else {
            coopworkCardInfoDto.setCardNum(cardReq.getCardNum());
        }
        return coopworkCardInfoDto;
    }
}
