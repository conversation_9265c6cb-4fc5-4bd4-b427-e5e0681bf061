package com.mi.oa.ee.safety.api.web.tool;

import com.mi.oa.ee.safety.common.dto.CardOpenMessage;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.infra.remote.sdk.notify.CardOpenNotifyProducerMessage;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@Api(tags = "工具接口")
@WebLog
@RestController
@Slf4j
@EnableAsync
@RequestMapping("/api/v1/card/tool")
public class CardToolController {

    @Resource
    private CardOpenNotifyProducerMessage notifySdk;


    @GetMapping("/notify/open/card")
    public BaseResp<Void> notifyOpenCard(@RequestParam @NotNull Long applyId) {
        CardOpenMessage cardOpenMessage = new CardOpenMessage();
        cardOpenMessage.setCardApplyStatusEnum(CardApplyStatusEnum.COMPLETED);
        cardOpenMessage.setId(applyId);
        notifySdk.produceMessage(cardOpenMessage, String.valueOf(applyId));
        return BaseResp.success();
    }
}
