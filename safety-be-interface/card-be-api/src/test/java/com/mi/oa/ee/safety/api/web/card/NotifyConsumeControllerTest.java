package com.mi.oa.ee.safety.api.web.card;

import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardRefundCallbackDto;
import com.mi.oa.ee.safety.application.service.card.reissue.ReissueCardApplyService;
import com.mi.oa.ee.safety.api.model.common.req.RefundResultReq;
import com.mi.oa.ee.safety.common.config.PayProperties;
import com.mi.oa.infra.oaucf.pay.enums.RefundStatusEnum;
import com.xiaomi.info.infra.notify.req.ConsumerResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotifyConsumeControllerTest {

    @InjectMocks
    private NotifyConsumeController notifyConsumeController;

    @Mock
    private ReissueCardApplyService reactionCardApplyService;

    @Mock
    private PayProperties payProperties;

    private RefundResultReq refundResultReq;

    @BeforeEach
    void setUp() {
        // 设置默认的 PayProperties 值
        when(payProperties.getBizId()).thenReturn("test_biz_id");

        // 初始化测试数据
        refundResultReq = new RefundResultReq();
        refundResultReq.setBizId("test_biz_id");
        refundResultReq.setPayOrderNo("test_pay_order_no");
        refundResultReq.setBizRefundNo("test_biz_refund_no");
        refundResultReq.setRefundNo("test_refund_no");
        refundResultReq.setRefundAmount(100L);
    }

    @Test
    void refundCallback_WithValidBizIdAndSuccessStatus_ShouldProcessSuccessfully() {
        // 设置退款成功状态
        refundResultReq.setRefundStatus(RefundStatusEnum.SUCCESS.getCode());

        // 执行测试
        ConsumerResponse response = notifyConsumeController.refundCallback(refundResultReq);

        // 验证结果
        assertEquals(ConsumerResponse.ok(), response);
        verify(reactionCardApplyService, times(1)).refundCallback(any(ReissueCardRefundCallbackDto.class));
    }

    @Test
    void refundCallback_WithValidBizIdAndFailedStatus_ShouldProcessSuccessfully() {
        // 设置退款失败状态
        refundResultReq.setRefundStatus(0); // 使用 0 表示失败状态

        // 执行测试
        ConsumerResponse response = notifyConsumeController.refundCallback(refundResultReq);

        // 验证结果
        assertEquals(ConsumerResponse.ok(), response);
        verify(reactionCardApplyService, times(1)).refundCallback(argThat(dto -> dto.getStatus() == 0));
    }

    @Test
    void refundCallback_WithInvalidBizId_ShouldNotProcess() {
        // 设置无效的业务 ID
        refundResultReq.setBizId("invalid_biz_id");
        refundResultReq.setRefundStatus(RefundStatusEnum.SUCCESS.getCode());

        // 执行测试
        ConsumerResponse response = notifyConsumeController.refundCallback(refundResultReq);

        // 验证结果
        assertEquals(ConsumerResponse.ok(), response);
        verify(reactionCardApplyService, never()).refundCallback(any(ReissueCardRefundCallbackDto.class));
    }

    @Test
    void refundCallback_WithNullRefundAmount_ShouldProcessSuccessfully() {
        // 设置空的退款金额
        refundResultReq.setRefundAmount(null);
        refundResultReq.setRefundStatus(RefundStatusEnum.SUCCESS.getCode());

        // 执行测试
        ConsumerResponse response = notifyConsumeController.refundCallback(refundResultReq);

        // 验证结果
        assertEquals(ConsumerResponse.ok(), response);
        verify(reactionCardApplyService, times(1)).refundCallback(any(ReissueCardRefundCallbackDto.class));
    }

    @Test
    void refundCallback_WithNullPayOrderNo_ShouldProcessSuccessfully() {
        // 设置空的支付订单号
        refundResultReq.setPayOrderNo(null);
        refundResultReq.setRefundStatus(RefundStatusEnum.SUCCESS.getCode());

        // 执行测试
        ConsumerResponse response = notifyConsumeController.refundCallback(refundResultReq);

        // 验证结果
        assertEquals(ConsumerResponse.ok(), response);
        verify(reactionCardApplyService, times(1)).refundCallback(any(ReissueCardRefundCallbackDto.class));
    }

    @Test
    void refundCallback_WithNullBizRefundNo_ShouldProcessSuccessfully() {
        // 设置空的业务退款单号
        refundResultReq.setBizRefundNo(null);
        refundResultReq.setRefundStatus(RefundStatusEnum.SUCCESS.getCode());

        // 执行测试
        ConsumerResponse response = notifyConsumeController.refundCallback(refundResultReq);

        // 验证结果
        assertEquals(ConsumerResponse.ok(), response);
        verify(reactionCardApplyService, times(1)).refundCallback(any(ReissueCardRefundCallbackDto.class));
    }

    @Test
    void refundCallback_WithNullRefundNo_ShouldProcessSuccessfully() {
        // 设置空的退款单号
        refundResultReq.setRefundNo(null);
        refundResultReq.setRefundStatus(RefundStatusEnum.SUCCESS.getCode());

        // 执行测试
        ConsumerResponse response = notifyConsumeController.refundCallback(refundResultReq);

        // 验证结果
        assertEquals(ConsumerResponse.ok(), response);
        verify(reactionCardApplyService, times(1)).refundCallback(any(ReissueCardRefundCallbackDto.class));
    }
} 