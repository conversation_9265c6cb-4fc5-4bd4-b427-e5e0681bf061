package com.mi.oa.ee.safety.api.web.card.electron;

import com.mi.oa.ee.safety.api.converter.card.ElectronicCardVoConverter;
import com.mi.oa.ee.safety.api.errorcode.InterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.model.electronic.req.ElectronicCardFindReq;
import com.mi.oa.ee.safety.api.model.electronic.vo.ElectronicCardForAppVo;
import com.mi.oa.ee.safety.application.service.card.electronic.CardElectronRecordService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CardElectronAppControllerTest {

    @InjectMocks
    private CardElectronAppController controller;

    @Mock
    private CardElectronRecordService electronicCardService;

    @Mock
    private ElectronicCardVoConverter converter;

    @Test
    void findCardElectronList_WhenDeviceIdEmpty_ShouldThrowException() {
        // Arrange
        ElectronicCardFindReq req = new ElectronicCardFindReq();
        req.setUid("testUid");
        
        // Act & Assert
        assertThrows(BizException.class, () -> controller.findCardElectronList(req));
        verify(electronicCardService, never()).findCardElectronList(any());
    }

    @Test
    void findCardElectronList_WhenUidEmpty_ShouldThrowException() {
        // Arrange
        ElectronicCardFindReq req = new ElectronicCardFindReq();
        req.setElectronDeviceId("testDeviceId");
        
        // Act & Assert
        assertThrows(BizException.class, () -> controller.findCardElectronList(req));
        verify(electronicCardService, never()).findCardElectronList(any());
    }

    @Test
    void findCardElectronList_WhenCanOpenCard_ShouldReturnSuccess() {
        // Arrange
        ElectronicCardFindReq req = new ElectronicCardFindReq();
        req.setUid("testUid");
        req.setElectronDeviceId("testDeviceId");
        
        when(electronicCardService.isUsingNew("testUid")).thenReturn(true);
        when(electronicCardService.findCardElectronList(any())).thenReturn(Collections.emptyList());
        
        // Act
        BaseResp<ElectronicCardForAppVo> response = controller.findCardElectronList(req);
        
        // Assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertTrue(response.getData().getIsCanOpenCard());
        assertTrue(response.getData().getNewOrOld());
        verify(electronicCardService).isUsingNew("testUid");
        verify(electronicCardService).findCardElectronList(any());
    }

    @Test
    void findCardElectronList_WhenCannotOpenCard_ShouldReturnSuccessWithFalse() {
        // Arrange
        ElectronicCardFindReq req = new ElectronicCardFindReq();
        req.setUid("testUid");
        req.setElectronDeviceId("testDeviceId");
        
        when(electronicCardService.isUsingNew("testUid")).thenReturn(true);
        doThrow(new BizException(InterfaceErrorCodeEnum.ELECTRONIC_IMEI_EMPTY))
            .when(electronicCardService).checkCanOpenCardElectronByUid("testUid");
        
        // Act
        BaseResp<ElectronicCardForAppVo> response = controller.findCardElectronList(req);
        
        // Assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertFalse(response.getData().getIsCanOpenCard());
        assertTrue(response.getData().getNewOrOld());
        verify(electronicCardService).isUsingNew("testUid");
        verify(electronicCardService).checkCanOpenCardElectronByUid("testUid");
    }
} 