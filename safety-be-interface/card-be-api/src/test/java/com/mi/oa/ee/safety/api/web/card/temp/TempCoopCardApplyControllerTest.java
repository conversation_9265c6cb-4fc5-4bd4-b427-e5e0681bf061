package com.mi.oa.ee.safety.api.web.card.temp;

import cn.hutool.log.Log;
import com.google.common.collect.Maps;
import com.mi.oa.ee.BaseTest;
import com.mi.oa.ee.safety.application.converter.common.ImportConverter;
import com.mi.oa.ee.safety.application.impl.common.export.TempCardApplyImportExecutorImpl;
import com.mi.oa.ee.safety.domain.service.AsyncImportTaskDomainService;
import com.mi.oa.infra.uc.common.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @since 2023/6/12 22:20
 */

@Ignore
@Slf4j
class TempCoopCardApplyControllerTest extends BaseTest {

    @Resource
    private TempCardApplyImportExecutorImpl tempCardApplyImportExecutor;

    @Resource
    private AsyncImportTaskDomainService asyncImportTaskDomainService;

    @Resource
    private ImportConverter importConverter;

    //    @Test
    void pageConditionList() throws Exception {
        Map<String, String> param = Maps.newHashMap();
        param.put("cityId", "205");
        param.put("firstDeptId", "AU");
        String response = mockMvc.perform(
                MockMvcRequestBuilders.post("/api/v1/card/temp/apply/page/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(GsonUtil.toJsonString(param))
                        .cookie(cookie())
        ).andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
        System.out.println(response);
        Assertions.assertNotNull(response);
    }

    //    @Test
    void batchImport() throws Exception {
        Map<String, String> param = Maps.newHashMap();
        param.put("fileName", "制卡数据模版.xlsx");
        param.put("url", "https://staging-cnbj2-fds.api.xiaomi.net/space-be/test/data.xlsx");
        String response = mockMvc.perform(
                MockMvcRequestBuilders.post("/api/v1/card/temp/apply/batchImport")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(GsonUtil.toJsonString(param))
                        .cookie(cookie())
        ).andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
        System.out.println(response);
        Assertions.assertNotNull(response);
    }

    /*@Test
    @Ignore
    void batchImportDirectly() {
        String batchId = "435cf44355614063a7c3cd501a2cbd16";
        AsyncImportTaskDo asyncImportTaskDo = asyncImportTaskDomainService.findByBatchId(batchId);
        AsyncImportTaskDto asyncImportTaskDto = importConverter.toDto(asyncImportTaskDo);
        tempCardApplyImportExecutor.batchImport(asyncImportTaskDto);
    }*/

//    @Test
    void edit() throws InterruptedException {
        for (int a = 0; a < 10; a++) {
            final int x = a;
            Thread thread = new Thread(() -> {
                try {
                    editCard(x);
                } catch (Exception e) {
                    System.out.println(x + "fail message" + e.toString());
                }
            });
            thread.start();
            thread.join();
        }
    }

    private void editCard(Integer x) throws Exception {
        System.out.println(x);
        Map<String, String> param = Maps.newHashMap();
        param.put("applyId", "7845");
        param.put("uid", "18fa43e21cae41f1b3427eb16d3c261a");
        param.put("startTime", "2024-06-01T16:00:00.000Z");
        param.put("endTime", "2024-07-30T15:59:59.999Z");
        param.put("cardNum", "");
        param.put("mediumPhysicsCode", "aaa27845");
        param.put("mediumEncryptCode", "2323333332137845");
        String response = mockMvc.perform(
                MockMvcRequestBuilders.post("/api/v1/card/temp/apply/edit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(GsonUtil.toJsonString(param))
                        .cookie(cookie())
        ).andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
        System.out.println(response);
        Assertions.assertNotNull(response);
    }

    //    @Test
    void detail() throws Exception {
        String response = mockMvc.perform(
                MockMvcRequestBuilders.get("/api/v1/card/temp/apply/detail")
                        .contentType(MediaType.APPLICATION_JSON)
                        .param("applyId", "6473")
                        .cookie(cookie())
        ).andReturn().getResponse().getContentAsString(StandardCharsets.UTF_8);
        System.out.println(response);
        Assertions.assertNotNull(response);
    }
}
