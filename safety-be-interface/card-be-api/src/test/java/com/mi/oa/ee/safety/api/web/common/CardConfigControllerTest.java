package com.mi.oa.ee.safety.api.web.common;

import com.mi.oa.ee.safety.api.model.common.req.SearchCardReq;
import com.mi.oa.ee.safety.api.model.coop.vo.CardDetailVo;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.extension.ExtendWith;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CardConfigControllerTest {

    @InjectMocks
    private CardConfigController cardConfigController;

    @Mock
    private CardService cardService;

    @Test
    void getDetailApplyInfo_ShouldReturnError_WhenNoCardFound() {
        // 准备测试数据
        SearchCardReq cardReq = new SearchCardReq();
        cardReq.setCardNum("123456");
        cardReq.setCardType("COOPERATION_CARD");

        // 执行测试
        BaseResp<CardDetailVo> response = cardConfigController.getDetailApplyInfo(cardReq);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("{{{未查到匹配的工卡，请检查卡类型和卡编号}}}", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    void getDetailApplyInfo_ShouldHandleNullRequest() {
        // 执行测试
        BaseResp<CardDetailVo> response = cardConfigController.getDetailApplyInfo(null);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("{{{未查到匹配的工卡，请检查卡类型和卡编号}}}", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    void getDetailApplyInfo_ShouldHandleEmptyRequest() {
        // 准备测试数据
        SearchCardReq cardReq = new SearchCardReq();

        // 执行测试
        BaseResp<CardDetailVo> response = cardConfigController.getDetailApplyInfo(cardReq);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("{{{未查到匹配的工卡，请检查卡类型和卡编号}}}", response.getMessage());
        assertNull(response.getData());
    }
} 