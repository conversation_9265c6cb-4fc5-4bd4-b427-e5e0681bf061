package com.mi.oa.ee.safety.receptionapi.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/10/12 16:46
 */
@Data
@ApiModel(description = "操作日志")
public class SafetyOperateLogVo {

    @ApiModelProperty(value = "操作人", dataType = "java.lang.String")
    private String updateUser;

    @ApiModelProperty(value = "操作人姓名", dataType = "java.lang.String")
    private String updateUserName;

    @ApiModelProperty(value = "操作时间", dataType = "java.time.ZonedDateTime")
    private ZonedDateTime updateTime;

    @ApiModelProperty(value = "操作事件", dataType = "java.lang.String")
    private String operateDesc;

    @ApiModelProperty(value = "操作内容", dataType = "java.lang.String")
    private String requestParams;
}
