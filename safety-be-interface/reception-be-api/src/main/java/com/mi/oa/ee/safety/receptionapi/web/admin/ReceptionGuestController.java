package com.mi.oa.ee.safety.receptionapi.web.admin;

import com.mi.oa.ee.safety.application.dto.reception.ReceptionGuestDto;
import com.mi.oa.ee.safety.application.service.reception.ReceptionGuestService;
import com.mi.oa.ee.safety.receptionapi.converter.ReceptionGuestVoConverter;
import com.mi.oa.ee.safety.receptionapi.model.req.ReceptionGuestParamReq;
import com.mi.oa.ee.safety.receptionapi.model.req.ReceptionGuestQueryReq;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionGuestVo;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/5/9 19:16
 */
@Api(tags = "接待-接待来宾")
@WebLog
@Slf4j
@RestController
@RequestMapping("/api/v1/reception/admin")
public class ReceptionGuestController {
    @Autowired
    private ReceptionGuestVoConverter converter;

    @Autowired
    private ReceptionGuestService guestService;

    @ApiOperation(value = "接待来宾分页")
    @GetMapping("/guest/page")
    public BaseResp<PageVO<ReceptionGuestVo>> getGuestPage(ReceptionGuestQueryReq queryReq) {
        PageModel<ReceptionGuestDto> page = guestService.guestPage(
                converter.queryToDto(queryReq),
                queryReq.getPageNum(),
                queryReq.getPageSize()
        );
        return BaseResp.success(converter.toPageVo(page));
    }

    @ApiOperation(value = "接待来宾列表")
    @GetMapping("/guest/list")
    public BaseResp<List<ReceptionGuestVo>> getGuestList(ReceptionGuestQueryReq queryReq) {
        List<ReceptionGuestDto> list = guestService.guestList(converter.queryToDto(queryReq));
        return BaseResp.success(converter.toVoList(list));
    }

    @ApiOperation(value = "添加接待来宾")
    @PostMapping("/guest/create")
    public BaseResp<ReceptionGuestVo> createGuest(@RequestBody @Valid ReceptionGuestParamReq paramReq) {
        ReceptionGuestDto guestDto = guestService.createGuest(converter.paramToDto(paramReq));
        return BaseResp.success(converter.toVo(guestDto));
    }

    @ApiOperation(value = "更新接待来宾")
    @PostMapping("/guest/{id}/update")
    public BaseResp updateGuest(@PathVariable("id") @ApiParam(value = "接待等级id") Long id,
                                @RequestBody @Valid ReceptionGuestParamReq paramReq) {
        guestService.updateGuest(id, converter.paramToDto(paramReq));
        return BaseResp.success();
    }

    @ApiOperation(value = "禁用接待来宾")
    @PostMapping("/guest/{id}/disable")
    public BaseResp disableGuest(@PathVariable("id") @ApiParam(value = "接待来宾id") Long id) {
        guestService.disableGuest(id);
        return BaseResp.success();
    }

    @ApiOperation(value = "启用接待来宾")
    @PostMapping("/guest/{id}/enable")
    public BaseResp enableGuest(@PathVariable("id") @ApiParam(value = "接待来宾id") Long id) {
        guestService.enableGuest(id);
        return BaseResp.success();
    }
}
