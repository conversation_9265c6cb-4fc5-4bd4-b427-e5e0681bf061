package com.mi.oa.ee.safety.api.model.safety.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/13 18:47
 */
@Data
@ApiModel(value = "SafetyCarrierClassVo", description = "安防载体分类信息")
public class SafetyClassVo {
    private Long id;

    @ApiModelProperty(value = "分类编码", dataType = "java.lang.String", required = false)
    private String classCode;

    @ApiModelProperty(value = "分类名称", dataType = "java.lang.String", required = false)
    private String name;
}
