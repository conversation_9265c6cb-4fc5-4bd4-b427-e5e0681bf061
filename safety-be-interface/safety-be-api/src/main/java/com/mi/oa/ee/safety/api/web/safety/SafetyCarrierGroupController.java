package com.mi.oa.ee.safety.api.web.safety;

import com.alibaba.excel.util.StringUtils;
import com.mi.oa.ee.safety.api.converter.safety.SafetyCarrierGroupVoConverter;
import com.mi.oa.ee.safety.api.converter.safety.SafetyCarrierVoConverter;
import com.mi.oa.ee.safety.api.converter.safety.SafetyClassVoConverter;
import com.mi.oa.ee.safety.api.errorcode.InterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.errorcode.SafetyInterfaceMessageCodeEnum;
import com.mi.oa.ee.safety.api.model.safety.req.SafetyCarrierGroupReq;
import com.mi.oa.ee.safety.api.model.safety.vo.SafetyCarrierGroupVo;
import com.mi.oa.ee.safety.api.model.safety.vo.SafetyCarrierVo;
import com.mi.oa.ee.safety.api.model.safety.vo.SafetyClassVo;
import com.mi.oa.ee.safety.application.dto.safety.SafetyCarrierDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyClassDto;
import com.mi.oa.ee.safety.application.service.safety.SafetyCarrierGroupService;
import com.mi.oa.ee.safety.application.service.safety.SafetyCarrierService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 权限组（安防载体集）管理
 *
 * <AUTHOR>
 * @date 2022/11/16 21:01
 */
@Api(tags = "安防-权限组（安防载体集）管理")
@Validated
@WebLog
@Slf4j
@RestController
@RequestMapping("/api/v1/safety/carriergroup")
public class SafetyCarrierGroupController {

    @Autowired
    SafetyCarrierGroupService safetyCarrierGroupService;

    @Autowired
    SafetyCarrierService safetyCarrierService;

    @Autowired
    SafetyClassVoConverter safetyClassVoConverter;

    @Autowired
    SafetyCarrierGroupVoConverter safetyCarrierGroupVoConverter;

    @Autowired
    SafetyCarrierVoConverter safetyCarrierVoConverter;

    @ApiOperation(value = "保存权限组（安防载体集）信息（包含门与权限组）")
    @PostMapping("/save")
    public BaseResp<String> saveCarrier(@Valid @RequestBody SafetyCarrierGroupReq safetyCarrierGroupReq) {
        safetyCarrierGroupService.saveOrUpdate(safetyCarrierGroupVoConverter.reqToDto(safetyCarrierGroupReq));
        return BaseResp.success();
    }

    @ApiOperation(value = "批量更新权限组（安防载体集）信息")
    @PostMapping("/batch/update")
    public BaseResp<String> batchUpdate(@Valid @RequestBody SafetyCarrierGroupReq safetyCarrierGroupReq) {
        safetyCarrierGroupService.batchUpdate(safetyCarrierGroupVoConverter.reqToDto(safetyCarrierGroupReq));
        return BaseResp.success("更新成功");
    }

    @ApiOperation(value = "批量删除权限组（安防载体集）信息")
    @PostMapping("/batch/delete")
    public BaseResp<String> batchDelete(@RequestBody List<Long> carrierGroupIds) {
        safetyCarrierGroupService.batchDelete(carrierGroupIds);
        return BaseResp.success(SafetyInterfaceMessageCodeEnum.DELETE_SUCCESS_MESSAGE.getErrDesc());
    }

    @ApiOperation(value = "查询权限组列表（分页）")
    @PostMapping("/query")
    public BaseResp<PageVO<SafetyCarrierGroupVo>> getCarrierByPage(@RequestBody SafetyCarrierGroupReq safetyCarrierGroupReq) {
        SafetyCarrierGroupDto query = safetyCarrierGroupVoConverter.reqToDto(safetyCarrierGroupReq);
        query.setUpdateUser(safetyCarrierGroupReq.getUpdaterUid());
        PageModel<SafetyCarrierGroupDto> pageModel = safetyCarrierGroupService.pageCondition(query);
        return BaseResp.success(PageVO.build(safetyCarrierGroupVoConverter.toVoList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal()));
    }

    @ApiOperation(value = "查询权限组详情")
    @GetMapping("/get")
    public BaseResp<SafetyCarrierGroupVo> getCarrierGroup(@RequestParam String carrierGroupCode) {
        SafetyCarrierGroupDto safetyCarrierGroupDto = safetyCarrierGroupService.getCarrierGroup(carrierGroupCode);
        return BaseResp.success(safetyCarrierGroupVoConverter.dtoToVo(safetyCarrierGroupDto));
    }

    @ApiOperation(value = "查询权限组对应的门的列表（分页）")
    @PostMapping("/carrier/query")
    public BaseResp<PageVO<SafetyCarrierVo>> getCarrierByPage(@RequestParam String carrierGroupCode, @RequestParam Long pageNum, @RequestParam Long pageSize) {
        PageModel<SafetyCarrierDto> pageModel = safetyCarrierService.pageByCarrierGroupCode(carrierGroupCode, pageNum, pageSize);
        return BaseResp.success(PageVO.build(safetyCarrierVoConverter.toVoList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal()));
    }

    @ApiOperation(value = "获取安防载体集类型集合")
    @GetMapping("/class")
    public BaseResp<List<SafetyClassVo>> getCarrierGroupClass() {
        List<SafetyClassDto> list = safetyCarrierGroupService
                .getSafetyClassByParentClassCode(SafetyConstants.DEFAULT_CARRIER_GROUP_PARENT_CLASS_CODE);
        return BaseResp.success(safetyClassVoConverter.toVoList(list));
    }


    @ApiOperation(value = "权限组异步导出")
    @GetMapping("/export")
    public BaseResp<String> exportCarrierGroup(@RequestParam String queryData) {
        //防止频繁点击
        String token = RedisUtils.tryLock("CARRIER_GROUP_LIST", 30);
        if (StringUtils.isEmpty(token)) {
            throw new BizException(InterfaceErrorCodeEnum.INTERFACE_EXPORT_ERROR);
        }
        safetyCarrierGroupService.asyncExportCarrierGroup(queryData);
        return BaseResp.success(SafetyInterfaceMessageCodeEnum.OPERATE_SUCCESS_MESSAGE.getErrDesc());
    }

    //权限组异步导入
    @ApiOperation(value = "权限组异步导入")
    @PostMapping("/import")
    public BaseResp<String> importCarrierGroup(@RequestParam MultipartFile file) {
        safetyCarrierGroupService.asyncImportCarrierGroup(file);
        return BaseResp.success(SafetyInterfaceMessageCodeEnum.OPERATE_SUCCESS_MESSAGE.getErrDesc());
    }


}
