package com.mi.oa.ee.safety.task.consumer;

import api.consumer.NormalConsumer;
import com.mi.oa.ee.safety.application.service.card.reissue.ReissueCardApplyService;
import com.mi.oa.ee.safety.common.enums.RocketMqTopicEnum;
import com.mi.oa.ee.safety.infra.remote.mq.consumer.BaseConsumerNormal;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service("payRefundConsumer")
public class PayRefundConsumer extends BaseConsumerNormal {

    private static final String CONSUMER_GROUP = "info_mims_ee_safety_consumer";

    @Resource
    private ReissueCardApplyService reissueCardApplyService;

    @Override
    public String getConsumerGroup() {
        return CONSUMER_GROUP;
    }

    @Override
    protected void subscribe(NormalConsumer normalConsumer) throws MQClientException {
        normalConsumer.subscribe(RocketMqTopicEnum.REFUND_NOTICE.getTopicName(), "*");
    }

    public ConsumeConcurrentlyStatus doConsumer(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
       for (int i = 0; i < msgs.size(); i++) {
           MessageExt msg = msgs.get(i);
           String refundId = new String(msg.getBody());
           log.info("consumer topic : {},  msg : {}, body:{} ", RocketMqTopicEnum.REFUND_NOTICE.getTopicName(),
                   msg.getMsgId(), refundId);
           try {
               reissueCardApplyService.payRefund(refundId);
           } catch (BizException e) {
               log.info("consumer error topic : {},  msg : {}, errormsg:{} ", RocketMqTopicEnum.REFUND_NOTICE.getTopicName(),
                       msg.getMsgId(), e.getMessage());
               context.setAckIndex(i - 1);
               return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
           } catch (Exception e) {
               log.info("consumer error topic : {},  msg : {} ", RocketMqTopicEnum.REFUND_NOTICE.getTopicName(),
                       msg.getMsgId(), e);
               context.setAckIndex(i - 1);
               return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
           }
       }

       return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
   }
}
