package com.mi.oa.ee.safety.task.job;

import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyImportDto;
import com.mi.oa.ee.safety.application.service.card.coop.CardApplyService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/3/25 20:51
 */
@Component
@Slf4j
@PlanTask(name = "cardMigrationTask", quartzCron = "0 0 0,10 * * ? *", description = "工卡白名单迁移任务")
public class CardMigrationTask implements PlanExecutor {

    @Resource
    private CardApplyService cardApplyService;

    @Override
    public void execute() {
        String url = PlanThreadLocal.getRequestData();
        if (StringUtils.isEmpty(url)) {
            log.warn("card migration task url not found");
            return;
        }
        CardApplyImportDto importDto = new CardApplyImportDto();
        importDto.setCardType("migrate");
        importDto.setUrl(url);
        importDto.setOperator("admin");
        importDto.setFileName("工卡迁移.xlsx");
        cardApplyService.batchImportEmpCard(importDto);
    }
}
