package com.mi.oa.ee.safety.task.job;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.service.safety.SafetyUmsNotifyService;
import com.mi.oa.ee.safety.common.dto.SafetyUmsConfigDto;
import com.mi.oa.ee.safety.common.dto.SafetyUmsNotifyDto;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsSendStatusEnum;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/***
 * create by roger
 */
@Component
@Slf4j
@PlanTask(name = "safetyUmsTask", quartzCron = "0 0/1 * * * ?", description = "安防中所有消息任务的执行")
public class SafetyUmsTask implements PlanExecutor {

    @Autowired
    SafetyUmsNotifyService safetyUmsNotifyService;

    @Override
    public void execute() {
        //所有的需要处理的数据，当前已处理的数据
        int allCount = 0, nowCount = 0;
        try {
            PlanThreadLocal.updateProgress(0, "处理安防短信开始");
            List<SafetyUmsNotifyDto> list = safetyUmsNotifyService.getNowNeedSendNotify(null, ZonedDateTime.now());
            //对所有消息记录按照configId进行分组
            if (CollectionUtils.isNotEmpty(list)) {
                //失败的列表
                List<SafetyUmsNotifyDto> errorList = Lists.newArrayList();
                //所有的需要处理的数据
                allCount = list.size();
                Map<Long, SafetyUmsConfigDto> safetyUmsConfigDTOMap =
                        safetyUmsNotifyService.getAllSafetyUmsConfig(null);
                Map<Long, List<SafetyUmsNotifyDto>> map = buildNotifyMap(list);
                if (MapUtils.isNotEmpty(map)) {
                    for (Map.Entry<Long, List<SafetyUmsNotifyDto>> entry : map.entrySet()) {
                        SafetyUmsConfigDto safetyUmsConfigDTO = safetyUmsConfigDTOMap.get(entry.getKey());
                        List<SafetyUmsNotifyDto> safetyUmsNotifyDtos = entry.getValue();
                        //计算当前处理的数据
                        nowCount += safetyUmsNotifyDtos.size();
                        if (safetyUmsConfigDTO == null) {
                            PlanThreadLocal.updateProgress(nowCount * 100 / allCount,
                                    "当前数据没有对应的配置ID:" + entry.getKey() + " 数量: " + safetyUmsNotifyDtos.size());
                        } else {
                            //发送对应的消息到ums
                            errorList = safetyUmsNotifyService.sendNotify(safetyUmsNotifyDtos, safetyUmsConfigDTO);
                            PlanThreadLocal.updateProgress(nowCount * 100 / allCount, "处理安防消息中");
                        }
                    }
                }
                PlanThreadLocal.updateProgress(nowCount * 100 / allCount, "更新所有安防信息状态，错误数：" + errorList.size());
                //更新对应的消息状态
                updateSendStatus(list, errorList);
            }
            PlanThreadLocal.updateProgress(100, "处理安防消息结束");
        } catch (Exception e) {
            PlanThreadLocal.updateProgress(1, e.getMessage());
            log.error("SafetyUmsTask error", e);
            throw e;
        }
    }

    /**
     * 更新对应的消息状态
     *
     * @param list
     * @param errorList
     * @return void
     * <AUTHOR>
     * @date 2022/8/29 20:05
     */
    private void updateSendStatus(List<SafetyUmsNotifyDto> list, List<SafetyUmsNotifyDto> errorList) {
        Map<Integer, List<Long>> map = Maps.newHashMap();
        List<Long> allErrorIds = Lists.newArrayList();
        //错误信息不为空的时候
        if (CollectionUtils.isNotEmpty(errorList)) {
            for (SafetyUmsNotifyDto safetyUmsNotifyDTO : errorList) {
                List<Long> ids = map.get(safetyUmsNotifyDTO.getSendStatus());
                if (CollectionUtils.isEmpty(ids)) {
                    ids = Lists.newArrayList(safetyUmsNotifyDTO.getId());
                } else {
                    ids.add(safetyUmsNotifyDTO.getId());
                }
                map.put(safetyUmsNotifyDTO.getSendStatus(), ids);
                allErrorIds.add(safetyUmsNotifyDTO.getId());
            }
        }

        //正确信息不为空的时候
        if (CollectionUtils.isNotEmpty(list)) {
            //不在错误信息中的，就是成功的
            List<Long> successIds = list.stream().filter(item -> !allErrorIds.contains(item.getId()))
                    .map(SafetyUmsNotifyDto::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(successIds)) {
                map.put(SafetyUmsSendStatusEnum.SEND_SUCCESS.getStatus(), successIds);
            }
        }

        //对map进行处理
        if (MapUtils.isNotEmpty(map)) {
            for (Map.Entry<Integer, List<Long>> entry : map.entrySet()) {
                safetyUmsNotifyService.updateSendStatus(entry.getValue(), entry.getKey());
            }
        }
    }

    /**
     * 组装以configId为单位
     *
     * @param list
     * @return java.util.Map<java.lang.Long, java.util.List < com.mi.oa.ee.safety.common.dto.SafetyUmsNotifyDTO>>
     * <AUTHOR>
     * @date 2022/8/29 19:49
     */
    private Map<Long, List<SafetyUmsNotifyDto>> buildNotifyMap(List<SafetyUmsNotifyDto> list) {
        Map<Long, List<SafetyUmsNotifyDto>> map = Maps.newHashMap();
        for (SafetyUmsNotifyDto safetyUmsNotifyDTO : list) {
            List<SafetyUmsNotifyDto> nowList = map.get(safetyUmsNotifyDTO.getConfigId());
            if (CollectionUtils.isNotEmpty(nowList)) {
                nowList.add(safetyUmsNotifyDTO);
            } else {
                nowList = Lists.newArrayList();
                nowList.add(safetyUmsNotifyDTO);
            }
            //以模板的配置为单位拆分消息
            map.put(safetyUmsNotifyDTO.getConfigId(), nowList);
        }
        return map;
    }
}
