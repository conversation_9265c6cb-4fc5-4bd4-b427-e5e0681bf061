package com.mi.oa.ee.safety.task.service;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.application.converter.card.CommonCardDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.CardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardPermissionApplyDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardTravelRecordDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionAddDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionDeleteDto;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.ee.safety.application.service.card.perssion.CardPermissionApplyService;
import com.mi.oa.ee.safety.application.service.card.shared.CardTravelRecordService;
import com.mi.oa.ee.safety.application.service.card.shared.CommonCardService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants.Common;
import com.mi.oa.ee.safety.common.dto.CountryDto;
import com.mi.oa.ee.safety.common.enums.card.CardTravelRecordSpecialStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTravelRecordStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTravelRecordUmsSendStatusEnum;
import com.mi.oa.ee.safety.domain.model.CardTravelRecordDo;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.ee.safety.infra.repository.CardTravelRecordRepository;
import com.mi.oa.ee.safety.infra.repository.query.CardTravelRecordQuery;
import com.mi.oa.infra.oaucf.idm.api.IdmUserService;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.idm.api.rep.UserInfoDto;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/22 12:02
 */
@Service
@Slf4j
public class CardTaskService {

    @Resource
    private CardService cardService;

    @Resource
    private CardTravelRecordService cardTravelRecordService;

    @Resource
    private CommonCardService commonCardService;

    @Resource
    private AddressSdk addressSdk;

    @Resource
    private CardPermissionApplyService cardPermissionApplyService;

    @Resource
    private IdmUserService idmUserService;

    @Resource
    private CardTravelRecordRepository cardTravelRecordRepository;

    @Resource
    private CommonCardDtoConverter converter;

    /**
     * 发送消息
     *
     * @param bizId
     * @return void
     * <AUTHOR>
     * @date 2023/8/22 14:11
     */
    public void cardTravelUmsSend(String bizId) {
        List<CardTravelRecordDto> list = cardTravelRecordService
                .queryCardTravelRecordListByUmsSendStatus(CardTravelRecordUmsSendStatusEnum.WAIT_SEND.getCode());
        log.info("----- cardTravelUmsSend bizId : {} wait send size : {}", bizId, list.size());
        if (CollectionUtils.isNotEmpty(list)) {
            //发送ums的消息
            sendUmsMessage(list, bizId);
        }
    }

    /**
     * 开权限
     *
     * @param bizId
     * @return void
     * <AUTHOR>
     * @date 2023/8/11 18:59
     */
    public void cardTravelRecordOpenRight(String bizId) {
        dealRight(CardTravelRecordStatusEnum.WAIT_DEAL.getCode(), bizId);
    }

    /**
     * 关权限
     *
     * @param bizId
     * @return void
     * <AUTHOR>
     * @date 2023/8/11 18:59
     */
    public void cardTravelRecordRemoveRight(String bizId) {
        dealRight(CardTravelRecordStatusEnum.OPENED_RIGHT.getCode(), bizId);
    }

    /**
     * 处理权限
     *
     * @param status
     * @param bizId
     * @return void
     * <AUTHOR>
     * @date 2023/8/11 19:00
     */
    private void dealRight(Integer status, String bizId) {
        String actionName = CardTravelRecordStatusEnum.WAIT_DEAL.getCode().equals(status) ? "cardTravelRecordOpenRight" : "cardTravelRecordRemoveRight";
        List<CardTravelRecordDto> list = cardTravelRecordService.queryCardTravelRecordListByStatus(status);
        if (CollectionUtils.isNotEmpty(list)) {
            List<CardTravelRecordDto> errorList = Lists.newLinkedList();
            List<CardTravelRecordDto> needSendUmsList = Lists.newLinkedList();
            list.forEach(item -> {
                try {
                    if (CardTravelRecordStatusEnum.WAIT_DEAL.getCode().equals(status)) {
                        PermissionAddDto permissionAddDto = cardTravelRecordService.getPermissionAddDtoForOpenRight(item);
                        permissionAddDto.setCardTravelRecordId(item.getId());
                        if (CollectionUtils.isNotEmpty(permissionAddDto.getPermissionDetailList())) {
                            commonCardService.addPermission(permissionAddDto);
                            //添加完权限后更新对应的差旅状态
                            item.setStatus(CardTravelRecordStatusEnum.OPENED_RIGHT.getCode());
                            cardTravelRecordService.updateStatusById(item);
                        }
                        //出发地不等于目的地则发送通知
                        if (permissionAddDto.getHasCarrierGroup() && !StringUtils.equalsIgnoreCase(item.getDestination(), item.getOrigin())) {
                            needSendUmsList.add(item);
                        }
                    } else if (checkIsNeedRevokeAndUpdate(item)) {
                        PermissionDeleteDto permissionDeleteDto = cardTravelRecordService.getPermissionAddDtoForRemoveRight(item);
                        permissionDeleteDto.setCardTravelRecordId(item.getId());
                        if (CollectionUtils.isNotEmpty(permissionDeleteDto.getCarrierGroupCodeList())) {
                            commonCardService.removePermission(permissionDeleteDto);
                            //删除完权限后更新对应的差旅状态
                            item.setStatus(CardTravelRecordStatusEnum.REMOVED_RIGHT.getCode());
                            cardTravelRecordService.updateStatusById(item);
                        }
                    }
                } catch (Exception e) {
                    errorList.add(item);
                    log.error("----- bizid : {} {} error travelApplyId : {}", bizId, actionName, item.getTravelApplyId(), e);
                }
            });
            if (CollectionUtils.isNotEmpty(needSendUmsList)) {
                boolean noticeExpired = LocalDateTime.now().getHour() >= 14;
                needSendUmsList.forEach(item -> {
                    item.setUmsSendStatus(noticeExpired ? CardTravelRecordUmsSendStatusEnum.NOT_NEED_SEND.getCode()
                            : CardTravelRecordUmsSendStatusEnum.WAIT_SEND.getCode());
                    cardTravelRecordService.updateUmsSendStatusById(item);
                });
            }
            log.info("----- bizid : {} {} list : {}  errorList : {}", bizId, actionName, list.size());
        } else {
            log.info("----- bizid : {} {} list : 0", bizId, actionName);
        }
    }

    private Boolean checkIsNeedRevokeAndUpdate(CardTravelRecordDto item) {
        CardTravelRecordQuery cardTravelRecordQuery = new CardTravelRecordQuery();
        cardTravelRecordQuery.setUid(item.getUid());
        cardTravelRecordQuery.setCityIds(item.getCityIds());
        cardTravelRecordQuery.setCountryId(item.getCountryId());
        ZonedDateTime endTime = item.getEndDate();
        cardTravelRecordQuery.setQueryEndTimeFrom(endTime.plusDays(2L));
        cardTravelRecordQuery.setQueryStartTimeTo(ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now()).plusDays(3L));
        cardTravelRecordQuery.setStatusList(Lists.newArrayList(CardTravelRecordStatusEnum.WAIT_DEAL.getCode(),
                CardTravelRecordStatusEnum.NOT_HAVE_RIGHT.getCode()));
        CardTravelRecordDo cardTravelRecordDo =
                cardTravelRecordRepository.findNowPersonLastEndTimeApply(cardTravelRecordQuery);
        if (cardTravelRecordDo != null && Objects.nonNull(cardTravelRecordDo.getId())) {
            //延长当前待回收权限时间
            item.setPermissionRevokeTime(cardTravelRecordDo.getEndTime());
            cardTravelRecordRepository.updateById(converter.toCardTravelRecordDo(item));
            return false;
        }
        return true;
    }

    private void sendUmsMessage(List<CardTravelRecordDto> needSendUmsList, String bizId) {
        //为空不做任何处理
        if (CollectionUtils.isEmpty(needSendUmsList)) {
            return;
        }
        Map<String, List<CardTravelRecordDto>> map = needSendUmsList.stream()
                .collect(Collectors.toMap(item -> item.getTravelApplyId() + "-" + item.getUid(),
                        p -> new ArrayList<>(Arrays.asList(p)), (oldList, newList) -> {
                            oldList.addAll(newList);
                            return oldList;
                        }));
        List<CardTravelRecordDto> sendDtoList = Lists.newArrayList();
        for (Map.Entry<String, List<CardTravelRecordDto>> entry : map.entrySet()) {
            CardTravelRecordDto cardTravelRecordDto = entry.getValue().get(0);
            //检查当前差旅审批单下是否全部成功推给霍尼
            Boolean isAllDone = cardTravelRecordService.checkSafetyOperateLogIsDone(entry.getValue());
            if (isAllDone) {
                //全部推了，说明需要发消息
                LinkedList<String> destination = Lists.newLinkedList();
                entry.getValue().forEach(recordDto -> {
                    if (AddressSdk.isChinaMainland(recordDto.getCountryId())) {
                        destination.add(recordDto.getDestination());
                    } else if (StringUtils.isNotBlank(recordDto.getCountryId())) {
                        CountryDto countryDto = addressSdk.getCountryById(recordDto.getCountryId());
                        Optional.ofNullable(countryDto).ifPresent(item -> destination.addFirst(countryDto.getCnName()));
                    }
                });

                List<String> countryIds = entry.getValue().stream().map(CardTravelRecordDto::getCountryId)
                        .filter(countryId -> !AddressSdk.isChinaMainland(countryId)).collect(Collectors.toList());
                List<String> cityIdList = entry.getValue().stream().map(CardTravelRecordDto::getCityIds).collect(Collectors.toList());
                List<Long> operateLogList = entry.getValue().stream().map(CardTravelRecordDto::getSafetyOperateLogId).collect(Collectors.toList());
                cardTravelRecordDto.setDestination(StringUtils.join(destination, Common.CAESURA));
                cardTravelRecordDto.setCityIds(StringUtils.join(cityIdList, ","));
                cardTravelRecordDto.setCountryIdList(countryIds);
                cardTravelRecordDto.setSafetyOperateLogIdList(operateLogList);

                for (CardTravelRecordDto travelRecordDto : entry.getValue()){
                    if(travelRecordDto.getSpecialStatus().equals(CardTravelRecordSpecialStatusEnum.HAVE_SPECIAL.getCode())){
                        cardTravelRecordDto.setSpecialStatus(travelRecordDto.getSpecialStatus());
                        break;
                    }
                }

                Resp<UserInfoDto> useInfoByUid = idmUserService.findUseInfoByUid(cardTravelRecordDto.getUid());
                if (useInfoByUid != null && useInfoByUid.getData().getName() != null) {
                    cardTravelRecordDto.setName(useInfoByUid.getData().getName());
                } else {
                    cardTravelRecordDto.setName("小米同学");
                }
                sendDtoList.add(cardTravelRecordDto);
                entry.getValue().stream().forEach(item -> {
                    //更新成消息发送成功
                    item.setUmsSendStatus(CardTravelRecordUmsSendStatusEnum.SEND_SUCCESS.getCode());
                    cardTravelRecordService.updateUmsSendStatusById(item);
                });
            } else {
                log.info("----- sendUmsMessage {} applyCode : {} is not all done", bizId, entry.getKey());
            }
        }
        //处理了差旅才发送消息
        sendDtoList.stream().forEach(item -> cardTravelRecordService.sendUmsMessageForDealRight(item));
    }

    /**
     * @param cardInfoDtos
     * @return void
     * @desc 处理过期的工卡
     * <AUTHOR> denghui
     * @date 2022/12/22 15:15
     */
    @Async("asyncTaskExecutor")
    public Future<List<CardInfoDto>> doExpire(List<CardInfoDto> cardInfoDtos) {
        List<CardInfoDto> errorList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(cardInfoDtos)) {
            for (CardInfoDto cardInfoDto : cardInfoDtos) {
                //没有处理成功 添加到错误集合
                if (!dealingExpireFail(cardInfoDto)) {
                    errorList.add(cardInfoDto);
                }
            }
        }
        return new AsyncResult<>(errorList);
    }

    private boolean dealingExpireFail(CardInfoDto cardInfoDto) {
        boolean reFlag = true;
        if (!ObjectUtils.isEmpty(cardInfoDto)) {
            try {
                cardService.doExpire(cardInfoDto);
            } catch (Exception e) {
                log.error("do expire exception {}", cardInfoDto);
                reFlag = false;
            }
        } else {
            log.warn("----- warning error card");
            reFlag = false;
        }
        return reFlag;
    }

    /**
     * @param cardInfoDtos
     * @return void
     * @desc 处理需要激活的工卡
     * <AUTHOR> denghui
     * @date 2022/12/22 15:30
     */
    @Async("asyncTaskExecutor")
    public Future<List<CardInfoDto>> doActive(List<CardInfoDto> cardInfoDtos) {
        List<CardInfoDto> errorList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(cardInfoDtos)) {
            for (CardInfoDto cardInfoDto : cardInfoDtos) {
                //没有处理成功 添加到错误集合
                if (!dealingActiveFail(cardInfoDto)) {
                    errorList.add(cardInfoDto);
                }
            }
        }
        return new AsyncResult<>(errorList);
    }

    private boolean dealingActiveFail(CardInfoDto cardInfoDto) {
        boolean reFlag = true;
        if (!ObjectUtils.isEmpty(cardInfoDto)) {
            try {
                cardService.doActive(cardInfoDto);
            } catch (Exception e) {
                log.error("do active exception {}", cardInfoDto);
                reFlag = false;
            }
        } else {
            log.warn("----- warningApply error card");
            reFlag = false;
        }
        return reFlag;
    }

    /**
     * @param nowList
     * @return java.util.concurrent.Future<java.util.List < com.mi.oa.ee.safety.application.dto.card.CardInfoDto>>
     * @desc 处理一周后过期需要发通知的卡
     * <AUTHOR> denghui
     * @date 2022/12/28 14:33
     */
    @Async("asyncTaskExecutor")
    public Future<List<CardInfoDto>> doNotify(List<CardInfoDto> nowList) {
        List<CardInfoDto> errorList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(nowList)) {
            dealingNotifyFail(nowList);
        }
        return new AsyncResult<>(errorList);
    }

    private void dealingNotifyFail(List<CardInfoDto> cardInfoDto) {
        if (CollectionUtils.isNotEmpty(cardInfoDto)) {
            try {
                cardService.doNotify(cardInfoDto);
            } catch (Exception e) {
                log.error("do notify exception {}", cardInfoDto);
            }
        } else {
            log.warn("----- warningApply error card or responsibleAccount not exist");
        }
    }

    @Async("asyncTaskExecutor")
    public Future<List<CardPermissionApplyDto>> doPermissionExpireClose(List<CardPermissionApplyDto> list) {
        List<CardPermissionApplyDto> errorList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (CardPermissionApplyDto permissionApplyDto : list) {
                //没有处理成功 添加到错误集合
                if (!checkDealIsSuccess(permissionApplyDto)) {
                    errorList.add(permissionApplyDto);
                }
            }
        }
        return new AsyncResult<>(errorList);
    }

    private boolean checkDealIsSuccess(CardPermissionApplyDto permissionApplyDto) {
        boolean reFlag = true;
        if (!ObjectUtils.isEmpty(permissionApplyDto)) {
            try {
                cardPermissionApplyService.doPermissionExpireClose(permissionApplyDto);
            } catch (Exception e) {
                log.error("do permission close params:{} exception:{}", permissionApplyDto, e);
                reFlag = false;
            }
        } else {
            log.warn("----- warningApply error card");
            reFlag = false;
        }
        return reFlag;
    }

    @Async("asyncTaskExecutor")
    public Future<List<CardPermissionApplyDto>> doPermissionExpireNotify(List<CardPermissionApplyDto> list) {
        List<CardPermissionApplyDto> errorList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (CardPermissionApplyDto permissionApplyDto : list) {
                //没有处理成功 添加到错误集合
                if (!checkDealNotifyIsSuccess(permissionApplyDto)) {
                    errorList.add(permissionApplyDto);
                }
            }
        }
        return new AsyncResult<>(errorList);
    }

    private boolean checkDealNotifyIsSuccess(CardPermissionApplyDto permissionApplyDto) {
        boolean reFlag = true;
        if (!ObjectUtils.isEmpty(permissionApplyDto)) {
            try {
                cardPermissionApplyService.doPermissionExpireNotify(permissionApplyDto);
            } catch (Exception e) {
                log.error("do permission close params:{} exception:{}", permissionApplyDto, e);
                reFlag = false;
            }
        } else {
            log.warn("----- warningApply error card");
            reFlag = false;
        }
        return reFlag;
    }
}
