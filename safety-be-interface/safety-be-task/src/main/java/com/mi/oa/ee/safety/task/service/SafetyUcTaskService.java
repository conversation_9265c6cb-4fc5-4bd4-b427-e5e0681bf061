package com.mi.oa.ee.safety.task.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.dto.safety.SafetyMediumDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyOperateLogDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyPersonMediumDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyRightDto;
import com.mi.oa.ee.safety.application.service.safety.SafetyCarrierGroupService;
import com.mi.oa.ee.safety.application.service.safety.SafetyMediumService;
import com.mi.oa.ee.safety.application.service.safety.SafetyOperateLogService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyMediumTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierCodeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.infra.remote.sdk.UcSdk;
import com.mi.oa.ee.safety.task.converter.SafetyConverter;
import com.mi.oa.ee.safety.task.errorcode.TaskErrorCodeEnum;
import com.mi.oa.ee.safety.task.model.SafetyPersonMediumVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.EncryptUtils;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.uc.common.model.ChangePermissionDto;
import com.mi.oa.infra.uc.common.model.IdentityAuthorityChangeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 安防中台和UC数据转换服务
 *
 * <AUTHOR>
 * @date 2022/9/20 9:41
 */
@Slf4j
@Service
public class SafetyUcTaskService {

    @Autowired
    SafetyOperateLogService safetyOperateLogService;

    @Value("${safety.uc.dimension-code:safety-right}")
    String safetyDimensionCode;

    @Autowired
    SafetyMediumService safetyMediumService;

    @Autowired
    SafetyCarrierGroupService safetyCarrierGroupService;

    @Autowired
    SafetyConverter safetyConverter;

    @Autowired
    UcSdk ucSdk;

    @Async("asyncTaskExecutor")
    public Future<List<String>> dealCarrierGroupCodeList(List<String> carrierGroupCodeList,
                                                         List<SafetyPersonMediumVO> safetyPersonMediumVOList,
                                                         Boolean isDelete) {
        List<String> errorList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(carrierGroupCodeList) && CollectionUtils.isNotEmpty(safetyPersonMediumVOList)) {
            List<SafetyCarrierGroupDto> safetyCarrierGroupDtos =
                    safetyCarrierGroupService.listByGroupCodes(carrierGroupCodeList);
            if (CollectionUtils.isEmpty(safetyCarrierGroupDtos)) {
                safetyCarrierGroupDtos = Lists.newArrayList();
            }
            String uid = safetyPersonMediumVOList.get(0).getUid();
            log.info("----- dealCarrierGroupCodeList uid {} exists {} all {}",
                    uid, safetyCarrierGroupDtos.size(), carrierGroupCodeList.size());
            Map<Integer, List<SafetyPersonMediumVO>> mediumCodeMap = buildMediumCodeMap(safetyPersonMediumVOList);
            Map<Integer, List<SafetyCarrierGroupDto>> carrierGroupCodeMap =
                    buildCarrierGroupCodeMap(safetyCarrierGroupDtos);
            for (Map.Entry<Integer, List<SafetyPersonMediumVO>> mediumCodeEntry : mediumCodeMap.entrySet()) {
                SafetyMediumTypeEnum nowSafetyMediumType = SafetyMediumTypeEnum.getEnumByCode(mediumCodeEntry.getKey());
                if (nowSafetyMediumType != null) {
                    List<SafetyCarrierGroupDto> safetyCarrierGroupDtoList = Lists.newArrayList();
                    for (Integer carrierType : nowSafetyMediumType.getCarrierTypeList()) {
                        if (CollectionUtils.isNotEmpty(carrierGroupCodeMap.get(carrierType))) {
                            safetyCarrierGroupDtoList.addAll(carrierGroupCodeMap.get(carrierType));
                        }
                    }
                    List<SafetyRightDto> safetyRightDtos = buildSafeRightDto(
                            safetyCarrierGroupDtoList, mediumCodeEntry.getValue(), uid);
                    log.info("----- dealCarrierGroupCodeList uid {} mediumCodeList {} safetyCarrierGroupDtoList {} safetyRightDtos {}",
                            uid, mediumCodeEntry.getValue().size(), safetyCarrierGroupDtoList.size(), safetyRightDtos.size());
                    if (!isDelete) {
                        //保存
                        safetyMediumService.saveSafetyRightList(safetyRightDtos);
                    } else {
                        //删除
                        safetyMediumService.deleteSafetyRightList(safetyRightDtos);
                    }
                }
            }
        }
        return new AsyncResult<>(errorList);
    }

    /**
     * @param safetyCarrierGroupDtos
     * @param safetyPersonMediumVOList
     * @param uid
     * @return java.util.List<com.mi.oa.ee.safety.application.dto.safety.SafetyRightDto>
     * <AUTHOR>
     * @date 2022/9/22 12:30
     */
    private List<SafetyRightDto> buildSafeRightDto(List<SafetyCarrierGroupDto> safetyCarrierGroupDtos,
                                                   List<SafetyPersonMediumVO> safetyPersonMediumVOList, String uid) {
        List<SafetyRightDto> safetyRightDtoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(safetyCarrierGroupDtos) && CollectionUtils.isNotEmpty(safetyPersonMediumVOList)) {
            for (SafetyCarrierGroupDto safetyCarrierGroupDto : safetyCarrierGroupDtos) {
                for (SafetyPersonMediumVO safetyPersonMediumVO : safetyPersonMediumVOList) {
                    //介质类型和安防载体集类型相同，则认为权限绑定
                    if (!ObjectUtils.isEmpty(safetyCarrierGroupDto) && checkCarrierGroupAndMediumIsMate(safetyCarrierGroupDto,
                            safetyPersonMediumVO.getSafetyMediumDto())) {
                        SafetyRightDto safetyRightDto = new SafetyRightDto();
                        safetyRightDto.setCarrierGroupCode(safetyCarrierGroupDto.getCarrierGroupCode());
                        safetyRightDto.setSupplierCode(safetyCarrierGroupDto.getSupplierCode());
                        safetyRightDto.setSupplierAccessCode(safetyCarrierGroupDto.getSupplierAccessCode());
                        safetyRightDto.setUid(safetyPersonMediumVO.getUid());
                        safetyRightDto.setMediumCode(safetyPersonMediumVO.getSafetyMediumDto().getMediumCode());
                        safetyRightDto.setSupplierCheckCode(
                                safetyPersonMediumVO.getSafetyMediumDto().getMediumPhysicsCode());
                        if (SafetySupplierCodeEnum.VISITOR.getSupplierCode().equals(safetyCarrierGroupDto.getSupplierCode()) ||
                                SafetySupplierCodeEnum.ZYD.getSupplierCode().equals(safetyCarrierGroupDto.getSupplierCode())) {
                            //不需要同步给供应商的给成已同步
                            safetyRightDto.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
                        } else {
                            //需要同步给供应商的给成待同步
                            safetyRightDto.setSyncStatus(SafetySyncStatusEnum.WAIT_SYNC.getCode());
                        }
                        safetyRightDto.setOperateTime(ZonedDateTime.now());
                        safetyRightDto.setStartTime(safetyPersonMediumVO.getStartTime());
                        safetyRightDto.setEndTime(safetyPersonMediumVO.getEndTime());
                        safetyRightDto.setTenantCode("");
                        safetyRightDtoList.add(safetyRightDto);
                    }
                }
            }
        } else {
            log.error("----- buildSafeRightDto uid : {} safetyCarrierGroupDtos or mediumDtoList is null", uid);
        }
        return safetyRightDtoList;
    }

    /**
     * 校验安防介质和安防载体是否匹配
     *
     * @param safetyCarrierGroupDto
     * @param safetyMediumDto
     * @return
     */
    private Boolean checkCarrierGroupAndMediumIsMate(SafetyCarrierGroupDto safetyCarrierGroupDto,
                                                     SafetyMediumDto safetyMediumDto) {
        Boolean isMate = false;
        if (safetyCarrierGroupDto != null && safetyMediumDto != null) {
            SafetyMediumTypeEnum nowMediumType = SafetyMediumTypeEnum.getEnumByCode(safetyMediumDto.getMediumType());
            if (nowMediumType != null) {
                //类型相同，并且所属园区是相同的
                isMate = nowMediumType.getCarrierTypeList().contains(safetyCarrierGroupDto.getCarrierGroupType()) &&
                        (SafetyConstants.ALL_PARK_CODE.equals(safetyMediumDto.getParkCode()) ||
                                safetyCarrierGroupDto.getParkCode().equals(safetyMediumDto.getParkCode()));
            } else {
                throw new BizException(TaskErrorCodeEnum.MEDIUM_TYPE_NOT_EXISTS_ERROR);
            }
        }
        return isMate;
    }

    /**
     * 组装载体集类型
     *
     * @param safetyCarrierGroupDtos
     * @return java.util.Map<java.lang.Integer, java.util.List < java.lang.String>>
     * <AUTHOR>
     * @date 2022/9/22 12:03
     */
    private Map<Integer, List<SafetyCarrierGroupDto>> buildCarrierGroupCodeMap(
            List<SafetyCarrierGroupDto> safetyCarrierGroupDtos) {
        Map<Integer, List<SafetyCarrierGroupDto>> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(safetyCarrierGroupDtos)) {
            for (SafetyCarrierGroupDto safetyCarrierGroupDto : safetyCarrierGroupDtos) {
                List<SafetyCarrierGroupDto> safetyCarrierGroupList =
                        map.get(safetyCarrierGroupDto.getCarrierGroupType());
                if (CollectionUtils.isNotEmpty(safetyCarrierGroupList)) {
                    safetyCarrierGroupList.add(safetyCarrierGroupDto);
                } else {
                    safetyCarrierGroupList = Lists.newArrayList(safetyCarrierGroupDto);
                }
                map.put(safetyCarrierGroupDto.getCarrierGroupType(), safetyCarrierGroupList);
            }
        }
        return map;
    }

    /**
     * 组装介质类型
     *
     * @param safetyPersonMediumVOList
     * @return java.util.Map<java.lang.Integer, java.util.List < java.lang.String>>
     * <AUTHOR>
     * @date 2022/9/22 12:00
     */
    private Map<Integer, List<SafetyPersonMediumVO>> buildMediumCodeMap(
            List<SafetyPersonMediumVO> safetyPersonMediumVOList) {
        Map<Integer, List<SafetyPersonMediumVO>> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(safetyPersonMediumVOList)) {
            for (SafetyPersonMediumVO safetyPersonMediumVO : safetyPersonMediumVOList) {
                //安防介质不为空的数据才能正常处理
                if (safetyPersonMediumVO.getSafetyMediumDto() != null) {
                    List<SafetyPersonMediumVO> mediumCodeList =
                            map.get(safetyPersonMediumVO.getSafetyMediumDto().getMediumType());
                    if (CollectionUtils.isNotEmpty(mediumCodeList)) {
                        mediumCodeList.add(safetyPersonMediumVO);
                    } else {
                        mediumCodeList = Lists.newArrayList(safetyPersonMediumVO);
                    }
                    map.put(safetyPersonMediumVO.getSafetyMediumDto().getMediumType(), mediumCodeList);
                } else {
                    log.warn("----- buildMediumCodeMap safetyMedium {} is not exists", safetyPersonMediumVO.getUid());
                }
            }
        }
        return map;
    }

    /**
     * @param uid
     * @return com.mi.oa.ee.safety.task.model.SafetyPersonMediumVO
     * <AUTHOR>
     * @date 2022/9/22 11:25
     */
    public List<SafetyPersonMediumVO> getSafetyPersonMediumVO(String uid) {
        List<SafetyPersonMediumDto> safetyPersonMediumDtoList = safetyMediumService.getSafetyPersonMediumByUid(uid);
        return safetyConverter.toSafetyPersonMediumVOList(safetyPersonMediumDtoList);
    }

    public void fillCarrierGroupCodeList(IdentityAuthorityChangeDto identityAuthorityChangeDto,
                                         List<String> addCarrierGroupCodeList,
                                         List<String> revokeCarrierGroupCodeList) {
        //填装角色修改导致的安防载体集变化
        fillRoleChange(identityAuthorityChangeDto, addCarrierGroupCodeList, revokeCarrierGroupCodeList);
        //填装数据权限修改导致的安防载体集变化
        fillDataChange(identityAuthorityChangeDto, addCarrierGroupCodeList, revokeCarrierGroupCodeList);
    }

    /**
     * 填装角色修改导致的安防载体集变化
     *
     * @param identityAuthorityChangeDto
     * @param addCarrierGroupCodeList
     * @param revokeCarrierGroupCodeList
     * @return void
     * <AUTHOR>
     * @date 2022/9/22 10:35
     */
    private void fillRoleChange(IdentityAuthorityChangeDto identityAuthorityChangeDto,
                                List<String> addCarrierGroupCodeList, List<String> revokeCarrierGroupCodeList) {
        //处理角色的变更信息
        ChangePermissionDto.ChangeRoleDto changeRoleDto =
                identityAuthorityChangeDto.getChangePermissionDto().getChangeRoleDto();
        if (changeRoleDto == null) {
            log.info("----- uid : {} dealChangeRoleDto is null ", identityAuthorityChangeDto.getIdentityCode());
        } else {
            //组装需要添加的权限
            if (CollectionUtils.isNotEmpty(changeRoleDto.getAddRoleCodeList())) {
                addCarrierGroupCodeList.addAll(buildAllCarrierGroupCodeList(changeRoleDto.getAddRoleCodeList()));
            } else {
                log.info("----- uid : {} addRoleCodeList is null ", identityAuthorityChangeDto.getIdentityCode());
            }
            //组装需要删除的权限
            if (CollectionUtils.isNotEmpty(changeRoleDto.getRevokeRoleCodeList())) {
                revokeCarrierGroupCodeList.addAll(buildAllCarrierGroupCodeList(changeRoleDto.getRevokeRoleCodeList()));
            } else {
                log.info("----- uid : {} revokeRoleCodeList is null ", identityAuthorityChangeDto.getIdentityCode());
            }
        }
    }

    /**
     * 填装数据权限修改导致的安防载体集变化
     *
     * @param identityAuthorityChangeDto
     * @param addCarrierGroupCodeList
     * @param revokeCarrierGroupCodeList
     * @return void
     * <AUTHOR>
     * @date 2022/9/22 10:35
     */
    private void fillDataChange(IdentityAuthorityChangeDto identityAuthorityChangeDto,
                                List<String> addCarrierGroupCodeList, List<String> revokeCarrierGroupCodeList) {
        //处理普通权限的变更
        List<ChangePermissionDto.ChangeDataPermissionDto> dataPermissionDtos =
                identityAuthorityChangeDto.getChangePermissionDto().getChangeDataPermissionDtoList();
        if (CollectionUtils.isNotEmpty(dataPermissionDtos)) {
            ChangePermissionDto.ChangeDataPermissionDto safetyChangeDataPermissionDto =
                    getSafetyChangeDataPermissionDto(dataPermissionDtos);
            if (safetyChangeDataPermissionDto != null) {
                //组装需要添加的权限
                if (CollectionUtils.isNotEmpty(safetyChangeDataPermissionDto.getAddResourceCodeList())) {
                    addCarrierGroupCodeList.addAll(safetyChangeDataPermissionDto.getAddResourceCodeList());
                } else {
                    log.info("----- uid : {} addResourceCodeList is null ",
                            identityAuthorityChangeDto.getIdentityCode());
                }
                //组装需要删除的权限
                if (CollectionUtils.isNotEmpty(safetyChangeDataPermissionDto.getRevokeResourceCodeList())) {
                    revokeCarrierGroupCodeList.addAll(safetyChangeDataPermissionDto.getRevokeResourceCodeList());
                } else {
                    log.info("----- uid : {} revokeResourceCodeList is null ",
                            identityAuthorityChangeDto.getIdentityCode());
                }
            } else {
                log.info("----- uid : {} safetyChangeDataPermissionDto is null ",
                        identityAuthorityChangeDto.getIdentityCode());
            }
        } else {
            log.info("----- uid : {} dataPermissionDtos is null ", identityAuthorityChangeDto.getIdentityCode());
        }
    }

    /**
     * 创建对应的安防载体集列表
     *
     * @param changeRoleCodeList
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/9/22 10:19
     */
    private List<String> buildAllCarrierGroupCodeList(List<String> changeRoleCodeList) {
        List<String> allCarrierGroupCodeList = Lists.newArrayList();
        for (String roleCode : changeRoleCodeList) {
            List<String> nowCarrierGroupCodeAddList = ucSdk.getRoleResourceByRoleCode(roleCode, safetyDimensionCode);
            if (CollectionUtils.isNotEmpty(nowCarrierGroupCodeAddList)) {
                allCarrierGroupCodeList.addAll(nowCarrierGroupCodeAddList);
            }
        }
        return allCarrierGroupCodeList;
    }

    /**
     * 获取对应的变更
     *
     * @param dataPermissionDtos
     * @return com.mi.oa.infra.uc.common.model.ChangePermissionDto.ChangeDataPermissionDto
     * <AUTHOR>
     * @date 2022/9/22 9:56
     */
    private ChangePermissionDto.ChangeDataPermissionDto getSafetyChangeDataPermissionDto(
            List<ChangePermissionDto.ChangeDataPermissionDto> dataPermissionDtos) {
        ChangePermissionDto.ChangeDataPermissionDto safetyChangeDataPermissionDto = null;
        for (ChangePermissionDto.ChangeDataPermissionDto changeDataPermissionDto : dataPermissionDtos) {
            if (safetyDimensionCode.equals(changeDataPermissionDto.getDimensionCode())) {
                safetyChangeDataPermissionDto = changeDataPermissionDto;
                break;
            }
        }
        return safetyChangeDataPermissionDto;
    }

    public SafetyOperateLogDto getOperateLogById(Long logId) {
        return safetyOperateLogService.findById(logId);
    }

    /**
     * 保存对应的操作日志
     *
     * @param safetyOperateLogDto
     * @return void
     * <AUTHOR>
     * @date 2022/9/20 20:14
     */
    public void saveOperateLog(SafetyOperateLogDto safetyOperateLogDto) {
        try {
            safetyOperateLogService.save(safetyOperateLogDto);
        } catch (Exception e) {
            log.error("----- notifySync error", e);
            throw new BizException(TaskErrorCodeEnum.INTERFACE_NOTIFY_SYNC_ERROR);
        }
    }

    public SafetyOperateLogDto buildSafetyOperateLogDto(IdentityAuthorityChangeDto identityAuthorityChangeDto) {
        SafetyOperateLogDto safetyOperateLogDto = new SafetyOperateLogDto();
        try {
            String ucChange = JacksonUtils.bean2Json(identityAuthorityChangeDto);
            String keyCode = EncryptUtils.md5Encrypt(ucChange);
            safetyOperateLogDto.setAppCode(AppCodeEnum.SAFETY.getAppCode());
            safetyOperateLogDto.setBizId(keyCode);
            safetyOperateLogDto.setResponseCode("200");
            safetyOperateLogDto.setRequestUrl("uc_user_authorize_change");
            //认为是不需要同步供应商的
            safetyOperateLogDto.setOperateStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
            safetyOperateLogDto.setOperateDesc("UC变化消息处理");
            safetyOperateLogDto.setRequestParams(ucChange);
            safetyOperateLogDto.setResponseParams("{}");
            safetyOperateLogDto.setTenantCode("");
        } catch (Exception e) {
            log.error("----- buildSafetyOperateLogDto error", e);
            throw new BizException(TaskErrorCodeEnum.INTERFACE_NOTIFY_SYNC_ERROR);
        }

        return safetyOperateLogDto;
    }

    public List<SafetyOperateLogDto> findByBizId(SafetyOperateLogDto safetyOperateLogDto) {
        return safetyOperateLogService.findByBizId(safetyOperateLogDto.getBizId());
    }
}
