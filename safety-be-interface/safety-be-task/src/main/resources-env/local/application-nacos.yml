nacos:
  config:
    bootstrap:
      enable: true
    remote-first: true
    server-addr: http://dev-nacos.api.xiaomi.net:80
    namespace: safety_dev
    username: safety_dev
    password@kc-sid: oa-ee.g
    password: GDCfuM52vQYaJ4qNyP9mfPHA7hcJPMppoqcD/VMR/adVUeanIwSkf4E1mG4gKJlOcd4YElgGPTYWyk3TuRE4Ftq9X+mJ/xgQIjy6cm+KRT2DFQktjFu4HBgUoGkJrwt9VM5zOJByobve8eOzEqEA
    auto-refresh: true
    data-ids: application-actuator.yml,application-datasource.yml,application-jackson.yml,application-orm.yml,application-redis.yml,application-plan.yml,common-oaucf.yml,common-web.yml,task-oaucf.yml,new-between-old.yml
    group: ee.safety
    max-retry: 3
    config-retry-time: 2000
    config-long-poll-timeout: 30000
    enable-remote-sync-config: true
    type: yaml