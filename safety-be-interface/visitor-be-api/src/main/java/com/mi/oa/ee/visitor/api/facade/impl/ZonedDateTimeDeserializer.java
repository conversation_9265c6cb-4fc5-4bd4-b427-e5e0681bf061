package com.mi.oa.ee.visitor.api.facade.impl;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/2/3 17:09
 */
public class ZonedDateTimeDeserializer extends JsonDeserializer<ZonedDateTime> {

    @Override
    public ZonedDateTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
            throws IOException, JsonProcessingException {
        String time = jsonParser.getValueAsString();
        ZonedDateTime zonedDateTime = null;
        if (StringUtils.isNotBlank(time)) {
            zonedDateTime = ZonedDateTime.parse(time).withZoneSameInstant(ZoneId.systemDefault());
        }
        return zonedDateTime;
    }
}
