package com.mi.oa.ee.visitor.api.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/2/22 16:30
 */
@Data
@ApiModel(description = "签到记录对象")
public class VisitorRecordReq {
    @ApiModelProperty(value = "安防载体编码")
    @NotNull(message = "{{{载体编码不能为空}}}")
    private String carrierCode;

    @ApiModelProperty(value = "{{{记录时间}}}")
    @NotNull(message = "{{{记录时间不能为空}}}")
    private ZonedDateTime recordTime;
}
