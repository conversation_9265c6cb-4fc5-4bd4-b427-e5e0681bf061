package com.mi.oa.ee.visitor.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 部门vo
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/9/6 17:59
 */
@ApiModel(value = "DeptVo", description = "部门信息")
@Data
public class DeptVo {

    @ApiModelProperty(value = "部门名称", dataType = "java.lang.String")
    private String deptName;

    @ApiModelProperty(value = "部门层级", dataType = "java.lang.String")
    private String level;
}
