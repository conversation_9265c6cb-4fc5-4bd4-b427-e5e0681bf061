package com.mi.oa.ee.visitor.api.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.mi.info.comb.neptune.client.NeptuneClient;
import lombok.Data;

/**
 * 导出excel对象
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/10/10 16:29
 */
@Data
public class VisitorInfoExportVo {
    private String uid;

    @ExcelProperty("申请单据")
    private String applyCode;

    @ExcelProperty("申请类型")
    private String applyTypeDesc;

    @ExcelProperty("来访地点")
    private String visitPlace;

    @ExcelProperty("来访原因")
    private String visitReasonName;

    @ExcelProperty("公司名称")
    private String companyName;

    @ExcelProperty("访客名称")
    private String name;

    @ExcelProperty("访客国籍/地区名称")
    private String areaName;

    @ExcelProperty("访客手机号")
    private String phone;

    @ExcelProperty("访客邮箱")
    private String email;

    @ExcelProperty("来访日期")
    private String visitTime;

    @ExcelProperty("来访结束日期")
    private String visitEndTime;

    @ExcelProperty("邀请人")
    private String applyName;

    @ExcelProperty("申请人邮箱")
    private String applicantEmail;

    @ExcelProperty("申请人部门")
    private String applyDept;

    @ExcelProperty("接待人")
    private String receiverName;

    @ExcelProperty("接待人邮箱")
    private String receiverEmail;

    @ExcelProperty("接待人部门")
    private String receiverDept;

    @ExcelProperty("申请单状态")
    private String visitStatusName;

    @ExcelProperty("人员状态")
    private String visitInfoStatusName;

    @ExcelProperty("申请时间")
    private String applyTime;

    @ExcelProperty("访客码")
    private String visitCode;

    @ExcelProperty("访客类型")
    private String visitTypeDesc;

    @ExcelProperty("审批通过时间")
    private String approvalTime;

    @ExcelProperty("首次签到时间")
    private String checkInTime;

    @ExcelProperty("签出时间")
    private String checkOutTime;

    @ExcelProperty("车辆信息")
    private String plateNumber;

    @ExcelProperty("车辆来访状态")
    private String vehicleStatusDesc;

    @ExcelProperty("隐私保护")
    private String privacyDesc;

    public void setUid(String uid) {
        this.uid = uid;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public void setApplyTypeDesc(String applyTypeDesc) {
        this.applyTypeDesc = NeptuneClient.getInstance().parseEntryTemplate(applyTypeDesc);
    }

    public void setVisitPlace(String visitPlace) {
        this.visitPlace = visitPlace;
    }

    public void setVisitReasonName(String visitReasonName) {
        this.visitReasonName = visitReasonName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setVisitTime(String visitTime) {
        this.visitTime = visitTime;
    }

    public void setVisitEndTime(String visitEndTime) {
        this.visitEndTime = visitEndTime;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public void setApplicantEmail(String applicantEmail) {
        this.applicantEmail = applicantEmail;
    }

    public void setApplyDept(String applyDept) {
        this.applyDept = applyDept;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public void setReceiverEmail(String receiverEmail) {
        this.receiverEmail = receiverEmail;
    }

    public void setReceiverDept(String receiverDept) {
        this.receiverDept = receiverDept;
    }

    public void setVisitStatusName(String visitStatusName) {
        this.visitStatusName = NeptuneClient.getInstance().parseEntryTemplate(visitStatusName);
    }

    public void setVisitInfoStatusName(String visitInfoStatusName) {
        this.visitInfoStatusName = NeptuneClient.getInstance().parseEntryTemplate(visitInfoStatusName);
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public void setVisitTypeDesc(String visitTypeDesc) {
        this.visitTypeDesc = NeptuneClient.getInstance().parseEntryTemplate(visitTypeDesc);
    }

    public void setApprovalTime(String approvalTime) {
        this.approvalTime = approvalTime;
    }

    public void setCheckInTime(String checkInTime) {
        this.checkInTime = checkInTime;
    }

    public void setCheckOutTime(String checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public void setPrivacyDesc(String privacyDesc) {
        this.privacyDesc = NeptuneClient.getInstance().parseEntryTemplate(privacyDesc);
    }
}
